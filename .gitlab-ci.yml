# GitLab CI/CD 自动合并配置
# 功能：自动合并 V{version}-develop 到 V{version}-release
#
# 执行条件：
# 1. 只在 V*-develop 分支执行（如：V2.16.8-develop）
# 2. 版本号自动从 package.json 读取
# 3. 定时任务：每天晚上12点（UTC时间16点）
# 4. 手动触发：GitLab界面或API
#
# 设置步骤：
# 1. 在GitLab项目中设置 Pipeline Schedule
# 2. Cron表达式：0 16 * * *
# 3. Target Branch：V2.16.8-develop（或当前版本的develop分支）

stages:
  - merge

variables:
  GIT_STRATEGY: clone
  GIT_DEPTH: 0

# 定时合并任务（每天晚上12点）
scheduled_merge:
  stage: merge
  image: alpine/git:latest
  rules:
    # 只在定时任务时执行，且只在develop分支执行
    - if: $CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_REF_NAME =~ /^V.*-develop$/
  before_script:
    - apk add --no-cache curl jq
    - git config --global user.name "<EMAIL>"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_JOB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
    # 从package.json读取版本号并设置分支名称
    - export VERSION=$(cat package.json | jq -r '.version')
    - export DEVELOP_BRANCH="V${VERSION}-develop"
    - export RELEASE_BRANCH="V${VERSION}-release"
    - echo "检测到版本号: ${VERSION}"
    - echo "Develop分支: ${DEVELOP_BRANCH}"
    - echo "Release分支: ${RELEASE_BRANCH}"
  script:
    - echo "🚀 开始定时自动合并 ${DEVELOP_BRANCH} -> ${RELEASE_BRANCH}"
    - |
      # 获取最新代码
      git fetch origin
      
      # 检查是否需要合并
      DEVELOP_COMMIT=$(git rev-parse origin/${DEVELOP_BRANCH})
      RELEASE_COMMIT=$(git rev-parse origin/${RELEASE_BRANCH})
      
      echo "Develop分支最新提交: ${DEVELOP_COMMIT}"
      echo "Release分支最新提交: ${RELEASE_COMMIT}"
      
      if [ "${DEVELOP_COMMIT}" = "${RELEASE_COMMIT}" ]; then
        echo "✅ 两个分支已经同步，无需合并"
        exit 0
      fi
      
      # 切换到release分支
      git checkout ${RELEASE_BRANCH}
      git reset --hard origin/${RELEASE_BRANCH}
      
      # 检查是否有冲突
      if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
        git merge --abort
        echo "✅ 无合并冲突，开始自动合并"
        
        # 执行合并
        MERGE_MSG="chore: 自动合并 ${DEVELOP_BRANCH} 到 ${RELEASE_BRANCH}

        Pipeline: ${CI_PIPELINE_URL}
        合并时间: $(date '+%Y-%m-%d %H:%M:%S')
        Develop提交: ${DEVELOP_COMMIT}
        Release提交: ${RELEASE_COMMIT}"
        
        git merge origin/${DEVELOP_BRANCH} --no-ff -m "${MERGE_MSG}"
        git push origin ${RELEASE_BRANCH}
        
        echo "✅ 自动合并完成！"
        
        # 发送成功通知（可选）
        echo "📧 发送合并成功通知..."
        
      else
        git merge --abort
        echo "⚠️ 检测到合并冲突，需要手动处理"
        
        # 创建Issue提醒
        echo "创建Issue提醒开发者手动处理冲突..."
        
        # 发送冲突通知
        echo "📧 发送冲突通知..."
        
        exit 1
      fi
  after_script:
    - echo "定时合并任务完成"

# 测试用定时任务（每5分钟执行一次，仅用于测试）
# 测试完成后请删除此任务或注释掉
test_scheduled_merge:
  stage: merge
  image: alpine/git:latest
  rules:
    # 测试用：每5分钟执行一次，只在develop分支
    - if: $CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_REF_NAME =~ /^V.*-develop$/ && $TEST_MODE == "true"
  before_script:
    - apk add --no-cache curl jq
    - git config --global user.name "GitLab Test Merge Bot"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_JOB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
    # 从package.json读取版本号并设置分支名称
    - export VERSION=$(cat package.json | jq -r '.version')
    - export DEVELOP_BRANCH="V${VERSION}-develop"
    - export RELEASE_BRANCH="V${VERSION}-release"
    - echo "🧪 测试模式 - 检测到版本号: ${VERSION}"
    - echo "Develop分支: ${DEVELOP_BRANCH}"
    - echo "Release分支: ${RELEASE_BRANCH}"
  script:
    - echo "🧪 测试模式 - 开始测试自动合并逻辑"
    - |
      # 获取最新代码
      git fetch origin

      # 检查是否需要合并
      DEVELOP_COMMIT=$(git rev-parse origin/${DEVELOP_BRANCH})
      RELEASE_COMMIT=$(git rev-parse origin/${RELEASE_BRANCH})

      echo "Develop分支最新提交: ${DEVELOP_COMMIT}"
      echo "Release分支最新提交: ${RELEASE_COMMIT}"

      if [ "${DEVELOP_COMMIT}" = "${RELEASE_COMMIT}" ]; then
        echo "✅ 测试结果：两个分支已经同步，无需合并"
      else
        echo "🔄 测试结果：检测到需要合并的提交"

        # 测试模式：只检查，不实际合并
        git checkout ${RELEASE_BRANCH}
        git reset --hard origin/${RELEASE_BRANCH}

        if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
          git merge --abort
          echo "✅ 测试结果：可以自动合并，无冲突"
          echo "🚀 正式模式下会执行合并操作"
        else
          git merge --abort
          echo "⚠️ 测试结果：检测到合并冲突"
          echo "🛑 正式模式下会停止并通知"
        fi
      fi

      echo "🧪 测试完成！脚本逻辑正常"
  after_script:
    - echo "测试任务完成"

# 手动触发合并
manual_merge:
  stage: merge
  image: alpine/git:latest
  rules:
    # 只在手动触发时执行，且只在develop分支执行
    - if: $CI_PIPELINE_SOURCE == "web" && $CI_COMMIT_REF_NAME =~ /^V.*-develop$/
      when: manual
      allow_failure: false
    # 或者通过API触发
    - if: $CI_PIPELINE_SOURCE == "api" && $MANUAL_MERGE == "true" && $CI_COMMIT_REF_NAME =~ /^V.*-develop$/
  before_script:
    - apk add --no-cache curl jq
    - git config --global user.name "GitLab Manual Merge Bot"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_JOB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
    # 从package.json读取版本号并设置分支名称
    - export VERSION=$(cat package.json | jq -r '.version')
    - export DEVELOP_BRANCH="V${VERSION}-develop"
    - export RELEASE_BRANCH="V${VERSION}-release"
    - echo "检测到版本号: ${VERSION}"
    - echo "Develop分支: ${DEVELOP_BRANCH}"
    - echo "Release分支: ${RELEASE_BRANCH}"
  script:
    - echo "🔧 手动触发合并 ${DEVELOP_BRANCH} -> ${RELEASE_BRANCH}"
    - echo "强制合并模式: ${FORCE_MERGE:-false}"
    - echo "仅检查模式: ${DRY_RUN:-false}"
    - |
      # 获取最新代码
      git fetch origin
      
      # 检查是否需要合并
      DEVELOP_COMMIT=$(git rev-parse origin/${DEVELOP_BRANCH})
      RELEASE_COMMIT=$(git rev-parse origin/${RELEASE_BRANCH})
      
      echo "Develop分支最新提交: ${DEVELOP_COMMIT}"
      echo "Release分支最新提交: ${RELEASE_COMMIT}"
      
      if [ "${DEVELOP_COMMIT}" = "${RELEASE_COMMIT}" ]; then
        echo "✅ 两个分支已经同步，无需合并"
        exit 0
      fi
      
      # DRY RUN 模式
      if [ "${DRY_RUN}" = "true" ]; then
        echo "🔍 DRY RUN 模式 - 仅检查，不实际合并"
        
        git checkout ${RELEASE_BRANCH}
        git reset --hard origin/${RELEASE_BRANCH}
        
        if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
          git merge --abort
          echo "✅ 可以自动合并，无冲突"
        else
          git merge --abort
          echo "⚠️ 检测到合并冲突"
        fi
        exit 0
      fi
      
      # 切换到release分支
      git checkout ${RELEASE_BRANCH}
      git reset --hard origin/${RELEASE_BRANCH}
      
      # 检查冲突并合并
      if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
        git merge --abort
        echo "✅ 无合并冲突，开始合并"
        
        MERGE_MSG="chore: 手动合并 ${DEVELOP_BRANCH} 到 ${RELEASE_BRANCH}

        Pipeline: ${CI_PIPELINE_URL}
        触发者: ${GITLAB_USER_NAME} (${GITLAB_USER_EMAIL})
        合并时间: $(date '+%Y-%m-%d %H:%M:%S')
        Develop提交: ${DEVELOP_COMMIT}
        Release提交: ${RELEASE_COMMIT}"
        
        git merge origin/${DEVELOP_BRANCH} --no-ff -m "${MERGE_MSG}"
        git push origin ${RELEASE_BRANCH}
        
        echo "✅ 手动合并完成！"
        
      else
        git merge --abort
        
        if [ "${FORCE_MERGE}" = "true" ]; then
          echo "🔥 强制合并模式 - 用develop分支覆盖release分支"
          git reset --hard origin/${DEVELOP_BRANCH}
          git push --force-with-lease origin ${RELEASE_BRANCH}
          echo "⚠️ 强制合并完成！release分支已被develop分支覆盖"
        else
          echo "⚠️ 检测到合并冲突，需要手动处理或使用强制合并模式"
          echo "设置 FORCE_MERGE=true 变量可强制合并"
          exit 1
        fi
      fi
  after_script:
    - echo "手动合并任务完成"

# 检查合并状态（可选）
check_merge_status:
  stage: merge
  image: alpine/git:latest
  rules:
    # 只在手动触发检查时执行，且只在develop分支执行
    - if: $CI_PIPELINE_SOURCE == "web" && $CHECK_ONLY == "true" && $CI_COMMIT_REF_NAME =~ /^V.*-develop$/
      when: manual
  before_script:
    - apk add --no-cache jq
    - git config --global user.name "GitLab Check Bot"
    - git config --global user.email "<EMAIL>"
    # 从package.json读取版本号并设置分支名称
    - export VERSION=$(cat package.json | jq -r '.version')
    - export DEVELOP_BRANCH="V${VERSION}-develop"
    - export RELEASE_BRANCH="V${VERSION}-release"
    - echo "检测到版本号: ${VERSION}"
    - echo "Develop分支: ${DEVELOP_BRANCH}"
    - echo "Release分支: ${RELEASE_BRANCH}"
  script:
    - echo "🔍 检查合并状态"
    - |
      git fetch origin
      
      DEVELOP_COMMIT=$(git rev-parse origin/${DEVELOP_BRANCH})
      RELEASE_COMMIT=$(git rev-parse origin/${RELEASE_BRANCH})
      
      echo "Develop分支最新提交: ${DEVELOP_COMMIT}"
      echo "Release分支最新提交: ${RELEASE_COMMIT}"
      
      if [ "${DEVELOP_COMMIT}" = "${RELEASE_COMMIT}" ]; then
        echo "✅ 两个分支已经同步"
      else
        echo "🔄 两个分支不同步，需要合并"
        
        # 检查冲突
        git checkout ${RELEASE_BRANCH}
        git reset --hard origin/${RELEASE_BRANCH}
        
        if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
          git merge --abort
          echo "✅ 可以自动合并，无冲突"
        else
          git merge --abort
          echo "⚠️ 有合并冲突，需要手动处理"
        fi
      fi
