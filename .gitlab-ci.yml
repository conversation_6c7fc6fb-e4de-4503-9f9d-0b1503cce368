# GitLab CI/CD 自动合并配置
# 功能：自动合并 V{version}-develop 到 V{version}-release
# 设置步骤：
# 1. 在GitLab项目中设置 Pipeline Schedule
# 2. Cron表达式：0 16 * * * (每天晚上12点)
# 3. Target Branch：V2.16.8-develop

stages:
  - merge

variables:
  GIT_STRATEGY: clone
  GIT_DEPTH: 0

# 定时合并任务
scheduled_merge:
  stage: merge
  image: alpine/git:latest
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $CI_COMMIT_REF_NAME =~ /^V.*-develop$/
  before_script:
    - apk add --no-cache curl jq
    - git config --global user.name "<EMAIL>"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_JOB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
    - export VERSION=$(cat package.json | jq -r '.version')
    - export DEVELOP_BRANCH="V${VERSION}-develop"
    - export RELEASE_BRANCH="V${VERSION}-release"
    - echo "检测到版本号:${VERSION}"
    - echo "Develop分支:${DEVELOP_BRANCH}"
    - echo "Release分支:${RELEASE_BRANCH}"
  script:
    - echo "开始自动合并 ${DEVELOP_BRANCH} -> ${RELEASE_BRANCH}"
    - git fetch origin
    - DEVELOP_COMMIT=$(git rev-parse origin/${DEVELOP_BRANCH})
    - RELEASE_COMMIT=$(git rev-parse origin/${RELEASE_BRANCH})
    - echo "Develop分支最新提交:${DEVELOP_COMMIT}"
    - echo "Release分支最新提交:${RELEASE_COMMIT}"
    - |
      if [ "${DEVELOP_COMMIT}" = "${RELEASE_COMMIT}" ]; then
        echo "两个分支已经同步，无需合并"
        exit 0
      fi
    - git checkout ${RELEASE_BRANCH}
    - git reset --hard origin/${RELEASE_BRANCH}
    - |
      if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
        git merge --abort
        echo "无合并冲突，开始自动合并"
        MERGE_MSG="chore: 自动合并 ${DEVELOP_BRANCH} 到 ${RELEASE_BRANCH}"
        git merge origin/${DEVELOP_BRANCH} --no-ff -m "${MERGE_MSG}"
        git push origin ${RELEASE_BRANCH}
        echo "自动合并完成！"
      else
        git merge --abort
        echo "检测到合并冲突，需要手动处理"
        exit 1
      fi
  after_script:
    - echo "定时合并任务完成"

# 手动触发合并
manual_merge:
  stage: merge
  image: alpine/git:latest
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^V.*-develop$/
      when: manual
      allow_failure: false
  before_script:
    - apk add --no-cache curl jq
    - git config --global user.name "<EMAIL>"
    - git config --global user.email "<EMAIL>"
    - git remote set-url origin https://oauth2:${CI_JOB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
    - export VERSION=$(cat package.json | jq -r '.version')
    - export DEVELOP_BRANCH="V${VERSION}-develop"
    - export RELEASE_BRANCH="V${VERSION}-release"
    - echo "检测到版本号:${VERSION}"
    - echo "Develop分支:${DEVELOP_BRANCH}"
    - echo "Release分支:${RELEASE_BRANCH}"
  script:
    - echo "手动触发合并 ${DEVELOP_BRANCH} -> ${RELEASE_BRANCH}"
    - echo "强制合并模式:${FORCE_MERGE:-false}"
    - echo "仅检查模式:${DRY_RUN:-false}"
    - git fetch origin
    - DEVELOP_COMMIT=$(git rev-parse origin/${DEVELOP_BRANCH})
    - RELEASE_COMMIT=$(git rev-parse origin/${RELEASE_BRANCH})
    - echo "Develop分支最新提交:${DEVELOP_COMMIT}"
    - echo "Release分支最新提交:${RELEASE_COMMIT}"
    - |
      if [ "${DEVELOP_COMMIT}" = "${RELEASE_COMMIT}" ]; then
        echo "两个分支已经同步，无需合并"
        exit 0
      fi
    - |
      if [ "${DRY_RUN}" = "true" ]; then
        echo "DRY RUN 模式 - 仅检查，不实际合并"
        git checkout ${RELEASE_BRANCH}
        git reset --hard origin/${RELEASE_BRANCH}
        if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
          git merge --abort
          echo "可以自动合并，无冲突"
        else
          git merge --abort
          echo "检测到合并冲突"
        fi
        exit 0
      fi
    - git checkout ${RELEASE_BRANCH}
    - git reset --hard origin/${RELEASE_BRANCH}
    - |
      if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
        git merge --abort
        echo "无合并冲突，开始合并"
        MERGE_MSG="chore: 手动合并 ${DEVELOP_BRANCH} 到 ${RELEASE_BRANCH}"
        git merge origin/${DEVELOP_BRANCH} --no-ff -m "${MERGE_MSG}"
        git push origin ${RELEASE_BRANCH}
        echo "手动合并完成！"
      else
        git merge --abort
        if [ "${FORCE_MERGE}" = "true" ]; then
          echo "强制合并模式 - 用develop分支覆盖release分支"
          git reset --hard origin/${DEVELOP_BRANCH}
          git push --force-with-lease origin ${RELEASE_BRANCH}
          echo "强制合并完成！"
        else
          echo "检测到合并冲突，需要手动处理或使用强制合并模式"
          exit 1
        fi
      fi
  after_script:
    - echo "手动合并任务完成"

# 检查合并状态（随时可手动执行）
check_merge_status:
  stage: merge
  image: alpine/git:latest
  rules:
    - if: $CI_COMMIT_REF_NAME =~ /^V.*-develop$/
      when: manual
      allow_failure: true
  before_script:
    - apk add --no-cache jq
    - export VERSION=$(cat package.json | jq -r '.version')
    - export DEVELOP_BRANCH="V${VERSION}-develop"
    - export RELEASE_BRANCH="V${VERSION}-release"
    - echo "检测到版本号:${VERSION}"
    - echo "Develop分支:${DEVELOP_BRANCH}"
    - echo "Release分支:${RELEASE_BRANCH}"
  script:
    - echo "检查合并状态"
    - git fetch origin
    - DEVELOP_COMMIT=$(git rev-parse origin/${DEVELOP_BRANCH})
    - RELEASE_COMMIT=$(git rev-parse origin/${RELEASE_BRANCH})
    - echo "Develop分支最新提交:${DEVELOP_COMMIT}"
    - echo "Release分支最新提交:${RELEASE_COMMIT}"
    - |
      if [ "${DEVELOP_COMMIT}" = "${RELEASE_COMMIT}" ]; then
        echo "✅ 两个分支已经同步"
      else
        echo "🔄 两个分支不同步，需要合并"
        git checkout ${RELEASE_BRANCH}
        git reset --hard origin/${RELEASE_BRANCH}
        if git merge --no-commit --no-ff origin/${DEVELOP_BRANCH}; then
          git merge --abort
          echo "✅ 可以自动合并，无冲突"
        else
          git merge --abort
          echo "⚠️ 有合并冲突，需要手动处理"
        fi
      fi
  after_script:
    - echo "状态检查完成"
