#!/bin/bash

# 检查版本和分支的脚本
# 用于验证 package.json 中的版本号对应的分支是否存在

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在git仓库中
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是git仓库"
        exit 1
    fi
}

# 读取版本号
get_version() {
    if [ ! -f "package.json" ]; then
        log_error "package.json 文件不存在"
        exit 1
    fi
    
    # 检查是否安装了jq
    if ! command -v jq &> /dev/null; then
        log_warning "未安装 jq，尝试使用 grep 和 sed 解析"
        # 备用方案：使用 grep 和 sed
        VERSION=$(grep '"version"' package.json | sed 's/.*"version": *"\([^"]*\)".*/\1/')
    else
        VERSION=$(cat package.json | jq -r '.version')
    fi
    
    if [ -z "$VERSION" ] || [ "$VERSION" = "null" ]; then
        log_error "无法从 package.json 中读取版本号"
        exit 1
    fi
    
    log_info "检测到版本号: $VERSION"
}

# 检查分支是否存在
check_branches() {
    local develop_branch="V${VERSION}-develop"
    local release_branch="V${VERSION}-release"
    
    log_info "检查分支是否存在..."
    echo "Develop分支: $develop_branch"
    echo "Release分支: $release_branch"
    echo ""
    
    # 获取最新的远程分支信息
    git fetch origin > /dev/null 2>&1
    
    # 检查develop分支
    if git show-ref --verify --quiet refs/remotes/origin/$develop_branch; then
        log_success "✅ $develop_branch 分支存在"
        
        # 显示最新提交信息
        local develop_commit=$(git rev-parse origin/$develop_branch)
        local develop_date=$(git log -1 --format="%ci" origin/$develop_branch)
        echo "   最新提交: $develop_commit"
        echo "   提交时间: $develop_date"
    else
        log_error "❌ $develop_branch 分支不存在"
        DEVELOP_EXISTS=false
    fi
    
    echo ""
    
    # 检查release分支
    if git show-ref --verify --quiet refs/remotes/origin/$release_branch; then
        log_success "✅ $release_branch 分支存在"
        
        # 显示最新提交信息
        local release_commit=$(git rev-parse origin/$release_branch)
        local release_date=$(git log -1 --format="%ci" origin/$release_branch)
        echo "   最新提交: $release_commit"
        echo "   提交时间: $release_date"
        
        # 检查两个分支是否同步
        local develop_commit=$(git rev-parse origin/$develop_branch 2>/dev/null || echo "")
        if [ -n "$develop_commit" ] && [ "$develop_commit" = "$release_commit" ]; then
            log_success "✅ 两个分支已同步"
        elif [ -n "$develop_commit" ]; then
            log_warning "⚠️ 两个分支不同步，需要合并"
            
            # 检查是否有冲突
            git checkout $release_branch > /dev/null 2>&1 || git checkout -b $release_branch origin/$release_branch > /dev/null 2>&1
            if git merge --no-commit --no-ff origin/$develop_branch > /dev/null 2>&1; then
                git merge --abort > /dev/null 2>&1
                log_info "   可以自动合并，无冲突"
            else
                git merge --abort > /dev/null 2>&1
                log_warning "   有合并冲突，需要手动处理"
            fi
        fi
    else
        log_error "❌ $release_branch 分支不存在"
        RELEASE_EXISTS=false
    fi
}

# 显示可用的版本分支
show_available_branches() {
    log_info "显示所有版本相关的分支..."
    echo ""
    
    # 显示所有V开头的分支
    local version_branches=$(git branch -r | grep -E 'origin/V[0-9]+\.[0-9]+\.[0-9]+-(develop|release)' | sort)
    
    if [ -n "$version_branches" ]; then
        echo "可用的版本分支："
        echo "$version_branches" | while read branch; do
            echo "  $branch"
        done
    else
        log_warning "未找到版本分支"
    fi
}

# 主函数
main() {
    log_info "检查版本和分支状态..."
    echo ""
    
    # 检查环境
    check_git_repo
    
    # 读取版本号
    get_version
    echo ""
    
    # 检查分支
    DEVELOP_EXISTS=true
    RELEASE_EXISTS=true
    check_branches
    
    echo ""
    
    # 显示可用分支
    show_available_branches
    
    echo ""
    
    # 总结
    if [ "$DEVELOP_EXISTS" = true ] && [ "$RELEASE_EXISTS" = true ]; then
        log_success "✅ 版本 $VERSION 的分支配置正确，可以进行自动合并"
    else
        log_error "❌ 版本 $VERSION 的分支配置不完整，请检查分支是否存在"
        exit 1
    fi
}

# 执行主函数
main "$@"
