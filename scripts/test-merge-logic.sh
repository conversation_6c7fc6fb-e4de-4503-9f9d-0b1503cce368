#!/bin/bash

# 本地测试自动合并逻辑脚本
# 模拟GitLab CI/CD的合并逻辑，但在本地环境执行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${CYAN}[TEST]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查本地环境..."
    
    # 检查是否在git仓库中
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是git仓库"
        exit 1
    fi
    
    # 检查package.json
    if [ ! -f "package.json" ]; then
        log_error "package.json 文件不存在"
        exit 1
    fi
    
    # 检查jq
    if ! command -v jq &> /dev/null; then
        log_warning "未安装jq，尝试使用备用方案解析package.json"
        USE_JQ=false
    else
        USE_JQ=true
    fi
    
    log_success "环境检查通过"
}

# 读取版本号（模拟CI脚本逻辑）
get_version() {
    log_info "从package.json读取版本号..."
    
    if [ "$USE_JQ" = true ]; then
        VERSION=$(cat package.json | jq -r '.version')
    else
        # 备用方案：使用grep和sed
        VERSION=$(grep '"version"' package.json | sed 's/.*"version": *"\([^"]*\)".*/\1/')
    fi
    
    if [ -z "$VERSION" ] || [ "$VERSION" = "null" ]; then
        log_error "无法从package.json中读取版本号"
        exit 1
    fi
    
    # 设置分支名称（模拟CI环境变量）
    export DEVELOP_BRANCH="V${VERSION}-develop"
    export RELEASE_BRANCH="V${VERSION}-release"
    
    log_success "检测到版本号: $VERSION"
    log_info "Develop分支: $DEVELOP_BRANCH"
    log_info "Release分支: $RELEASE_BRANCH"
}

# 检查分支是否存在
check_branches() {
    log_info "检查远程分支是否存在..."
    
    # 获取最新的远程分支信息
    git fetch origin
    
    # 检查develop分支
    if git show-ref --verify --quiet refs/remotes/origin/$DEVELOP_BRANCH; then
        log_success "✅ $DEVELOP_BRANCH 分支存在"
    else
        log_error "❌ $DEVELOP_BRANCH 分支不存在"
        exit 1
    fi
    
    # 检查release分支
    if git show-ref --verify --quiet refs/remotes/origin/$RELEASE_BRANCH; then
        log_success "✅ $RELEASE_BRANCH 分支存在"
    else
        log_error "❌ $RELEASE_BRANCH 分支不存在"
        exit 1
    fi
}

# 测试合并逻辑（模拟CI脚本）
test_merge_logic() {
    log_test "开始测试合并逻辑..."
    
    # 获取最新提交
    DEVELOP_COMMIT=$(git rev-parse origin/$DEVELOP_BRANCH)
    RELEASE_COMMIT=$(git rev-parse origin/$RELEASE_BRANCH)
    
    echo ""
    log_info "Develop分支最新提交: $DEVELOP_COMMIT"
    log_info "Release分支最新提交: $RELEASE_COMMIT"
    echo ""
    
    # 检查是否需要合并
    if [ "$DEVELOP_COMMIT" = "$RELEASE_COMMIT" ]; then
        log_success "✅ 两个分支已经同步，无需合并"
        return 0
    fi
    
    log_test "检测到新提交，需要合并"
    
    # 保存当前分支
    CURRENT_BRANCH=$(git branch --show-current)
    
    # 切换到release分支进行测试
    log_test "切换到 $RELEASE_BRANCH 分支进行冲突检测..."
    
    # 确保本地有release分支
    if ! git show-ref --verify --quiet refs/heads/$RELEASE_BRANCH; then
        git checkout -b $RELEASE_BRANCH origin/$RELEASE_BRANCH
    else
        git checkout $RELEASE_BRANCH
        git reset --hard origin/$RELEASE_BRANCH
    fi
    
    # 测试合并（不实际提交）
    log_test "测试合并 $DEVELOP_BRANCH -> $RELEASE_BRANCH..."
    
    if git merge --no-commit --no-ff origin/$DEVELOP_BRANCH > /dev/null 2>&1; then
        git merge --abort > /dev/null 2>&1
        log_success "✅ 可以自动合并，无冲突"
        log_test "在CI环境中会执行实际合并"
        MERGE_RESULT="success"
    else
        git merge --abort > /dev/null 2>&1
        log_warning "⚠️ 检测到合并冲突"
        log_test "在CI环境中会停止并发送通知"
        MERGE_RESULT="conflict"
    fi
    
    # 恢复到原来的分支
    if [ -n "$CURRENT_BRANCH" ]; then
        git checkout $CURRENT_BRANCH
    fi
    
    return 0
}

# 显示测试结果总结
show_test_summary() {
    echo ""
    echo "=========================================="
    log_test "本地测试结果总结"
    echo "=========================================="
    echo "版本号: $VERSION"
    echo "Develop分支: $DEVELOP_BRANCH"
    echo "Release分支: $RELEASE_BRANCH"
    echo ""
    
    if [ "$MERGE_RESULT" = "success" ]; then
        log_success "✅ 合并测试通过 - CI环境中会自动合并"
        echo ""
        echo "预期的CI行为："
        echo "1. 自动合并 $DEVELOP_BRANCH 到 $RELEASE_BRANCH"
        echo "2. 推送合并结果到远程仓库"
        echo "3. 发送成功通知（如果配置了）"
    elif [ "$MERGE_RESULT" = "conflict" ]; then
        log_warning "⚠️ 检测到冲突 - CI环境中会停止合并"
        echo ""
        echo "预期的CI行为："
        echo "1. 停止自动合并流程"
        echo "2. 创建Issue或发送通知提醒手动处理"
        echo "3. 不会强制推送，保护代码安全"
    else
        log_info "ℹ️ 分支已同步 - CI环境中无需操作"
        echo ""
        echo "预期的CI行为："
        echo "1. 检测到分支已同步"
        echo "2. 跳过合并操作"
        echo "3. 正常结束流程"
    fi
    
    echo ""
    echo "=========================================="
    log_info "如需在GitLab中设置定时任务："
    echo "1. 进入项目 → CI/CD → Schedules"
    echo "2. 新建定时任务：0 16 * * * (每天晚上12点)"
    echo "3. Target Branch: $DEVELOP_BRANCH"
    echo "=========================================="
}

# 主函数
main() {
    echo "🧪 本地测试自动合并逻辑"
    echo "模拟GitLab CI/CD环境的合并检测"
    echo ""
    
    # 执行测试步骤
    check_environment
    echo ""
    
    get_version
    echo ""
    
    check_branches
    echo ""
    
    test_merge_logic
    
    # 显示总结
    show_test_summary
}

# 执行主函数
main "$@"
