#!/bin/bash

# 自动合并 V2.16.8-develop 到 V2.16.8-release 脚本
# 使用方法：
# ./scripts/auto-merge-develop-to-release.sh [选项]
# 选项：
#   --dry-run    仅检查，不实际合并
#   --force      强制合并（有冲突时用develop覆盖release）
#   --help       显示帮助信息

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
DRY_RUN=false
FORCE_MERGE=false

# 从package.json读取版本号并设置分支名称
get_version_from_package_json() {
    if [ ! -f "package.json" ]; then
        log_error "package.json 文件不存在"
        exit 1
    fi

    # 检查是否安装了jq
    if ! command -v jq &> /dev/null; then
        log_error "需要安装 jq 来解析 package.json"
        echo "请安装 jq: brew install jq (macOS) 或 apt-get install jq (Ubuntu)"
        exit 1
    fi

    VERSION=$(cat package.json | jq -r '.version')
    if [ "$VERSION" = "null" ] || [ -z "$VERSION" ]; then
        log_error "无法从 package.json 中读取版本号"
        exit 1
    fi

    DEVELOP_BRANCH="V${VERSION}-develop"
    RELEASE_BRANCH="V${VERSION}-release"

    log_info "检测到版本号: $VERSION"
    log_info "Develop分支: $DEVELOP_BRANCH"
    log_info "Release分支: $RELEASE_BRANCH"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --force)
            FORCE_MERGE=true
            shift
            ;;
        --help)
            echo "自动合并脚本使用说明："
            echo "  $0 [选项]"
            echo ""
            echo "选项："
            echo "  --dry-run    仅检查是否可以合并，不实际执行合并"
            echo "  --force      强制合并（有冲突时用develop分支覆盖release分支）"
            echo "  --help       显示此帮助信息"
            echo ""
            echo "示例："
            echo "  $0                    # 正常合并"
            echo "  $0 --dry-run          # 仅检查"
            echo "  $0 --force            # 强制合并"
            exit 0
            ;;
        *)
            echo -e "${RED}未知参数: $1${NC}"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在git仓库中
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是git仓库"
        exit 1
    fi
}

# 检查分支是否存在
check_branches_exist() {
    log_info "检查分支是否存在..."
    
    if ! git show-ref --verify --quiet refs/remotes/origin/$DEVELOP_BRANCH; then
        log_error "远程分支 origin/$DEVELOP_BRANCH 不存在"
        exit 1
    fi
    
    if ! git show-ref --verify --quiet refs/remotes/origin/$RELEASE_BRANCH; then
        log_error "远程分支 origin/$RELEASE_BRANCH 不存在"
        exit 1
    fi
    
    log_success "分支检查通过"
}

# 获取最新代码
fetch_latest() {
    log_info "获取最新代码..."
    git fetch origin
    log_success "代码获取完成"
}

# 检查是否需要合并
check_merge_needed() {
    log_info "检查是否需要合并..."
    
    DEVELOP_COMMIT=$(git rev-parse origin/$DEVELOP_BRANCH)
    RELEASE_COMMIT=$(git rev-parse origin/$RELEASE_BRANCH)
    
    echo "Develop分支最新提交: $DEVELOP_COMMIT"
    echo "Release分支最新提交: $RELEASE_COMMIT"
    
    if [ "$DEVELOP_COMMIT" = "$RELEASE_COMMIT" ]; then
        log_success "两个分支已经同步，无需合并"
        return 1  # 不需要合并
    else
        log_info "检测到新提交，需要合并"
        return 0  # 需要合并
    fi
}

# 检查合并冲突
check_conflicts() {
    log_info "检查是否有合并冲突..."
    
    # 切换到release分支
    git checkout $RELEASE_BRANCH
    git reset --hard origin/$RELEASE_BRANCH
    
    # 尝试合并但不提交
    if git merge --no-commit --no-ff origin/$DEVELOP_BRANCH > /dev/null 2>&1; then
        git merge --abort > /dev/null 2>&1
        log_success "无合并冲突，可以自动合并"
        return 0  # 无冲突
    else
        git merge --abort > /dev/null 2>&1
        log_warning "检测到合并冲突"
        return 1  # 有冲突
    fi
}

# 执行合并
do_merge() {
    local has_conflicts=$1
    
    if [ "$DRY_RUN" = true ]; then
        log_info "DRY RUN 模式 - 仅检查，不实际合并"
        if [ $has_conflicts -eq 0 ]; then
            log_success "✅ 可以自动合并，无冲突"
        else
            log_warning "⚠️ 有合并冲突，需要手动处理"
        fi
        return 0
    fi
    
    if [ $has_conflicts -eq 0 ]; then
        # 无冲突，正常合并
        log_info "开始自动合并 $DEVELOP_BRANCH -> $RELEASE_BRANCH"
        
        git checkout $RELEASE_BRANCH
        git reset --hard origin/$RELEASE_BRANCH
        
        MERGE_MSG="chore: 自动合并 $DEVELOP_BRANCH 到 $RELEASE_BRANCH

合并时间: $(date '+%Y-%m-%d %H:%M:%S')
Develop提交: $(git rev-parse origin/$DEVELOP_BRANCH)
Release提交: $(git rev-parse origin/$RELEASE_BRANCH)"
        
        git merge origin/$DEVELOP_BRANCH --no-ff -m "$MERGE_MSG"
        git push origin $RELEASE_BRANCH
        
        log_success "✅ 自动合并完成！"
        
    elif [ "$FORCE_MERGE" = true ]; then
        # 强制合并
        log_warning "强制合并模式 - 用develop分支覆盖release分支"
        
        git checkout $RELEASE_BRANCH
        git reset --hard origin/$DEVELOP_BRANCH
        git push --force-with-lease origin $RELEASE_BRANCH
        
        log_warning "⚠️ 强制合并完成！release分支已被develop分支覆盖"
        
    else
        # 有冲突且不强制合并
        log_error "检测到合并冲突，需要手动处理"
        echo ""
        echo "请手动执行以下命令解决冲突："
        echo "  git checkout $RELEASE_BRANCH"
        echo "  git merge $DEVELOP_BRANCH"
        echo "  # 解决冲突后："
        echo "  git add ."
        echo "  git commit"
        echo "  git push origin $RELEASE_BRANCH"
        echo ""
        echo "或者使用 --force 参数强制合并（会覆盖release分支）"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始自动合并流程..."
    echo "参数: DRY_RUN=$DRY_RUN, FORCE_MERGE=$FORCE_MERGE"
    echo ""

    # 读取版本号并设置分支名称
    get_version_from_package_json
    echo ""

    # 检查环境
    check_git_repo
    check_branches_exist
    
    # 获取最新代码
    fetch_latest
    
    # 检查是否需要合并
    if ! check_merge_needed; then
        exit 0
    fi
    
    # 检查冲突
    check_conflicts
    has_conflicts=$?
    
    # 执行合并
    do_merge $has_conflicts
    
    log_success "合并流程完成！"
}

# 执行主函数
main "$@"
