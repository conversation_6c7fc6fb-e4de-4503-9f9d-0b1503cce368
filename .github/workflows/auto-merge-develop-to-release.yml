name: Auto Merge Develop to Release

on:
  # 定时触发：每天上午10点执行
  schedule:
    - cron: '0 2 *  * *'  # UTC时间2点 = 北京时间10点
  
  # 手动触发
  workflow_dispatch:
    inputs:
      force_merge:
        description: '强制合并（即使有冲突也尝试合并）'
        required: false
        default: 'false'
        type: choice
        options:
          - 'false'
          - 'true'
      
      dry_run:
        description: '仅检查是否可以合并（不实际合并）'
        required: false
        default: 'false'
        type: choice
        options:
          - 'false'
          - 'true'

jobs:
  auto-merge:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Configure Git
      run: |
        git config --global user.name "Auto Merge Bot"
        git config --global user.email "<EMAIL>"
    
    - name: Fetch all branches
      run: |
        git fetch origin
        git checkout V2.16.8-develop
        git pull origin V2.16.8-develop
        git checkout V2.16.8-release
        git pull origin V2.16.8-release
    
    - name: Check if merge is needed
      id: check_merge
      run: |
        # 检查develop分支是否有新的提交
        DEVELOP_COMMIT=$(git rev-parse origin/V2.16.8-develop)
        RELEASE_COMMIT=$(git rev-parse origin/V2.16.8-release)
        
        echo "develop_commit=$DEVELOP_COMMIT" >> $GITHUB_OUTPUT
        echo "release_commit=$RELEASE_COMMIT" >> $GITHUB_OUTPUT
        
        if [ "$DEVELOP_COMMIT" = "$RELEASE_COMMIT" ]; then
          echo "need_merge=false" >> $GITHUB_OUTPUT
          echo "✅ V2.16.8-develop 和 V2.16.8-release 已经同步，无需合并"
        else
          echo "need_merge=true" >> $GITHUB_OUTPUT
          echo "🔄 检测到新提交，需要合并"
          
          # 检查是否有冲突
          git checkout V2.16.8-release
          if git merge --no-commit --no-ff origin/V2.16.8-develop; then
            git merge --abort
            echo "has_conflicts=false" >> $GITHUB_OUTPUT
            echo "✅ 可以自动合并，无冲突"
          else
            git merge --abort
            echo "has_conflicts=true" >> $GITHUB_OUTPUT
            echo "⚠️ 检测到合并冲突"
          fi
        fi
    
    - name: Dry run check
      if: ${{ github.event.inputs.dry_run == 'true' }}
      run: |
        echo "🔍 DRY RUN 模式 - 仅检查，不实际合并"
        echo "需要合并: ${{ steps.check_merge.outputs.need_merge }}"
        echo "有冲突: ${{ steps.check_merge.outputs.has_conflicts }}"
        echo "Develop提交: ${{ steps.check_merge.outputs.develop_commit }}"
        echo "Release提交: ${{ steps.check_merge.outputs.release_commit }}"
        exit 0
    
    - name: Auto merge without conflicts
      if: ${{ steps.check_merge.outputs.need_merge == 'true' && steps.check_merge.outputs.has_conflicts == 'false' && github.event.inputs.dry_run != 'true' }}
      run: |
        echo "🚀 开始自动合并 V2.16.8-develop -> V2.16.8-release"
        git checkout V2.16.8-release
        git merge origin/V2.16.8-develop --no-ff -m "chore: 自动合并 V2.16.8-develop 到 V2.16.8-release

        Develop提交: ${{ steps.check_merge.outputs.develop_commit }}
        Release提交: ${{ steps.check_merge.outputs.release_commit }}
        合并时间: $(date '+%Y-%m-%d %H:%M:%S')
        "
        
        git push origin V2.16.8-release
        echo "✅ 自动合并完成！"
    
    - name: Handle conflicts
      if: ${{ steps.check_merge.outputs.need_merge == 'true' && steps.check_merge.outputs.has_conflicts == 'true' && github.event.inputs.force_merge != 'true' && github.event.inputs.dry_run != 'true' }}
      run: |
        echo "⚠️ 检测到合并冲突，需要手动处理"
        echo "请手动执行以下命令解决冲突："
        echo "git checkout V2.16.8-release"
        echo "git merge V2.16.8-develop"
        echo "# 解决冲突后："
        echo "git add ."
        echo "git commit"
        echo "git push origin V2.16.8-release"
        
        # 创建Issue提醒
        echo "创建Issue提醒开发者手动处理冲突..."
        exit 1
    
    - name: Force merge with conflicts
      if: ${{ steps.check_merge.outputs.need_merge == 'true' && steps.check_merge.outputs.has_conflicts == 'true' && github.event.inputs.force_merge == 'true' && github.event.inputs.dry_run != 'true' }}
      run: |
        echo "🔥 强制合并模式 - 使用develop分支覆盖release分支"
        git checkout V2.16.8-release
        git reset --hard origin/V2.16.8-develop
        git push --force-with-lease origin V2.16.8-release
        echo "⚠️ 强制合并完成！release分支已被develop分支覆盖"
    
    - name: No merge needed
      if: ${{ steps.check_merge.outputs.need_merge == 'false' }}
      run: |
        echo "✅ V2.16.8-develop 和 V2.16.8-release 已经同步，无需合并"
    
    - name: Send notification
      if: ${{ steps.check_merge.outputs.need_merge == 'true' && github.event.inputs.dry_run != 'true' }}
      run: |
        echo "📧 发送合并通知..."
        # 这里可以添加钉钉、企业微信等通知逻辑
        echo "合并状态: ${{ job.status }}"
        echo "有冲突: ${{ steps.check_merge.outputs.has_conflicts }}"
