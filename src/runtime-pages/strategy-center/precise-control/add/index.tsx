import createStrategyAddFactory from "../../base/createStrategyAddFactory";

import {
    AUTH_TYPE_DEVICE,
    AUTH_TYPE_FLEET,
    AUTH_TYPE_VEHICLE,
    SCOPE_TYPE_WITHOUT_CHILD,
    SCOPE_TYPE_WITH_CHILD,
} from '../../base/constant';
import { CONFIGURE_TYPE_FLOW, INTERNATIONAL_TYPE } from "../constant";
import type { StepData } from "../../base/types";
import { i18n } from "@base-app/runtime-lib";
import DefaultBasicInfo from "../../base/components/DefaultBasicInfo";
import DefaultAuthorizeScope from "../../base/components/DefaultAuthorizeScope";
import type { ScopeModuleType } from '@/utils/constant';
import { useState } from "react";
import { useAsyncEffect } from "@streamax/hooks";
import { getCaptureScopeModules } from "@/utils/commonFun";
import ConfigControlForm from "@/runtime-pages/flow-center/flow-setting/components/ConfigControlForm";
import useUrlState from "@ahooksjs/use-url-state";
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { PageTabs } from '@/types/pageReuse/pageReuseBase';
import { getCustomItems } from '@/utils/pageReuse';

export type PreciseControlAddShareProps = PageTabs;

const PreciseControlAdd = (props: PreciseControlAddShareProps) => {

    /**定制***/ 
    const { getPageAddTabConfigItems } = props;
    /**定制***/ 

	const [modules, setModules] = useState<ScopeModuleType[]>([]);
	const [searchParams] = useUrlState({ appId: undefined });
	
	useAsyncEffect(async () => {
		const modules = await getCaptureScopeModules(searchParams.appId);
		setModules(modules);
	}, []);

	console.log("modules", modules);
	
	const stepList: StepData[] = [
		{
			key: 'basicConfig',
			stepName: i18n.t('name', '基本配置'),
			segmentList: [
				{
					key: 'basicInfo',
					segmentName: i18n.t('name', '基本信息'),
					segmentContent: (
						<DefaultBasicInfo type="add" internationalType={INTERNATIONAL_TYPE} />
					),
					bottomDivider: true,
				},
				{
					key: 'flowConfig',
					segmentName: i18n.t('name', '流量设置'),
					segmentContent: <ConfigControlForm pageType="precise" />,
				},
			],
		},
		{
			key: 'authorizeScope',
			stepName: i18n.t('name', '授权范围'),
			segmentList: [
				{
					key: 'authorizeScope',
					segmentName: i18n.t('name', '授权范围'),
					segmentContent: (
						<DefaultAuthorizeScope
							modules={modules}
							batchSave
							type="add"
							appId={searchParams.appId}
						/>
					),
				},
			],
		},
	];
	
	const resToStep1FormData = (res: any) => {
		const { description, appId, paramList = [] } = res;
		const baseInfo = {
			appId,
			description,
		};
		const flowSetting = paramList.find((item: any) => item.paramName === 'flowConfig')?.paramValue;
		const flowResData = JSON.parse(flowSetting || "{}");
		return [baseInfo, flowResData];
	};

	const formatStep1ParamData = (params: any) => {
		const data = Array.isArray(params) ? params[1] : params;
		return [
			{
				paramName: 'flowConfig',
				paramValue: JSON.stringify(data),
			},
		];
	};

	const formatStep2ReqData = (params: any) => {
		const { scopeFleet = [], scopeVehicle = [], scopeDevice = [] } = params || {};
		const authList = [];
		const includeChild = scopeFleet.filter((p: any) => p.scope === SCOPE_TYPE_WITH_CHILD);
		const excludeChild = scopeFleet.filter(
			(p: any) => p.scope === SCOPE_TYPE_WITHOUT_CHILD,
		);
		includeChild.length > 0 &&
			authList.push({
				authType: AUTH_TYPE_FLEET,
				authIds: includeChild.map((p: any) => p.fleetId).join(','),
				isFull: true,
				scope: SCOPE_TYPE_WITH_CHILD,
			});
		excludeChild.length > 0 &&
			authList.push({
				authType: AUTH_TYPE_FLEET,
				authIds: excludeChild.map((p: any) => p.fleetId).join(','),
				isFull: true,
				scope: SCOPE_TYPE_WITHOUT_CHILD,
			});
		scopeVehicle.length > 0 &&
			authList.push({
				authType: AUTH_TYPE_VEHICLE,
				authIds: scopeVehicle.map((p: any) => p.vehicleId).join(','),
				isFull: true,
			});
		scopeDevice.length > 0 &&
			authList.push({
				authType: AUTH_TYPE_DEVICE,
				authIds: scopeDevice.map((p: any) => p.deviceId).join(','),
				isFull: true,
			});
		return authList;
	};

    const newStepList = getCustomItems(getPageAddTabConfigItems, stepList);

	return createStrategyAddFactory(newStepList, CONFIGURE_TYPE_FLOW, {
		formatStep1ParamData,
		formatStep2ReqData,
		resToStep1FormData,
	});
};
export default withShareRootHOC(PreciseControlAdd);
