import { forwardRef, useRef, useState, useEffect } from 'react';
import { Form, Input, Select, Row, Col, Container } from '@streamax/poppy';
import {
    getAppGlobalData,
    i18n,
    utils,
    StarryAbroadFormItem as AFormItem,
} from '@base-app/runtime-lib';
import InternationalInput from '@/components/InternationalInput';
import { useAppList } from '@/hooks';
import './DefaultBasicInfo.less';
import { nameRepeat } from '@/service/strategy';
import { checkFieldSpace } from '@/utils/commonFun';
import { useRequest } from 'ahooks';
import { RspFormLayout } from "@streamax/responsive-layout";
import { SelectProps } from '@streamax/poppy/lib/select';
const { illegalCharacter } = utils.validator;

interface BasicInfoProps {
    type: 'add' | 'edit';
    internationalType: string;
    configureName?: string;
    configureType?: number;
    configureId?: string;
    isDefault?: boolean;
    onMount?: (r: any) => void;
    onInternationInfoSave?: (value: any) => void;
    onFieldsValueChange?: (values: any) => void;
    getAppId?: (value: number) => void;
    onAppIdChange?: (value: number) => void;
    appSelectProps?: SelectProps;
}

export default forwardRef((props: BasicInfoProps, ref: any) => {
    const {
        type,
        internationalType,
        onInternationInfoSave,
        onMount,
        configureId,
        configureName,
        configureType,
        isDefault,
        getAppId,
        onAppIdChange,
        appSelectProps
    } = props;
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const [internationalInfo, setInternationalInfo] = useState<any>();
    const internationalRef = useRef<any>();

    const handleInternationSave = (values: any) => {
        setInternationalInfo(values);
        onInternationInfoSave?.(values);
    };

    useEffect(() => {
        onMount?.(internationalRef.current);
    }, []);

    const repeatValidation = (rule: any, value: string) => {
        const appId = inSaaS
            ? ref?.current?.getFieldValue(['appId'])
            : getAppGlobalData('APP_ID');
        if (!value) return Promise.resolve();
        if (appId === undefined) return Promise.resolve();
        const params = {
            configureName: value || internationalInfo?.objectName,
            configureType,
            appId,
            tenantId: getAppGlobalData('APP_USER_INFO')['tenantId'],
            configureId: configureId || '',
        };
        return new Promise((resolve, reject) => {
            nameRepeat(params).then((rs) => {
                return rs
                    ? reject(i18n.t('message', '设置名称已重复，请重新输入'))
                    : resolve(true);
            });
        });
    };

    const { runAsync: repeatValidationName } = useRequest(repeatValidation, {
        manual: true,
        debounceWait: 300
    });

    const checkSpace = (rule: any, value: string) => {
        return checkFieldSpace(value, i18n.t('message', '设置名称不能为空'));
    };
    return (
        <Form
            name="basicInfoForm"
            layout="vertical"
            className="default-basicinfo-form-pro-container"
            scrollToFirstError={{
                behavior: 'smooth',
                block: 'center',
                scrollMode: 'if-needed',
            }}
            ref={ref}
            onValuesChange={(chanedValues: any, allValues: any) =>
                props?.onFieldsValueChange?.(allValues)
            }
        >
            {/* 自定义证据上传-添加证据上传 */}
            <RspFormLayout layoutType="auto">
                <AFormItem
                    label={i18n.t('name', '设置名称')}
                    name="configureName"
                    className="flex-item international-padding"
                    rules={[
                        { required: true, validator: checkSpace },
                        { validator: illegalCharacter },
                        { validator: repeatValidationName },
                    ]}
                    hidden={isDefault}
                >
                    <InternationalInput
                        maxLength={50}
                        placeholder={i18n.t('message', '请输入设置名称')}
                        internationalType={internationalType as any}
                        modalType={type}
                        allowClear
                        onSave={handleInternationSave}
                        entryKey={configureName}
                        entryIdOrCode={configureId}
                        ref={internationalRef as any}
                    />
                </AFormItem>
                {isDefault ? (
                    <Form.Item
                        label={i18n.t('name', '设置名称')}
                        className="flex-item international-padding"
                    >
                        {i18n.t(
                            `@i18n:@${internationalType}__${configureId}`,
                            configureName,
                        )}
                    </Form.Item>
                ) : null}
                {inSaaS ? (
                    <AFormItem
                        label={i18n.t('name', '归属应用')}
                        name="appId"
                        className="flex-item"
                        rules={[{ required: true }]}
                    >
                        <Select
                            disabled={type === 'edit' ? true : false}
                            allowClear
                            onChange={(value) => {
                                onAppIdChange?.(value as number); // 获取应用id
                                getAppId?.(value as number); // 获取应用id
                            }}
                            options={appList.filter((p: any) => p.value != 0)}
                            placeholder={i18n.t('message', '请选择归属应用')}
                            {...(appSelectProps || {})}
                        />
                    </AFormItem>
                ) : (
                    <div />
                )}
                <RspFormLayout.SingleRow>
                    <AFormItem
                        label={i18n.t('name', '描述')}
                        name="description"
                        rules={[
                            {
                                type: 'string',
                                min: 0,
                            },
                            {
                                type: 'string',
                                max: 500,
                            },
                        ]}
                    >
                        <Input.TextArea
                            maxLength={500}
                            showCount
                            allowClear
                            placeholder={i18n.t('message', '不多于500字')}
                        />
                    </AFormItem>
                </RspFormLayout.SingleRow>
            </RspFormLayout>
        </Form>
    );
});
