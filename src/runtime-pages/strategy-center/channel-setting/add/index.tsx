import createStrategyAddFactory from '../../base/createStrategyAddFactory';

import {
    AUTH_TYPE_DEVICE,
    AUTH_TYPE_FLEET,
    AUTH_TYPE_VEHICLE,
    SCOPE_TYPE_WITHOUT_CHILD,
    SCOPE_TYPE_WITH_CHILD,
} from '../../base/constant';
import { Actions, CONFIGURE_CHANNEL_TYPE, CONFIGURE_TYPE_DEVICE, CONFIGURE_TYPE_FLOW, INTERNATIONAL_CHANNEL_TYPE, INTERNATIONAL_TYPE } from '../constants';
import type { StepData } from '../../base/types';
import { i18n } from '@base-app/runtime-lib';
import DefaultBasicInfo from '../../base/components/DefaultBasicInfo';
import DefaultAuthorizeScope from '../../base/components/DefaultAuthorizeScope';
import type { ScopeModuleType } from '@/utils/constant';
import { useState } from 'react';
import { useAsyncEffect } from '@streamax/hooks';
import { getCaptureScopeModules } from '@/utils/commonFun';
import useUrlState from '@ahooksjs/use-url-state';
import DynamicChannelTableForm from '@/components/DynamicChannelTableForm';
import { formatChannelParams } from '@/hooks/useChannelData/utils';
import { isChannelDevice } from '../../base/utils';
import ChannelSetting from '../components/ChannelSetting';
import { addChannelStrategy, authChannelUpdate, deleteChannelAuthorization, editChannelStrategy, getChannelAuthorizedByPage, getChannelDetail, strategyChannelAuthorized } from '@/service/strategy';
import { getCurrentChannelMode } from '@/service/channel';
import { validateChannelMode } from '@/modules/strategy/ChannelStrategy/util';

const { addPage, copyPage } = Actions;

export default () => {
    const [modules, setModules] = useState<ScopeModuleType[]>([]);
	const [searchParams] = useUrlState({ appId: undefined, configureId: undefined, configureType: undefined });
    const configureType = searchParams?.configureType;
    const [deviceMode, setDeviceMode] = useState(isChannelDevice(configureType));
    
	const isCopy = !!searchParams?.configureId;
    const onAppIdChange = async (appId: string) => {
        const data = await getCurrentChannelMode({
            appId,
        });
        setDeviceMode(data?.channelSettingType === 'device' ? true : false);
    };
    useAsyncEffect(async () => {
        const modules = await getCaptureScopeModules(searchParams.appId);
        setModules(modules);
    }, []);
    const renderChannelSetting = () => {
        if (deviceMode) {
            return (
                <ChannelSetting inDetailPage={false} />
            );
        } else {
            return (
                <DynamicChannelTableForm
                    appId={searchParams.appId}
                    showOperate={false}
                    operateType={isCopy ? 'customCopy' : 'customAdd'}
                    buttonOperateType="link"
                    configureType={CONFIGURE_TYPE_FLOW}
                    authCodes={{
                        channelTypeCode: isCopy
                            ? copyPage.channelType.authCode
                            : addPage.channelType.authCode,
                    }}
                />
            );
        }
    };

    const stepList: StepData[] = [
        {
            key: 'basicConfig',
            stepName: i18n.t('name', '基本配置'),
            segmentList: [
                {
                    key: 'basicInfo',
                    segmentName: i18n.t('name', '基本信息'),
                    segmentContent: (
                        <DefaultBasicInfo appSelectProps={{
                            allowClear: false
                        }} onAppIdChange={onAppIdChange} type="add" internationalType={deviceMode ? INTERNATIONAL_CHANNEL_TYPE : INTERNATIONAL_TYPE} />
                    ),
                    bottomDivider: true,
                },
                {
                    key: 'channelSetting',
                    segmentName: i18n.t('name', '通道设置'),
                    segmentContent: renderChannelSetting(),
                },
            ],
        },
        {
            key: 'authorizeScope',
            stepName: i18n.t('name', '授权范围'),
            segmentList: [
                {
                    key: 'authorizeScope',
                    segmentName: i18n.t('name', '授权范围'),
                    segmentContent: (
                        <DefaultAuthorizeScope
                            modules={modules}
                            batchSave
                            type="add"
                            appId={searchParams.appId}
                            customStrategyAuthorized={strategyChannelAuthorized}
                            customDeleteAuthorization={deleteChannelAuthorization}
                            customGetAuthorizedByPage={getChannelAuthorizedByPage}
                            customAuthUpdate={authChannelUpdate}
                        />
                    ),
                },
            ],
        },
    ];

    const resToStep1FormData = (res: any) => {
        const { description, appId, paramList = [] } = res;
        const baseInfo = {
            appId,
            description,
        };
        const channelResData = formatChannelParams.toFormatFrontParams(paramList);
        return [baseInfo, channelResData];
    };

    const formatStep1ParamData = (params: any) => {
        const data = Array.isArray(params) ? params[1] : params;
        if(deviceMode) {
            return data;
        }
        const { channelSettingList = [] } = data || {};
        return formatChannelParams.toFormatBackParams(channelSettingList?.filter(Boolean));
    };

    const formatStep2ReqData = (params: any) => {
        const { scopeFleet = [], scopeVehicle = [], scopeDevice = [] } = params || {};
        const authList = [];
        const includeChild = scopeFleet.filter((p: any) => p.scope === SCOPE_TYPE_WITH_CHILD);
        const excludeChild = scopeFleet.filter((p: any) => p.scope === SCOPE_TYPE_WITHOUT_CHILD);
        includeChild.length > 0 &&
            authList.push({
                authType: AUTH_TYPE_FLEET,
                authIds: includeChild.map((p: any) => p.fleetId).join(','),
                isFull: true,
                scope: SCOPE_TYPE_WITH_CHILD,
            });
        excludeChild.length > 0 &&
            authList.push({
                authType: AUTH_TYPE_FLEET,
                authIds: excludeChild.map((p: any) => p.fleetId).join(','),
                isFull: true,
                scope: SCOPE_TYPE_WITHOUT_CHILD,
            });
        scopeVehicle.length > 0 &&
            authList.push({
                authType: AUTH_TYPE_VEHICLE,
                authIds: scopeVehicle.map((p: any) => p.vehicleId).join(','),
                isFull: true,
            });
        scopeDevice.length > 0 &&
            authList.push({
                authType: AUTH_TYPE_DEVICE,
                authIds: scopeDevice.map((p: any) => p.deviceId).join(','),
                isFull: true,
            });
        return authList;
    };
    const customAddChannelStrategy = async (requestData: any) => {
        const strategyId = await addChannelStrategy(requestData);
        return strategyId;
    };
    const validateStep1 = async (requestData: any) => {
        return await validateChannelMode({
            configureType: requestData.configureType,
            appId: requestData.appId,
        });
    };

    return createStrategyAddFactory(stepList, deviceMode ? CONFIGURE_TYPE_DEVICE : CONFIGURE_TYPE_FLOW, {
        formatStep1ParamData,
        formatStep2ReqData,
        resToStep1FormData,
        fetchDetail: getChannelDetail,
        updateStrategy: editChannelStrategy,
        createStrategy: customAddChannelStrategy,
        authStrategy: strategyChannelAuthorized,
        validateStep1: validateStep1
    });
};
