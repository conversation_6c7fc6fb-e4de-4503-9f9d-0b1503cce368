import React, { useState, useEffect } from 'react';
import { Form, Modal, Input, NewDatePicker, message } from '@streamax/poppy';
import {
    getAppGlobalData,
    i18n,
    utils,
    StarryAbroadFormItem as AFormItem,
} from '@base-app/runtime-lib';
import moment from 'moment';
import { recordCreate } from '@/service/evidence';
import AllChekcbox from '@/components/AllCheckbox';
import { checkFieldSpace } from '@/utils/commonFun';
import { isNeedControl, CONTROL_RANGE } from '@/utils/flow';

const FormItem = Form.Item;
const validatorVideoName = utils.validator.validateBaseVehicleNumber;
const userConfig = getAppGlobalData('APP_USER_CONFIG');

// 测试todo
const calculateSummerTimeOffset = (
    sourceTime: number,
    // @ts-ignore
    datumTime: number | Moment | undefined,
    dstDisable: boolean | undefined,
    minusOffset: boolean = true,
): number => {
    // 夏令时判断开始
    if (dstDisable) return 0; //禁用夏令时则不需要夏令时偏移
    // dst 是否实行夏令时1实行 0关闭  summerTimeList 实行的夏令时范围  summerTimeOffset 夏令时偏移量 单位：分钟
    const { dst, summerTimeList, summerTimeOffset } = getAppGlobalData('APP_USER_CONFIG');
    let dstTime = 0;
    if (parseInt(dst, 10) === 1) {
        // 实行夏令时
        if (summerTimeList) {
            // 夏令时范围时间段转为number，方便后面比较
            // @ts-ignore
            summerTimeList.forEach((item) => {
                item.startTime = parseInt(item.startTime as string, 10);
                item.endTime = parseInt(item.endTime as string, 10);
            });
            // 基准时间
            let startStamp = datumTime || sourceTime;
            // datumTime如果是moment对象转化为时间戳 这里如果是moment的话，
            // 特别注意，传入的startStamp不能是计算夏令时偏移后的moment， 如果是夏令时后的moment，后面通过moment转化为时间戳会多出1小时，
            // 导致在后面判断该时间出现1小时的误差，本来是夏令时了但是却没有减去夏令时偏移的60分钟，最终导致边看边录的时间提前1小时，导致录制失败
            if (moment.isMoment(startStamp)) {
                const format = 'YYYY-MM-DD HH:mm:ss';
                startStamp = moment.utc(startStamp.format(format)).unix();
            }
            // @ts-ignore 基准时间10进制number类型
            startStamp = parseInt(startStamp, 10);

            const datumTimeInSummerTime = summerTimeList.some(
                // @ts-ignore
                (item) =>
                    // @ts-ignore
                    startStamp >= item.startTime && startStamp <= item.endTime,
            );
            // 在夏令时范围内
            if (datumTimeInSummerTime) {
                dstTime = parseInt(summerTimeOffset, 10) * 60;
                // 判断夏令时偏移的正负
                dstTime = minusOffset ? dstTime : 0 - dstTime;
            }
        }
    } else {
        // 未实行夏令时
        dstTime = 0;
    }
    return dstTime;
    // 夏令时判断结束
};
const timestampToZeroTimeStamp = (
    // @ts-ignore
    sourceTime: number | Moment,
    sourceZone?: number,
    dstDisable?: boolean,
    // @ts-ignore
    datumTime?: number | Moment,
) => {
    const config = getAppGlobalData('APP_USER_CONFIG');
    if (sourceZone === undefined || sourceZone === null) {
        sourceZone = parseInt(config.timeZone, 10) || 0;
    }
    if (!sourceTime) {
        return sourceTime;
    }
    if (moment.isMoment(sourceTime)) {
        const format = 'YYYY-MM-DD HH:mm:ss';
        sourceTime = moment.utc(sourceTime.format(format)).unix();
    }
    // @ts-ignore
    sourceTime = parseInt(sourceTime, 10);
    const dstTime = calculateSummerTimeOffset(sourceTime, datumTime, dstDisable);
    // eslint-ignore no-console
    console.log('夏令时偏移量：', dstTime);
    return sourceTime - dstTime - sourceZone;
};
interface RealtimeRecordProps {
    visible: boolean;
    isFullScreen: boolean; //是否全屏
    recordInfo: any;
    onClose?: () => void;
}

const RealtimeRecord: React.FC<RealtimeRecordProps> = (props) => {
    const { visible = false, isFullScreen = false, recordInfo, onClose } = props;

    const {
        optId,
        recordTime,
        recordTimeStamp,
        recordChannels,
        vehicleNumber,
        deviceNo,
        deviceAlias,
        authId,
        deviceId,
        vehicleId,
    } = recordInfo;

    const [form] = Form.useForm();
    const [channelOptions, setChannelOptions] = useState<any[]>([]);
    const [submitLoading, setSubmitLoading] = useState(false);
    useEffect(() => {
        // 根据数据生成车辆名称、录制时间、视频名称、通道等
        // 车辆名称
        const vehicleName = vehicleNumber + '-' + (deviceAlias || deviceNo);
        // 录制时间
        // modify夏令时视频剪辑不调整
        const videoTime: any = [moment(recordTime[0]), moment(recordTime[1])];
        // 视频名称
        const evidenceName =
            vehicleNumber + '-' + moment().format(userConfig.timeFormat) + '-' + i18n.t('name', '视频');
        // 通道信息 未传设备id的情况下channelInfoList是没有的
        setChannelOptions(
            recordChannels.map((item: any) => ({
                label: item.channelAlias,
                value: item.channel,
            })),
        );
        const channelNos = (recordChannels || []).map((val: any) => val.channel);
        form.setFieldsValue({
            vehicleName,
            videoTime,
            channelNos,
            evidenceName,
        });
    }, [recordInfo, recordTime, recordChannels]);
    const handleOk = () => {
        form.validateFields().then(async (values) => {
            setSubmitLoading(true);
            const needControl = await isNeedControl(authId,CONTROL_RANGE.VIDEO_CUT);
            if(needControl){
                message.warn(i18n.t('message','流量使用超额，请处理流量限制。'));
            }
            const { channelNos, evidenceName, videoTime } = values;
            // recordTimeStamp 是服务器直接拿的时间，不用做转化
            const startTime = recordTimeStamp[0];
            const endTime = recordTimeStamp[1];
            recordCreate({
                evidenceName,
                deviceId: deviceId,
                vehicleId: vehicleId,
                startTime,
                endTime,
                authId: authId,
                channelNos: channelNos.join(','),
                optId: optId,
                // operationModelCode: 'video',
                // operationTypeCode: 'create',
                // operationDetailTypeCode: 'create',
                // logParams: [{ data: evidenceName }],
                prefixUrl: window.location.origin,
            }).then(() => {
                message.success(i18n.t('message', '录制成功'));
                onClose?.();
            }).finally(() => {
                setSubmitLoading(false);
            });
        });
    };

    const getDomContainer = () => {
        return isFullScreen ? document.getElementById('starry-player') : document.body;
    };
    const checkSpace = (rule: any, value: string) => {
        return checkFieldSpace(value, i18n.t('message', '视频名称不能为空'));
    };

    return (
        <Modal
            // @ts-ignore
            getContainer={getDomContainer}
            visible={visible}
            title={i18n.t('name', '录制视频')}
            maskClosable={false}
            width={440}
            okText={i18n.t('action', '确定')}
            cancelText={i18n.t('action', '取消')}
            onOk={handleOk}
            onCancel={() => onClose?.()}
            okButtonProps={{
                loading: submitLoading
            }}
        >
            <Form layout="vertical" form={form}>
                <AFormItem name="vehicleName" label={i18n.t('name', '录制车辆')}>
                    <Input disabled placeholder={i18n.t('message', '请选择车辆')} />
                </AFormItem>
                <AFormItem name="videoTime" label={i18n.t('name', '录制时间')}>
                    {/* @ts-ignore */}
                    <NewDatePicker.NewRangePicker
                        showTime
                        disabled
                        placeholder={[i18n.t('message', '开始时间'), i18n.t('message', '结束时间')]}
                        style={{ width: '100%' }}
                    />
                </AFormItem>
                <AFormItem
                    name="evidenceName"
                    label={i18n.t('name', '视频名称')}
                    rules={[
                        {
                            required: true,
                            validator: checkSpace,
                        },
                        {
                            validator: validatorVideoName,
                        },
                    ]}
                >
                    <Input
                        allowClear
                        maxLength={50}
                        placeholder={i18n.t('message', '请输入视频名称')}
                    />
                </AFormItem>
                <FormItem
                    name="channelNos"
                    label={i18n.t('name', '下载通道')}
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <AllChekcbox options={channelOptions} />
                </FormItem>
            </Form>
        </Modal>
    );
};

export default RealtimeRecord;
