import type { Store<PERSON><PERSON>, UseBoundStore } from 'zustand';
import { create } from 'zustand';
import type { RTDataGeneratorV1 } from '../../../RTDataGenerator';
import type {
    StateChangedType,
    VehicleStateMap,
} from '../../../DeviceStatusDataGenerator/utils';
import { countOnlineNumber } from './utils';
import type { BaseNode, BuildStaticTreeBaseData } from '../../../AsyncStarryTreeData';
import {
    parseTreeKey,
    TOP_FLEET_PARENT_ID,
} from '../../../AsyncStarryTreeData';
import { inPlaceSort } from 'fast-sort';
import rmPageConfig from '@/runtime-pages/monitoring-center/realtime-monitoring-new/page-config';
import { VEHICLE_STATES } from '@/const/vehicle';

interface DisableVehicleStatus {
    disableVehicleSet: Set<string>;
    changed: Set<string>;
}
export interface TreeStatusCount {
    fleetOnlineNumberMap: Record<string, number>;
    updateFleetOnlineNumberMap?: (
        fleetOnlineNumberMap: Record<string, number>,
    ) => void;
    resetStore: () => void;
}

export interface TreeDataInstance {
    getState: () =>
        | {
              loaded?: boolean;
              focusVehicles?: Set<string>;
              fleetList?: BuildStaticTreeBaseData[];
              vehicleList?: BuildStaticTreeBaseData[];
              vehicleIds?: Set<string>;
              vIdParents?: Record<string, Set<string>>;

              fleetChildrenMap?: Record<
                  string,
                  { vehicles?: string[]; fleets?: string[] }
              >;
              treeNodeKeyMap?: Record<string, BaseNode>;
              fleetOnlineNumberMap?: Record<string, number>;
              fleetTotalNumberMap?: Record<string, number>;
              /** 是否启用内部默认排序，默认为 true */
              enableInternalSort?: boolean;
          }
        | undefined;
    setState: (state: any) => void;
    refresh: () => void;
}

export abstract class BaseTreeStatusCountPluginAbstract<
    T extends TreeDataInstance = TreeDataInstance,
> {
    protected readonly treeDataInstance: T;
    public readonly useStatusCountStore: UseBoundStore<
        StoreApi<TreeStatusCount>
    >;
    public isFirstCalculation = true; // 添加标记是否是第一次计算的属性

    constructor(treeDataInstance: T) {
        this.treeDataInstance = treeDataInstance;
        this.useStatusCountStore = create<TreeStatusCount>((set) => ({
            fleetOnlineNumberMap: {},
            updateFleetOnlineNumberMap: (fleetOnlineNumberMap) =>
                set({ fleetOnlineNumberMap }),
            resetStore: () => set({ fleetOnlineNumberMap: {} }),
        }));
    }

    /**
     * 	更新车组的在线车辆数
     * 	@param {Record<string, number>} fleetOnlineNumberMap 车组id与在线车辆数的Map
     * 	@async
     */
    public async updateFleetOnlineNumberMap(params: {
        stateChanged: StateChangedType;
        deviceMap?: VehicleStateMap;
        disableVehicleStatus?: DisableVehicleStatus;
    }) {
        const { stateChanged, deviceMap, disableVehicleStatus } = params || {};
        const { vIdParents = {} } = this.treeDataInstance.getState() ?? {};
        const { fleetOnlineNumberMap } = this.useStatusCountStore.getState();
        const stateConfig =
            rmPageConfig.data.RTDataInstance.useDataStore.getState()
                .deviceStatus.stateConfig;
        const onLineIndex = stateConfig.findIndex(
            (i) => i.stateId === VEHICLE_STATES.ONLINE_STATE,
        );
        const updateData = countOnlineNumber(
            stateChanged,
            vIdParents,
            onLineIndex,
            deviceMap,
            disableVehicleStatus,
        );
        // 如果没有更新数据，直接返回原状态
        if (!updateData) {
            return fleetOnlineNumberMap;
        }

        const newFleetOnlineNumberMap: Record<string, number> = stateChanged?.allChanged ? {} : {
            ...fleetOnlineNumberMap,
        };
        
        for (const [key, value] of updateData) {
            if (!newFleetOnlineNumberMap.hasOwnProperty(key)) {
                newFleetOnlineNumberMap[key] = 0;
            }
            newFleetOnlineNumberMap[key] += value;
        }
        return newFleetOnlineNumberMap;
    }

    /**
     * 	对车组的子节点按照在线数降序排序
     * 	@param {stateChanged[]} stateChanged 状态变化的车辆
     * 	@param {deviceMap} deviceMap 车辆状态Map
     * 	@returns {Promise<Record<string, FleetOrVehicle>>} 排序后的车组子节点Map
     */
    public async orderFleetChildrenMap(params: {
        stateChanged: StateChangedType;
        deviceMap: VehicleStateMap;
        fleetOnlineNumberMap?: Record<string, number>;
    }) {
        const {
            stateChanged,
            deviceMap,
            fleetOnlineNumberMap: originFleetOnlineNumberMap,
        } = params || {};
        const { allChanged, changedMap } = stateChanged || {};
        const {
            vIdParents = {},
            fleetChildrenMap = {},
            treeNodeKeyMap = {},
            fleetOnlineNumberMap = {},
        } = this?.treeDataInstance?.getState() || {};

        const newFleetOnlineNumberMap =
            originFleetOnlineNumberMap ?? fleetOnlineNumberMap;

        // 如果是增量计算且没有变化，直接返回原数据
        if (!allChanged && Object.keys(changedMap || {}).length === 0) {
            return fleetChildrenMap;
        }

        const needOrderFIds = new Set<string>(TOP_FLEET_PARENT_ID);
        const newFleetChildrenMap = { ...fleetChildrenMap };

        if (allChanged) {
            // 全量模式：处理所有车组
            Object.keys(fleetChildrenMap).forEach((fId) => {
                needOrderFIds.add(fId);
            });
        } else {
            // 增量模式：只处理变化的车辆相关的车组
            Object.keys(changedMap || {}).forEach((vId) => {
                const parentIds = vIdParents[vId];
                if (parentIds?.size > 0) {
                    // 增量变化的车辆，需要对其父节点进行排序
                    parentIds.forEach((pId) => needOrderFIds.add(pId));
                }
            });
        }
        // 统一的车辆排序函数
        const sortVehicles = (vehicles: string[]) => {
            if (!vehicles?.length) return vehicles;
            return inPlaceSort(vehicles.slice(0)).by([
                {
                    desc: (key: string) =>
                        deviceMap[parseTreeKey(key)[2]]?.vOnlineNumber || 0,
                },
                {
                    asc: (key) => treeNodeKeyMap[key].nameIndex,
                },
            ]);
        };

        // 统一的车组排序函数
        const sortFleets = (fleets: string[]) => {
            if (!fleets?.length) return fleets;

            return fleets.slice(0).sort((a, b) => {
                const [, aId] = parseTreeKey(a);
                const [, bId] = parseTreeKey(b);
                const aNumber = newFleetOnlineNumberMap[aId] || 0;
                const bNumber = newFleetOnlineNumberMap[bId] || 0;

                if (aNumber !== bNumber) {
                    return bNumber - aNumber;
                }
                const aNameIndex = treeNodeKeyMap[a].nameIndex || 0;
                const bNameIndex = treeNodeKeyMap[b].nameIndex || 0;
                return aNameIndex - bNameIndex;
            });
        };
        // 统一处理需要排序的车组
        needOrderFIds.forEach((fId) => {
            const fleetChildren = newFleetChildrenMap[fId];
            if (!fleetChildren) return;
                      // 排序车辆
            if (fleetChildren.vehicles?.length) {
                newFleetChildrenMap[fId].vehicles = sortVehicles(
                    fleetChildren.vehicles,
                );
            }

            // 排序车组
            if (fleetChildren.fleets?.length) {
                newFleetChildrenMap[fId].fleets = sortFleets(
                    fleetChildren.fleets,
                );
            }
        });
        return newFleetChildrenMap as Record<
            string,
            {
                fleets: string[];
                vehicles: string[];
            }
        >;
    }

    // 抽象方法计算逻辑
    abstract apply(generator: RTDataGeneratorV1): void;
}
