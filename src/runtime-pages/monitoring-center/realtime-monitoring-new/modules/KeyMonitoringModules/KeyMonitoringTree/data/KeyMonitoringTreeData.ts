import { AsyncStarryTreeData, countFleetVehicle, getFleetParents, getVIdParents } from "../../../AsyncStarryTreeData";
import type { FocusVehicleItem, KeyMonitoringTreeState } from "../interface";
import { inventedFleet, inventedFleetId } from "../constants";
import { omit, orderBy } from "lodash";
import { fetchVehicleFocusList, settingVehicleFocus } from "@/service/vehicle";
import rmPageConfig from "@/runtime-pages/monitoring-center/realtime-monitoring-new/page-config";
import { message } from "@streamax/poppy";
import { i18n, performanceMeasureUtil } from "@base-app/runtime-lib";
import { StateActive } from "../../../DeviceStatusDataGenerator/utils";
import KeyMonitoringTreeStatusCountPlugin from "./plugin/KeyMonitoringTreeStatusCountPlugin";
import { VEHICLE_STATES } from "@/const/vehicle";
import { DeviceStatusStoreType } from "../../../rm-realtime-data/data/types";



const initialState: KeyMonitoringTreeState = {
    focusVehicles: new Set(),
    loaded: false,
    vehicleLoad: false,
    vehicleList: [],
    fleetList: [],
    fleetTotalNumberMap: {},
};

export default class KeyMonitoringTreeData extends AsyncStarryTreeData<KeyMonitoringTreeState> {
    // 状态统计插件注册是否注册
    private pluginRegistered = false;
    // 状态统计插件
    public plugin: KeyMonitoringTreeStatusCountPlugin<KeyMonitoringTreeData>;
    constructor() {
        super(initialState);
        this.plugin = new KeyMonitoringTreeStatusCountPlugin(this);
        this.loadFocusVehicleData();
    }

    /**
     * 	初始化加载流程
     * 	@async
     */
    async load() {
        // 注册插件
        this.registerPlugin();
        const { vehicleLoaded} = rmPageConfig.data.StaticDataInstance.useDataStore.getState() || {};
        // 依赖车辆数据加载
        vehicleLoaded && this.loadFleetVehicleData();
    }

    /**
     * 	卸载流程：
     * 	@async
     */
    unLoad= ()=> {
        this.pluginRegistered = false;
        this.unregisterPlugin();
        this.plugin.reset();
        this.reset();
    }

    private registerPlugin() {
        if (this.pluginRegistered) {
            return;
        }
        rmPageConfig.data.RTDataInstance.registerPlugin(
            'keyTreeStatusCount',
            this.plugin,
        );
        this.pluginRegistered = true;
    }

    private unregisterPlugin() {
        rmPageConfig.data.RTDataInstance.unregisterPlugin('keyTreeStatusCount');
    }

    //  初始化就加载加载车辆用于常规模块使用
    private async loadFocusVehicleData() {
        const focusVehicleIds: string[] = await fetchVehicleFocusList();
        const focusVehicleSetIds = new Set(focusVehicleIds);
        this.updateFocusVehicles(focusVehicleSetIds);
    }

    private filterFocusVehicleData() {
        const focusVehicles = this.getState()?.focusVehicles;
        const focusVehicleIds = Array.from(focusVehicles || []);
        const { vehicleMap } =
            rmPageConfig.data.StaticDataInstance.useDataStore.getState();
        // 停用车辆
        const disableVehicleSet =
            rmPageConfig.data.RTDataInstance.useDataStore.getState()
                .disableVehicle.disableVehicleSet;
        const focusVehicleList = focusVehicleIds
            .filter((vId) => {
                return vehicleMap[vId] && !disableVehicleSet.has(vId);
            })
            .map((vId) => ({
                id: vId,
                parentId: inventedFleetId,
                name: rmPageConfig.data.StaticDataInstance.useDataStore.getState()
                    .vehicleMap[vId].name,
            }));
        // const focusVehicleMap = this.createMap(focusVehicleList);

        return focusVehicleList;
    }

    /**
     * 	加载车组车辆数据流程：
     * 	1.	加载车组
     * 	2.	加载车辆
     * 	3.	更新树节点
     * 	4.	加载车组下车辆
     * 	5.	统计车组下总共有多少辆车
     * 	@async
     * 	@private
     */
    private async loadFleetVehicleData(params?: {
        isFirstLoad: boolean;
        stateChanged?: DeviceStatusStoreType['stateChanged'];
    }) {
        try {
            performanceMeasureUtil.tree.start('keyMonitoringTree.load');
             const { isFirstLoad = true, stateChanged } = params || {};
            const fleetList = [inventedFleet];
            const fIdParents = getFleetParents(fleetList);
            const vehicleList = this.filterFocusVehicleData();
            this.setState({
                fIdParents,
                fleetList,
                vehicleList,
            });
           const orderedVehicleList = orderBy(vehicleList, 'name', 'asc');
           await this.buildFleetChildrenMap(fleetList, orderedVehicleList);
           const vIdParents = {};
           await getVIdParents(orderedVehicleList, fIdParents, vIdParents);
            this.updateVIdParent(vIdParents);
            isFirstLoad && this.updateLoad(true);
           // 设备状态全量已经推送 并且还没有执行第一次计算
           if (this.plugin.isReLoadCalc() || !isFirstLoad) {
               // 执行计算
               await this.reloadCalcStatusAndOrder(stateChanged);
           }
            await this.loadFleets([inventedFleetId], true);
        } catch (error) {
            console.error('Failed to load fleet and vehicle data:', error);
        } finally {
            performanceMeasureUtil.tree.end('keyMonitoringTree.load');
            await this.updateLoad(true);
        }
    }


    /**
     * 	重新计算状态和排序
     * 	@async
     * 	@description
     * 		重新计算状态和排序
     * 		会将stateChanged.allChanged设置为true，表示需要重新计算所有数据
     * 		然后，会根据stateChanged和deviceMap重新计算fleetOnlineNumberMap和fleetChildrenMap
     * 		最后，会将计算结果更新到treeDataInstance和useStatusCountStore
     */
    private async reloadCalcStatusAndOrder(
        stateChanged?: DeviceStatusStoreType['stateChanged'],
    ) {
        const { deviceStatus, disableVehicle: disableVehicleStatus } =
            rmPageConfig.data.RTDataInstance.useDataStore.getState();

        const { fleetChildrenMap, fleetOnlineNumberMap, fleetTotalNumberMap } =
            await this.plugin.calcOrderAndStatusCount({
                deviceStatus: {
                    deviceMap: deviceStatus.deviceMap,
                    stateChanged: stateChanged ?? {
                        allChanged: true,
                        changedMap: deviceStatus.stateChanged.changedMap,
                    },
                },
                disableVehicleStatus,
                isCountFleetTotalNumber: true
            });
        this.plugin.isFirstCalculation = false;
        // @ts-ignore
        this.setState({
            fleetChildrenMap,
            fleetTotalNumberMap,
        });
        this.plugin.useStatusCountStore
            .getState()
            .updateFleetOnlineNumberMap?.(fleetOnlineNumberMap);
    }

    /**
     * 	更新阶段：
     * 停用车辆处理
     * 	@async
     */
    async disableVehicles(vId: string, fIds: Set<string>) {
        return this.deleteVehicle(vId, fIds);
    }

    /**
     * 更新车组树加载状态
     * @async
     * @param {boolean} loaded - 表示是否已加载的布尔值
     */
    async updateLoad(loaded: boolean) {
        this.setState(() => ({ loaded }));
    }

    /**
     * 	更新重点关注车辆
     * 	@async
     * 	@param {string[]} focusVehicles - 重点关注车辆id的数组
     */
    async updateFocusVehicles(focusVehicles: Set<string>) {
        this.setState(() => ({ focusVehicles }));
    }

    /**
     * 	获取车辆状态变化数据
     * 	@private
     * 	@param {string} vehicleId - 车辆id
     * 	@param {boolean} [isRemove=false] - 是否是移除
     * 	@returns {VehicleStateChangedItem} 车辆状态变化item
     */
    private getVehicleStateChanged(vehicleId: string, isRemove = false) {
        // 在线需要计算在线数量更新
        const isOnline =
            rmPageConfig.data.RTDataInstance.useDataStore.getState()
                .deviceStatus.deviceMap[vehicleId].isOnline;
        if (!isOnline)
            return {
                allChanged: false,
                changedMap: {},
            };
        const stateConfig =
            rmPageConfig.data.RTDataInstance.useDataStore.getState()
                .deviceStatus.stateConfig;
        const onLineIndex = stateConfig.findIndex(
            (i) => i.stateId === VEHICLE_STATES.ONLINE_STATE,
        );

        const preState = new Array(onLineIndex + 1).fill(StateActive.INACTIVE);
        const currentState = new Array(onLineIndex + 1).fill(
            StateActive.INACTIVE,
        );

        // 根据是添加还是移除设置在线状态
        if (isRemove) {
            preState[onLineIndex] = StateActive.ACTIVE;
        } else {
            currentState[onLineIndex] = StateActive.ACTIVE;
        }

        return {
            allChanged: false,
            changedMap: {
                [vehicleId]: { preState, currentState },
            },
        };
    }

    /**
     * 	添加关注车辆
     * 	@async
     * 	@param {string} vehicleId - 车辆id
     */
    async addFocusVehicles(vehicleId: string) {
        await settingVehicleFocus({ vehicleId, focusType: 1 });
        const { focusVehicles, loaded } = this.getState() || {};
        const newFocusVehicles = new Set(focusVehicles);
        newFocusVehicles?.add(vehicleId);
        this.updateFocusVehicles(newFocusVehicles);
        if (loaded) {
            const stateChanged = this.getVehicleStateChanged(vehicleId);
            await this.loadFleetVehicleData({
                isFirstLoad: false,
                stateChanged,
            });
        }
        message.success(i18n.t('message', '已添加至【重点监控】列表'));
    }

    /**
     * 	移除关注车辆
     * 	@async
     * 	@param {string} vehicleId - 车辆id
     */
    async removeFocusVehicles(vehicleId: string) {
        await settingVehicleFocus({ vehicleId, focusType: 0 });
        const { focusVehicles, loaded } = this.getState() || {};
        const newFocusVehicles = new Set(focusVehicles);
        newFocusVehicles?.delete(vehicleId);
        this.updateFocusVehicles(newFocusVehicles);
        // 已经加载需要移除和状态统计；没有则不不要删除
        if (loaded) {
            // 移除车辆
            await this.deleteVehicle(
                vehicleId,
                new Set<string>([inventedFleetId]),
            );
            this.refresh();
            // 计算全量
            const fleetTotalNumberMap =
                this.getState()?.fleetTotalNumberMap || {};
            fleetTotalNumberMap[inventedFleetId] =
                fleetTotalNumberMap[inventedFleetId] - 1 || 0;
            this.updateFleetTotalNumberMap(fleetTotalNumberMap);

            const stateChanged = this.getVehicleStateChanged(vehicleId, true);
            const fleetOnlineNumberMap =
                await this.plugin.updateFleetOnlineNumberMap({
                    stateChanged,
                });
            this.plugin.useStatusCountStore
                .getState()
                .updateFleetOnlineNumberMap?.(fleetOnlineNumberMap);
        }
        message.success(i18n.t('message', '已从【重点监控】列表移除'));
    }

    /**
     * 更新车组的车辆总数
     * @param {Record<string, number>} fleetTotalNumberMap 车组id与车辆总数的Map
     * @async
     */
    private async updateFleetTotalNumberMap(
        fleetTotalNumberMap: Record<string, number>,
    ) {
        this.setState(() => ({ fleetTotalNumberMap }));
    }

    reset() {
        this.resetInnerStore();
        // focusVehicles不能重置；初始化加载一次
        this.setState(omit(initialState, 'focusVehicles'));
    }
}