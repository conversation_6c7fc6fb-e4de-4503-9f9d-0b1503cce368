import { useEffect, useRef } from "react";
import { useMount, useUnmount } from "ahooks";
import type { BaseNode, TreeData } from "../../../../AsyncStarryTreeData";
import { parseTreeKey } from "../../../../AsyncStarryTreeData";
import { KeyMonitoringTreeNode } from "../../../KeyMonitoringTreeNode";
import rmPageConfig from "@/runtime-pages/monitoring-center/realtime-monitoring-new/page-config";
import { useSafeStore } from "@/runtime-lib";


/**
 * 将BaseNode转换为TreeData
 * @param item BaseNode
 * @returns 将BaseNode转换为TreeData的Promise
 * @description
 *  1.  BaseNode的key必须存在
 *  2.  title将被赋值为NormalTreeNode或NormalOperationTreeNode的实例
 *  3.  showIcon将被赋值为true
 *  4.  switcherIcon将被赋值为false
 *  5.  isLeaf将被赋值为type === 'vehicle'
 */
const convertTreeData2Node = async (item: BaseNode) => {
    const key = item.key as string;
    const [type] = parseTreeKey(key);
    return Object.assign(
        {
            title: <KeyMonitoringTreeNode treeKey={key} />,
            showIcon: true,
            switcherIcon: false,
            isLeaf: type === 'vehicle',
        },
        item,
    );
};

/**
 * 更新树节点的标题。
 *
 * @param item - 要更新的树节点数据。
 * @param key - 树节点的唯一标识符。
 * @returns 更新后的树节点数据。
 * 
 * @description
 * 根据节点类型更新节点的标题。对于操作类型的节点，标题将设置为
 * NormalOperationTreeNode 组件实例；对于其他类型的节点，标题将设置为
 * NormalTreeNode 组件实例。
 */

const updateTreeDataNodeTitle = (item: TreeData, key: string) => {
    item.title = <KeyMonitoringTreeNode treeKey={key} />;
    return item;
};

export default () => {
    const { StaticDataInstance, KeyMonitoringTreeInstance} = rmPageConfig.data;
    const vehicleLoaded = StaticDataInstance.useDataStore?.(
        (state) => state.vehicleLoaded,
    );

    useSafeStore(KeyMonitoringTreeInstance.useDataStore, KeyMonitoringTreeInstance.unLoad);

    // 初始化treeRef
    const treeRef = useRef(null);
    useMount(() => KeyMonitoringTreeInstance.updateTreeRef(treeRef));

    /**
     * 初始化构建树阶段
     * /1. 初始化基本树转换节点函数
     * 2. 初始化更新树节点标题函数
     * 3. 初始化过滤回调函数
     */
    useMount(() => {
        KeyMonitoringTreeInstance.config({
            convertTreeData2Node,
            updateTreeDataNodeTitle,
            vehicleDisplayConfig: {
                // 重点监控先不开启显示控制
                display: false,
            },
        });
    });

    /**
     * 初始化数据
     */
    useEffect(() => {
        KeyMonitoringTreeInstance.load();
    }, [vehicleLoaded]);

    useUnmount(() => KeyMonitoringTreeInstance.unLoad());

};