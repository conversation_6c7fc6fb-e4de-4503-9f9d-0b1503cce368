/*
 * @LastEditTime: 2024-11-15 18:30:31
 */
import { <PERSON><PERSON>, But<PERSON> } from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';
import { ReloadOutlined, LoadingOutlined } from '@ant-design/icons';
import { useState } from 'react';
import rmPageConfig from '@/runtime-pages/monitoring-center/realtime-monitoring-new/page-config';
import './index.scoped.less';

export const LoadFailErrorTips = () => {
    const { RTDataInstance } = rmPageConfig.data;
    const [loading, setLoading] = useState(false);
    const handleReload = async () => {
        setLoading(true);
        try {
            await RTDataInstance.reLoadDeviceState();
            setLoading(false);
        } catch (error) {
            setLoading(false);
        }
    };
    return (
        <Alert
            message={i18n.t('message', '车辆状态加载失败！')}
            type="error"
            action={
                <Button
                    type="primary"
                    shape="circle"
                    type="text"
                    icon={loading ? <LoadingOutlined /> : <ReloadOutlined />}
                    size="small"
                    onClick={handleReload}
                />
            }
        />
    );
};
