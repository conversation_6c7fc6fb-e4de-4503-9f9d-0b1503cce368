import type { BuildStaticTreeBaseData} from "../../../AsyncStarryTreeData";
import { AsyncStarryTreeData, getFleetParents, getTreeKey, getVIdParents, parseTreeKey, TOP_FLEET_PARENT_ID } from "../../../AsyncStarryTreeData";
import type { GroupMonitoringTreeState, IGroupMonitoringTreeData } from "../interface";
import { fetchMonitorGroup } from "../utils";   
import type { MonitorGroupDeviceParams, MonitorGroupParams} from "@/service/monitor-group";
import { addMonitorGroup, addMonitorGroupDevice, deleteMonitorGroup, deleteMonitorGroupDevice, updateMonitorGroup, type MonitorGroupItem } from "@/service/monitor-group";
import rmPageConfig from "@/runtime-pages/monitoring-center/realtime-monitoring-new/page-config";
import { message } from "@streamax/poppy";
import { g_emmiter, i18n, performanceMeasureUtil } from "@base-app/runtime-lib";
import { omit, orderBy } from "lodash";
import GroupMonitoringTreeStatusCountPlugin from "./plugin/GroupMonitoringTreeStatusCountPlugin";

const initialState: GroupMonitoringTreeState = {
    loaded: false,
    fleetTotalNumberMap: {},
    fleetMap: {},
    fleetList: [],
    vehicleList: [],
    vehicleIds: new Set(),
    vehicleToDeviceIdsMap: {},
};

export default class GroupMonitoringTreeData
    extends AsyncStarryTreeData<GroupMonitoringTreeState>
    implements IGroupMonitoringTreeData
{
    // 状态统计插件注册是否注册
    private pluginRegistered = false;
    // 状态统计插件
    public plugin: GroupMonitoringTreeStatusCountPlugin<GroupMonitoringTreeData>;

    constructor() {
        super(initialState);
        this.plugin = new GroupMonitoringTreeStatusCountPlugin(this);
    }

    /**
     * 	初始化加载流程
     */
    load() {
        // 注册插件
        this.registerPlugin();
        //初始化车组和车辆数据
        const {vehicleLoaded } = rmPageConfig.data.StaticDataInstance.useDataStore.getState() || {};
        vehicleLoaded && this.loadFleetVehicleData();
    }

    /**
     * 	重新加载流程：
     * 	@async
     * 	@description
     * 		重新加载流程
     * 		会卸载treeDataInstance，并重新loadFleetVehicleData
     */
    async reload() {
        await this.loadFleetVehicleData(true);
    }

    /**
     * 	卸载流程：
     * 	@async
     */
    unLoad=()=> {
        this.unregisterPlugin();
        this.plugin.reset();
        this.reset();
        this.pluginRegistered = false;
    }

    private registerPlugin() {
        if (this.pluginRegistered) {
            return;
        }
        rmPageConfig.data.RTDataInstance.registerPlugin(
            'groupMonitoringTreeStatusCount',
            this.plugin,
        );
        this.pluginRegistered = true;
    }

    private unregisterPlugin() {
        rmPageConfig.data.RTDataInstance.unregisterPlugin(
            'groupMonitoringTreeStatusCount',
        );
    }

    /**
     * 	初始化加载流程：
     * 第一次组装车组数据
     * 加载车辆车组车辆流程
     * 	@async
     */
    private async loadFleetVehicleData(isRefresh = false) {
        try {
            performanceMeasureUtil.tree.start('groupMonitoringTree.load');
            isRefresh &&
                this.plugin.useStatusCountStore.getState().resetStore();
            const { fleetList, vehicleList } = await fetchMonitorGroup();
            const { vehicleMap } =
                rmPageConfig.data.StaticDataInstance.useDataStore.getState() ||
                {};
            const fleetMap = fleetList?.reduce<
                Record<string, MonitorGroupItem>
            >((acc, fleet) => {
                acc[fleet.id] = fleet;
                return acc;
            }, {});

            // 停用车辆
            const disableVehicleSet =
                rmPageConfig.data.RTDataInstance.useDataStore.getState()
                    .disableVehicle.disableVehicleSet;

            const filterVehicleList = vehicleList
                .filter(
                    (item) =>
                        vehicleMap[item.vehicleId] &&
                        !disableVehicleSet.has(item.vehicleId),
                )
                .map((item) => ({
                    ...item,
                    id: item.vehicleId,
                    parentId: item.groupId,
                    name: vehicleMap[item.vehicleId]?.name,
                }));

            const vehicleToDeviceIdsMap = filterVehicleList?.reduce<
                Record<string, Set<string>>
            >((acc, { groupId, vehicleId, deviceIdList }) => {
                const key = getTreeKey(groupId, vehicleId);
                acc[key] = new Set(deviceIdList);
                return acc;
            }, {});
            const vehicleIds = new Set(
                filterVehicleList.map((item) => item.vehicleId),
            );
            const newFleetList = fleetList?.map((item) => ({
                ...item,
                parentId: TOP_FLEET_PARENT_ID,
            }));
            this.setState({
                fleetList: newFleetList,
                vehicleList: filterVehicleList,
                fleetMap,
                vehicleIds,
                vehicleToDeviceIdsMap,
            });
            const orderedFleetList = orderBy(
                newFleetList,
                'groupName',
                'asc',
            ) as BuildStaticTreeBaseData[];
            const orderedVehicleList = orderBy(
                filterVehicleList,
                'name',
                'asc',
            ) as BuildStaticTreeBaseData[];

            const fIdParents = getFleetParents(orderedFleetList);
            this.updateFIdParents(fIdParents);
            await this.buildFleetChildrenMap(
                orderedFleetList,
                orderedVehicleList,
            );
            const vIdParents = {};
            await getVIdParents(orderedVehicleList, fIdParents, vIdParents);
            this.updateVIdParent(vIdParents);
            if (this.plugin.isReLoadCalc() || isRefresh) {
                await this.reloadCalcStatusAndOrder();
            }
            const fids = newFleetList?.map((item) => item.id);
            await this.loadFleets(fids, true);
            this.updateLoad(true);
        } catch (error) {
            console.error('Failed to load fleet and vehicle data:', error);
        } finally {
            performanceMeasureUtil.tree.end('groupMonitoringTree.load');
            await this.updateLoad(true);
        }
    }

    /**
     * 	重新计算状态和排序
     * 	@async
     * 	@description
     * 		重新计算状态和排序
     * 		会将stateChanged.allChanged设置为true，表示需要重新计算所有数据
     * 		然后，会根据stateChanged和deviceMap重新计算fleetOnlineNumberMap和fleetChildrenMap
     * 		最后，会将计算结果更新到treeDataInstance和useStatusCountStore
     */
    private async reloadCalcStatusAndOrder() {
        const { deviceStatus, disableVehicle: disableVehicleStatus } =
            rmPageConfig.data.RTDataInstance.useDataStore.getState();

        const { fleetChildrenMap, fleetOnlineNumberMap, fleetTotalNumberMap } =
            await this.plugin.calcOrderAndStatusCount({
                deviceStatus: {
                    deviceMap: deviceStatus.deviceMap,
                    stateChanged: {
                        allChanged: true,
                        changedMap: deviceStatus.stateChanged.changedMap,
                    },
                },
                disableVehicleStatus,
                isCountFleetTotalNumber: true,
            });
        this.plugin.isFirstCalculation = false;
        // @ts-ignore
        this.setState({
            fleetChildrenMap,
            fleetTotalNumberMap,
        });
        this.plugin.useStatusCountStore
            .getState()
            .updateFleetOnlineNumberMap?.(fleetOnlineNumberMap);
    }

    /**
     * 基础监控操作后的处理逻辑
     * @private
     * @async
     * @param {boolean} showMessage - 是否显示成功消息
     * @returns {Promise<void>}
     */
    private async handleAddGroupMonitoringAfterOperation(
        showMessage = true,
    ): Promise<void> {
        const { loaded } = this.getState() ?? {};

        if (loaded) {
            await this.loadFleetVehicleData(true);
            rmPageConfig.data.GroupMonitoringFilterInstance.reload();
        }

        if (showMessage) {
            message.success(i18n.t('message', '操作成功'));
        }
    }

    /**
     * 	添加监控组
     * 	@async
     * 	@description
     * 		添加监控组
     * 		如果已经加载完毕，则重新加载数据和过滤器
     * 	@param {MonitorGroupParams} params 监控组参数
     * 	@returns {Promise<MonitorGroupItem>} 添加结果
     */
    async addGroupMonitoring(params: MonitorGroupParams) {
        const result = await addMonitorGroup(params);
        this.handleAddGroupMonitoringAfterOperation();
        return result;
    }

    /**
     * 	添加监控组车辆
     * 	@async
     * 	@description
     * 		添加监控组车辆
     * 		如果已经加载完毕，则重新加载数据和过滤器
     * 	@param {MonitorGroupDeviceParams} params 监控组车辆参数
     * 	@returns {Promise<true>} 添加结果
     */
    async addGroupMonitoringDevice(params: MonitorGroupDeviceParams) {
        const result = await addMonitorGroupDevice(params);
        this.handleAddGroupMonitoringAfterOperation();
        return result;
    }

    /**
     * 	更新监控组名称
     * 	@param param groupId: 监控组id
     * 	@param param groupName: 监控组名称
     * 	@async
     */
    async updateGroupMonitoringName(params: {
        groupId: string;
        groupName: string;
    }) {
      const result =  await updateMonitorGroup(params);
        this.loadFleetVehicleData(true);
        message.success(i18n.t('message', '操作成功'));
        return result;
    }

    /**
     * 	删除监控组
     * 	@param param groupId: 监控组id
     * 	@async
     */
    async deleteGroupMonitoring(params: { groupId: string }) {
      const result =  await deleteMonitorGroup(params);
        const {
            fleetTotalNumberMap,
            fleetList = [],
            fleetMap,
        } = this.getState() || {};
        // 移除当前车组信息
        this.setState((state) => ({
            fleetList: fleetList.filter((item) => item.id !== params.groupId),
            fleetMap: omit(fleetMap, [params.groupId]),
            fleetTotalNumberMap: omit(fleetTotalNumberMap, [params.groupId]),
        }));
        // 移除在线统计
        const fleetOnlineNumberMap =
            this.plugin.useStatusCountStore.getState()?.fleetOnlineNumberMap;
        this.plugin.useStatusCountStore
            .getState()
            .updateFleetOnlineNumberMap?.(
                omit(fleetOnlineNumberMap, [params.groupId]),
            );
        // 移除内部车组信息
        await this.deleteFleet(params.groupId, new Set([TOP_FLEET_PARENT_ID]));
        this.refresh();
        message.success(i18n.t('message', '操作成功'));
        return result;
    }

    /**
     * 	删除监控组下的某个车的某个设备
     * 	@param param vehicleId: 车id
     * 	@param param groupId: 监控组id
     * 	@param param deviceId: 设备id
     * 	@async
     */
    async deleteGroupMonitoringDevice(params: {
        vehicleId: string;
        groupId: string;
        deviceId: string;
    }) {
        const { vehicleId, groupId, deviceId } = params || {};
        const result =   await deleteMonitorGroupDevice({ groupId, deviceIdList: [deviceId] });

        // 兼容以前的代码
        g_emmiter.emit('remove.monitor.group', params);

        // 移除设备
        const key = getTreeKey(groupId, vehicleId);
        const { vehicleToDeviceIdsMap = {} } = this.getState() || {};
        const vehicleToDeviceIdsSet = new Set(vehicleToDeviceIdsMap?.[key]);
        vehicleToDeviceIdsSet.delete(deviceId);
        // 删除当前设备,单设备需要删除车辆；多设备直接移除设备
        if (vehicleToDeviceIdsSet.size === 0) {
            delete vehicleToDeviceIdsMap[key];
            this.loadFleetVehicleData(true);
        } else {
            const newVehicleToDeviceIdsMap = { ...vehicleToDeviceIdsMap };
            newVehicleToDeviceIdsMap[key] = vehicleToDeviceIdsSet;
            this.setState({
                vehicleToDeviceIdsMap: newVehicleToDeviceIdsMap,
            });
        }
        message.success(i18n.t('message', '操作成功'));
        return result;
    }

    /**
     * 	更新阶段：
     * 停用车辆处理
     * 	@async
     */
    async disableVehicles(vId: string, fIds: Set<string>) {
        return this.deleteVehicle(vId, fIds);
    }

    /**
     * 更新车组树加载状态
     * @async
     * @param {boolean} loaded - 表示是否已加载的布尔值
     */
    private async updateLoad(loaded: boolean) {
        this.setState(() => ({ loaded }));
    }

    reset() {
        this.resetStore();
    }
}
