import { i18n, useSystemComponentStyle } from '@base-app/runtime-lib';
import { Input } from '@streamax/poppy';
import type { InputProps } from '@streamax/poppy/lib/input';
import { forwardRef } from 'react';

const DEFAULT_STYLES: React.CSSProperties = {
    width:"100%"
};

export interface EditInputProps extends Omit<InputProps, 'onChange' | 'value'> {
    value?: string;
    onChange?: (value?: string) => void;
    onFinish?: () => void;
}

export default forwardRef<HTMLInputElement, EditInputProps>((props, ref) => {
    const { value, onChange, onFinish, style, ...restProps } = props || {};
    const { isAbroadStyle } = useSystemComponentStyle();
    return (
        <Input
            {...restProps}
            ref={ref}
            style={{ ...DEFAULT_STYLES, ...(style ?? {}) }}
            maxLength={50}
            onPressEnter={onFinish}
            onBlur={onFinish}
            placeholder={i18n.t('message', '请输入监控组名称')}
            title={i18n.t('message', '请输入监控组名称')}
            value={value}
            size={isAbroadStyle? "small" : "middle"}
            onChange={(e) => {
                e.stopPropagation();
                onChange?.(e.target.value);
            }}
            onClick={(e) => e.stopPropagation()}
            allowClear
        />
    );
});
