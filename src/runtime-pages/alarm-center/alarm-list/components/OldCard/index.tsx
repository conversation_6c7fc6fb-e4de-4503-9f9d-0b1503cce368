import { useState, useRef, useEffect } from 'react';
import {
    i18n,
    utils,
    Auth,
    g_emmiter,
    evidenceUtils, MosaicTypeEnum,
    H5VideoWithStatus,
} from '@base-app/runtime-lib';
import { IconAdress2, IconDetails, IconHandle } from '@streamax/poppy-icons';
//@ts-ignore
import { Action } from '@base-app/runtime-lib';
import { Space, Tooltip, Checkbox, Empty, Badge } from '@streamax/poppy';
import classNames from 'classnames';
import ImageView from '../../../../evidence-list/components/ImageView';
import Icon from '@streamax/poppy-icons/lib/Icon';
import LabelEllipsis from '../LabelEllipsis';
// @ts-ignore
import IconPlayFilled from '@/assets/icons/icon-play.svg';
// @ts-ignore
import IconPauseFilled from '@/assets/icons/icon-suspend.svg';
// @ts-ignore
import { ReactComponent as Downloading } from '@/assets/icons/black_state_downloading.svg';
// @ts-ignore
import { ReactComponent as FileDamage } from '@/assets/icons/black_state_fileDamage.svg';
// @ts-ignore
import { ReactComponent as Nodata } from '@/assets/icons/black_state_nodata.svg';
// @ts-ignore
import { ReactComponent as Waiting } from '@/assets/icons/black_state_waiting.svg';
import { H5Video, PlayState } from '@/components/HistoryPlayer/Video';
import { evidenceFormatData } from '@/utils/evidenceFormatData';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import type { PageCardButtons } from '@/types/pageReuse/pageReuseBase';
import { getCustomJsx } from '@/utils/pageReuse';
import {
    getAllVideos,
    getVideosByFileType,
    calcDuration,
    FileType,
} from '@/utils/commonFun';
import { head, orderBy } from 'lodash';
import AuthDriverShow from '@/components/AuthDriverShow';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import './index.less';
import DisableRightMouseClickImage from '@/components/DisableRightMouseClickImage';
import { ProtocolTypes } from '@/utils/protocol';
import { ProtocolTypesEnum } from '@/types';
import { renderEvidenceCardType } from '@/runtime-lib/modules/Evidence/utils';

const { zeroTimeStampToFormatTime, formatByte } = utils.formator;
/**列表页table定制复用 */
export type CardShareProps = PageCardButtons;
/**end */
interface CardProps {
    evidenceStates: {
        WAITING: number;
        DOING: number;
        DONE: number;
        FAIL: number;
        list: { value: number; label: any; background: any; cardLabel: any }[];
    };
    data: Record<string, any>;
    checked?: boolean;
    onCheck?: (checked: boolean, record: Record<string, any>) => void;
    onAddress?: (record: Record<string, any>) => void;
    onHandle?: (record: Record<string, any>) => void;
    onViewEvidence?: (record: Record<string, any>) => void;
    onClick?: (record: Record<string, any>) => void;
    channelOrder?: string;
}

// 是否抽帧视频；0-不抽帧，1-抽帧，只对视频/264有效
enum PickFrameEnum {
    NoPickFrame,
    PickFrame,
}

enum StreamTypeEnum {
    'MAJOR' = 1, // 主码流 - 高清
    'MINOR', // 子码流 - 标清
    'PICK_FRAME', // 抽帧
}

const streamTypeColor = {
    [StreamTypeEnum.MAJOR]: '#F98742',
    [StreamTypeEnum.MINOR]: '#0D6EDE',
    [StreamTypeEnum.PICK_FRAME]: '#744DE0',
};

const TYPE_IMAGE = 9;
// const TYPE_VIDEO = 1;

const AlarmCard = (props: CardProps & CardShareProps) => {
    /**定制项 */
    const { getCardButtons } = props;
    /**end */

    const {
        data = {},
        onAddress,
        onHandle,
        onCheck,
        checked,
        evidenceStates,
    } = props;
    const {
        evidence = {},
        alarmType,
        alarmTypeName,
        realtimeDriverList = [],
        vehicleInfo,
        startTime,
        address,
        labels = [],
    } = data;
    const { fileList = [], videoChannelList = [] } = evidence || {};
    const { protocolType } = vehicleInfo || {};
    const [isPlaying, setIsPlaying] = useState<boolean>(false);
    const [playState, setPlayState] = useState<PlayState>('readyPlay');
    const [showImagesView, setShowImagesView] = useState<boolean>(false);
    // const { existH264 } = evidenceFormatData(evidence);
    const { videoList, videoPriority } = evidenceUtils.getVideoList({
        fileList: fileList,
        channelPriority: videoChannelList,
        isN9M: protocolType === ProtocolTypesEnum.N9M,
    });
    const imageList = evidenceUtils.getImageList(fileList, videoPriority);

    const firstVideo = head(videoList) as any;
    const existH264 = firstVideo?.fileType === FileType.H264;
    const videoRef = useRef<HTMLMediaElement>();
    const historyPlayerRef: any = useRef();
    const renderEvidenceCardTypeObject = renderEvidenceCardType(firstVideo, imageList, data.fileChannelAuth, data.displayType);

    useEffect(() => {
        g_emmiter.on('alarm.manage.search', () => {
            resetVideo();
        });
        return () => {
            g_emmiter.off('alarm.manage.search');
        };
    }, []);

    const resetVideo = () => {
        setIsPlaying(false);
        if (existH264) {
            historyPlayerRef.current?.stop();
        } else if (videoRef.current) {
            videoRef.current.pause();
            videoRef.current.currentTime = 0;
        }
    };
    const handlePlay = () => {
        setIsPlaying(true);
        if (existH264) {
            historyPlayerRef.current?.play();
        } else {
            videoRef.current?.play();
        }
    };

    const handlePause = () => {
        setIsPlaying(false);
        if (existH264) {
            historyPlayerRef.current?.pause();
        } else {
            videoRef.current?.pause();
        }
    };

    const videoViewCom = (record: any, fileSize: number, videoTime: string) => {
        const streamType =
            record?.pickFrame === PickFrameEnum.PickFrame
                ? StreamTypeEnum.PICK_FRAME
                : record?.streamType;
        const videoSize = formatByte(fileSize);
        return (
            <>
                <H5VideoWithStatus
                    ref={historyPlayerRef}
                    evidenceData={evidence}
                    playChannel={record?.channelNo}
                    mosaicSourcode={MosaicTypeEnum.alarm}
                    fileType={record?.fileType}
                    existH264={existH264}
                    videoInfo={record}
                    hasProgress
                    isPreView
                    onPlayStateChange={(v: PlayState) => setPlayState(v)}
                    hasFileSize
                />
            </>
        );
    };

    const imageViewCom = (records: any[]) => {
        return (
            <DisableRightMouseClickImage>
                <img
                    src={records[0].url}
                    className="image-dom"
                    onClick={() => setShowImagesView(true)}
                />
            </DisableRightMouseClickImage>
        );
    };

    const fileListResult = orderBy(
        (evidence.fileList || []).filter(
            (fileItem: any) => fileItem.fileType === TYPE_IMAGE && fileItem.url,
        ),
        'endTime',
    );
    const duration = calcDuration(evidence, existH264);
    const processed = data.handleStatus !== 0 && data.handleStatus !== 2;
    const videoSize = getAllVideos(evidence.fileList).reduce(
        (total: any, item: any) => total + item.fileSize,
        0,
    );
    const evidenceState = evidenceStates.list.find(
        (i) => i.value === evidence.state,
    );
    const noDataFunction = () => {
        if (!evidence.state) {
            return (
                <Empty
                    description={<span>{i18n.t('message', '暂无数据')}</span>}
                    image={<Nodata />}
                />
            );
        }
        if (evidence.state === evidenceStates.WAITING) {
            return (
                <Empty
                    description={<span>{i18n.t('message', '等待中...')}</span>}
                    image={<Waiting />}
                />
            );
        } else if (evidence.state === evidenceStates.DOING) {
            return (
                <Empty
                    description={<span>{i18n.t('message', '进行中...')}</span>}
                    image={<Downloading />}
                />
            );
        } else if (evidence.state === evidenceStates.FAIL) {
            return (
                <Empty
                    description={<span>{i18n.t('message', '下载失败')}</span>}
                    image={<FileDamage />}
                />
            );
        } else {
            return (
                <Empty
                    description={<span>{i18n.t('message', '暂无数据')}</span>}
                    image={<Nodata />}
                />
            );
        }
    };
    const name = (
        <div className="alarm-name">
            {i18n.t(`@i18n:@alarmType__${alarmType}`, alarmTypeName)}
        </div>
    );
    const lookEvidenceRender = () => {
        return evidence?.state === evidenceStates.DONE ? (
            <Auth
                key="viewEvidence"
                code="@base:@page:alarm.list@action:view.evidence"
            >
                <Action
                    code="@base:@page:alarm.list@action:view.evidence"
                    url="/evidence/detail"
                    target="blank"
                    params={{
                        evidenceId: evidence.evidenceId,
                        alarmId: data.alarmId,
                        fromList: '1',
                        type: 'alarm',
                    }}
                >
                    <Tooltip
                        placement="top"
                        title={i18n.t('action', '查看证据')} // 修改原因是原来的tooltip不生效
                    >
                        <IconDetails />
                    </Tooltip>
                </Action>
            </Auth>
        ) : null;
    };
    const handleAlarmRender = () => {
        return !processed ? (
            <Auth
                key="processAlarm"
                code="@base:@page:alarm.list@action:batch.process"
            >
                <Tooltip placement="top" title={i18n.t('action', '处理报警')}>
                    <a onClick={() => onHandle?.(data)}>
                        <IconHandle />
                    </a>
                </Tooltip>
            </Auth>
        ) : null;
    };
    const cardButtons = [lookEvidenceRender(), handleAlarmRender()];
    return (
        <div className="alarm-list-card">
            <div className="top-wrapper">
                {!processed && (
                    <div className="check-wrap">
                        <Checkbox
                            checked={checked}
                            onChange={(e: any) => {
                                onCheck?.(e.target.checked, data);
                            }}
                        />
                    </div>
                )}
                {/* 证据下载完成才展示，否则展示证据状态 */}
                {evidence.state && evidence.state != evidenceStates.DONE ? (
                    <div className="center-icon">
                        <Icon component={evidenceState?.background} />
                        <div className="center-icon-label">
                            {evidenceState?.cardLabel}
                        </div>
                    </div>
                ) : (
                    <>
                        {renderEvidenceCardTypeObject.video && videoViewCom(firstVideo, videoSize, duration)}
                        {renderEvidenceCardTypeObject.image && imageViewCom(imageList)}
                        {renderEvidenceCardTypeObject.noData && (
                            <div className="center-icon no-data-wrapper">
                                <span className="no-data-icon">
                                    {noDataFunction()}
                                </span>
                            </div>
                        )}
                    </>
                )}
            </div>
            <div className="bottom-wrapper">
                <div className="alarm-name-tag">
                    <div
                        className="name"
                        title={i18n.t(
                            `@i18n:@alarmType__${alarmType}`,
                            alarmTypeName,
                        )}
                        style={{ maxWidth: labels.length ? '48%' : '' }}
                    >
                        <Action
                            code="@base:@page:alarm.list@action:alarm.detail"
                            url="/alarm-detail/:alarmId"
                            target="blank"
                            params={{
                                alarmId: data.alarmId,
                                hasEvidence: data.evidence ? 1 : 0,
                            }}
                            fellback={name}
                        >
                            {name}
                        </Action>
                    </div>
                    <div className="alarm-tag">
                        <LabelEllipsis
                            list={labels}
                            itemPosition="right"
                            defaultContent={null}
                        />
                    </div>
                </div>
                <div className="info-wrap">
                    <Tooltip
                        className="vehicle-number"
                        title={(vehicleInfo || {}).vehicleNumber}
                    >
                        {(vehicleInfo || {}).vehicleNumber}
                    </Tooltip>
                    <span
                        style={{ display: 'inline-block', padding: '0 14px' }}
                    >
                        |
                    </span>
                    <OverflowEllipsisContainer style={{ maxWidth: '44%' }}>
                        <AuthDriverShow
                            driverList={realtimeDriverList}
                            driverId={data?.driverId}
                        />
                    </OverflowEllipsisContainer>
                </div>
                <div className="address-wrap">
                    {address ? (
                        <Tooltip placement="topLeft" title={address}>
                            {address}
                        </Tooltip>
                    ) : (
                        <Tooltip
                            placement="topLeft"
                            title={i18n.t('action', '位置解析')}
                        >
                            <a onClick={() => onAddress?.(data)}>
                                <IconAdress2 />
                            </a>
                        </Tooltip>
                    )}
                </div>
                <div className="bottom-toolbar-wrap">
                    <span className="alarm-time">
                        {zeroTimeStampToFormatTime(startTime)}
                    </span>
                    <div className="operate-btns">
                        <Space size={16}>
                            {/* 证据下载完成才显示 */}
                            {getCustomJsx(getCardButtons, cardButtons, data)}
                        </Space>
                    </div>
                </div>
            </div>
            <ImageView
                vehicleData={data}
                visible={showImagesView}
                list={imageList}
                onClose={() => setShowImagesView(false)}
            />
        </div>
    );
};
export default withSharePropsHOC<CardProps, CardShareProps>(AlarmCard);
