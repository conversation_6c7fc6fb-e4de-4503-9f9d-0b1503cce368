import { useState, useRef, useEffect } from 'react';
import {
    IconMore,
    IconDownloadFill,
    IconPictureFill,
    IconQrCodeFill,
    IconMailboxFill,
} from '@streamax/poppy-icons';
import Icon from '@streamax/poppy-icons/lib/Icon';
import { Dropdown, Menu, Tooltip, Checkbox, message } from '@streamax/poppy';
import {
    g_emmiter,
    i18n,
    utils,
    StarryAbroadIcon,
    Auth,
    evidenceUtils, MosaicTypeEnum, mosaicManager,
    H5VideoWithStatus,
} from '@base-app/runtime-lib';
// @ts-ignore
import { Action, LocationResolution } from '@base-app/runtime-lib';
import classNames from 'classnames';
import ImageView from './ImageView';
import CodeModal from '@/components/EvidenceTools/components/code-modal';
import MailModal from '@/components/EvidenceTools/components/mail-modal';
import { ReactComponent as NoData } from '@/assets/icons/nodata.svg';
import { ReactComponent as <PERSON><PERSON>lean } from '@/assets/icons/icon_cleaned_dark.svg';
import type { CheckboxProps } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { evidenceFormatData } from '@/utils/evidenceFormatData';
import { H5Video } from '@/components/HistoryPlayer/Video';
import { FileChannelAuthEnum, FileType, getVideosByFileType, noChannelAuth } from '@/utils/commonFun';
import { videoSourceFilm } from '../index';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { PageCardButtons } from '@/types/pageReuse/pageReuseBase';
import { getCustomJsx } from '@/utils/pageReuse';
import { head, orderBy } from 'lodash';
import AuthDriverShow from '@/components/AuthDriverShow';
import './Card.less';
import { IndustryOSD } from '@/types/pageReuse/evidenceDetail';
import { VideoCardInfoItem } from '@/types/pageReuse/alarmList';
import DisableRightMouseClickImage from '@/components/DisableRightMouseClickImage';
import SpeedDownloadButton from '@/components/UserSearchVideoDownloadStore/component/SpeedDownloadButton';
import { PlaySpeed } from '@/components/UserSearchVideoDownloadStore/type';
import { ProtocolTypesEnum } from '@/types';
import { adjustEvidenceDetailData } from '@/service/evidence';
import { getEvidenceDownloadTooltip } from '@/runtime-pages/video-library/utils';
import { aspectRatioImage } from '@base-app/runtime-lib';
import {FileCleanStateEnum} from "@/modules/evidence/types";
import { renderEvidenceCardType } from '@/runtime-lib/modules/Evidence/utils';
/**定制类型项 */
export type EvidenceListCardShareProps = PageCardButtons &
    IndustryOSD &
    VideoCardInfoItem;

type CheckBoxType = Omit<CheckboxProps, 'onChange'> & {
    onChange: (checked: boolean) => void;
};

// 是否抽帧视频；0-不抽帧，1-抽帧，只对视频/264有效
enum PickFrameEnum {
    NoPickFrame,
    PickFrame,
}

interface PropsType {
    data: any;
    videoDrawFrameShow: '1' | '0';
    exportData?: (
        id: string,
        type: string,
        playSpeed?: PlaySpeed,
        fileChannelAuth?: FileChannelAuthEnum
    ) => Promise<void>;
    checkbox?: CheckBoxType;
    channelOrder: string;
    playSpeed?: PlaySpeed;
}

enum StreamTypeEnum {
    'MAJOR' = 1, // 主码流 - 高清
    'MINOR', // 子码流 - 标清
    'PICK_FRAME', // 抽帧
}

const streamTypeColor = {
    [StreamTypeEnum.MAJOR]: '#F98742',
    [StreamTypeEnum.MINOR]: '#0D6EDE',
    [StreamTypeEnum.PICK_FRAME]: '#744DE0',
};

const {
    formator: { zeroTimeStampToFormatTime },
} = utils;
const TYPE = 'evidence';

const EvidenceListCard = (props: PropsType & EvidenceListCardShareProps) => {
    /**定制项 */
    const {
        getCardButtons,
        getCardShareMenu,
        osdRenderFn,
        forcePlayH264,
        getVideoCardInfoItem,
    } = props;
    /**end */
    const { AspectRatioImage } = aspectRatioImage.AspectRatioImageV1;
    const { data, checkbox, exportData, playSpeed, videoDrawFrameShow } = props;
    const { onChange } = checkbox || {};
    const [showImages, setShowImages] = useState(false);
    const [showCode, setShowCode] = useState(false);
    const [showMail, setShowMail] = useState(false);
    const [isPlay, setIsPlay] = useState(false);
    const fromList = 1;
    // @ts-ignore
    // const { existH264 } = evidenceFormatData(data, undefined, forcePlayH264);
    const videoRef: any = useRef();
    const historyPlayerRef = useRef<any>(null);
    const { fileList = [], vehicleInfo, videoChannelList, fileChannelAuth } = data;
    const { videoList, videoPriority } = evidenceUtils.getVideoList({
        fileList: fileList,
        channelPriority: videoChannelList,
        isN9M: vehicleInfo?.protocolType === ProtocolTypesEnum.N9M,
    });
    const firstVideo = head(videoList) as any;
    const existH264 = firstVideo?.fileType === FileType.H264;
    let imageList = evidenceUtils.getImageList(fileList, videoPriority);
    imageList = evidenceUtils.filterVideoDrawFrameShowImage(imageList, videoDrawFrameShow);
    const renderEvidenceCardTypeObject = renderEvidenceCardType(firstVideo, imageList, data.fileChannelAuth);
    useEffect(() => {
        g_emmiter.on('evidence.manage.search', () => {
            handleEnd();
        });
        return () => {
            g_emmiter.off('evidence.manage.search');
        };
    }, []);
    const handleEnd = () => {
        setIsPlay(false);
        if (existH264) {
            historyPlayerRef.current?.stop();
        } else if (videoRef.current) {
            videoRef.current.pause();
            videoRef.current.currentTime = 0;
        }
    };

    const getViewImageList = (list: any) => {
        const images: any[] = [...list];
        return evidenceUtils.filterVideoDrawFrameShowImage(images, videoDrawFrameShow);
    };
    const hasMosaic = mosaicManager.useMosaicConfig(MosaicTypeEnum.alarm);
    const mosaicFlag = hasMosaic ? 1 : 0;
    const viewImage = () => {
        setShowImages(true);
    };
    const viewCode = async () => {
        if (noChannelAuth(fileChannelAuth)) {
            return;
        }
        const detailData = await adjustEvidenceDetailData({
            evidenceId: evidenceId,
            fields: 'all',
            accessLog: fromList,
            mosaicFlag,
        });
        const vehicleId = detailData?.vehicleInfo?.vehicleId;
        if (!vehicleId) {
            const info = i18n.t('message', '车辆不存在');
            message.error(info);
            throw info;
        } else setShowCode(true);
    };
    const viewMail = async () => {
        if (noChannelAuth(fileChannelAuth)) {
            return;
        }
        const detailData = await adjustEvidenceDetailData({
            evidenceId: evidenceId,
            fields: 'all',
            accessLog: fromList,
            mosaicFlag,
        });
        const vehicleId = detailData?.vehicleInfo?.vehicleId;
        if (!vehicleId) {
            const info = i18n.t('message', '车辆不存在');
            message.error(info);
            throw info;
        } else setShowMail(true);
    };
    const downLoadButton = () => {
        const speed = playSpeed || PlaySpeed['1X'];
        const tooltipText = getEvidenceDownloadTooltip({ playSpeed: speed, hasMosaic });

        return (
            <SpeedDownloadButton
                key="downLoadButton"
                playSpeed={speed}
                tooltip={tooltipText}
                onClick={() =>
                    exportData &&
                    exportData(data.evidenceId, '1,2,3', playSpeed, data.fileChannelAuth)
                }
            />
        );
    };
    const viewImageButton = () => {
        return (
            <Tooltip
                key="viewImageButton"
                title={i18n.t('message', '查看图片')}
            >
                <a onClick={viewImage}>
                    <IconPictureFill />
                </a>
            </Tooltip>
        );
    };

    const qrCodeShareMenu = () => {
        return <Auth key="qrCodeShareButton" code="@base:@page:evidence.list@action:qrcode">
            <span>
                <Menu.Item key={1}>
                    <a onClick={viewCode}>
                        <StarryAbroadIcon>
                            <IconQrCodeFill />
                        </StarryAbroadIcon>
                        {i18n.t('action', '二维码分享')}
                    </a>
                </Menu.Item>
            </span>
        </Auth>;
    };
    const emailShareMenu = () => {
        return <Auth key="emailShareButton" code="@base:@page:evidence.list@action:view.mail">
             <span>
                <Menu.Item key={2}>
                    <a onClick={viewMail} className="share-btns">
                        <StarryAbroadIcon>
                            <IconMailboxFill />
                        </StarryAbroadIcon>
                        {i18n.t('action', '邮件分享')}
                    </a>
                </Menu.Item>
            </span>
        </Auth>;
    };
    const shareButtons = () => {
        const btns = data.fileCleaned === FileCleanStateEnum.CLEAN ? [] : [qrCodeShareMenu(), emailShareMenu()];
        let menuList: any =
            getCustomJsx(
                getCardShareMenu,
                btns,
                data,
            ) || [];
        // 兼容返回不是数组的情况
        if (!Array.isArray(menuList)) {
            menuList = [menuList];
        }
        if (menuList?.some((item) => item)) {
            return (
                <Dropdown
                    key="shareButtons"
                    placement="bottomRight"
                    overlayClassName="share-dropdown"
                    overlay={
                        <Menu>
                            {getCustomJsx(
                                getCardShareMenu,
                                btns,
                                data,
                            )}
                        </Menu>
                    }
                >
                    <a className="more">
                        <IconMore />
                    </a>
                </Dropdown>
            );
        }
        return null;
    };

    const driverInfoSource = videoSourceFilm?.includes(data?.evidenceType)
        ? 'montageDriverList'
        : 'realtimeDriverList';
    const driverList = data[driverInfoSource];
    const name = i18n.t(
        `@i18n:@alarmType__${data.alarmType}`,
        data.evidenceName,
    );
    const { evidenceId, driverId, alarmInfo, createTime } = data;
    const evidenceName = (
        <div className="evidence-name" key="evidenceName">
            <span className="name" title={name}>
                <Action
                    code="@base:@page:evidence.list@action:evidence.detail"
                    url="/evidence/detail"
                    target="blank"
                    params={{
                        evidenceId: evidenceId,
                        fromList: fromList,
                        type: TYPE,
                    }}
                    fellback={name}
                >
                    {name}
                </Action>
            </span>
        </div>
    );
    const vehicleDriver = (
        <div className="info-item-vehicle-driver" key="vehicleDriver">
            <Tooltip
                className="info-item-vehicle-number"
                title={vehicleInfo ? vehicleInfo.vehicleNumber : ''}
            >
                {vehicleInfo ? vehicleInfo.vehicleNumber : ''}
            </Tooltip>
            <span
                className="info-item-vehicle-driver-split"
                style={{ display: 'inline-block' }}
            >
                |
            </span>
            <OverflowEllipsisContainer style={{ maxWidth: '44%' }}>
                {/* montageDriverList剪辑类型特殊，list未过滤权限，不用去判断外层的driverId是否有权限 */}
                <AuthDriverShow
                    driverList={driverList}
                    driverId={
                        driverInfoSource === 'realtimeDriverList'
                            ? driverId
                            : null
                    }
                />
            </OverflowEllipsisContainer>
        </div>
    );
    const locationPlace = (
        <div className="info-item location" key="locationPlace">
            <LocationResolution autoParse point={alarmInfo} />
        </div>
    );
    const alarmTime = (
        <div className="info-item time" key="alarmTime">
            {/* {i18n.t('name', '报警时间')}： */}
            <OverflowEllipsisContainer>
                <span>{zeroTimeStampToFormatTime(createTime)}</span>
            </OverflowEllipsisContainer>
        </div>
    );
    return (
        <div className="evidence-card">
            {/* 选择组件 */}
            {checkbox && (
                <div className="card-checkbox-bg-container">
                    <Checkbox
                        className="card-checkbox"
                        checked={checkbox.checked}
                        disabled={checkbox.disabled}
                        onChange={(e: CheckboxChangeEvent) =>
                            onChange && onChange(e.target.checked)
                        }
                    />
                </div>
            )}
            <div className={classNames('image-container', { play: isPlay })}>
                {
                    data.fileCleaned === FileCleanStateEnum.CLEAN ? (
                        <div className="center-icon">
                            <Icon component={FileClean} />
                            <div className="center-icon-label">
                                {i18n.t('message', '证据被清理')}
                            </div>
                        </div>
                        ):(
                        <>
                            {renderEvidenceCardTypeObject.video ? (
                                <>
                                    <H5VideoWithStatus
                                        ref={historyPlayerRef}
                                        evidenceData={data}
                                        playChannel={firstVideo?.channelNo}
                                        fileType={firstVideo?.fileType}
                                        videoInfo={firstVideo}
                                        existH264={existH264}
                                        hasProgress
                                        isPreView
                                        hasFileSize
                                        mosaicSourcode={MosaicTypeEnum.alarm}
                                        osdRenderFn={osdRenderFn}
                                        forcePlayH264={forcePlayH264}
                                    />
                                </>
                            ) : null}
                            {renderEvidenceCardTypeObject.image ? (
                                <DisableRightMouseClickImage>
                                    <AspectRatioImage
                                        src={imageList[0].url}
                                        preview={false}
                                        onClick={viewImage}
                                        style={{ cursor: 'pointer' }}
                                    />
                                </DisableRightMouseClickImage>
                            ) : null}
                            {renderEvidenceCardTypeObject.noData ? (
                                <div className="center-icon">
                                    <Icon component={NoData} />
                                    <div className="center-icon-label">
                                        {i18n.t('message', '暂无数据')}
                                    </div>
                                </div>
                            ) : null}
                        </>
                    )
                }
            </div>
            <div className="evidence-info">
                {getCustomJsx(
                    getVideoCardInfoItem,
                    [evidenceName, vehicleDriver, locationPlace, alarmTime],
                    {
                        evidenceId,
                        vehicleInfo,
                        alarmInfo,
                        createTime,
                        driverId,
                        driverInfoSource,
                        driverList,
                    },
                )}
            </div>
            <div className="btns">
                {getCustomJsx(
                    getCardButtons,
                    [data.fileCleaned !== FileCleanStateEnum.CLEAN && downLoadButton(),
                        viewImageButton(),
                        shareButtons()
                    ].filter(Boolean),
                    data,
                )}
            </div>
            <ImageView
                vehicleData={data}
                visible={showImages}
                list={getViewImageList(imageList)}
                onClose={() => setShowImages(false)}
            />
            <CodeModal
                visible={showCode}
                close={() => setShowCode(false)}
                evidenceId={data.evidenceId}
                data={data}
            />
            <MailModal
                visible={showMail}
                close={() => setShowMail(false)}
                evidenceId={data.evidenceId}
                type={TYPE}
            />
        </div>
    );
};
export default withSharePropsHOC<PropsType, EvidenceListCardShareProps>(
    EvidenceListCard,
);
