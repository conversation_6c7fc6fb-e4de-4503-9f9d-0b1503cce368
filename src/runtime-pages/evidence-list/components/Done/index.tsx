// @ts-ignore
import {
    Action,
    Auth,
    evidenceUtils,
    g_emmiter,
    i18n,
    mosaicManager,
    MosaicTypeEnum,
    SimplePagination,
    StarryAbroadOverflowEllipsisContainer,
    StarryStorage,
    useUrlSearchStore,
    utils,
} from '@base-app/runtime-lib';
import React, { useEffect, useState } from 'react';
import { fetchBaseData } from '../../../../utils/filterFetch';
import IconList from '@streamax/poppy-icons/lib/icons/IconList';
import IconApplication from '@streamax/poppy-icons/lib/icons/IconApplication';
import {
    Button,
    Checkbox,
    Container,
    Dropdown,
    Empty,
    Form,
    Menu,
    message,
    ProForm,
    ProTable,
    Space,
    Spin,
    Tooltip,
} from '@streamax/poppy';
import {
    IconDeleteFill,
    IconDownloadFill,
    IconMailboxFill,
    IconMoreDotH,
    IconPictureFill,
    IconQrCodeFill,
} from '@streamax/poppy-icons';
import EvidenceRequest from '../../../../service/evidence';
import Card from '../../../evidence-list/components/Card';
import CodeModal from '@/components/EvidenceTools/components/code-modal';
import MailModal from '@/components/EvidenceTools/components/mail-modal';
import ImageView from '../../../evidence-list/components/ImageView';
import cn from 'classnames';
import useFormUrlSearchStore from '@/hooks/useFormUrlSearchStore';
import moment from 'moment';
import calcHeight from '@/utils/calcStickyHeight';
import { TabProps, videoSourceFilm } from '../../index';
import { calcDuration, checkAuth, FileChannelAuthEnum, getAllVideos, getInitTimeRange, getVideosByFileType, noChannelAuth } from '@/utils/commonFun';
import { RspCardLayout } from '@streamax/responsive-layout';
import { evidenceFormatData } from '@/utils/evidenceFormatData';
import { fetchParameterDetail } from '@/service/parameter';
import { useDebounceFn, useLockFn, useUpdateEffect } from '@streamax/hooks';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { Instances, ListPageQueryForm, ListPageTableBase, ListPlaySpeed } from '@/types/pageReuse/pageReuseBase';
import { getCustomItems, getCustomJsx, getTableIconBtns, runCustomFun } from '@/utils/pageReuse';
import AuthDriverShow from '@/components/AuthDriverShow';
import AuthFleetShow from '@/components/AuthFleetShow';
import { isNil, orderBy } from 'lodash';
import './index.less';
import getWaterMask from '@/utils/getWaterMask';
import useHideTableStickyScroll from '@/hooks/useHideTableStickyScroll';
import UserSearchVideoDownloadStore from '@/components/UserSearchVideoDownloadStore';
import { PlaySpeed, PlaySpeedMap, StorePageCode } from '@/components/UserSearchVideoDownloadStore/type';
import { ProtocolTypesEnum } from '@/types/index';
import { getEvidenceDownloadTooltip } from '@/runtime-pages/video-library/utils';
import {FileCleanStateEnum} from "@/modules/evidence/types";

const PAGINATION_HEIGHT = 64;

/**列表页table定制复用 */
export type EvidenceListDoneShareProps = ListPageTableBase &
    ListPageQueryForm &
    Instances &
    ListPlaySpeed;

export type SearchPage = {
    page: number;
};
export type SearchReset = {
    reset: boolean;
};
const { QueryForm } = ProForm;

interface EvidenceQueryParams {
    fileTypes: string;
    evidenceId: string;
    watermarkFlag?: number;
    watermarkValue?: string[];
    playSpeed?: PlaySpeed;
    mosaicVideo?: boolean;
}

// @ts-ignore
const {
    timestampToZeroTimeStamp,
    zeroTimeStampToZoneTime,
    formatByte,
    getLocalMomentByZeroTimeStamp,
} = utils.formator;
const EvidenceListDone = (
    props: TabProps & EvidenceListDoneShareProps,
    ref: any,
) => {
    /**定制项 */
    const {
        getColumns,
        onSelectRows,
        getTableLeftRender,
        injectSearchList,
        getIconBtns,
        getQueryForm,
        getInstances,
        getColumnSetting,
        getDownloadSetRender,
        getInitQueryData,
        tabKey,
    } = props;
    const queryItems = getCustomItems(getQueryForm, props.queryItems);
    /**end */
    const searchStore = useUrlSearchStore();
    const { params: formQuery, setSearchStore } = useFormUrlSearchStore(
        'page',
        'pageSize',
        ['alarmTypes', 'startTime', 'endTime', 'includeSubFleet'],
    );
    const [layoutType, setLayoutType] = useState<'list' | 'card'>(
        formQuery.layoutType || 'card',
    );
    const [list, setList] = useState<any[]>([]);
    const [videoDrawFrameShow, setVideoDrawFrameShow] = useState<string>('');
    const [page, setPage] = useState<number>(1);
    const [pageSize] = useState<number>(20);
    const [queryParams, setQueryParams] = useState<any>({});
    const [showImage, setShowImage] = useState(false);
    const [evidenceCheck, setEvidenceCheck] = useState(false);
    const [imageList, setImageList] = useState<any[]>([]);
    const [currentVehicle, setCurrentVehicle] = useState(null);
    const [showCode, setShowCode] = useState(false);
    const [showMail, setShowMail] = useState(false);
    const [evidenceId, setEvidenceId] = useState<string>('');
    const [sortName, setSortName] = useState<any>('create_time');
    const [sortType, setSortType] = useState<any>('descend');
    const [loading, setLoading] = useState(false);
    const [selectedRows, setSelectedRows] = useState<React.Key[]>([]);
    const [form] = Form.useForm();
    const initTimeRange = getInitTimeRange(7); // 默认7天内数据\
    const [hasNextPage, setHasNextPage] = useState<boolean>();
    const [playSpeed, setPlaySpeed] = useState<PlaySpeed>(PlaySpeed['1X']);

    const storage = StarryStorage();
    const hasMosaic = mosaicManager.useMosaicConfig(MosaicTypeEnum.alarm);

    const { showSticky } = useHideTableStickyScroll({
        paginAtionHight: PAGINATION_HEIGHT,
        dataSource: list,
    });

    useEffect(() => {
        setSearchStore({
            ...searchStore.get(),
            layoutType,
        });
    }, [layoutType]);

    const getSettingValue = () => {
        fetchParameterDetail(
            {
                parameterKey: 'VIDEO.DRAW.FRAME.SHOW',
            },
            false,
        ).then((data: any) => {
            setVideoDrawFrameShow(data.parameterValue || '0');
        });
    };
    const getValid = (valid: unknown) => {
        return valid ? Number(valid) : undefined;
    };

    const viewCode = (evidenceId: string, fileChannelAuth: FileChannelAuthEnum) => {
        if (noChannelAuth(fileChannelAuth)) {
            return;
        }
        setShowCode(true);
        setEvidenceId(evidenceId);
    };
    const viewMail = (evidenceId: string, fileChannelAuth: FileChannelAuthEnum) => {
        if (noChannelAuth(fileChannelAuth)) {
            return;
        }
        setShowMail(true);
        setEvidenceId(evidenceId);
    };
    const viewImage = (data: any) => {
        const { fileList = [], vehicleInfo, videoChannelList } = data;
        const { videoList, videoPriority } = evidenceUtils.getVideoList({
            fileList: fileList,
            channelPriority: videoChannelList,
            isN9M: vehicleInfo?.protocolType === ProtocolTypesEnum.N9M,
            isForceH264: false,
        });
        const imageList = evidenceUtils.getImageList(fileList, videoPriority);
        let images: any[] = [...imageList];
        if (videoDrawFrameShow != '1') {
            images = images.filter(
                (item: any) => item.subType != '3' && item.subType != '6',
            );
        }
        setShowImage(true);
        setCurrentVehicle(data);
        setImageList(images);
    };
    const deleteRecords = async (
        records: Record<string, any>,
        batchDelete = false,
    ) => {
        await props.deleteRecords(records, batchDelete);
        setSelectedRows([]);
        query();
    };
    const exportData = async (id: any, type: string, playSpeed?: PlaySpeed, fileChannelAuth?: FileChannelAuthEnum) => {
        if(noChannelAuth(fileChannelAuth)) {
            return;
        }
        const hasMosaic = mosaicManager.checkMosaicEnable(MosaicTypeEnum.alarm);
        const { waterFlag, customerIp, account, entryName, formatTime } =
            await getWaterMask();
        let evidenceQueryParams: EvidenceQueryParams = {
            fileTypes: type,
            evidenceId: id,
            playSpeed,
            mosaicVideo: hasMosaic,
        };
        // 若水印开启，则传入水印相关参数
        if (waterFlag) {
            evidenceQueryParams = {
                ...evidenceQueryParams,
                watermarkFlag: waterFlag,
                watermarkValue: [
                    entryName,
                    `${customerIp} ${formatTime}`,
                    account,
                ],
            };
        }
        await EvidenceRequest.exportFileAsync({
            serviceCode: 'c182d4926be44c0683fbd8f0928a4751',
            fileName: playSpeed
                ? `evidence_${moment().unix()}_${PlaySpeedMap[playSpeed]}`
                : `evidence_${moment().unix()}`,
            isAsync: true,
            evidenceQueryParams,
        });
        message.success(
            i18n.t('message', '导出成功，请到个人中心中查看导出详情'),
        );
    };
    const sourceList: Record<number, string> = {
        1: i18n.t('name', '自动上传'),
        2: i18n.t('name', '手动上传'),
    };
    const columns = [
        {
            title: i18n.t('name', '报警类型'),
            dataIndex: 'evidenceName',
            key: 'evidenceName',
            ellipsis: { showTitle: false },
            fixed: 'left',
            width: 200,
            render: (text: any, record: any) => {
                const evidenceName = i18n.t(
                    `@i18n:@alarmType__${record.alarmType}`,
                    text,
                );
                return (
                    <Action
                        code="@base:@page:evidence.list@action:evidence.detail"
                        url="/evidence/detail"
                        target="blank"
                        fellback={evidenceName}
                        params={{
                            evidenceId: record.evidenceId,
                            fromList: 1,
                            type: 'evidence',
                        }}
                    >
                        {evidenceName}
                    </Action>
                );
            },
        },
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            key: 'vehicleNumber',
            ellipsis: { showTitle: true },
            width: 250,
        },
        {
            title: i18n.t('name', '司机姓名'),
            dataIndex: 'driverName',
            key: 'driverName',
            width: 200,
            ellipsis: { showTitle: true },
            render: (_: unknown, row: any) => {
                const driverInfo = videoSourceFilm?.includes(row?.evidenceType)
                    ? 'montageDriverList'
                    : 'realtimeDriverList';
                const driverList = row[driverInfo];
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthDriverShow
                            driverList={driverList}
                            driverId={
                                driverInfo === 'realtimeDriverList'
                                    ? row.driverId
                                    : null
                            }
                        />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        // {
        //     title: i18n.t('name', '归属车组'),
        //     // dataIndex: 'vehicleInfo.vehicleNumber',
        //     // key: 'vehicleInfo.vehicleNumber',
        //     dataIndex: 'fleetName',
        //     key: 'fleetName',
        // },
        {
            title: i18n.t('name', '报警设备'),
            dataIndex: 'deviceNo',
            key: 'deviceNo',
            width: 180,
            ellipsis: { showTitle: true },
            render: (_value: unknown, row: any) => {
                const { deviceNo, deviceAlias } = row.vehicleInfo || {};
                const text = deviceAlias || deviceNo || '-';
                const title = deviceAlias
                    ? `${deviceAlias}(${deviceNo})`
                    : deviceNo;
                return <Tooltip title={title}>{text}</Tooltip>;
            },
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetName',
            key: 'fleetName',
            calcWidth: () => 200,
            width: 280,
            ellipsis: { showTitle: true },
            render: (text: number, row: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow
                            fleetList={row?.vehicleInfo?.fleetList}
                        />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '视频大小'),
            dataIndex: 'totalSize',
            key: 'totalSize',
            width: 180,
            ellipsis: { showTitle: true },
            render: (text: number, record: any) => {
                // @ts-ignore
                const { existH264 } = evidenceFormatData(record);
                const videos = getVideosByFileType(record.fileList, existH264);
                const videoSize = getAllVideos(record.fileList).reduce(
                    (total: any, item: any) => total + item.fileSize,
                    0,
                );
                // 视频证据才显示大小；反之显示0kb
                const size = videos.length > 0 ? formatByte(videoSize) : '0KB';
                return size;
            },
        },
        {
            title: i18n.t('name', '视频时长'),
            key: 'duration',
            width: 180,
            ellipsis: { showTitle: true },
            render: (_: unknown, record: any) => {
                // 和场频确认后展示所有通道的视频时长
                const duration = calcDuration(record, true);
                return duration;
            },
        },
        {
            title: i18n.t('name', '证据来源'),
            dataIndex: 'sourceType',
            key: 'sourceType',
            width: 180,
            ellipsis: { showTitle: true },
            render: (_value: unknown, row: any) => {
                const text = sourceList[row.sourceType] || '-';
                return text;
            },
        },
        {
            title: i18n.t('name', '证据时间'),
            dataIndex: 'createTime',
            key: 'createTime',
            sorter: true,
            sortDirections: ['ascend', 'descend', null],
            ellipsis: { showTitle: true },
            calcWidth: () => 165,
            width: 180,
            render: (text: number) => {
                const time = utils.formator.zeroTimeStampToFormatTime(text);
                return time;
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            key: 'operate',
            ellipsis: true,
            width: 180,
            fixed: 'right',
            calcWidth: () => utils.general.calcTableOperateWidth(4),
            render: (_text: unknown, row: any) => {

                /** @description 当没有下拉操作权限时，不显示更多按钮 */
                const hasDropdown =
                    checkAuth('@base:@page:evidence.list@action:qrcode') ||
                    checkAuth('@base:@page:evidence.list@action:view.mail');
                const fileClean =  row.fileCleaned ===FileCleanStateEnum.CLEAN;
                const tooltipText = getEvidenceDownloadTooltip({ playSpeed, hasMosaic });

                return (
                    <Space size={20} key="operateSpace">

                            {
                                fileClean ?
                                    null :
                                    <Auth
                                        key="downloadEvidenceButton"
                                        code="@base:@page:evidence.list@action:download"
                                    >
                                        <Tooltip
                                            title={tooltipText}
                                        >
                                            <a
                                                onClick={() =>
                                                    exportData(
                                                        row.evidenceId,
                                                        '1,2,3',
                                                        playSpeed,
                                                        row.fileChannelAuth
                                                    )
                                                }
                                            >
                                                <IconDownloadFill />
                                            </a>
                                        </Tooltip>
                                    </Auth>
                        }
                        <Auth
                            key="lookImgButton"
                            code="@base:@page:evidence.list@action:view.picture"
                        >
                            <Tooltip title={i18n.t('action', '查看图片')}>
                                <a onClick={() => viewImage(row)}>
                                    <IconPictureFill />
                                </a>
                            </Tooltip>
                        </Auth>
                        <Auth
                            key="deleteButton"
                            code="@base:@page:evidence.list@action:batch.delete.task"
                        >
                            <Tooltip title={i18n.t('action', '删除')}>
                                <a onClick={() => deleteRecords([row], false)}>
                                    <IconDeleteFill />
                                </a>
                            </Tooltip>
                        </Auth>
                        {hasDropdown && !fileClean && (
                            <Dropdown
                                key="moreButton"
                                placement="bottomRight"
                                // trigger="click"
                                overlayClassName="share-dropdown"
                                overlay={
                                    <Menu key="moreMenu">
                                        <span key="qrCodeShareButton">
                                            <Menu.Item key={1}>
                                                <Auth code="@base:@page:evidence.list@action:qrcode">
                                                    <a
                                                        onClick={() => {
                                                            viewCode(
                                                                row.evidenceId,
                                                                row.fileChannelAuth
                                                            );
                                                        }}
                                                    >
                                                        <IconQrCodeFill className="evidence-list-dropdown-icon" />
                                                        {i18n.t(
                                                            'action',
                                                            '二维码分享',
                                                        )}
                                                    </a>
                                                </Auth>
                                            </Menu.Item>
                                        </span>
                                        <span key="emailShareButton">
                                            <Menu.Item key={2}>
                                                <Auth code="@base:@page:evidence.list@action:view.mail">
                                                    <a
                                                        onClick={() => {
                                                            viewMail(
                                                                row.evidenceId,
                                                                row.fileChannelAuth
                                                            );
                                                        }}
                                                    >
                                                        <IconMailboxFill className="evidence-list-dropdown-icon" />
                                                        {i18n.t(
                                                            'action',
                                                            '邮件分享',
                                                        )}
                                                    </a>
                                                </Auth>
                                            </Menu.Item>
                                        </span>
                                    </Menu>
                                }
                            >
                                <a className="more">
                                    <IconMoreDotH />
                                </a>
                            </Dropdown>
                        )}
                    </Space>
                );
            },
        },
    ];

    const batchDeleteButton = () => {
        return (
            <Auth
                key="batchDeleteButton"
                code="@base:@page:evidence.list@action:batch.delete.task"
            >
                <Button
                    icon={<IconDeleteFill />}
                    disabled={selectedRows.length === 0}
                    onClick={() => deleteRecords(selectedRows, true)}
                >
                    {i18n.t('action', '批量删除')}
                </Button>
            </Auth>
        );
    };
    const handleCardCheckAll = (e: any) => {
        const { checked } = e.target;
        checked ? setSelectedRows(list) : setSelectedRows([]);
    };
    useEffect(() => {
        list.length > 0 && selectedRows.length === list.length
            ? setEvidenceCheck(true)
            : setEvidenceCheck(false);
    }, [selectedRows, list]);

    const listBtn = () => {
        return (
            <a
                key="showTable"
                title={i18n.t('name', '列表')}
                onClick={() => {
                    setLayoutType('list');
                }}
                className={cn('layout-type-button', {
                    selected: layoutType === 'list',
                })}
            >
                <IconList />
            </a>
        );
    };

    const cardBtn = () => {
        return (
            <a
                key="showCard"
                title={i18n.t('name', '卡片')}
                onClick={() => {
                    setLayoutType('card');
                }}
                className={cn('layout-type-button', {
                    selected: layoutType === 'card',
                })}
            >
                <IconApplication />
            </a>
        );
    };
    const cardCheckAll = () => {
        return (
            <Checkbox
                key="cardCheckAll"
                checked={evidenceCheck}
                onChange={handleCardCheckAll}
                disabled={!list.length}
            >
                {i18n.t('name', '全选')}
            </Checkbox>
        );
    };

    const storeOnSave = (data: Record<any, any>) => {
        if (data?.playSpeed) {
            setPlaySpeed(data.playSpeed);
        }
    };
    const storeOnInit = (data: Record<any, any>) => {
        if (data?.playSpeed) {
            setPlaySpeed(data.playSpeed);
        }
    };
    const downloadButton = () => {
        return (
            <UserSearchVideoDownloadStore
                page={StorePageCode.EvidenceList}
                onSave={storeOnSave}
                onInit={storeOnInit}
            />
        );
    };
    const downloadSet = () => {
        return (
            <>
                {getCustomJsx(getDownloadSetRender, [downloadButton()], {
                    playSpeed,
                    page: StorePageCode.EvidenceList,
                })}
            </>
        );
    };

    const toolBar = (
        <div className="switch-layout-container">
            {layoutType === 'card' && (
                <Space>
                    {getCustomJsx(
                        getTableLeftRender,
                        [batchDeleteButton()],
                        list,
                    )}
                </Space>
            )}
            <Space size="middle">
                {getTableIconBtns(
                    getIconBtns,
                    [cardCheckAll(), downloadSet(), listBtn(), cardBtn()],
                    list,
                )}
            </Space>
        </div>
    );
    const query = useLockFn(async () => {
        try {
            setLoading(true);
            const values = form.getFieldsValue();
            const {
                vehicle,
                company,
                valid,
                sourceTypes,
                alarmTypes,
                time,
                ...rest
            } = values;
            setSearchStore({
                vehicleSearchType: vehicle?.type,
                vehicleSearchValue: vehicle?.value,
                fleetIds: company?.fleetIds,
                includeSubFleet: company?.includeSubFleet ?? 1,
                sourceTypes,
                alarmTypes,
                startTime: queryParams.startTime,
                endTime: queryParams.endTime,
                valid,
                complexSort:
                    sortType && `${sortName} ${sortType.replace(/end/, '')}`,
                page: queryParams.page || 1,
            });
            const params = {
                ...queryParams,
                page: queryParams.page || 1,
                pageSize,
                // 默认不查询关联，多个用逗号分割，all-全部，file-文件列表，file_url-文件下载链接，vehicle-车辆快照，driver-司机快照
                fields: 'noTotal,driver,file,alarm,vehicle,file_url,montage_driver,videoChannel',
                evidenceTypes: '1, 9',
                states: 3,
                mosaicFlag: mosaicManager.checkMosaicEnable(MosaicTypeEnum.alarm) ? 1 : 0,
                ...rest,
            };
            if (injectSearchList) {
                const { list, hasNextPage } =
                (await injectSearchList(values)) || {};
                setHasNextPage(hasNextPage);
                setList(list);
                return;
            }
            let ids;
            if (vehicle && vehicle.value && vehicle.value.replace(/\s/g, '')) {
                ids = await fetchBaseData(vehicle);
                if (ids) {
                    params[`${vehicle.type}Ids`] = ids;
                } else {
                    setList([]);
                    setHasNextPage(false);
                    return;
                }
            }
            if (sortType) {
                params.complexSort = `${sortName} ${sortType.replace(
                    /end/,
                    '',
                )}`;
            }

            const { list: resList, hasNextPage } =
                await EvidenceRequest.getPageList(params);
            //排序先时间，再通道。todo后续是否可以让后端或者s17排序，前端似乎很多地方都需要这个排序
            resList?.forEach((item: any) => {
                item.fileList = orderBy(
                    orderBy(item?.fileList || [], 'startTime', 'asc'),
                    'channelNo',
                    'asc',
                );
            });
            setList(resList);
            setSelectedRows([]);
            setHasNextPage(hasNextPage);
        } finally {
            setLoading(false);
            setTimeout(() => {
                // 创建一个新的事件
                const event = new Event('resize');
                // 触发resize事件，触发Affix重新计算，【100744】当筛选数据较少时，分页的固定位置需要重新计算
                window.dispatchEvent(event);
            }, 100);
        }
    });

    const handleSearchInner = async (values: any) => {
        const {
            vehicle,
            company,
            time,
            valid,
            sourceTypes,
            alarmTypes,
            page = 1,
        } = values;
        let ids;
        const initialTimeValue = queryItems.find((p) => p.name == 'time')
            ?.itemProps?.initialValue;
        let startTime, endTime;
        if (time) {
            startTime = timestampToZeroTimeStamp(time[0]);
            endTime = timestampToZeroTimeStamp(time[1]);
        } else {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                time: initialTimeValue,
            });
            startTime = timestampToZeroTimeStamp(initialTimeValue[0]);
            endTime = timestampToZeroTimeStamp(initialTimeValue[1]);
        }

        const params = {
            ...(company || {}),
            page,
            alarmTypes,
            sourceTypes,
            startTime,
            endTime,
        };
        g_emmiter.emit('evidence.manage.search', params);
        if (vehicle && vehicle.value) {
            ids = await fetchBaseData(vehicle);
            if (ids) {
                params[`${vehicle.type}Ids`] = ids;
            } else {
                setSearchStore({
                    vehicleSearchType: vehicle?.type,
                    vehicleSearchValue: vehicle?.value,
                    fleetIds: company?.fleetIds,
                    includeSubFleet: company?.includeSubFleet ?? 1,
                    sourceTypes,
                    alarmTypes,
                    startTime: queryParams.startTime,
                    endTime: queryParams.endTime,
                    valid,
                    complexSort:
                        sortType &&
                        `${sortName} ${sortType.replace(/end/, '')}`,
                    page,
                });
                setList([]);
                setHasNextPage(false);
                return;
            }
        }
        setPage(page);
        setQueryParams(params);
        // 重置页码
        // query();
    };

    const { run: handleSearch } = useDebounceFn(handleSearchInner, {
        wait: 500,
    });

    const onPageChange = (page: number) => {
        setPage(page);
        setQueryParams({
            ...queryParams,
            page,
        });
    };

    const handleTableChange = (
        _pagination: unknown,
        _filters: unknown,
        sorter: any,
    ) => {
        const { field, order } = sorter;
        if (field) {
            const name = field.replace(
                /[A-Z]/g,
                (r: string) => `_${r.toLowerCase()}`,
            );
            setSortName(name);
            setSortType(order);
        }
    };

    useEffect(() => {
        if (!Object.keys(queryParams).length) return;
        if (queryParams.startTime == undefined) {
            handleSearch({
                time: queryItems.find((p) => p.name == 'time')?.itemProps
                    ?.initialValue,
            });
        } else {
            query();
        }
    }, [pageSize, queryParams, sortName, sortType]);

    useEffect(() => {
        const {
            vehicleSearchType,
            vehicleSearchValue,
            startTime,
            endTime,
            alarmTypes,
            fleetIds,
            includeSubFleet,
            sourceTypes,
            valid,
            page = 1,
            ...value
        } = formQuery;
        const searchList = {
            ...value,
            vehicle: {
                type: vehicleSearchType,
                value: vehicleSearchValue,
            },
            alarmTypes: alarmTypes,
            time:
                startTime && endTime
                    ? [
                        getLocalMomentByZeroTimeStamp(startTime) || null,
                        getLocalMomentByZeroTimeStamp(endTime) || null,
                    ]
                    : initTimeRange,
            sourceTypes: sourceTypes,
            valid: valid == 'handleStatus_0' ? valid : getValid(valid),
            company: { fleetIds, includeSubFleet: includeSubFleet ?? 1 },
            page,
        };
        /***fix 130238, 支持定制回显时的数据**/
        const searchData = getCustomItems(getInitQueryData, searchList);
        form.setFieldsValue(searchData);
        // 首次请求，无需经过防抖，避免等待500ms
        handleSearchInner(searchData);
        getSettingValue();
        setPage(page);
    }, []);

    /***定制查询**/
    const resetData = () => {
        setQueryParams(() => {
            return {
                ...queryParams,
                page: 1,
            };
        });
        setPage(1);
    };
    const reloadData = (resetParams: SearchReset = { reset: false }) => {
        const { reset } = resetParams;
        if (reset) {
            resetData();
            return;
        }
        query();
    };
    const loadDataSource = (newParams: SearchPage = {} as SearchPage) => {
        const page = newParams?.page || queryParams.page;
        setQueryParams(() => {
            return {
                ...queryParams,
                ...newParams,
                page,
            };
        });
        setPage(page);
    };

    useUpdateEffect(() => {
        runCustomFun(onSelectRows, selectedRows);
    }, [selectedRows]);
    runCustomFun(getInstances, {
        form,
        table: {
            loadDataSource,
            reload: reloadData,
            reset: resetData,
        },
    });
    /***定制查询end**/

    const showPageNext = isNil(hasNextPage)
        ? list.length == pageSize
        : hasNextPage;

    const evidence = list.find((p) => p.evidenceId == evidenceId) || {};
    const cardList = layoutType === 'card' && (
        <div className="card-container">
            {!list.length && <Empty style={{ margin: '30px 0' }} imageStyle={{ height: '55px' }} />}
            <RspCardLayout minWidth={280}>
                {list.map((item: any) => {
                    return (
                        // @ts-ignore
                        <Card
                            data={item}
                            key={item.evidenceId}
                            exportData={exportData}
                            videoDrawFrameShow={videoDrawFrameShow}
                            playSpeed={playSpeed}
                            checkbox={{
                                checked: Boolean(
                                    selectedRows.find((i: any) => i.evidenceId === item.evidenceId),
                                ),
                                onChange: (checked: boolean) => {
                                    const index = selectedRows.findIndex(
                                        (i: any) => i.evidenceId === item.evidenceId,
                                    );
                                    const newRows = [...selectedRows];
                                    if (checked && index === -1) {
                                        newRows.push(item);
                                    } else if (!checked && index !== -1) {
                                        newRows.splice(index, 1);
                                    }
                                    setSelectedRows(newRows);
                                },
                            }}
                        />
                    );
                })}
            </RspCardLayout>

        </div>
    );

    return (
        <div className="evidence-done">
            <Container className="query-form-container">
                <QueryForm
                    className="query-form-custom-picker"
                    minFieldWidth={280}
                    items={queryItems as any[]}
                    onSearch={handleSearch}
                    onReset={() => setQueryParams({ page: 1, pageSize: 20 })}
                    layout="vertical"
                    form={form}
                    collapseCacheKey="@base:@page:evidence.list.done"
                />
            </Container>
            <Container noStyle={layoutType === 'card'}>
                <Spin spinning={loading}>
                    <div className="card-top-area">
                        {layoutType === 'card' && toolBar}
                    </div>
                    {layoutType === 'card' && cardList}
                    {layoutType === 'list' && (
                        <ProTable
                            className={cn('evidence-done-table', {
                                'evidence-list-table-scroll': !showSticky,
                            })}
                            aroundBordered
                            // @ts-ignore
                            columns={getCustomItems(getColumns, columns, list)}
                            dataSource={list}
                            rowKey={'evidenceId'}
                            onChange={handleTableChange}
                            scroll={{ x: '100%' }}
                            toolbar={{
                                onReload: query,
                                leftRender: () => (
                                    <Space>
                                        {getCustomJsx(
                                            getTableLeftRender,
                                            [batchDeleteButton()],
                                            list,
                                        )}
                                    </Space>
                                ),
                                // rightRender:() => toolBar,
                                iconBtns: getTableIconBtns(
                                    getIconBtns,
                                    [
                                        'reload',
                                        'column-setting',
                                        downloadSet(),
                                        listBtn(),
                                        cardBtn(),
                                    ],
                                    list,
                                ),
                                columnSetting: {
                                    // @ts-ignore
                                    storageKey:
                                        '@base:@page:evidence.list.done',
                                    disabledKeys: [
                                        'evidenceName',
                                        'vehicleNumber',
                                    ],
                                    ...getColumnSetting?.(),
                                },
                            }}
                            rowSelection={{
                                onChange: (
                                    selectedKeys: React.Key[],
                                    selectedRows: any[],
                                ) => {
                                    setSelectedRows(selectedRows);
                                },
                                selectedRowKeys: selectedRows.map(
                                    (i: any) => i.evidenceId,
                                ),
                            }}
                            sticky={{
                                offsetScroll: PAGINATION_HEIGHT,
                                offsetHeader: calcHeight(), // 距离container顶部的高度
                            }}
                        />
                    )}
                    {
                        <SimplePagination
                            page={page}
                            hasNextPage={hasNextPage}
                            onPageChange={onPageChange}
                            className={`evidence-pagination-box ${
                                layoutType === 'card'
                                    ? 'evidence-pagination-box-has-container'
                                    : ''
                            }`}
                        />
                    }
                </Spin>
            </Container>

            <MailModal
                visible={showMail}
                close={() => setShowMail(false)}
                evidenceId={evidenceId}
            />
            <CodeModal
                visible={showCode}
                close={() => setShowCode(false)}
                evidenceId={evidenceId}
                data={evidence}
            />
            <ImageView
                vehicleData={currentVehicle}
                visible={showImage}
                onClose={() => setShowImage(false)}
                list={imageList}
            />
        </div>
    );
};
export default withSharePropsHOC<TabProps, EvidenceListDoneShareProps>(
    EvidenceListDone,
);
