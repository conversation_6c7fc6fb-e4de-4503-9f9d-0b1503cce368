import {
    StarryAbroadOverflowEllipsisContainer,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import { useState, useEffect, useRef } from 'react';
import { getAppGlobalData, i18n, utils, useUrlSearchStore } from '@base-app/runtime-lib';
import { PageBreadcrumbLayout, PageCardLayout } from '@base-app/runtime-lib';
import { ListDataContainer, TableColumnSetting } from '@streamax/starry-components';
import { Input, Table, Form, Tooltip, message, Spin, Container } from '@streamax/poppy';
import {
    StatisticsData,
    STATUS_ABNORMAL,
    STATUS_FAIL,
    STATUS_NORMAL,
} from './components/Statistics';
import Statistics from './components/Statistics';
import DateRange from '../../../components/DateRange';
import { getLogs, getLogStatistics } from '../../../service/log-manage';
import moment from 'moment';
import './index.less';
import { IconExport } from '@streamax/poppy-icons';
import { exportExcel } from '@/service/import-export';
import { disabledAfterDate, getInitTimeRange, getPickerRanges } from '@/utils/commonFun';
import { getTenatLoginSetedConfig } from '@/service/tenant';
import { tableStorageKey } from '@/const/consants';

const clsPrefix = 'login-log-manage';
const exportServiceCode = 'ae64300c89cd4e21ab9c5258b9d61a26';

const { timestampToZeroTimeStamp, zeroTimeStampToZoneTime, getLocalMomentByZeroTimeStamp } =
    utils.formator;

export default () => {
    const [statistics, setStatistics] = useState<StatisticsData>({
        all: 0,
        normal: 0,
        abnormal: 0,
        fail: 0,
    });
    const [exportSpin, setExportSpin] = useState<boolean>(false);
    const statusMap: Record<string, any> = {
        [STATUS_NORMAL]: { name: i18n.t(STATUS_NORMAL), type: 'success' },
        [STATUS_ABNORMAL]: {
            name: i18n.t(STATUS_ABNORMAL),
            type: 'warning',
        },
        [STATUS_FAIL]: { name: i18n.t(STATUS_FAIL), type: 'error' },
    };

    const defaultDateRange = getInitTimeRange(7); // 默认7天内数据
    const [reqParams, setReqParams] = useState<Record<string, any>>({
        timeRange: defaultDateRange,
        operationUserName: (getAppGlobalData('APP_USER_INFO') || {}).account,
        sort: 'desc',
    });
    const containerRef = useRef<any>();
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const formQuery = searchStore.get();
    const [privacyAgreementFlag, setPrivacyAgreementFlag] = useState<0 | 1>(0);
    const isNumber = (value: any) => {
        return typeof value === 'number' && !isNaN(value);
    };
    useEffect(() => {
        if (
            Object.keys(formQuery).length > 0 &&
            isNumber(formQuery?.startTime) &&
            isNumber(formQuery?.endTime)
        ) {
            // 临时手动设置，ListDataSource内部表单映射有问题
            form.setFieldsValue({
                timeRange: [
                    getLocalMomentByZeroTimeStamp(Number(formQuery?.startTime)) || null,
                    getLocalMomentByZeroTimeStamp(Number(formQuery?.endTime)) || null,
                ],
                operationUserName: formQuery?.operationUserName || null,
            });
            containerRef.current?.loadDataSource({
                timeRange: [
                    getLocalMomentByZeroTimeStamp(Number(formQuery?.startTime)) || null,
                    getLocalMomentByZeroTimeStamp(Number(formQuery?.endTime)) || null,
                ],
                operationUserName: formQuery?.operationUserName || null,
                sort: formQuery?.sort || null,
                type: Number(formQuery?.type) || null,
                status: formQuery?.modifyType || null,
                page: Number(formQuery?.page) || 1,
                pageSize: Number(formQuery?.pageSize) || 20,

            });
        } else {
            containerRef.current?.loadDataSource({
                status: formQuery?.modifyType || null,
            });
        }
        getConfig();
    }, []);

    const getConfig = async () => {
        const config = await getTenatLoginSetedConfig();
        setPrivacyAgreementFlag(config.privacyAgreementFlag);
    };
    const queryItems = [
        {
            label: i18n.t('name', '登录账号'),
            name: 'operationUserName',
            field: Input,
            itemProps: {
                initialValue: (getAppGlobalData('APP_USER_INFO') || {}).account,
            },
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请输入登录账号'),
                maxLength: 50,
            },
        },
        {
            label: i18n.t('name', '登录时间'),
            name: 'timeRange',
            field: DateRange,
            colSize: 2,
            itemProps: {
                initialValue: defaultDateRange,
            },
            fieldProps: {
                maxInterval: {
                    value: 'Infinity',
                    unitOfTime: 'days',
                },
                pickerProps: {
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    allowClear: true,
                    dropdownClassName: 'login-log-manage-datepicker-dropdown',
                    ranges: getPickerRanges(),
                    disabledDate: disabledAfterDate,
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];
    const columns: any = [
        {
            title: i18n.t('name', '登录账号'),
            dataIndex: 'operationUserName',
            fixed: 'left',
            width: 200,
            render: (text: string) => (
                <StarryAbroadOverflowEllipsisContainer maxWidth={200}>
                    <span>{text}</span>
                </StarryAbroadOverflowEllipsisContainer>
            ),
        },
        {
            title: i18n.t('name', 'IP地址'),
            dataIndex: 'ip',
            width: 200,
            render: (text: any, record: any) => (JSON.parse(record.extraInfo) || {})['ip'] || '-',
        },
        {
            title: i18n.t('name', '登录时间'),
            dataIndex: 'createTime',
            width: 260,
            defaultSortOrder: `${formQuery?.sort ? `${formQuery.sort}end`: "descend"}`,
            sorter: (a: any, b: any) => a.createTime - b.createTime,
            render: (text: number) => (
                <StarryAbroadOverflowEllipsisContainer>
                    {text ? utils.formator.zeroTimeStampToFormatTime(text) : '-'}
                </StarryAbroadOverflowEllipsisContainer>
            ),
        },
        {
            title: i18n.t('name', '登录状态'),
            dataIndex: 'modifyType',
            width: 200,
            render: (text: any) => {
                return (
                    <span className="login-log-status-span">
                        <span className={`dot ${statusMap[text]?.type}-dot`} />
                        <span>{statusMap[text]?.name}</span>
                    </span>
                );
            },
        },
        {
            title: i18n.t('name', '登录状态详情'),
            dataIndex: 'content',
            width: 180,
            render: (text: any) => {
                const detailInfo = text?.split(',') || [''];
                let result = i18n.t(detailInfo[0]);
                if(detailInfo.length>1){
                    result += `(${i18n.t(detailInfo[1],'-')})`;
                }
                return result || '-';
            },
        },
    ];
    if (privacyAgreementFlag) {
        // 从第四列插入两列显示版本号
        columns.splice(
            3,
            0,
            {
                title: i18n.t('name', '用户协议版本'),
                dataIndex: 'userAgreementVersion',
                width: 200,
                render: (text: string) => {
                    return text || '-';
                },
            },
            {
                title: i18n.t('name', '隐私政策版本'),
                dataIndex: 'privacyPolicyVersion',
                width: 200,
                render: (text: string) => {
                    return text || '-';
                },
            },
        );
    }

    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting(columns, {
        disabledKeys: ['operationUserName', 'createTime', 'modifyType'],
        storageKey: '@base:@page:login.log.table',
    });
    const handleExport = () => {
        const excelHeaders = columns.map((item: any, index: number) => {
            return {
                title: item.title,
                columnName: item.dataIndex,
                index,
                isOption: false,
            };
        });
        const queryParam: any = {
            ...reqParams,
            type: 1,
            modifyType: reqParams.status,
            startTime: timestampToZeroTimeStamp(reqParams.timeRange[0]),
            endTime: timestampToZeroTimeStamp(reqParams.timeRange[1]),
        };
        delete queryParam.timeRange;
        delete queryParam.status;
        const params = {
            serviceCode: exportServiceCode,
            fileName: `${i18n.t('name', '登录日志')}_${moment().unix()}`,

            excelType: 'XLSX',
            isAsync: true,
            sheetQueryParams: [
                {
                    sheetName: 'login log sheet',
                    queryParam: { param: queryParam },
                    excelHeaders,
                },
            ],
        };
        setExportSpin(true);
        exportExcel(params)
            .then(async () => {
                message.success(i18n.t('message', '导出成功，请到个人中心中查看导出详情'));
            })
            .finally(() => {
                setExportSpin(false);
            });
    };

    const toolbar = {
        hasReload: false,
        extraIconBtns: [
            <Tooltip overlay={i18n.t('action', '导出')}>
                <span>
                    <StarryAbroadIcon>
                        <IconExport onClick={handleExport} />
                    </StarryAbroadIcon>
                </span>
            </Tooltip>,
            // @ts-ignore
            <TableColumnSetting key="setting" {...tableColumnSettingProps} />,
        ],
    };
    const getDataSource = (params: any) => {
        const newReqParams = { ...reqParams, ...params };
        setReqParams(newReqParams);
        let startTime, endTime;
        if (newReqParams.timeRange) {
            startTime = timestampToZeroTimeStamp(newReqParams.timeRange[0]);
            endTime = timestampToZeroTimeStamp(newReqParams.timeRange[1]);
        } else {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                timeRange: defaultDateRange,
            });
            startTime = timestampToZeroTimeStamp(defaultDateRange[0]);
            endTime = timestampToZeroTimeStamp(defaultDateRange[1]);
        }
        if (!isNumber(startTime) || !isNumber(endTime)) {
            message.error(i18n.t('message', '时间格式错误，请重新选择登录时间'));
            return;
        }
        const query = {
            ...newReqParams,
            type: 1,
            modifyType: newReqParams.status,
            startTime,
            endTime,
        };
        delete query.timeRange;
        delete query.status;
        getLogStatistics({
            operationUserName: query.operationUserName,
            // status: newReqParams.status,
            startTime: query.startTime,
            endTime: query.endTime,
        }).then((rs: any) => {
            const obj = (rs || []).reduce((a: any, b: any) => ({ ...a, [b.status]: b.count }), {});
            const t: StatisticsData = {
                all: 0,
                normal: obj[STATUS_NORMAL] || 0,
                abnormal: obj[STATUS_ABNORMAL] || 0,
                fail: obj[STATUS_FAIL] || 0,
            };
            t.all = t.normal + t.fail + t.abnormal;
            setStatistics(t);
        });
        searchStore.set(query);
        return getLogs(query).then((rs: any) => ({
            ...rs,
            total: Number(rs.total),
        }));
    };

    const handleStatusChange = (key: string) => {
        containerRef.current.loadDataSource({
            status: key,
            page: 1,
        });
    };

    const handleTableChange = (pagination: any, filters: any, sorter: any) => {
        const { order = 'descend' } = sorter;
        containerRef.current.loadDataSource({
            sort: order === 'descend' ? 'desc' : 'asc',
        });
    };

    return (
        <PageBreadcrumbLayout>
            <PageCardLayout>
                <Spin spinning={exportSpin}>
                    <div className={`${clsPrefix}-container`}>
                        <Container>
                            <Statistics
                                data={statistics}
                                defaultData={formQuery?.modifyType}
                                onChange={handleStatusChange}
                            />
                        </Container>
                        <ListDataContainer
                            ref={containerRef}
                            loadDataSourceOnMount={false}
                            getDataSource={getDataSource}
                            queryForm={{
                                items: queryItems,
                                form,
                            }}
                            toolbar={toolbar}
                            listRender={(data) => {
                                return (
                                    <Table
                                        columns={tableColumns}
                                        dataSource={data}
                                        pagination={false}
                                        scroll={{ x: '100%' }}
                                        rowKey="id"
                                        onChange={handleTableChange}
                                    />
                                );
                            }}
                        />
                    </div>
                </Spin>
            </PageCardLayout>
        </PageBreadcrumbLayout>
    );
};
