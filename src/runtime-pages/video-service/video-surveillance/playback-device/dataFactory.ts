import { getDeviceDayquery } from '@/service/device';
import { fetchEvidenceFileList, fetchEvidenceFileListWithChannelFilter } from '@/service/evidence';
import { fetchParameterDetail } from '@/service/parameter';
import { getTravelSimpleGps } from '@/service/track-playback';
import { getAppGlobalData } from '@base-app/runtime-lib';
import { utils } from '@base-app/runtime-lib';
import { groupBy } from 'lodash';
import moment from 'moment';
import { DEVICE_PROTOCOL_TYPE, FILE_TYPE, PAGE_ORIGIN } from './constant';
import { ChangeParams, ChannelData, ImageData, SpeedData, StoreType } from './types';
import { CONTROL_RANGE, isNeedControl } from '@/utils/flow';
import { check28181 } from '@/utils/28181utils';

const { timestampToZeroTimeStamp, zeroTimeStampToFormatTime } = utils.formator;

const FILE_TYPE_STRATEGY = 1; // 设置抓拍
const FILE_TYPE_ALARM = 2; //  报警抓拍
const FILE_TYPE_VIDEO = 3; // 视频自动生成

// 各种类型图片权重
const imageWeight = {
    [FILE_TYPE_ALARM]: 1000,
    [FILE_TYPE_STRATEGY]: 100,
    [FILE_TYPE_VIDEO]: 10,
};

// 图片去重
const duplicateRemoval = (list: ImageData[]) => {
    const groupDataByTime = groupBy(list, 'startTime');
    const result = Object.values(groupDataByTime).map((items: ImageData[]) => {
        if (items.length <= 1) {
            // 没有重复项
            return items[0];
        } else {
            // 存在重复项，需要去重，按照报警抓拍 > 设置抓拍 > 视频生成的优先级取一张图片
            // 根据权重进行比较，取优先级最高的那个
            return items.sort(
                (a: ImageData, b: ImageData) => imageWeight[b.subType] - imageWeight[a.subType],
            )[0];
        }
    });
    // 根据时间排序
    result.sort((a: ImageData, b: ImageData) => a.startTime - b.startTime);
    return result;
};

// 获取图片数据
export const getImageData = async (params: ChangeParams) => {
    const { deviceInfo, startTime, endTime, vehicleInfo, mosaicFlag } = params;
    const channelsList = deviceInfo.deviceChannelList || [];
    const filterChannelNos = channelsList?.map(item => item.channelNo);
    // 使用新的支持通道号过滤的接口
    const list = await fetchEvidenceFileListWithChannelFilter({
        vehicleId: vehicleInfo.vehicleId,
        startTime,
        endTime,
        fileType: FILE_TYPE.image,
        fields: 'file_url',
        deviceId: deviceInfo.deviceId,
        mosaicFlag,
        channelNos: filterChannelNos
    });
    
    // 创建通道映射
    const channelMap: Record<string | number, string> = {};
    channelsList.forEach((item) => {
        channelMap[item.channelNo] = item.channelAlias || '';
    });
    
    // 去除无url的数据并设置channelAlias属性
    const imgList: ImageData[] = [];
    for (let i = 0; i < list.length; i++) {
        const item: ImageData = list[i];
        if (item.url) {
            imgList.push({
                ...item,
                channelAlias: channelMap[item.channelNo] || '',
            });
        }
    }

    // 对数据按通道进行分组
    const groupImgData: Record<string, ImageData[]> = groupBy(imgList, 'channelNo');
    // 图片去重&排序
    Object.keys(groupImgData).forEach((key: string) => {
        groupImgData[key] = duplicateRemoval(groupImgData[key]);
    });
    return groupImgData;
};

// 查询日历数据
export const getVideoData = async (params: ChangeParams, pageOrigin: string) => {
    const { deviceInfo, startTime, endTime, channel, vehicleInfo } = params;
    const { authId, protocolType } = deviceInfo;

    const platformTimeZone = Number(getAppGlobalData('PLATFORM_TIME_ZONE') || 0);
    const datetimeFormat = 'YYYY-MM-DD HH:mm:ss';

    const deviceChannelList = deviceInfo.deviceChannelList || [];
    const channels = check28181(deviceInfo) ? [channel] : deviceChannelList.map((device) => device.channelNo) || [];

    const fetchDeviceDayData = async (st: string, et: string) => {
        let storeType: StoreType = 'ALL',
            streamType = 'ALL';
        if (protocolType == DEVICE_PROTOCOL_TYPE.JT905 && pageOrigin !== PAGE_ORIGIN.server) {
            storeType = 'MASTER';
            streamType = 'MAJOR';
        }
        if (pageOrigin === PAGE_ORIGIN.mix) {
            storeType = 'MIX';
            const isStopUse = await isNeedControl(authId, CONTROL_RANGE.PLAY_BACK);
            if (isStopUse) storeType = 'CENTER';
        } else if (pageOrigin === PAGE_ORIGIN.server) {
            storeType = 'CENTER';
        }
        let showChangeMessage = true;
        // 跳转到回放的 没有图片视频模式切换的 message只提示设备离线 fix: [63688]
        if (pageOrigin === PAGE_ORIGIN.alarmUploadEvidence || pageOrigin === PAGE_ORIGIN.alarmPlayBack) {
            showChangeMessage = false;
        }
        return await getDeviceDayquery(
            {
                devId: authId,
                chan: protocolType === 1 ? channels.join(',') : '0',
                storeType,
                streamType,
                beginTime: st,
                endTime: et,
            },
            {
                _accessKey: `${localStorage.getItem('AUTH_TOKEN')}#${getAppGlobalData('APP_ID')}`,
            },
            showChangeMessage
        );
    };

    // 将0时区的时间戳转换成对应平台时区的字符串格式
    // 因为在源头设置startTime和endTime时，处理了夏令时，所以这里不用再处理夏令时
    const dateStart = zeroTimeStampToFormatTime(startTime, platformTimeZone, datetimeFormat, true);
    // modify夏令时日历时间查询
    const dateEnd = zeroTimeStampToFormatTime(endTime, platformTimeZone, datetimeFormat, true);

    // 去掉跨天分两次调接口搜索的逻辑，此过程在s17做兼容了
    const dataList = await fetchDeviceDayData(dateStart, dateEnd);
    let channelList: ChannelData[] = [];
    // 组装通道数据
    dataList.map((val) => {
        const index = channelList.findIndex((p) => p.channel === val.chan);
        let startUnix = timestampToZeroTimeStamp(moment(val.beginTime), platformTimeZone, true);
        startUnix = startUnix < startTime ? startTime : startUnix;
        let endUnix = timestampToZeroTimeStamp(moment(val.endTime), platformTimeZone, true);
        endUnix = endUnix > endTime ? endTime : endUnix;
        if (index > -1) {
            channelList[index].videoList.push({
                startUnix,
                endUnix,
                fileSize: val.fileSize,
                streamType: val.streamType,
                storeType: val.storeType,
                channel: val.chan,
                fileId: val.fileId,
                fileUuid: val.fileUuid,
            });
        } else {
            channelList.push({
                channel: val.chan,
                channelAlias: '',
                videoList: [
                    {
                        startUnix,
                        endUnix,
                        fileSize: val.fileSize,
                        streamType: val.streamType,
                        storeType: val.storeType,
                        channel: val.chan,
                        fileId: val.fileId,
                        fileUuid: val.fileUuid,
                    },
                ],
            });
        }
    });

    channelList = channelList
        .map((item) => {
            return {
                ...item,
                vehicleNumber: vehicleInfo?.vehicleNumber,
                channelAlias:
                    (deviceChannelList.find((p) => p.channelNo === item.channel) || {})
                        .channelAlias || '',
            };
        })
        .filter((p) => p.channelAlias)
        .sort((a, b) => a.channel - b.channel);
    return { channelList, dataList };
};

// 获取速度数据
export const getSpeedData = async (params: ChangeParams) => {
    const { startTime, endTime, vehicleInfo } = params;
    const data = await getTravelSimpleGps({
        vehicleId: vehicleInfo.vehicleId,
        startTime,
        endTime,
        // 按产品要求，在速度展示时，获取的gps不过滤gps为0的数据
        validGps: 1,
    });
    const speedData: SpeedData[] = (data as []).map((item: string) => {
        const [time, , , speed] = item.split(',').map((val: string) => Number(val));
        return {
            gpsTime: time,
            speed,
        };
    });
    return speedData;
};

// 获取已上传到服务器的视频的时间段
export const getUploadedTimeData = async (params: ChangeParams) => {
    const { startTime, endTime, vehicleInfo, deviceInfo, mosaicFlag } = params;
    const channelsList = deviceInfo.deviceChannelList || [];
    const filterChannelNos = channelsList?.map(item => item.channelNo);
    const list = await fetchEvidenceFileListWithChannelFilter({
        vehicleId: vehicleInfo.vehicleId,
        startTime,
        endTime,
        fileType: FILE_TYPE.video,
        deviceId: deviceInfo.deviceId,
        mosaicFlag,
        channelNos: filterChannelNos
    });
    return list;
};

// 获取系统参数
export const getSystemParam = async (name: string) => {
    try {
        const result = await fetchParameterDetail({ parameterKey: name }, false);
        return result?.parameterValue;
    } catch (error) {
        return '';
    }
};
