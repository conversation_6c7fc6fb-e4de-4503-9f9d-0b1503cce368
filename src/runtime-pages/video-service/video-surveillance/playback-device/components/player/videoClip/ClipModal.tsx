import React, { useEffect, useRef, useState } from 'react';
import { i18n, utils, getAppGlobalData } from '@base-app/runtime-lib';
import { useHistory, useLocation } from '@base-app/runtime-lib/core';
import {
    Modal,
    Row,
    Col,
    Form,
    Input,
    Button,
    TimePicker,
    Checkbox,
    Radio,
    message,
    Select,
} from '@streamax/poppy';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import moment from 'moment';
import { videoCut } from '@/service/evidence';
import { handleAlarmRecord } from '@/service/alarm';
import { PlayMode } from '../../../types';
import { STORE_TYPE_VALUE, STREAM_TYPE_VALUE, PLAY_MODE } from '../../../constant';
import { getOffsetTimeByTimeStamp } from '@/utils/commonFun';
import { omit } from 'lodash';
import './clipModal.less';
import { CONTROL_RANGE, isNeedControl } from '@/utils/flow';
import { vehicleIsOnline } from '../../../utils';
import usePageOrigin from '../../../hooks/usePageOrigin';
import { PAGE_ORIGIN } from '../../../constant';
import { check28181 } from '@/utils/28181utils';
const FormItem = Form.Item;
const CheckboxGroup = Checkbox.Group;
const RadioGroup = Radio.Group;

interface VideoEditModel {
    playMode: PlayMode;
    visible: boolean;
    vehicleInfo: any;
    deviceInfo: any;
    selectedDate: string;
    channelListData: any[];
    streamType?: number;
    storeType?: number;
    clipVideoTimeRange?: number[];
    onCloseModal: () => void;
    defaultStoreStream?: {
        streamType: number;
        storeType: number;
    };
}
const VideoEdit: React.FC<VideoEditModel> = (props) => {
    // const timeLineRef = useRef<any>();
    const validatorVideoName = utils.validator.validateBaseVehicleNumber;
    const history: any = useHistory();
    const {
        playMode,
        visible,
        vehicleInfo,
        deviceInfo,
        selectedDate,
        channelListData,
        streamType,
        storeType,
        clipVideoTimeRange = [0, 0],
        onCloseModal,
        defaultStoreStream,
    } = props;

    // 页面来源
    const origin = usePageOrigin();
    const { deviceId, authId, protocolType = 1 } = deviceInfo;
    const deviceOnline = vehicleIsOnline(deviceInfo);

    const [moreSetting, setMoreSetting] = useState<boolean>(false);
    const [channelOptions, setChannelOptions] = useState<any[]>([]);
    const offsetTimeRef = useRef(0);
    const [priorityOptions] = useState([
        {
            label: i18n.t('name', '低'),
            value: 0,
        },
        {
            label: i18n.t('name', '中'),
            value: 1,
        },
        {
            label: i18n.t('name', '高'),
            value: 2,
        },
    ]);
    const [streamTypeOptions, setStreamTypeOptions] = useState([
        {
            label: i18n.t('name', '主码流'),
            value: 1,
        },
        {
            label: i18n.t('name', '子码流'),
            value: 2,
        },
    ]);
    const [storeTypeOptions, setStoreTypeOptions] = useState([
        {
            label: i18n.t('name', '主存储器'),
            value: 1,
        },
        {
            label: i18n.t('name', '子存储器'),
            value: 2,
        },
    ]);
    const [checkedList, setCheckedList] = useState<any[]>([]);
    const [indeterminate, setIndeterminate] = useState(false);
    const [checkAll, setCheckAll] = useState(true);
    const [startTime, setStartTime] = useState<number>(0);
    const [endTime, setEndTime] = useState<number>(0);
    const [timeStartStep, setTimeStartStep] = useState<number>(10);
    const [timeEndStep, setTimeEndStep] = useState<number>(10);
    // 初始化是否完成
    // 第一次打开时，应该使用外部传入的存储器和码流类型
    // 后面再切换存储器时，按照默认设置进行选择
    const [initFinish, setInitFinish] = useState<boolean>(false);
    const playerRef: any = useRef();
    const [form] = Form.useForm();
    const storeOptions = {
        MASTER: 1,
        BACKUP: 2,
        CENTER: 3,
    };
    const location: any = useLocation();
    const timeFormat = getAppGlobalData('APP_USER_CONFIG')?.timeFormat;

    useEffect(() => {
        if (visible) {
            const formateDate = moment(selectedDate).format(timeFormat);
            form.setFieldsValue({
                evidenceName: `${vehicleInfo?.vehicleNumber}-${formateDate}-${i18n.t(
                    'name',
                    '视频',
                )}`,
            });
            // 设置通道数据
            const channels: any[] = channelListData.map((val: any) => {
                return {
                    label: val.channelAlias || i18n.t('name', '通道') + val.channel,
                    value: val.channel,
                };
            });
            setChannelOptions(channels || []);
            setStartTime(Math.round(clipVideoTimeRange[0]) || 0);
            setEndTime(Math.round(clipVideoTimeRange[1]) || 0);
            const offsetTime = getOffsetTimeByTimeStamp(
                moment(Math.round(clipVideoTimeRange[0]) * 1000),
                'minute',
            );
            form.setFieldsValue({
                channelNoList: (channels || []).map((val: any) => val.value),
                storeType: storeType || 1,
                startTime: moment(Math.round(clipVideoTimeRange[0]) * 1000).utcOffset(offsetTime),
                endTime: moment(Math.round(clipVideoTimeRange[1]) * 1000).utcOffset(offsetTime),
                videoLength: formatTime(
                    Math.round(clipVideoTimeRange[1] - Math.round(clipVideoTimeRange[0])),
                ),
            });
            if (streamType) {
                form.setFieldsValue({
                    streamType: streamType || 2,
                });
            }
            // 视频模式
            if (playMode === PLAY_MODE['video']) {
                initMemoryOptions((channels || []).map((val: any) => val.value));
                // 图片模式
            } else {
                // 默认选中主存储器子码流
                form.setFieldsValue({
                    storeType: defaultStoreStream?.storeType || STORE_TYPE_VALUE.MASTER,
                    streamType: defaultStoreStream?.streamType || STREAM_TYPE_VALUE.MAJOR,
                });
            }
        }
    }, [visible]);

    useEffect(() => {
        if (startTime > 0 && endTime > 0) {
            const deffer = endTime - startTime;
            form.setFieldsValue({
                videoLength: formatTime(deffer),
            });
        }
    }, [startTime, endTime]);

    const formatTime = (time: number) => {
        if (time < 0) return '0s';
        const hours = Math.floor(time / 3600);
        const minutes = Math.floor((time % 3600) / 60);
        const seconds = time % 60;
        return `${hours > 0 ? hours + 'h' : ''} ${
            hours > 0 || minutes > 0 ? minutes + 'm' : ''
        } ${seconds}s`;
    };
    // 通道全选
    const onCheckAllChange = (e: any) => {
        const list = e.target.checked ? channelOptions.map((val) => val.value) : [];
        setCheckedList(list);
        form.setFieldsValue({ channelNoList: list });
        setIndeterminate(false);
        setCheckAll(e.target.checked);
        const tempChannelNoList = form.getFieldValue('channelNoList');
        const tempStoreType = form.getFieldValue('storeType');
        formValuesChange({ storeType: tempStoreType, channelNoList: tempChannelNoList });
    };
    // 通道change
    const downloadChannelChange = (list: any) => {
        setCheckedList(list);
        setIndeterminate(!!list.length && list.length < channelOptions.length);
        setCheckAll(list.length === channelOptions.length);
    };

    // 确认
    const handleOk = async () => {
        if (origin === PAGE_ORIGIN.device || origin === PAGE_ORIGIN.other) {
            const needControlCut = await isNeedControl(authId, CONTROL_RANGE.VIDEO_CUT);
            if (needControlCut) {
                message.warn(i18n.t('message', '流量使用超额，请处理流量限制。'));
            }
        }
        form.validateFields().then(async (values) => {
            const {
                evidenceName,
                priority,
                streamType,
                startTime,
                endTime,
                storeType,
                channelNoList,
            } = values;
            if (origin === PAGE_ORIGIN.mix && storeType !== 3 && deviceOnline) {
                // 融合回放、设备存储器、在线
                const needControlCut = await isNeedControl(authId, CONTROL_RANGE.VIDEO_CUT);
                if (needControlCut) {
                    message.warn(i18n.t('message', '流量使用超额，请处理流量限制。'));
                }
            }
            const startTimeStr = moment(values.startTime).format('HH:mm:ss');
            const endTimeStr = moment(values.endTime).format('HH:mm:ss');
            const startTimeStamp = utils.formator.timestampToZeroTimeStamp(
                moment(`${selectedDate} ${startTimeStr}`),
            );
            const endTimeStamp = utils.formator.timestampToZeroTimeStamp(
                moment(`${selectedDate} ${endTimeStr}`),
            );
            if (startTime && endTime && endTimeStamp - startTimeStamp > 3600) {
                message.error(i18n.t('message', '剪辑时长超过1小时'));
                return;
            }
            if (startTime && endTime && endTimeStamp <= startTimeStamp) {
                message.error(i18n.t('message', '结束时间应大于开始时间'));
                return;
            }
            const storeTypeVal = location.pathname.includes('playback-serve') ? undefined : storeType;
            let alarmParams: any;
            if (location?.query?.alarmInfo) {
                alarmParams = JSON.parse(decodeURIComponent(location?.query?.alarmInfo));
            }
            const sourceType = location?.query?.alarmSourceType == 2 ? 9 : 1;
            const pathType = location.pathname.includes('playback-serve') ? 5 : 2;
            const paramList = [
                {
                    fileType: 1,
                    streamType: streamType,
                    storageType: storeTypeVal,
                    channelNoList: channelNoList,
                },
                {
                    fileType: 2,
                    streamType: streamType,
                    storageType: storeTypeVal,
                },
                {
                    fileType: 3,
                    streamType: streamType,
                    storageType: storeTypeVal,
                },
                {
                    fileType: 4,
                    streamType: streamType,
                    storageType: storeTypeVal,
                },
            ];
            const resultParams = check28181(deviceInfo || {}) ? paramList.filter(i => i.fileType === 1) : paramList;
            const param: any = {
                evidenceName: evidenceName,
                evidenceType: alarmParams ? sourceType : pathType,
                sourceType: '2',
                vehicleId: vehicleInfo.vehicleId,
                deviceId: deviceId,
                authId: authId,
                fileParam: {
                    priority: priority,
                    startTime: startTimeStamp,
                    endTime: endTimeStamp,
                    protocolType: protocolType,
                    needScreenSnap: 0,
                    evtId: location?.query?.evtId || undefined,
                    uuid: alarmParams?.uuid || '',
                    paramList: resultParams,
                },
                alarmInfo: alarmParams ? { ...alarmParams } : undefined,
                prefixUrl: window.location.origin,
            };
            if (location?.query?.driverId) {
                param.driverId = location?.query?.driverId;
            }
            videoCut(param).then((rs) => {
                onCloseModal?.();
                if (alarmParams) {
                    message.success(i18n.t('message', '视频剪辑成功,已存放在证据列表'));
                } else {
                    message.success(i18n.t('message', '视频剪辑成功,已存放在视频中心'));
                }
                // 从报警详情跳转过来的，剪辑成功后需要提交处理记录
                // @ts-ignore
                if (location?.query?.alarmId && origin === PAGE_ORIGIN.alarmUploadEvidence) {
                    handleAlarmRecord([
                        {
                            // @ts-ignore
                            alarmId: location?.query?.alarmId,
                            type: 'saas.search.alarm.handle.type.uploadevidence',
                            content: rs.evidenceId,
                        },
                    ]);
                    history.replace({
                        query: omit(location?.query, ['alarmId', 'alarmInfo']),
                    });
                }
            });
        });
    };
    const initMemoryOptions = (channel: any[]) => {
        const memoryOption = [];
        let masterFlag = false;
        let backupFlag = false;
        let centerFlag = false;
        let defaultMemoryFlag = false;
        channelListData.map((val) => {
            if ((channel || []).indexOf(val.channel) > -1) {
                val.videoList.map((video: any) => {
                    if (video.storeType === 'MASTER') {
                        masterFlag = true;
                    } else if (video.storeType === 'BACKUP') {
                        backupFlag = true;
                    } else if (video.storeType === 'CENTER') {
                        centerFlag = true;
                    }
                });
                const defaultMemoryList =
                    val.videoList.filter(
                        (p: any) => p.storeType === playerRef.current?.currentStoreType,
                    ) || [];
                if (defaultMemoryList.length > 0) defaultMemoryFlag = true;
            }
        });
        if (masterFlag) {
            memoryOption.push({
                label: i18n.t('name', '主存储器'),
                value: 1,
            });
        }
        if (backupFlag) {
            memoryOption.push({
                label: i18n.t('name', '子存储器'),
                value: 2,
            });
        }
        if (centerFlag) {
            memoryOption.push({
                label: i18n.t('name', '中心存储器'),
                value: 3,
            });
        }
        setStoreTypeOptions(memoryOption);
        let memoryValue =
            backupFlag && (playerRef.current?.currentStoreType === 'BACKUP' || !masterFlag) ? 2 : 1;
        if (defaultMemoryFlag)
            memoryValue =
                (playerRef.current?.currentStoreType === 'BACKUP' ? 2 : 1) || storeType || 1;
        if (centerFlag) memoryValue = 3;
        // 获取当前选中的存储器的值
        const finish = initFinish ? memoryValue : defaultStoreStream?.storeType || memoryValue;
        const defaultStoreType = memoryValue !== 3 ? finish : memoryValue;
        form.setFieldsValue({
            storeType: defaultStoreType,
        });
        initStreamOptions(channel, defaultStoreType);
    };
    const initStreamOptions = (channel: any[], memory: number) => {
        const streamOption = [];
        let majorFlag = false;
        let minorFlag = false;
        channelListData.map((val) => {
            if (channel.indexOf(val.channel) > -1) {
                val.videoList.map((video: any) => {
                    if (video.streamType === 'MAJOR' && memory == storeOptions[video.storeType]) {
                        majorFlag = true;
                    } else if (
                        video.streamType === 'MINOR' &&
                        memory == storeOptions[video.storeType]
                    ) {
                        minorFlag = true;
                    }
                });
            }
        });
        if (majorFlag) {
            streamOption.push({
                label: i18n.t('name', '主码流'),
                value: 1,
            });
        }
        if (minorFlag) {
            streamOption.push({
                label: i18n.t('name', '子码流'),
                value: 2,
            });
        }
        setStreamTypeOptions(streamOption);
        const majorOrMinor = playerRef.current?.currentStreamType === 'MAJOR' || !minorFlag;
        const flag = majorOrMinor ? 1 : 2;
        const streamValue = majorFlag && flag;
        const type = initFinish ? streamValue : defaultStoreStream?.streamType;
        const defaultStreamType = type || streamValue;
        form.setFieldsValue({
            streamType: defaultStreamType,
        });
        setInitFinish(true);
    };
    const formValuesChange = (values: any) => {
        // 如果是图片模式,则不需要重新计算码流类型数据
        if (playMode === PLAY_MODE['image']) return;
        if (values.channelNoList && !location.pathname.includes('playback-serve')) {
            initMemoryOptions(values.channelNoList);
        } else if (values.channelNoList && location.pathname.includes('playback-serve')) {
            const selectChannel = form.getFieldValue('channelNoList');
            initStreamOptions(selectChannel, 3);
        } else if (values.storeType) {
            const selectChannel = form.getFieldValue('channelNoList');
            initStreamOptions(selectChannel, values.storeType);
        }
    };
    const onStartTimeChange = (val: any) => {
        const timeStr = moment(val).format('HH:mm:ss');
        const timeStamp = utils.formator.timestampToZeroTimeStamp(
            moment(`${selectedDate} ${timeStr}`),
        );
        if (timeStamp >= endTime) {
            form.setFieldsValue({
                startTime: moment(utils.formator.zeroTimeStampToFormatTime(startTime)),
            });
            return;
        }
        setStartTime(timeStamp);
    };
    const onEndTimeChange = (val: any) => {
        const timeStr = moment(val).format('HH:mm:ss');
        const timeStamp = utils.formator.timestampToZeroTimeStamp(
            moment(`${selectedDate} ${timeStr}`),
        );
        if (timeStamp <= startTime) {
            form.setFieldsValue({
                endTime: moment(utils.formator.zeroTimeStampToFormatTime(endTime)),
            });
            return;
        }
        setEndTime(timeStamp);
    };
    const addTime = (type: 'start' | 'end') => {
        if (type === 'start') {
            const timeStr = moment(form.getFieldValue('startTime')).format('HH:mm:ss');
            const startTimeStamp =
                utils.formator.timestampToZeroTimeStamp(moment(`${selectedDate} ${timeStr}`)) +
                timeStartStep;
            if (startTimeStamp >= endTime) return;
            setStartTime(startTimeStamp);
            const offsetTime = getOffsetTimeByTimeStamp(startTimeStamp, 'minute');
            offsetTimeRef.current = offsetTime;
            const startTime = moment(startTimeStamp * 1000).utcOffset(offsetTime);
            const deffer = endTime - startTimeStamp > 0 ? endTime - startTimeStamp : 0;
            form.setFieldsValue({
                startTime: startTime,
                videoLength: formatTime(deffer),
            });
        } else {
            const timeStr = moment(form.getFieldValue('endTime')).format('HH:mm:ss');
            const endTimeStamp =
                utils.formator.timestampToZeroTimeStamp(moment(`${selectedDate} ${timeStr}`)) +
                timeEndStep;
            if (endTimeStamp <= startTime) return;
            setEndTime(endTimeStamp);

            const endTime = moment(endTimeStamp * 1000).utcOffset(offsetTimeRef.current);
            const deffer = endTimeStamp - startTime > 0 ? endTimeStamp - startTime : 0;
            form.setFieldsValue({
                endTime: endTime,
                videoLength: formatTime(deffer),
            });
        }
    };
    const reduceTime = (type: 'start' | 'end') => {
        if (type === 'start') {
            const timeStr = moment(form.getFieldValue('startTime')).format('HH:mm:ss');
            const startTimeStamp =
                utils.formator.timestampToZeroTimeStamp(moment(`${selectedDate} ${timeStr}`)) -
                timeStartStep;
            if (startTimeStamp >= endTime) return;
            setStartTime(startTimeStamp);
            const offsetTime = getOffsetTimeByTimeStamp(startTimeStamp, 'minute');
            offsetTimeRef.current = offsetTime;
            const startTime = moment(startTimeStamp * 1000).utcOffset(offsetTime);
            const deffer = endTime - startTimeStamp > 0 ? endTime - startTimeStamp : 0;
            form.setFieldsValue({
                startTime: startTime,
                videoLength: formatTime(deffer),
            });
        } else {
            const timeStr = moment(form.getFieldValue('endTime')).format('HH:mm:ss');
            const endTimeStamp =
                utils.formator.timestampToZeroTimeStamp(moment(`${selectedDate} ${timeStr}`)) -
                timeEndStep;
            if (endTimeStamp <= startTime) return;
            setEndTime(endTimeStamp);
            const endTime = moment(endTimeStamp * 1000).utcOffset(offsetTimeRef.current);
            const deffer = endTimeStamp - startTime > 0 ? endTimeStamp - startTime : 0;
            form.setFieldsValue({
                endTime: endTime,
                videoLength: formatTime(deffer),
            });
        }
    };

    return (
        <Modal
            title={i18n.t('action', '视频剪辑')}
            visible={visible}
            maskClosable={false}
            width={440}
            zIndex={1002}
            // getContainer={() => document.getElementById('playback-h5-player') as HTMLElement}
            okText={i18n.t('action', '确定')}
            cancelText={i18n.t('action', '取消')}
            onOk={handleOk}
            onCancel={() => onCloseModal?.()}
        >
            <div className="video-edit-page">
                <div className="video-edit-form">
                    <Form layout="vertical" form={form} onValuesChange={formValuesChange}>
                        <Row>
                            <Col span={24}>
                                <FormItem
                                    label={i18n.t('name', '开始时间')}
                                    rules={[{ required: true }]}
                                >
                                    <FormItem
                                        name="startTime"
                                        style={{
                                            width: 'calc(100% - 91px)',
                                            display: 'inline-block',
                                            marginBottom: '0px',
                                        }}
                                    >
                                        <TimePicker
                                            placeholder={i18n.t('message', '开始时间')}
                                            allowClear={false}
                                            style={{ width: '100%' }}
                                            onChange={onStartTimeChange}
                                        />
                                    </FormItem>
                                    <div className="time-step-control">
                                        <div className="time-step-btns">
                                            <div
                                                className="time-step-add"
                                                onClick={() => addTime('start')}
                                            >
                                                <UpOutlined />
                                            </div>
                                            <div
                                                className="time-step-reduce"
                                                onClick={() => reduceTime('start')}
                                            >
                                                <DownOutlined />
                                            </div>
                                        </div>
                                        <Select
                                            style={{ width: '68px' }}
                                            value={timeStartStep}
                                            onChange={(e: any) => setTimeStartStep(e)}
                                        >
                                            <Select.Option value={1}>1s</Select.Option>
                                            <Select.Option value={10}>10s</Select.Option>
                                            <Select.Option value={30}>30s</Select.Option>
                                        </Select>
                                    </div>
                                </FormItem>
                            </Col>
                            <Col span={24}>
                                <FormItem
                                    label={i18n.t('name', '结束时间')}
                                    rules={[{ required: true }]}
                                >
                                    <FormItem
                                        name="endTime"
                                        style={{
                                            width: 'calc(100% - 91px)',
                                            display: 'inline-block',
                                            marginBottom: '0px',
                                        }}
                                    >
                                        <TimePicker
                                            placeholder={i18n.t('message', '结束时间')}
                                            allowClear={false}
                                            style={{ width: '100%' }}
                                            onChange={onEndTimeChange}
                                        />
                                    </FormItem>
                                    <div className="time-step-control">
                                        <div className="time-step-btns">
                                            <div
                                                className="time-step-add"
                                                onClick={() => addTime('end')}
                                            >
                                                <UpOutlined />
                                            </div>
                                            <div
                                                className="time-step-reduce"
                                                onClick={() => reduceTime('end')}
                                            >
                                                <DownOutlined />
                                            </div>
                                        </div>
                                        <Select
                                            style={{ width: '68px' }}
                                            value={timeEndStep}
                                            onChange={(e: any) => setTimeEndStep(e)}
                                        >
                                            <Select.Option value={1}>1s</Select.Option>
                                            <Select.Option value={10}>10s</Select.Option>
                                            <Select.Option value={30}>30s</Select.Option>
                                        </Select>
                                    </div>
                                </FormItem>
                            </Col>
                            <Col span={24}>
                                <FormItem name="videoLength" label={i18n.t('name', '视频时长')}>
                                    <Input disabled />
                                </FormItem>
                            </Col>
                            <Col span={24}>
                                <FormItem
                                    name="evidenceName"
                                    label={i18n.t('name', '视频名称')}
                                    rules={[
                                        {
                                            required: true,
                                        },
                                        {
                                            validator: validatorVideoName,
                                        },
                                    ]}
                                >
                                    <Input
                                        allowClear
                                        maxLength={50}
                                        placeholder={i18n.t('message', '请输入视频名称')}
                                    />
                                </FormItem>
                            </Col>
                            <Col span={24} style={{ textAlign: 'right' }}>
                                <Button type="link" onClick={() => setMoreSetting(!moreSetting)}>
                                    {i18n.t('name', '高级设置')}
                                    {moreSetting ? <UpOutlined /> : <DownOutlined />}
                                </Button>
                            </Col>
                        </Row>
                        <div className={classNames('more-form', { 'form-show': moreSetting })}>
                            <Row>
                                <Col span={24} className="all-checkbox">
                                    <FormItem
                                        name="channelNoList"
                                        rules={[
                                            {
                                                required: true,
                                                message: i18n.t('message', '下载通道不能为空'),
                                            },
                                        ]}
                                        label={
                                            <>
                                                <span style={{ marginRight: 28 }}>
                                                    {i18n.t('name', '下载通道')}
                                                </span>
                                                <Checkbox
                                                    indeterminate={indeterminate}
                                                    onChange={onCheckAllChange}
                                                    checked={checkAll}
                                                >
                                                    {i18n.t('name', '全选')}
                                                </Checkbox>
                                            </>
                                        }
                                    >
                                        <CheckboxGroup
                                            value={checkedList}
                                            onChange={downloadChannelChange}
                                        >
                                            {channelOptions.map((item: any) => {
                                                return (
                                                    <Checkbox value={item.value} key={item.value}>
                                                        <span title={item.label}>
                                                            {' '}
                                                            {item.label}
                                                        </span>
                                                    </Checkbox>
                                                );
                                            })}
                                        </CheckboxGroup>
                                    </FormItem>
                                </Col>
                                {location.pathname.includes('playback-serve') ? null : (
                                    <Col span={24}>
                                        <FormItem
                                            name="storeType"
                                            label={i18n.t('name', '存储器类型')}
                                            // initialValue={2}
                                            rules={[
                                                {
                                                    required: true,
                                                },
                                            ]}
                                        >
                                            <RadioGroup options={storeTypeOptions} />
                                        </FormItem>
                                    </Col>
                                )}
                                <Col span={24}>
                                    <FormItem
                                        name="streamType"
                                        label={i18n.t('name', '码流类型')}
                                        // initialValue={2}
                                        rules={[
                                            {
                                                required: true,
                                            },
                                        ]}
                                    >
                                        <RadioGroup options={streamTypeOptions} />
                                    </FormItem>
                                </Col>
                                <Col span={24}>
                                    <FormItem
                                        name="priority"
                                        initialValue={0}
                                        rules={[
                                            {
                                                required: true,
                                            },
                                        ]}
                                        label={i18n.t('name', '优先级')}
                                    >
                                        <RadioGroup options={priorityOptions} />
                                    </FormItem>
                                </Col>
                            </Row>
                        </div>
                    </Form>
                </div>
            </div>
        </Modal>
    );
};

export default VideoEdit;
