@import '~@streamax/poppy-themes/starry/index.less';
.picture-preview-card {
    position: relative;
    display: inline-block;
    width: 100px;
    height: 55px !important;
    margin-top: 20px;
    overflow: hidden;
    text-align: center;
    border-radius: 4px;
    user-select: none;
    z-index: 2;
    .time {
        position: absolute;
        bottom: 4px;
        left: 4px;
        color: rgba(255, 255, 255, 0.45);
        font-size: 12px;
        z-index: 10;
    }
    img {
        width: 100px;
        height: 50px;
        border-radius: 4px;
        object-fit: cover;
    }
    .flag-wra {
        position: absolute;
        bottom: 0;
        left: ~'calc(50% - 8px)';
        display: inline-block;
        width: 16px;
        height: 4px;
        &.flag-3 {
            background: #1890ff;
        }
        &.flag-2 {
            background: #ff4d4f;
        }
        &.flag-6 {
            background: #1890ff;
        }
    }
    .video-flag {
        position: absolute;
        right: 4px;
        bottom: 4px;
        display: none;
        color: rgba(255, 255, 255, 0.45);
        &.show {
            display: inline-block;
        }
    }
    &:hover {
        z-index: 4;
        transform: scale(2);
        cursor: pointer;
        .flag-wra {
            display: none;
        }
        .time {
            bottom: 2px;
        }
        .video-flag {
            bottom: 2px;
        }
    }
    &.cliping:hover {
        cursor: not-allowed;
    }
    &.active {
        z-index: 3;
        border: 2px solid @primary-color;
        transform: scale(1.5);
        cursor: pointer;
        .flag-wra {
            display: none;
        }
        .time {
            bottom: 0px;
        }
        .video-flag {
            bottom: 0px;
        }
        &:hover {
            .time {
                bottom: 0px;
            }
            .video-flag {
                bottom: 0px;
            }
        }
    }
    &:last-child {
        transform-origin: right center;
    }
    &:nth-of-type(1) {
        transform-origin: left center;
    }
}
.poppy-spin-spinning {
    color: hsla(0, 0%, 100%, 0.85);
}