import { PlayState } from '../../../types';
import { exitFullScreen, fullScreen } from '@/utils/commonFun';
import classNames from 'classnames';
import React, {
    forwardRef,
    ReactElement,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
    useState,
} from 'react';
import Controller, { PageInfo } from './controller/controller';
import Image from './image/image';

import { ImageData, ImgPlayerLoadedData } from '../../../types';
import './index.less';
import {useEventListener, useLockFn, useUpdateEffect} from '@streamax/hooks';
import { cloneDeep, omit, pick } from 'lodash';
import { IconReplay } from '@streamax/poppy-icons';
import { i18n, utils } from '@base-app/runtime-lib';
import { DEFAULT_IMAGE_PLAY_INTERVAL, userPLayIMageIntervalMap } from './constants';
import { queryUserSearchParam, saveUserSearchParam } from '@/service/user';
import { useHistory } from '@base-app/runtime-lib/core';

export { ImagePlayerRefProps };

type StartFC = (startImageOptions: Record<number, ImageData[]>) => void;
interface ImagePlayerRefProps {
    start: StartFC; // 启动播放器事件，初始化播放器数据
    pause: () => void; // 暂停播放⽅法
    play: () => void; // 恢复播放⽅法
    close: () => void; // 停⽌播放⽅法
    seek: (seekTime: number) => void;
}
interface ImagePlayerProps {
    currentBaseChannel: number; // 当前基准通道
    onPlayTimeChange?: (currentTime: number) => void; // 当前播放时间变化事件
    onPlayStateChange?: (playState: PlayState) => void; // 播放状态变化事件
    onPageChange?: (currentPage: number) => void; // 当前⻚码变化事件
    onLayoutNumberChange: (layoutNumber: number) => void; // 当前布局通道数量变化事件
    onLoaded: (loadedData: ImgPlayerLoadedData) => void; // 数据加载完成事件
    onClip: () => void; // 点击剪辑按钮事件
    onSelectedChannelChange: (selectedChannels: number[]) => void;
    progressBar?: ReactElement;
}
const PAGE_SIZE = 4;
const { videoAspectRatio } = utils;

const ImagePlayer: React.ForwardRefRenderFunction<ImagePlayerRefProps, ImagePlayerProps> = (
    props,
    ref,
) => {
    const {
        currentBaseChannel = 1,
        onPlayTimeChange,
        onPlayStateChange,
        onLayoutNumberChange,
        onLoaded,
        onPageChange,
        onClip,
        onSelectedChannelChange,
        progressBar
    } = props;
    const [pageInfo, setPageInfo] = useState({
        total: 1,
        currentPage: 1,
    });
    const [selectedPlayer, setSelectedPlayer] = useState<number>(); //选中的播放通道
    const [currentPlayChannel, setCurrentPlayChannel] = useState<number[]>([]); // 当前播放的通道list ---- 控制条
    const [imgList, setImgList] = useState<Record<number, ImageData[]>>({});
    const imgsWraperRef = useRef<HTMLDivElement>(null);
    const [fullscreen, setFullscreen] = useState(false);
    const [currentTime, setCurrentTime] = useState(0); //当前播放时间
    const [playerState, setPlayerState] = useState<PlayState>('idle');
    const [intervalInfo, setIntervalInfo] = useState<{
        interval: number;
        id?: number;
    }>({
        id: undefined,
        interval: DEFAULT_IMAGE_PLAY_INTERVAL,
    });
    const[ isInit, setIsInit ] = useState(false);
    const [videoRatio, setVideoRatio] = useState();
    const timer = useRef<any>();

    const history = useHistory();

    useEffect(() => {
        getUserIntervalInfo();
    },[]);

    useEffect(() => {
        onSelectedChannelChange(currentPlayChannel);
    }, [currentPlayChannel]);

    useEffect(() => {
        onPlayStateChange?.(playerState);
    }, [playerState]);

    useEventListener(
        'fullscreenchange',
        () => {
            const isFullscreen = document.fullscreenElement !== null;
            if (!isFullscreen){
                setFullscreen(isFullscreen); // 设置标识为退出全屏
            }
        },
        { target: imgsWraperRef },
    );

    // 监听窗口大小变化，确保布局正确更新
    useEventListener(
        'resize',
        () => {
            // 窗口大小变化时，强制重新渲染以确保布局正确
            if (imgsWraperRef.current) {
                // 触发重新计算布局
                const event = new Event('resize');
                window.dispatchEvent(event);
            }
        },
        { target: window },
    );

    const handleControlFullScreen = () => {
        if (document.fullscreen && fullscreen) {
            setFullscreen(false);
            exitFullScreen();
        } else {
            setFullscreen(true);
            fullScreen(imgsWraperRef.current as Element);
        }
    };

    const _start: StartFC = async (startImageOptions) => {
        _close();
        const num = Object.keys(startImageOptions).length;
        setCurrentTime(0);
        console.log('num', num, startImageOptions);
        const aspectRatio = await videoAspectRatio.getVideoAspectRatio();
        setVideoRatio(aspectRatio);
        if (num) {
            setPlayerState('loading');
            setImgList(cloneDeep(startImageOptions));
            setCurrentPlayChannel(Object.keys(startImageOptions).map((item) => Number(item)));
            setPageInfo({
                total: num > PAGE_SIZE ? Math.ceil(num / PAGE_SIZE) : 1,
                currentPage: 1,
            });
            setPlayerState('playing');
            setIsInit(true);
        } else {
            setImgList({});
            setIsInit(true);
        }
    };
    useUpdateEffect(() => {
        /**
         * start  存入imgList ，监听后初始化数据
         * 首图加载完成 ，数据准备完成
         *
         */
        if (Object.keys(imgList).length && currentBaseChannel) {
            setSelectedPlayer(currentBaseChannel); // 设置选中的player
            if (playerState === 'playing') {
                clearTimeout(timer.current);
                timer.current = setTimeout(() => {
                    getNextImg();
                }, intervalInfo.interval * 1000);
            }
            if (playerState === 'loading') {
                // start 过程
                getNextImg();
                onLoaded?.({
                    selectedPlayChannels: Object.keys(imgList).map((item) => Number(item)),
                });
                setPlayerState('pause');
            }
        } else if (!Object.keys(imgList).length) {
            onLoaded?.({
                selectedPlayChannels: [],
            });
        }
    }, [imgList, currentBaseChannel, intervalInfo.interval]);

    useUpdateEffect(() => {
        // 播放时
        onPlayTimeChange?.(currentTime);
        if (playerState === 'playing') {
            clearTimeout(timer.current);
            timer.current = setTimeout(() => {
                getNextImg();
            }, intervalInfo.interval * 1000);
        }
        return () => {
            clearTimeout(timer.current);
        };
    }, [currentTime, intervalInfo.interval, playerState]);

    const getNextImg = () => {
        const hasData = Object.keys(imgList).length;
        if (hasData) {
            const nextImg = imgList[currentBaseChannel]?.find(
                (item) => item.startTime > currentTime,
            );
            if (nextImg) {
                setCurrentTime(nextImg?.startTime as number);
                clearTimeout(timer.current);
            } else {
                _seek(imgList[currentBaseChannel]?.[0]?.startTime);
                setPlayerState('stop');
                clearTimeout(timer.current);
                timer.current = undefined;
            }
        }
    };

    const _play = () => {
        if (!timer.current) {
            setPlayerState('playing');
            timer.current = setTimeout(() => {
                getNextImg();
            }, intervalInfo.interval * 1000);
        }
    };

    const _pause = () => {
        if (timer.current) {
            clearTimeout(timer.current);
            timer.current = undefined;
        }
        setPlayerState('pause');
    };

    const _close = () => {
        if (timer.current) {
            clearTimeout(timer.current);
            timer.current = undefined;
        }
        setPlayerState('stop');
    };
    const _seek = (seekTime: number) => {
        clearTimeout(timer.current);
        setCurrentTime(seekTime);
    };

    useImperativeHandle(ref, () => {
        return {
            start: _start,
            pause: _pause,
            play: _play,
            reload: _play,
            close: _close,
            seek: _seek,
        };
    });

    const imagePlayList = useMemo(() => {
        /**
         * 获取布局相应的通道个数 不足的话补位
         *
         */
        const values = Object.values(pick(imgList, ...currentPlayChannel));
        let temp_value: any = [];
        if (values.length) {
            if (pageInfo.total > 1) {
                // 多页时 每页都需展示4个窗口
                temp_value = values.slice(
                    (pageInfo.currentPage - 1) * PAGE_SIZE,
                    (pageInfo.currentPage - 1) * PAGE_SIZE + PAGE_SIZE,
                );
                const num = PAGE_SIZE - temp_value.length;
                for (let index = 0; index < num; index++) {
                    temp_value.push([]);
                }
            } else {
                temp_value = values;
                // 当前播放窗口为3 补冲一个窗口，其余layout适配
                if (temp_value.length === 3) temp_value.push([]);
            }
        }
        return temp_value;
    }, [imgList, currentPlayChannel, pageInfo]);

    const pageChange = (pageInfo: PageInfo) => {
        onPageChange?.(pageInfo.currentPage);
        setPageInfo(pageInfo);
    };

    const getUserIntervalInfo = async () => {
        try {
            // [88281] 根据路径url判断参数，需注意被行业层引用后，路径的变化
            const targetPath = Object.keys(userPLayIMageIntervalMap).find(i => history.location.pathname.indexOf(i) !== -1);
            const data = await queryUserSearchParam({
                paramType: userPLayIMageIntervalMap[targetPath || 'playback-mix'],
            });
            const { id, paramValue } = data?.[0] || {};
            const { interval } = JSON.parse(paramValue || '{}');
            setIntervalInfo({
                id,
                interval: interval || DEFAULT_IMAGE_PLAY_INTERVAL,
            });
        } catch {
            // 查询失败不用处理，已经存在默认值和上次的值
        }
    };

    const handleIntervalChange = useLockFn(async (interval: number) => {
        setIntervalInfo({
            id: intervalInfo.id,
            interval: interval || DEFAULT_IMAGE_PLAY_INTERVAL,
        });
        const targetPath = Object.keys(userPLayIMageIntervalMap).find(i => history.location.pathname.indexOf(i) !== -1);
        const params = {
            paramType: userPLayIMageIntervalMap[targetPath || 'playback-mix'],
            paramValue: JSON.stringify({
                interval,
            }),
        };
        await  saveUserSearchParam(params);
    });
    return (
        <div ref={imgsWraperRef} className="image-player-wraper">
            {!imagePlayList.length && isInit && (
                <div className="no-data-play-wraper">{i18n.t('name', '暂无数据')}</div>
            )}
            <div
                className={classNames('layout-wraper', {
                    'layout-one': imagePlayList?.length == 1,
                    'layout-two': imagePlayList?.length == 2,
                    'layout-four': imagePlayList?.length > 2,
                    'layout-wraper-full-screen':fullscreen,
                })}
            >
                {playerState === 'stop' && imagePlayList.length && (
                    <div className="re-play-wraper">
                        <a onClick={() => _play()}>
                            <IconReplay />
                        </a>
                    </div>
                )}
                {imagePlayList.map((item: any, index: number) => {
                    return (
                        <div
                            key={item[0]?.channelNo}
                            className={classNames('item', {
                                'selected-img': item[0]?.channelNo === selectedPlayer,
                            })}
                            onClick={() => item.length && setSelectedPlayer(item[0]?.channelNo)}
                        >
                            <Image 
                                currentTime={currentTime} 
                                imgSrcList={item}  
                                progressBar={progressBar}
                                aspectRatio={videoRatio}
                            />
                        </div>
                    );
                })}
            </div>
            <Controller
                pageInfo={pageInfo}
                setPageInfo={pageChange}
                onClip={onClip}
                fullScreen={handleControlFullScreen}
                isFullscreen={fullscreen}
                currentTime={currentTime}
                currentPlayChannel={currentPlayChannel}
                channelChange={setCurrentPlayChannel}
                imgList={imgList}
                interval={intervalInfo.interval}
                onIntervalChange={handleIntervalChange}
            />
            {fullscreen ? progressBar : null}
        </div>
    );
};
const ImagePlayWrapper = forwardRef(ImagePlayer) as (
    props: React.PropsWithChildren<ImagePlayerProps> & {
        ref?: React.Ref<ImagePlayerRefProps>;
    },
) => React.ReactElement;

export default ImagePlayWrapper;
