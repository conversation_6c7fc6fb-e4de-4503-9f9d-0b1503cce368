import { describe, it, expect } from '@jest/globals';
import { computeScrollOffset, ComputeScrollOffsetParams } from './computeScrollOffset';

describe('computeScrollOffset', () => {
    // 基础测试数据
    const baseParams: ComputeScrollOffsetParams = {
        containerWidth: 1200,
        prevContainerWidth: null,
        scaleLevel: 0,
        prevScrollOffset: [0, 1200],
        currentPlayTime: 43200, // 12:00:00
        originStartTime: 0,
        originEndTime: 86400, // 24小时
        scaleLevelTotal: 100,
    };

    describe('场景1：基础功能测试', () => {
        it('应该在缩放级别为0时显示全部内容', () => {
            const result = computeScrollOffset(baseParams);
            
            expect(result.scrollOffset).toEqual([0, 1200]);
            expect(result.updatedContainerWidth).toBe(1200);
        });

        it('应该在缩放50%时正确计算可视区域', () => {
            const params = {
                ...baseParams,
                scaleLevel: 50,
                prevContainerWidth: 1200,
            };
            
            const result = computeScrollOffset(params);
            
            // 可视宽度应该是 1200 * 50% = 600px
            const viewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            expect(viewWidth).toBe(600);
            
            // 播放点应该在可视区域中心（12:00:00 = 600px）
            const playPosition = 600;
            const relativePosition = (playPosition - result.scrollOffset[0]) / viewWidth;
            expect(relativePosition).toBeCloseTo(0.5, 2);
        });

        it('应该在缩放75%时正确计算更小的可视区域', () => {
            const params = {
                ...baseParams,
                scaleLevel: 75,
                prevContainerWidth: 1200,
                prevScrollOffset: [450, 750] as [number, number], // 之前的可视区域
            };
            
            const result = computeScrollOffset(params);
            
            // 可视宽度应该是 1200 * 25% = 300px
            const viewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            expect(viewWidth).toBe(300);
        });
    });

    describe('场景2：容器宽度变化测试', () => {
        it('应该在容器从1200px变为1800px时保持相对位置', () => {
            // 初始状态：1200px容器，缩放50%
            const initialParams = {
                ...baseParams,
                containerWidth: 1200,
                prevContainerWidth: 1200,
                scaleLevel: 50,
                prevScrollOffset: [300, 900] as [number, number], // 播放点在80%位置
            };
            
            // 计算初始相对位置
            const initialPlayPos = 600; // 12:00:00在1200px容器中
            const initialRelPos = (initialPlayPos - 300) / 600; // 0.5
            
            // 容器变大到1800px
            const newParams = {
                ...initialParams,
                containerWidth: 1800,
                prevContainerWidth: 1200,
            };
            
            const result = computeScrollOffset(newParams);
            
            // 新的播放点位置
            const newPlayPos = 900; // 12:00:00在1800px容器中
            const newViewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            const newRelPos = (newPlayPos - result.scrollOffset[0]) / newViewWidth;
            
            // 相对位置应该保持不变
            expect(newRelPos).toBeCloseTo(initialRelPos, 2);
            expect(newViewWidth).toBe(900); // 1800 * 50% = 900px
        });

        it('应该在容器从1800px变为1200px时保持相对位置', () => {
            // 初始状态：1800px容器，缩放50%
            const initialParams = {
                ...baseParams,
                containerWidth: 1800,
                prevContainerWidth: 1800,
                scaleLevel: 50,
                currentPlayTime: 64800, // 18:00:00
                prevScrollOffset: [900, 1800] as [number, number],
            };
            
            // 18:00:00在1800px中的位置 = 1350px
            const initialPlayPos = 1350;
            const initialRelPos = (initialPlayPos - 900) / 900; // 0.5
            
            // 容器变小到1200px
            const newParams = {
                ...initialParams,
                containerWidth: 1200,
                prevContainerWidth: 1800,
            };
            
            const result = computeScrollOffset(newParams);
            
            // 新的播放点位置
            const newPlayPos = 900; // 18:00:00在1200px容器中
            const newViewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            const newRelPos = (newPlayPos - result.scrollOffset[0]) / newViewWidth;
            
            // 相对位置应该保持不变
            expect(newRelPos).toBeCloseTo(initialRelPos, 2);
            expect(newViewWidth).toBe(600); // 1200 * 50% = 600px
        });
    });

    describe('场景3：连续操作测试', () => {
        it('应该支持连续的容器变化和缩放操作', () => {
            let params = { ...baseParams };
            let result;
            
            // 步骤1：初始状态 1200px，缩放0%
            result = computeScrollOffset(params);
            expect(result.scrollOffset).toEqual([0, 1200]);
            
            // 步骤2：缩放到50%
            params = {
                ...params,
                scaleLevel: 50,
                prevContainerWidth: result.updatedContainerWidth,
                prevScrollOffset: result.scrollOffset,
            };
            result = computeScrollOffset(params);
            const step2ViewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            expect(step2ViewWidth).toBe(600);
            
            // 步骤3：容器变大到1800px
            params = {
                ...params,
                containerWidth: 1800,
                prevContainerWidth: result.updatedContainerWidth,
                prevScrollOffset: result.scrollOffset,
            };
            result = computeScrollOffset(params);
            const step3ViewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            expect(step3ViewWidth).toBe(900); // 1800 * 50% = 900
            
            // 步骤4：在1800px下缩放到75%
            params = {
                ...params,
                scaleLevel: 75,
                prevContainerWidth: result.updatedContainerWidth,
                prevScrollOffset: result.scrollOffset,
            };
            result = computeScrollOffset(params);
            const step4ViewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            expect(step4ViewWidth).toBe(450); // 1800 * 25% = 450
            
            // 步骤5：容器变回1200px
            params = {
                ...params,
                containerWidth: 1200,
                prevContainerWidth: result.updatedContainerWidth,
                prevScrollOffset: result.scrollOffset,
            };
            result = computeScrollOffset(params);
            const step5ViewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            expect(step5ViewWidth).toBe(300); // 1200 * 25% = 300
        });
    });

    describe('场景4：边界情况测试', () => {
        it('应该处理播放点在最左边的情况', () => {
            const params = {
                ...baseParams,
                currentPlayTime: 0, // 00:00:00
                scaleLevel: 50,
                prevContainerWidth: 1200,
            };
            
            const result = computeScrollOffset(params);
            
            // 播放点在0位置，可视区域应该从0开始
            expect(result.scrollOffset[0]).toBe(0);
            expect(result.scrollOffset[1]).toBe(600);
        });

        it('应该处理播放点在最右边的情况', () => {
            const params = {
                ...baseParams,
                currentPlayTime: 86400, // 24:00:00
                scaleLevel: 50,
                prevContainerWidth: 1200,
            };
            
            const result = computeScrollOffset(params);
            
            // 播放点在1200位置，可视区域应该到1200结束
            expect(result.scrollOffset[1]).toBe(1200);
            expect(result.scrollOffset[0]).toBe(600);
        });

        it('应该处理播放点不在可视区域内的情况', () => {
            const params = {
                ...baseParams,
                currentPlayTime: 21600, // 06:00:00 = 300px
                scaleLevel: 50,
                prevContainerWidth: 1200,
                prevScrollOffset: [600, 1200] as [number, number], // 播放点不在此区域
            };
            
            const result = computeScrollOffset(params);
            
            // 播放点应该居中显示
            const playPos = 300;
            const viewWidth = 600;
            const expectedStart = playPos - viewWidth * 0.5; // 300 - 300 = 0
            
            expect(result.scrollOffset[0]).toBe(expectedStart);
            expect(result.scrollOffset[1]).toBe(expectedStart + viewWidth);
        });
    });

    describe('场景5：特殊情况测试', () => {
        it('应该处理首次计算（prevContainerWidth为null）', () => {
            const params = {
                ...baseParams,
                prevContainerWidth: null,
                scaleLevel: 50,
            };
            
            const result = computeScrollOffset(params);
            
            // 应该正常计算
            const viewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            expect(viewWidth).toBe(600);
        });

        it('应该处理缩放级别为100%的情况', () => {
            const params = {
                ...baseParams,
                scaleLevel: 100,
                prevContainerWidth: 1200,
            };

            const result = computeScrollOffset(params);

            // 缩放100%时，应该有最小可视区域而不是显示全部
            const viewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            const expectedMinWidth = 1200 * 0.005; // 6px
            expect(viewWidth).toBe(expectedMinWidth);
            expect(viewWidth).toBeLessThan(1200); // 不应该是全屏
        });

        it('应该处理极小的可视区域', () => {
            const params = {
                ...baseParams,
                scaleLevel: 99, // 只显示1%的内容
                prevContainerWidth: 1200,
            };

            const result = computeScrollOffset(params);

            const viewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            expect(viewWidth).toBe(12); // 1200 * 1% = 12px
        });

        it('应该确保从缩放99%到100%的平滑过渡', () => {
            // 测试99%缩放
            const params99 = {
                ...baseParams,
                scaleLevel: 99,
                prevContainerWidth: 1200,
            };

            const result99 = computeScrollOffset(params99);
            const viewWidth99 = result99.scrollOffset[1] - result99.scrollOffset[0];

            // 测试100%缩放
            const params100 = {
                ...baseParams,
                scaleLevel: 100,
                prevContainerWidth: 1200,
            };

            const result100 = computeScrollOffset(params100);
            const viewWidth100 = result100.scrollOffset[1] - result100.scrollOffset[0];

            // 验证时间跨度的平滑过渡
            const totalSeconds = 86400; // 24小时
            const timeSpan99 = (viewWidth99 / 1200) * totalSeconds; // 864秒 = 14.4分钟
            const timeSpan100 = (viewWidth100 / 1200) * totalSeconds; // 43.2秒

            // 99%: 12px -> 14.4分钟
            // 100%: 6px -> 7.2分钟
            expect(viewWidth99).toBe(12);
            expect(viewWidth100).toBe(6);
            expect(timeSpan99).toBeCloseTo(864, 1); // 14.4分钟
            expect(timeSpan100).toBeCloseTo(432, 1); // 7.2分钟

            // 时间跨度比例应该是合理的（2倍关系而不是巨大跳跃）
            const ratio = timeSpan99 / timeSpan100;
            expect(ratio).toBeCloseTo(2, 1);
        });
    });

    describe('场景6：实际业务场景测试', () => {
        it('应该正确处理您提到的具体场景', () => {
            // 场景：放大91%，时间在21:52:31，相对位置80%
            const params = {
                containerWidth: 1920,
                prevContainerWidth: 1920,
                scaleLevel: 91,
                prevScrollOffset: [1612, 1784.8] as [number, number],
                currentPlayTime: 78751, // 21:52:31
                originStartTime: 0,
                originEndTime: 86400,
                scaleLevelTotal: 100,
            };
            
            // 初始状态验证
            const playPos1920 = 1920 * (78751 / 86400); // 1750.24px
            const initialRelPos = (playPos1920 - 1612) / 172.8; // 0.8
            expect(initialRelPos).toBeCloseTo(0.8, 2);
            
            // 容器变小到1200px
            const newParams = {
                ...params,
                containerWidth: 1200,
                prevContainerWidth: 1920,
            };
            
            const result = computeScrollOffset(newParams);
            
            // 验证相对位置保持不变
            const playPos1200 = 1200 * (78751 / 86400); // 1093.9px
            const newViewWidth = result.scrollOffset[1] - result.scrollOffset[0];
            const newRelPos = (playPos1200 - result.scrollOffset[0]) / newViewWidth;
            
            expect(newRelPos).toBeCloseTo(0.8, 2);
        });
    });

    describe('场景7：极端缩放边界情况测试', () => {
        it('应该处理极端缩放下播放点在末尾的情况', () => {
            const params = {
                ...baseParams,
                currentPlayTime: 86400, // 24:00:00 (最末尾)
                scaleLevel: 99, // 极高缩放
                prevContainerWidth: 1200,
                prevScrollOffset: [1188, 1200] as [number, number], // 之前在末尾的小区域
            };

            const result = computeScrollOffset(params);

            // 播放点位置应该是1200px (末尾)
            const playPosition = 1200;
            const viewWidth = result.scrollOffset[1] - result.scrollOffset[0];

            // 验证播放点在可视区域内
            expect(playPosition).toBeGreaterThanOrEqual(result.scrollOffset[0]);
            expect(playPosition).toBeLessThanOrEqual(result.scrollOffset[1]);

            // 验证可视区域在容器范围内
            expect(result.scrollOffset[0]).toBeGreaterThanOrEqual(0);
            expect(result.scrollOffset[1]).toBeLessThanOrEqual(1200);

            // 验证可视宽度正确
            expect(viewWidth).toBe(12); // 1200 * 1% = 12px
        });

        it('应该处理缩放100%时播放点在末尾的极端情况', () => {
            const params = {
                ...baseParams,
                currentPlayTime: 86400, // 24:00:00 (最末尾)
                scaleLevel: 100, // 最高缩放
                prevContainerWidth: 1200,
                prevScrollOffset: [1194, 1200] as [number, number], // 之前在末尾的极小区域
            };

            const result = computeScrollOffset(params);

            // 播放点位置应该是1200px (末尾)
            const playPosition = 1200;
            const viewWidth = result.scrollOffset[1] - result.scrollOffset[0];

            // 验证播放点在可视区域内
            expect(playPosition).toBeGreaterThanOrEqual(result.scrollOffset[0]);
            expect(playPosition).toBeLessThanOrEqual(result.scrollOffset[1]);

            // 验证可视区域在容器范围内
            expect(result.scrollOffset[0]).toBeGreaterThanOrEqual(0);
            expect(result.scrollOffset[1]).toBeLessThanOrEqual(1200);

            // 验证最小宽度
            expect(viewWidth).toBe(6); // 1200 * 0.5% = 6px (最小宽度)

            // 验证可视区域应该在容器末尾
            expect(result.scrollOffset[1]).toBe(1200);
            expect(result.scrollOffset[0]).toBe(1194); // 1200 - 6 = 1194
        });

        it('应该处理连续缩放98→99→100时播放点在末尾的情况', () => {
            let params = {
                ...baseParams,
                currentPlayTime: 86400, // 24:00:00 (最末尾)
                scaleLevel: 98,
                prevContainerWidth: 1200,
                prevScrollOffset: [1176, 1200] as [number, number], // 98%时的区域
            };

            // 步骤1: 缩放到99%
            params = {
                ...params,
                scaleLevel: 99,
                prevScrollOffset: [1176, 1200] as [number, number],
            };
            let result = computeScrollOffset(params);

            // 验证99%时播放点在可视区域内
            expect(1200).toBeGreaterThanOrEqual(result.scrollOffset[0]);
            expect(1200).toBeLessThanOrEqual(result.scrollOffset[1]);

            // 步骤2: 缩放到100%
            params = {
                ...params,
                scaleLevel: 100,
                prevScrollOffset: result.scrollOffset,
            };
            result = computeScrollOffset(params);

            // 验证100%时播放点仍在可视区域内
            expect(1200).toBeGreaterThanOrEqual(result.scrollOffset[0]);
            expect(1200).toBeLessThanOrEqual(result.scrollOffset[1]);

            // 验证最终可视区域在容器末尾
            expect(result.scrollOffset[1]).toBe(1200);
            expect(result.scrollOffset[0]).toBe(1194); // 1200 - 6 = 1194
        });
    });
});
