import { UpOutlined } from '@ant-design/icons';
import {
    useUpdateEffect,
    usePrevious,
    useDebounceEffect,
    useThrottleFn,
    useThrottleEffect,
    useRafState,
    useAsyncEffect,
    useGetState,
    useUpdate,
    useDebounceFn,
} from '@streamax/hooks';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { RspHorizontal, } from '@streamax/responsive-layout';
import { Badge, Space, Tooltip } from '@streamax/poppy';
import {
    IconPauseFill,
    IconChannelLine,
    IconSpeed01Line,
    IconPlay01Fill,
    IconCarStateOpenDoorLine,
    IconDragLine,
    IconCustomize,
    IconOilcircuitBreakLine,
} from '@streamax/poppy-icons';
import {
    utils,
    getAppGlobalData,
    i18n,
    g_emmiter,
    // @ts-ignore
    StarryStorage,
} from '@base-app/runtime-lib';
import classNames from 'classnames';
import React, {
    useState,
    useRef,
    useContext,
    useMemo,
    useEffect,
    forwardRef,
    useImperativeHandle,
} from 'react';
import Channel<PERSON>hart, {
    CHANNEL_BAR_HEIGHT,
    CHANNEL_BAR_SPACE,
} from './ChannelChart';
import type { ChannelChartProps } from './ChannelChart';
import MarkLine from './MarkLine';
import Panel from './Panel';
import ProgressBar from './ProgressBar';
import type { ProcessBarProps } from './ProgressBar';
import ScaleControl from './ScaleControl';
import ScrollBar from './ScrollBar';
import SpeedChart from './SpeedChart';
import type { SpeedData } from './SpeedChart';
import TimeLine from './TimeLine';
import {
    transformTimestampForRound,
    mergeChannelTimeList,
    useGetSize,
    getClipTimeRangeByCurrentPlayTime,
    transformUploadedTimeList,
    timestampToOffsetImpl,
    offsetToTimestampImpl,
} from './utils';
import {
    PAGE_CLS_PREFIX_CTRL,
    DRAW_COLOR,
    CTRL_REFERENCE_GRID,
    CTRL_PROCESS_LINE_OFFSET,
    PLAY_STATE,
    PLAY_MODE,
    CTRL_PROCESS_DOT_OFFSET,
    CTRL_TIMELINE_INTERVAL,
    CTRL_SCALE_LEVEL_TOTAL,
    CTRL_SCALE_LEVEL_STEP,
    PLAYER_RELOAD_EVENT_TYPE,
    getColorTipsNodes,
    STREAM_TYPE,
    CHART_TYPES,
    chartLineGroup,
    LineType,
    LineTypeSimple,
    chartLineTypes,
    MIX_AND_DEVICE_PLAYBACK,
} from '../../constant';
import { Context, useFrequentStore } from '../../store';
import type { PlayState, StreamType } from '../../types';
import type { VideoEditReuse, VideoTipItems } from '@/types/pageReuse/playbackDevice';
import { getCustomItems, getCustomJsx, runCustomFun } from '@/utils/pageReuse';
import { getUnitName, convertSpeed, getValueByUnit } from '@/utils/unit';
import './index.less';
import useElementMouseX from '@/hooks/useElementX';
import { Instances } from '@/types/pageReuse/pageReuseBase';
import StreamTypeSwitchButton from './StreamTypeSwitchButton';
import {
    CHANNEL_DRAW_COLOR,
    PlayBackDisplayModeEnum,
    StreamTypeEnum,
} from '../../../constant';
import usePageOrigin from '../../hooks/usePageOrigin';
import { PAGE_ORIGIN } from '../../../playback-device/constant';
import { control } from 'leaflet';
import BaseLineChart from './BaseLineChart';
import LineControl from './LineControl';
import useBlackBoxData from '../../blackBoxModule/store/useBlackBoxData';
import type { BlackBoxData } from '../../blackBoxModule/store/useBlackBoxData';
import BlackBoxRequestManager from '../../blackBoxModule/BlackBoxRequestManager';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { computeScrollOffset } from './computeScrollOffset';
export type DevicePlayBackTipShareProps = VideoTipItems;

type ScrollOffset = [number, number?];

// 最小的图表区域高度
const MIN_CHART_HEIGHT = 100;

// 默认的时间轴最小间隔(一分钟)
const DEFAULT_MIN_TIMELINE_INTERVAL = 60;

// 剪辑中的时候，时间轴最大的间隔(一个小时)
const MAX_TIMELINE_INTERVAL_ON_CLIPING = 3600;

// 剪辑中的时候，时间轴最小的间隔(一分钟)
const MIN_TIMELINE_INTERVAL_ON_CLIPING = 60;

// 进入剪辑时，默认的时间轴间隔(20分钟)
const DEFAULT_TIMELINE_INTERVAL_ON_CLIPING = 1200;

// 进入剪辑时，当前播放进度默认展示在可视区 1/4 处
const DEFAULT_CURRENT_PROCESS_ON_CLIPING = 1 / 4;

// 统一节流时间
const DEFAULT_THROTTLE_WAIT = 100;

// PLAYBACK_PRIORITIES_SHOW_MODULE的本地存储key
const PLAYBACK_PRIORITIES_SHOW_MODULE_KEY = 'PLAYBACK_PRIORITIES_SHOW_MODULE';

const prefixCls = PAGE_CLS_PREFIX_CTRL;

const SEEK_POP_WIDTH = 82;
const PLAT_LINES = ['speed', 'channel'];

type ControllerProps = DevicePlayBackTipShareProps &
    Instances & {
        className?: string;
        style?: React.CSSProperties;
        onSeek?: (currentPlayTime: number) => void;
        onPlayStateChange?: (state: Omit<PlayState, 'play' | 'pause'>) => void;
        onChartCollapsed?: (collapse: boolean) => void;
        onStreamTypeChange?: (streamType: StreamType) => void;
        playBackBlackBoxPlayMode?: 0 | 1;
        blackBoxRequestManager: any;
        hideChart?: boolean; // 是否隐藏图表，给全屏展示进度条用
    } & VideoEditReuse;

// offset(偏移位置)都是从容器的左边0像素处开始计算
const Controller: React.FC<ControllerProps> = (props) => {
    const storage = StarryStorage();
    {
        /** 定制 */
    }
    const {
        getVideoTip,
        getInstances,
        onTimeLineChange,
        onScrollContainerWidthChange,
        onChartTabChange,
        getCustomChartNodes,
        getCustomChartMainNodes,
        getCustomChartMarkLineNodes,
        getVideoEditBlock,
        hideChart
    } = props;

    const {
        className,
        style,
        playBackBlackBoxPlayMode,
        blackBoxRequestManager,
        onSeek,
        onPlayStateChange,
        onChartCollapsed,
        onStreamTypeChange,
    } = props;
    const [tipNodes, setTipNodes] = useState<any[]>([]);
    const forceUpdate = useUpdate();
    const origin = usePageOrigin();

    useAsyncEffect(async () => {
        const tipNodesCurrent = await getColorTipsNodes(origin);
        setTipNodes(tipNodesCurrent);
    }, []);

    const {
        queryParams,
        showAlarmListData,
        currentShowChannelListData,
        speedData,
        cliping,
        setClipVideoTimeRange,
        uploadedTimeList,
        clipVideoTimeRange,
        currentPlayState,
        PLAYBACK_PRIORITIES_SHOW_MODULE,
        playMode,
        dispatch,
        showTimeRange,
        clipStreamType,
        currentStreamType,
        playBackDisplayMode,
    } = useContext(Context);
    const blackBoxData = useBlackBoxData((state) => state.blackBoxData);
    const lineCheckBoxGroupMap = useBlackBoxData(
        (state) => state.lineCheckBoxGroupMap,
    );

    const fieldsType = useBlackBoxData((state) => state.fieldsType);
    const updateGps = useFrequentStore((state) => state.updateGps);
    // @ts-ignore
    const simpleFieldsType = fieldsType.map((item) => LineTypeSimple[item]);
    const getCurrentPlayTime = useFrequentStore(
        (state) => state.getCurrentPlayTime,
    );
    const outerCurrentPlayTime = getCurrentPlayTime();
    const { startTime: originStartTime, endTime: originEndTime } =
        queryParams || {
            startTime: 0,
            endTime: 0,
        };

    const isPlayModeImage = playMode === PLAY_MODE['image'];

    // 播放按钮禁用状态
    const disableChangePlayState =
        PLAY_STATE['play'] !== currentPlayState &&
        PLAY_STATE['pause'] !== currentPlayState &&
        PLAY_STATE['stop'] !== currentPlayState;

    //主子码流切换禁用
    const streamTypeSwitchButtonDisabled =
        PLAY_STATE['stop'] === currentPlayState ||
        PLAY_STATE['idle'] === currentPlayState;

    // 通道数据，图片播放模式下，没有通道数据
    const channelListData = isPlayModeImage ? [] : currentShowChannelListData;

    // 图表收起状态
    const [chartCollapsed, setChartCollapsed] = useState(true);

    // 当前展示的图表类型
    const [activeChart, setActiveChart, getActiveChart] = useGetState(
        CHART_TYPES['speed'],
    );
    const renderTypes = chartLineGroup[activeChart];
    const checkBoxOptions = renderTypes?.map((item) => {
        return chartLineTypes[item];
    });
    // 当前缩放等级
    const [scaleLevel, setScaleLevel] = useState(0);

    // 基准容器ref
    const referenceContainerRef = useRef<HTMLDivElement>(null);

    // 滚动条容器ref
    const scrollContainerRef = useRef<HTMLDivElement>(null);

    // 基准容器宽度
    const {
        width: referenceContainerWidth,
        getWidth: getReferenceContainerWidth,
    } = useGetSize(referenceContainerRef);

    // 滚动条容器宽度
    const { width: scrollContainerWidth, getWidth: getScrollContainerWidth } =
        useGetSize(scrollContainerRef);

    // 当前滚动条的偏移位置(起始值)
    const [currentScrollOffset, setCurrentScrollOffset] =
        useRafState<ScrollOffset>([0, undefined]);
    
    // 记录上一次计算时的容器宽度
    const [lastComputedContainerWidth, setLastComputedContainerWidth] = useState<number | null>(null);

    // 当前滚动条其实位置
    const [currentScrollOffsetStart, currentScrollOffsetEnd = 0] =
        currentScrollOffset;

    // 当前滚动条宽度
    const currentScrollWidth =
        currentScrollOffsetEnd - currentScrollOffsetStart;

    //切换码流按钮显示
    const [showStreamTypeSwitchButton, setShowStreamTypeSwitchButton] =
        useState(false);

    // 原始时间轴数据 / gps数据 / 最大的有效速度时间点(非补充的速度数据)
    const {
        list: originTimestampList,
        gpsData,
        effectiveSpeedTime,
    } = useMemo<{
        gpsData: Record<string, any>;
        list: number[];
        effectiveSpeedTime: number;
    }>(() => {
        // 向下取整除10的数
        const startTime = transformTimestampForRound(originStartTime);
        // 向上取整除10的数
        let endTime = transformTimestampForRound(originEndTime, 'ceil');

        const list: number[] = [];

        // gps数据
        const gpsData: Record<string, number> = {};

        if (!startTime || !endTime)
            return { gpsData, list, effectiveSpeedTime: 0 };

        // 如果结束时间格式化出来是00:00:00，减去1秒，为23:59:59
        if (
            utils.formator.zeroTimeStampToFormatTime(
                endTime,
                Number(getAppGlobalData('APP_USER_CONFIG')?.timeZone) || 0,
                'HH:mm:ss',
            ) === '00:00:00'
        ) {
            endTime = endTime - 1;
        }

        for (const s of speedData) {
            // 找最近的整除时间（gps时间点和时间轴的不一定对应）
            const gpsTime = transformTimestampForRound(Number(s.gpsTime));
            const speed = convertSpeed(s.speed / 10);
            gpsData[gpsTime] = speed;
        }
        const speedMaxTime =
            Math.max(...speedData.map((p) => p.gpsTime)) || endTime;
        // 按10秒的间隔划分
        for (
            let target = startTime;
            target < endTime;
            target += CTRL_TIMELINE_INTERVAL
        ) {
            list.push(target);
            gpsData[target] = findApproximateSpeed(target);
        }

        // endtime
        list.push(endTime);
        gpsData[endTime] = findApproximateSpeed(endTime + 1);

        // 找速度近似值（往前找两个时间点）
        function findApproximateSpeed(time: number) {
            let speed = gpsData[time];

            // 本身就有值，直接返回
            if (speed !== undefined) {
                return speed;
            }

            let tmp = time;

            // 往前取两个点，找到第一个有值的
            for (let i = 0; i < 2; i++) {
                tmp = tmp - CTRL_TIMELINE_INTERVAL;
                if (gpsData[tmp] || gpsData[tmp] === 0) {
                    speed = gpsData[tmp];
                    break;
                }
            }

            return speed || 0;
        }
        updateGps(gpsData);
        return {
            gpsData,
            list,
            effectiveSpeedTime: speedMaxTime,
        };
    }, [originStartTime, originEndTime, speedData]);

    // 通过滚动条起始位置，计算数据（有点小复杂）
    const {
        // 计算后的开始时间
        startTime: computedStartTime,
        // 计算后的结束事件
        endTime: computedEndTime,
        // 计算后的时间轴数据
        timestampList: computedTimestampList,
        // 计算后的速度数据
        speedList: computedSpeedList,
    } = useMemo<{
        startTime: number;
        endTime: number;
        speedList: SpeedData[];
        timestampList: number[];
    }>(() => {
        const width = getScrollContainerWidth();

        if (!width) {
            return {
                startTime: 0,
                endTime: 0,
                speedList: [],
                timestampList: originTimestampList,
            };
        }

        const len = originTimestampList.length;

        // 当前滚动条起止位置
        const [start, end = width] = currentScrollOffset;

        // 通过滚动条位置计算新的数据开始结束索引
        // 位置百分比 * 数据总数，向上取整，再减1  得到原始时间轴数据的索引
        let startIdx = Math.ceil(len * (start / width)) - 1;
        let endIdx = Math.ceil(len * (end / width)) - 1;

        startIdx < 0 && (startIdx = 0);
        endIdx < 0 && (endIdx = 0);

        const list: number[] = [];
        const speedList = [];

        // 生成数据，这里看后面怎么做优化
        for (let i = startIdx; i <= endIdx; i++) {
            const time = originTimestampList[i];
            list.push(time);
            if (time <= effectiveSpeedTime) {
                if (time <= effectiveSpeedTime) {
                    speedList.push({ time, speed: getGpsSpeed(time) });
                } else {
                    speedList.push({ time, speed: null });
                }
            } else {
                speedList.push({ time, speed: null });
            }
        }

        // 新的起止时间
        const startTime = list[0] || 0;
        const endTime = list[list.length - 1] || 0;
        return {
            startTime,
            endTime,
            timestampList: list,
            speedList,
        };
    }, [originTimestampList, currentScrollOffsetStart, currentScrollOffsetEnd]);
    useEffect(() => {
        if (!playBackBlackBoxPlayMode) return;
        useBlackBoxData
            .getState()
            ?.updateRenderTimeRange?.([computedStartTime, computedEndTime]);
        useBlackBoxData
            .getState()
            ?.updateComputedTimestampList?.(computedTimestampList);
        // 计算缩放后的blackBox数据
        updateScaleData(computedStartTime, computedEndTime, computedTimestampList);
    }, [computedStartTime, computedEndTime, computedTimestampList]);
    const { run: updateScaleData } = useDebounceFn(
        (computedStartTime, computedEndTime, computedTimestampList) => {
            blackBoxRequestManager?.updateBlackBoxData?.({
                startTime: computedStartTime,
                endTime: computedEndTime,
                computedTimestampList,
            });
        },
        {
            wait: 30,
        },
    );
    // 缩放等级计算
    const scaleLevels = useMemo(() => {
        const difference =
            originTimestampList[originTimestampList.length - 1] -
            originTimestampList[0];

        const getLevel = (time: number) => {
            if (time >= difference) return DEFAULT_CTRL_SCALE_LEVEL_MAX;
            const l =
                CTRL_SCALE_LEVEL_TOTAL -
                (time / difference) * CTRL_SCALE_LEVEL_TOTAL;

            // 保留两位小数，根据缩放等级的步长定(目前是0.01)
            const level = Number(
                l.toFixed(
                    CTRL_SCALE_LEVEL_STEP.toString().split('.')[1]?.length || 0,
                ),
            );

            // 确保返回值在合理范围内
            return Math.min(
                Math.max(level, 0),
                DEFAULT_CTRL_SCALE_LEVEL_MAX
            );
        };

        const levels: { min?: number; max?: number } = {
            max: getLevel(DEFAULT_MIN_TIMELINE_INTERVAL),
        };

        // 剪辑时，需求为一分钟到一个小时
        if (cliping) {
            levels.min = getLevel(MAX_TIMELINE_INTERVAL_ON_CLIPING);
            levels.max = getLevel(MIN_TIMELINE_INTERVAL_ON_CLIPING);
        }

        return levels;
    }, [originTimestampList, cliping]);

    // 获取gps速度
    function getGpsSpeed(time: number) {
        let speed = gpsData[time];

        // gps数据中没有值(gps中存储的是整除时间点)，这里的time可能是其他值
        // 按照两个时间点中间做梯度上升或下降
        if (speed === undefined) {
            const leftTime = transformTimestampForRound(time, 'floor');
            const rightTime = transformTimestampForRound(time, 'ceil');

            const leftSpeed = gpsData[leftTime];
            const rightSpeed = gpsData[rightTime];

            const diffTime = rightTime - leftTime;
            const diffSpeed = rightSpeed - leftSpeed;

            if (diffSpeed === 0) {
                speed = leftSpeed;
            } else {
                speed = (diffSpeed * (time - leftTime)) / diffTime + leftSpeed;
            }
        }

        if (!Number.isInteger(speed)) {
            speed = Number(speed.toFixed(1));
        }
        return speed;
    }

    // 提示框信息
    function getBlackBoxToolTip(offset: number) {
        // @ts-ignore
        const time = parseInt(offsetToTimestamp(offset));
        const timeStr = `${utils.formator.zeroTimeStampToFormatTime(
            time,
            Number(getAppGlobalData('APP_USER_CONFIG')?.timeZone) || 0,
            'HH:mm:ss',
        )}`;
        const strs = [<span>{timeStr}</span>];
        const lineDoms: any = [];
        if (BlackBoxRequestManager.staticeBlackBoxDataMap[time]) {
            // 判断如果没有曲线就不展示hover
            chartLineGroup[activeChart]?.forEach((item) => {
                const { value, unit } =
                    BlackBoxRequestManager.staticeBlackBoxDataMap[time][item] ||
                    {};
                const { value: transValue, unit: transUnit } = getValueByUnit(
                    value,
                    unit,
                );
                // 只展示勾选中的提示, 车辆操作不展示
                if (
                    lineCheckBoxGroupMap[activeChart].includes(item) &&
                    !chartLineGroup['control'].includes(item) &&
                    simpleFieldsType.includes(item)
                    // @ts-ignore
                ) {
                    // 都换行
                    lineDoms.push(
                        <div className='chart-tooltip-wrap-item'>
                            <span className="chart-tooltip-wrap-label">
                                <OverflowEllipsisContainer>
                                    {chartLineTypes[item]?.label}
                                </OverflowEllipsisContainer>
                            </span>
                            <span>
                                : {transValue ?? '-'}
                                {typeof transValue == 'number' ? transUnit : ''}
                            </span>
                        </div>,
                    );
                }
            });
        }
        // 大于两条线才放入wrap中
        if (lineDoms.length > 1) {
            // 左右两个div
            const leftLine: any = [];
            const rightLine: any = [];
            lineDoms.forEach((item: any, index: number) => {
                if (index % 2 === 0) {
                    leftLine.push(item);
                } else {
                    rightLine.push(item);
                }
            });
            strs.push(
                <div className="chart-tooltip-wrap">
                    <div className="chart-tooltip-wrap-left">{leftLine}</div>
                    <div className="chart-tooltip-wrap-right">{rightLine}</div>
                </div>,
            );
        } else {
            strs.push(...lineDoms);
        }
        return strs;
    }
    // 计算提示框信息
    function getToolTip(offset: number, hasSpeed = true, isProgress = false) {
        if (playBackBlackBoxPlayMode && ![PAGE_ORIGIN.server].includes(origin) && !isProgress) {
            return getBlackBoxToolTip(offset);
        }
        // @ts-ignore
        const time = parseInt(offsetToTimestamp(offset));
        const timeStr = `${utils.formator.zeroTimeStampToFormatTime(
            time,
            Number(getAppGlobalData('APP_USER_CONFIG')?.timeZone) || 0,
            'HH:mm:ss',
        )}`;
        const strs = [<span>{timeStr}</span>];
        const speedStr =
            time > effectiveSpeedTime
                ? ''
                : `${getGpsSpeed(time) || 0.0}${getUnitName('speed')}`;
        if (hasSpeed) {
            strs.push(
                <div>
                    {i18n.t('name', '车速')}: {speedStr ?? '-'}
                </div>,
            );
        }
        return strs;
    }

    // 时间点转换为相对偏移位置
    function timestampToOffset(timestamp: number, ceil?: boolean) {
        return timestampToOffsetImpl(
            timestamp,
            computedStartTime,
            computedEndTime,
            getReferenceContainerWidth(),
            ceil,
        );
    }

    // 相对偏移位置转换为时间点
    function offsetToTimestamp(offset: number) {
        return offsetToTimestampImpl(
            offset,
            computedStartTime,
            computedEndTime,
            getReferenceContainerWidth(),
        );
    }

    // 单个位置是否在可视区
    function isOffsetInViewPort(offset: number) {
        const viewPortWidth = getReferenceContainerWidth();
        return offset >= 0 && offset <= viewPortWidth;
    }

    // 转换为可视区位置（起始位置）
    function transformOffsetsToViewPort(start: number, end: number) {
        const viewPortWidth = getReferenceContainerWidth();
        // 不在可视区
        if (end < 0 || start > viewPortWidth) {
            return null;
        }

        // eslint-disable-next-line no-param-reassign
        start < 0 && (start = 0);
        // eslint-disable-next-line no-param-reassign
        end > viewPortWidth && (end = viewPortWidth);

        // if (end - start < 1) return null;

        return { start, end };
    }

    /* 图表区域hover逻辑 start */
    // 图表容器ref
    const chartContainerRef = useRef<HTMLDivElement>(null);

    // 鼠标离容器左侧的距离
    let { x: chartContainerHoverOffset } = useElementMouseX({
        ref: chartContainerRef,
    });
    if (chartContainerHoverOffset !== null) {
        // 取整数秒的位置(四舍五入，类似echarts)
        chartContainerHoverOffset = timestampToOffset(
            Math.round(offsetToTimestamp(chartContainerHoverOffset)),
        );
    }

    // 更新全局hovertime
    useDebounceEffect(() => {
        let hoverTime = null;
        if (chartContainerHoverOffset !== null) {
            hoverTime = offsetToTimestamp(chartContainerHoverOffset);
        }
        dispatch({
            type: 'hoverTime',
            payload: hoverTime,
        });
    }, [chartContainerHoverOffset]);
    /* 图表区域hover逻辑 end */

    /* 当前播放进度逻辑 start */
    const currentSeekingRef = useRef(false);

    const [currentPlayTime, setCurrentPlayTime] = useState(
        outerCurrentPlayTime || originStartTime,
    );
    // 更新context中的当前速度
    useUpdateEffect(() => {
        if (!currentSeekingRef.current) {
            setCurrentPlayTime(outerCurrentPlayTime || originStartTime);
        }
    }, [outerCurrentPlayTime]);

    // 当前播放时间对应的位置
    const currentPlayOffset = timestampToOffset(currentPlayTime);

    // 剪辑时间变化时seek到结束事件
    useUpdateEffect(() => {
        cliping && onSeek?.(clipVideoTimeRange[1]);
    }, [clipVideoTimeRange[1]]);

    // <IconStopFill />
    const currentPlayStateIcons = {
        [PLAY_STATE['play']]: <IconPauseFill />,
        [PLAY_STATE['stop']]: <IconPlay01Fill />,
        [PLAY_STATE['pause']]: <IconPlay01Fill />,
    };
    /* 当前播放进度逻辑 end */

    /* 滚动条相关逻辑 start */
    // 前一次的滚动条容器宽度
    const prevScrollContainerWidth =
        usePrevious(scrollContainerWidth) || scrollContainerWidth;
    // 计算并设置当前滚动条的位置（使用纯函数）
    const genCurrentScrollOffset = (
        level = scaleLevel,
        prevScrollOffset = currentScrollOffset,
    ): void => {
        // 当前滚动条容器宽度
        const containerWidth = getScrollContainerWidth();
        if (!containerWidth) return;

        // 调用纯函数计算新的滚动偏移
        const result = computeScrollOffset({
            containerWidth,
            prevContainerWidth: lastComputedContainerWidth,
            scaleLevel: level,
            prevScrollOffset: prevScrollOffset as [number, number],
            currentPlayTime,
            originStartTime,
            originEndTime,
            scaleLevelTotal: CTRL_SCALE_LEVEL_TOTAL,
        });

        // 更新状态
        setCurrentScrollOffset(result.scrollOffset);
        setLastComputedContainerWidth(result.updatedContainerWidth);
    };

    // 监听缩放等级和容器宽度变化
    useThrottleEffect(
        () => {
            genCurrentScrollOffset();
            onScrollContainerWidthChange?.(scrollContainerWidth);
        },
        [scrollContainerWidth],
        { wait: DEFAULT_THROTTLE_WAIT },
    );

    // 当播放时间变化时，如果容器宽度已经稳定，更新记录的容器宽度
    useEffect(() => {
        const currentWidth = getScrollContainerWidth();
        if (currentWidth && currentWidth !== lastComputedContainerWidth) {
            // 延迟一下确保容器宽度已经稳定
            const timer = setTimeout(() => {
                const stableWidth = getScrollContainerWidth();
                if (stableWidth === currentWidth) {
                    setLastComputedContainerWidth(stableWidth);
                }
            }, 100);
            return () => clearTimeout(timer);
        }
    }, [currentPlayTime, scrollContainerWidth]);

    useEffect(() => {
        onTimeLineChange?.({
            computedStartTime,
            computedEndTime,
        });
    }, [computedStartTime, computedEndTime]);

    // 导出方法
    runCustomFun(getInstances, {
        // 获取当前播放时间、时间轴范围
        getCurrentTimeData: () => {
            return {
                getCurrentPlayTime,
                uploadedTimeList,
                scaleLevel,
            };
        },
        // 设置chart是否收起
        setChartState: (state: boolean) => {
            setChartCollapsed(state);
            onChartCollapsed?.(state);
        },
        // 设置当前激活的chart
        setActiveChart,
        // 获取当前激活的chart
        getActiveChart,
        // 设置缩放等级
        setScaleLevel,
        // 当前滚动条的偏移位置
        currentScrollOffset,
        // 设置滚动条偏移
        getClipingScrollOffset,
        // 获取滚动条容器宽度
        getScrollContainerWidth,
        // 设置滚动条偏移
        setCurrentScrollOffset,
        // 计算并设置滚动条偏移
        genCurrentScrollOffset,
        // Controller样式前缀
        prefixCls,
        // 强制刷新
        forceUpdate,
    });

    // 计算获取进入剪辑时，滚动条位置
    // 需求要求进入剪辑时，满足时间轴间隔默认为20分钟，且当前播放时间(这里即剪辑结束时间)展示在图表区域的1/4处
    // 需求就是这样，看不懂不要找我- -
    function getClipingScrollOffset(nextPlayTime: number) {
        // 原始的开始时间
        const firstOriginTime = originTimestampList[0];
        // 原始的结束事件
        const lastOriginTime =
            originTimestampList[originTimestampList.length - 1];
        // 它们的差值
        const originTimeDiffrence = lastOriginTime - firstOriginTime;

        // 如果原始时间小于20分钟，不做处理，直接返回
        if (originTimeDiffrence < DEFAULT_TIMELINE_INTERVAL_ON_CLIPING) {
            return [currentScrollOffsetStart, currentScrollOffsetEnd];
        }

        // 新的开始时间：即当前播放时间 - 1200s(20分钟) * 1/4
        let startTime =
            nextPlayTime -
            DEFAULT_TIMELINE_INTERVAL_ON_CLIPING *
                DEFAULT_CURRENT_PROCESS_ON_CLIPING;
        // 新的开始时间：即当前播放时间 + 1200s(20分钟) * 3/4
        let endTime =
            nextPlayTime +
            DEFAULT_TIMELINE_INTERVAL_ON_CLIPING *
                (1 - DEFAULT_CURRENT_PROCESS_ON_CLIPING);

        // 处理边界情况(当前时间本身就不足原总时间的1/4或者大于了总时间的3/4)
        startTime < firstOriginTime && (startTime = firstOriginTime);
        endTime > lastOriginTime && (endTime = lastOriginTime);

        // 通过差值计算开始和结束位置的相对比例
        const startPercent =
            (startTime - firstOriginTime) / originTimeDiffrence;
        const endPercent = (endTime - firstOriginTime) / originTimeDiffrence;

        const scrollContainerWidth = getScrollContainerWidth();

        // 通过比例计算开始和结束的绝对偏移位置
        const scrollOffsetStart = scrollContainerWidth * startPercent;
        const scrollOffsetEnd = scrollContainerWidth * endPercent;

        return [scrollOffsetStart, scrollOffsetEnd];
    }

    // 进入剪辑时
    useUpdateEffect(() => {
        if (cliping) {
            // 记录上一次剪辑范围的结束时间
            const lastNextPlayTime = clipVideoTimeRange[1];
            // 根据当前播放时间生成剪辑范围，前后一分钟
            // 若前后不足一分钟，自动前移或后移，保证范围为一分钟
            const clipingTimeRange = getClipTimeRangeByCurrentPlayTime(
                currentPlayTime,
                originStartTime,
                originEndTime,
            );
            // 设置剪辑时间范围
            setClipVideoTimeRange(clipingTimeRange);
            const nextPlayTime = clipingTimeRange[1];
            // 由于与上一次剪辑范围的结束时间相同时，不会触发钩子然后自动进行seek，所以此特殊情况需要手动调用一次seek
            if (lastNextPlayTime === nextPlayTime) {
                onSeek?.(nextPlayTime);
            }

            // 滚动条位置
            const scrollOffset = getClipingScrollOffset(nextPlayTime);

            // 根据滚动条位置反推缩放等级
            const rawScaleLevel =
                CTRL_SCALE_LEVEL_TOTAL -
                ((scrollOffset[1] - scrollOffset[0]) /
                    getScrollContainerWidth()) *
                    CTRL_SCALE_LEVEL_TOTAL;

            // 添加边界检查，确保缩放等级在合理范围内
            const scaleLevel = Math.min(
                Math.max(
                    rawScaleLevel,
                    scaleLevels.min || 0
                ),
                scaleLevels.max || DEFAULT_CTRL_SCALE_LEVEL_MAX
            );
            setScaleLevel(scaleLevel);
            setCurrentScrollOffset(scrollOffset as any);
        }
    }, [cliping]);
    /* 滚动条相关逻辑 end */

    // 进度条上的绘制数据
    const processBarData = useMemo<ProcessBarProps['list']>(() => {
        const list: ProcessBarProps['list'] = [];

        if (!computedEndTime) return list;

        const mergedChannelList = mergeChannelTimeList(channelListData);
        // 将有视频的最后时间点存到context中
        dispatch({
            type: 'hasVideoLastTime',
            payload: mergedChannelList?.[mergedChannelList.length - 1]?.[1],
        });
        const transformedUploadedTimeMap = transformUploadedTimeList(
            uploadedTimeList,
            mergedChannelList,
        );
        const alarmList = showAlarmListData;
        for (let i = tipNodes.length - 1; i >= 0; i--) {
            const { key, color } = tipNodes[i];
            if (key === 'hasVideo') {
                mergedChannelList.forEach(([start, end]) => {
                    const offset = transformOffsetsToViewPort(
                        timestampToOffset(start),
                        timestampToOffset(end, true),
                    );
                    offset && list.push({ offset, color });
                });
            } else if (key === 'alarm') {
                alarmList.forEach((alarm) => {
                    const start =
                        timestampToOffset(Number(alarm.startTime)) - 0.5;
                    const end = start + 1;
                    const offset = transformOffsetsToViewPort(start, end);
                    offset && list.push({ offset, color });
                });
            } else {
                ((transformedUploadedTimeMap as any)[key] || []).forEach(
                    (item: [number, number]) => {
                        const [start, end] = item;
                        const offset = transformOffsetsToViewPort(
                            timestampToOffset(start),
                            timestampToOffset(end, true),
                        );
                        offset && list.push({ offset, color });
                    },
                );
            }
        }
        return list;
    }, [
        channelListData.map((item) => JSON.stringify(item)).join(),
        showAlarmListData,
        computedStartTime,
        computedEndTime,
        referenceContainerWidth,
        tipNodes
    ]);

    // 通道图表绘制数据
    const channelChartData = useMemo(() => {
        const data: ChannelChartProps['data'] = [];
        if (!computedEndTime) return data;

        for (const chl of channelListData) {
            const offsetListOfVideo = [];
            const subOffsetListOfVideo = [];

            for (const video of chl.videoList) {
                const offset = transformOffsetsToViewPort(
                    timestampToOffset(video.startUnix),
                    timestampToOffset(video.endUnix, true),
                );

                if (!offset) continue;
                if (playBackDisplayMode === PlayBackDisplayModeEnum.MIX) {
                    //开启主子码流切换且只有一种码流
                    if (video.streamType === 'MAJOR') {
                        offsetListOfVideo.push({
                            color: CHANNEL_DRAW_COLOR['major'],
                            offset,
                        });
                    } else {
                        subOffsetListOfVideo.push({
                            color: CHANNEL_DRAW_COLOR['minor'],
                            offset,
                        });
                    }
                } else {
                    offsetListOfVideo.push({
                        color:
                            video.streamType === 'MAJOR'
                                ? CHANNEL_DRAW_COLOR['major']
                                : CHANNEL_DRAW_COLOR['minor'],
                        offset,
                    });
                }
            }
            //主子码流模式设置通道bar
            if (
                playBackDisplayMode === PlayBackDisplayModeEnum.MIX &&
                chl.cacheVideoList
            ) {
                for (const video of chl.cacheVideoList) {
                    const offset = transformOffsetsToViewPort(
                        timestampToOffset(video.startUnix),
                        timestampToOffset(video.endUnix, true),
                    );

                    if (!offset) continue;

                    if (video.streamType === 'MAJOR') {
                        offsetListOfVideo.push({
                            color: CHANNEL_DRAW_COLOR['major'],
                            offset,
                        });
                    } else {
                        subOffsetListOfVideo.push({
                            color: CHANNEL_DRAW_COLOR['minor'],
                            offset,
                        });
                    }
                }
            }
            //主子码流模式设置通道bar
            if (playBackDisplayMode === PlayBackDisplayModeEnum.MIX) {
                data.push({
                    channelNumber: chl.channel,
                    channelAlias: chl.channelAlias,
                    list: offsetListOfVideo,
                    subList: subOffsetListOfVideo,
                });
            } else {
                data.push({
                    channelNumber: chl.channel,
                    channelAlias: chl.channelAlias,
                    list: offsetListOfVideo,
                });
            }
        }
        return data;
    }, [
        channelListData,
        computedStartTime,
        computedEndTime,
        referenceContainerWidth,
        currentStreamType,
        playBackDisplayMode,
    ]);

    // 速度曲线图表数据
    const speedChartData = useMemo(() => {
        if (!computedEndTime) return { speed: [], alarm: [] };

        const alarmList = showAlarmListData;

        const alarmOffsetData = [];

        for (const alarm of alarmList) {
            const start = timestampToOffset(Number(alarm.startTime)) - 0.5;
            const end = start + 1;
            const offset = transformOffsetsToViewPort(start, end);

            if (!offset) continue;

            alarmOffsetData.push({
                offset,
                color: DRAW_COLOR['alarm'],
            });
        }
        return { speed: computedSpeedList, alarm: alarmOffsetData };
    }, [
        showAlarmListData,
        computedSpeedList,
        computedStartTime,
        computedEndTime,
        referenceContainerWidth,
    ]);

    /* 回调定义 start */
    // 播放状态
    const handlePlayStateChange = () => {
        if (disableChangePlayState || cliping) {
            return;
        }
        let newState = PLAY_STATE['play'];
        if (currentPlayState !== PLAY_STATE['pause']) {
            newState = PLAY_STATE['pause'];
        }
        if (currentPlayState == PLAY_STATE['stop']) {
            newState = PLAY_STATE['stop'];
        }
        onPlayStateChange?.(newState);
    };

    const handleSeekStart = () => {
        currentSeekingRef.current = true;
    };
    // 进度条seek中
    const handleSeeking = (offset: number) => {
        setCurrentPlayTime(offsetToTimestamp(offset));
    };

    // 进度条seek完成
    const handleSeekEnd = (offset: number) => {
        currentSeekingRef.current = false;
        const timestamp = offsetToTimestamp(offset);
        onSeek?.(timestamp);
    };

    // 滚动条
    const { run: handleScroll } = useThrottleFn(
        (offset: number) => {
            setCurrentScrollOffset([offset, offset + currentScrollWidth]);
        },
        { wait: DEFAULT_THROTTLE_WAIT },
    );

    // 处理剪辑范围
    const handleClipRangeChange = ([start, end]: [number, number]) => {
        setClipVideoTimeRange([
            offsetToTimestamp(start),
            offsetToTimestamp(end),
        ]);
    };

    // 剪辑范围进度条点击
    const handleClipingSeek = (offset: number) => {
        setClipVideoTimeRange([
            offsetToTimestamp(offset),
            offsetToTimestamp(offset) + 60,
        ]);
    };

    // 图表容器点击(图表点击seek功能)
    const handleChartContainerClick = () => {
        if (chartContainerHoverOffset !== null && !cliping) {
            handleSeekEnd(chartContainerHoverOffset);
        }
    };

    // 缩放
    const { run: handleScale } = useThrottleFn(
        (level: number) => {
            // 确保缩放等级在合理范围内
            const clampedLevel = Math.min(
                Math.max(level, scaleLevels.min || 0),
                scaleLevels.max || DEFAULT_CTRL_SCALE_LEVEL_MAX
            );
            setScaleLevel(clampedLevel);
            genCurrentScrollOffset(clampedLevel);
        },
        { wait: DEFAULT_THROTTLE_WAIT },
    );

    const handleChartTabClick = (type: 'channel' | 'speed') => {
        setActiveChart(type);
        onChartTabChange?.(type);
        storage.setItem(PLAYBACK_PRIORITIES_SHOW_MODULE_KEY, type);
    };
    /* 回调定义 end */

    /* 其他逻辑 */
    // 更新当前计算过后展示的开始结束时间
    useUpdateEffect(() => {
        if (computedStartTime && computedEndTime) {
            dispatch({
                type: 'showTimeRange',
                payload: [computedStartTime, computedEndTime],
            });
        }
    }, [
        computedStartTime,
        computedEndTime,
        showTimeRange[0],
        showTimeRange[1],
    ]);

    // 播放模式或者起止时间改变，重新初始化
    useUpdateEffect(() => {
        if (isPlayModeImage) {
            setActiveChart(CHART_TYPES['speed']);
        }
        setScaleLevel(0);
        setCurrentScrollOffset([0, scrollContainerWidth]);
    }, [playMode, originStartTime, originEndTime]);

    // 设置默认展示的图表类型
    useAsyncEffect(async () => {
        let storageModule = await storage.getItem(
            PLAYBACK_PRIORITIES_SHOW_MODULE_KEY,
        );
        // 如果是服务器回放 那么不能取上次选择tab，可能是黑匣子的图表
        if (
            origin == PAGE_ORIGIN.server &&
            MIX_AND_DEVICE_PLAYBACK.includes(storageModule)
        ) {
            storageModule = CHART_TYPES['speed'];
        }
        // 租户参数重新配置后的异常处理
        if (
            playBackBlackBoxPlayMode &&
            !showChartTab(fieldsType, storageModule)
        ) {
            storageModule = CHART_TYPES['chl'];
        }
        if (
            !playBackBlackBoxPlayMode &&
            (storageModule !== CHART_TYPES['chl'] ||
                storageModule !== CHART_TYPES['speed'])
        ) {
            storageModule = CHART_TYPES['speed'];
        }
        setActiveChart(
            isPlayModeImage
                ? CHART_TYPES['speed']
                : storageModule || PLAYBACK_PRIORITIES_SHOW_MODULE,
        );
    }, [isPlayModeImage, PLAYBACK_PRIORITIES_SHOW_MODULE]);

    // 播放器reload后 充值缩放等级和滚动条位置
    useEffect(() => {
        g_emmiter.on(PLAYER_RELOAD_EVENT_TYPE, () => {
            setScaleLevel(0);
            setCurrentScrollOffset([0, getScrollContainerWidth()]);
        });
        return () => {
            g_emmiter.off(PLAYER_RELOAD_EVENT_TYPE);
        };
    }, []);

    // 图表高度计算
    const channelCount = channelListData.length;

    let chartHeight =
        channelCount * CHANNEL_BAR_HEIGHT +
        (channelCount - 1) * CHANNEL_BAR_SPACE;
    chartHeight < MIN_CHART_HEIGHT && (chartHeight = MIN_CHART_HEIGHT);

    /** 监听进度条鼠标移动事件，显示时间 start  */
    const mouseX = useElementMouseX({
        ref: referenceContainerRef,
        isDefaultOpen: false,
    });
    let { x: progressBarRefHoverOffset } = mouseX;
    const { addEvent, removeEvent } = mouseX;
    if (progressBarRefHoverOffset !== null) {
        // 取整数秒的位置(四舍五入，类似echarts)
        progressBarRefHoverOffset = timestampToOffset(
            Math.round(offsetToTimestamp(progressBarRefHoverOffset)),
        );
    }

    /** 剪辑时关闭事件监听 */
    useEffect(() => {
        if (cliping) {
            removeEvent();
        } else {
            if (originTimestampList.length > 1) {
                dispatch({
                    type: 'showTimeRange',
                    payload: [originTimestampList[0], originTimestampList[1]],
                });
            }
            addEvent();
        }
    }, [cliping]);
    const getSeekPop = (offset: number) => {
        /** 当移动到右侧一定位置时，为避免时间显示框超出当前容器，计算并设置最大left */
        const processBarMargin = 10;
        const processBarWrapperMargin = 20;
        const maxLeft =
            getReferenceContainerWidth() -
            (SEEK_POP_WIDTH / 2 - processBarMargin - processBarWrapperMargin);
        const left =
            offset + SEEK_POP_WIDTH / 2 > maxLeft
                ? maxLeft
                : offset + SEEK_POP_WIDTH / 2;
        return (
            <div
                className={classNames(`${prefixCls}-progress-seek-pop`)}
                style={{
                    left: `${left}px`,
                }}
            >
                {getToolTip(offset, false, true)}
            </div>
        );
    };
    const getChartGroupData = (
        inBlackBoxData: BlackBoxData,
        chartType: string,
    ) => {
        const chartBlackBoxDataMap: any = {};
        // @ts-ignore
        Object.keys(inBlackBoxData).forEach((key: string) => {
            // 1、过滤获取组，2、过滤获取租户参数配置过的，3、过滤获取选中的  最终得到要渲染的数据
            if (
                (chartLineGroup as any)[chartType].includes(key) &&
                simpleFieldsType.includes(key) &&
                (lineCheckBoxGroupMap as any)[chartType].includes(key)
            ) {
                chartBlackBoxDataMap[key] = (inBlackBoxData as any)[key];
            }
        });
        return chartBlackBoxDataMap;
    };
    const showChartTab = (inFieldsType: string[], chartType: string) => {
        const simpleFieldsType = inFieldsType.map(
            (item) => (LineTypeSimple as any)[item],
        );
        return (chartLineGroup as any)[chartType]?.some((lineType: any) =>
            simpleFieldsType.includes(lineType),
        );
    };
    /** 监听进度条鼠标移动事件，显示时间 end  */
    /* 视图定义 */
    // 进度条
    const processBarNode = (
        <div
            className={`${prefixCls}-process-wrapper`}
            style={{
                margin: `0 ${CTRL_REFERENCE_GRID - CTRL_PROCESS_DOT_OFFSET}px`,
            }}
        >
            <div
                ref={referenceContainerRef}
                style={{
                    width: '100%',
                    margin: `0 ${CTRL_PROCESS_DOT_OFFSET}px`,
                }}
            >
                <ProgressBar
                    streamType={clipStreamType}
                    currentPlayOffset={currentPlayOffset}
                    cliping={cliping}
                    clipOffsetRange={[
                        timestampToOffset(clipVideoTimeRange[0]),
                        timestampToOffset(clipVideoTimeRange[1]),
                    ]}
                    list={processBarData}
                    onSeekStart={handleSeekStart}
                    onSeekEnd={handleSeekEnd}
                    onSeeking={handleSeeking}
                    onClipRangeChange={handleClipRangeChange}
                    onClipingSeek={handleClipingSeek}
                    getVideoEditBlock={getVideoEditBlock}
                />
            </div>
        </div>
    );

    // 图表选项卡tab按钮
    const chartTabNodes: React.ReactNode[] = [];
    // 图表入口节点
    const chartMainNodes: React.ReactNode[] = [];

    const chartConfig = {
        [CHART_TYPES['chl']]: {
            title: i18n.t('name', '通道展示'),
            icon: <IconChannelLine />,
            main: (
                <ChannelChart
                    data={channelChartData}
                    playBackDisplayMode={playBackDisplayMode}
                />
            ),
            showMenu: [PAGE_ORIGIN.mix, PAGE_ORIGIN.device, PAGE_ORIGIN.alarmPlayBack,PAGE_ORIGIN.alarmUploadEvidence,PAGE_ORIGIN.other,PAGE_ORIGIN.track, PAGE_ORIGIN.server],
        },
        [CHART_TYPES['speed']]: {
            title: i18n.t('name', '速度曲线'),
            icon: <IconSpeed01Line />,
            main:
                playBackBlackBoxPlayMode && origin !== PAGE_ORIGIN.server ? (
                    <BaseLineChart
                        lines={getChartGroupData(
                            blackBoxData,
                            CHART_TYPES.speed,
                        )}
                        computedTimestampList={computedTimestampList}
                    />
                ) : (
                    <SpeedChart data={speedChartData} />
                ),
            showMenu: [PAGE_ORIGIN.mix, PAGE_ORIGIN.device, PAGE_ORIGIN.alarmPlayBack,PAGE_ORIGIN.alarmUploadEvidence,PAGE_ORIGIN.other,PAGE_ORIGIN.track, PAGE_ORIGIN.server],
        },
        [CHART_TYPES['vehicle']]: {
            title: i18n.t('name', '车辆信息'),
            icon: <IconCarStateOpenDoorLine />,
            main: (
                <BaseLineChart
                    lines={getChartGroupData(blackBoxData, CHART_TYPES.vehicle)}
                    computedTimestampList={computedTimestampList}
                />
            ),
            showMenu: [PAGE_ORIGIN.mix, PAGE_ORIGIN.device, PAGE_ORIGIN.alarmPlayBack,PAGE_ORIGIN.alarmUploadEvidence,PAGE_ORIGIN.other,PAGE_ORIGIN.track],
        },
        [CHART_TYPES['fuel']]: {
            title: i18n.t('name', '油耗曲线'),
            icon: <IconOilcircuitBreakLine />,
            main: (
                <BaseLineChart
                    lines={getChartGroupData(blackBoxData, CHART_TYPES.fuel)}
                    computedTimestampList={computedTimestampList}
                />
            ),
            showMenu: [PAGE_ORIGIN.mix, PAGE_ORIGIN.device, PAGE_ORIGIN.alarmPlayBack,PAGE_ORIGIN.alarmUploadEvidence,PAGE_ORIGIN.other,PAGE_ORIGIN.track],
        },
        [CHART_TYPES['gSensor']]: {
            title: i18n.t('name', 'G-sensor曲线'),
            icon: <IconDragLine />,
            main: (
                <BaseLineChart
                    lines={getChartGroupData(blackBoxData, CHART_TYPES.gSensor)}
                    computedTimestampList={computedTimestampList}
                />
            ),
            showMenu: [PAGE_ORIGIN.mix, PAGE_ORIGIN.device, PAGE_ORIGIN.alarmPlayBack,PAGE_ORIGIN.alarmUploadEvidence,PAGE_ORIGIN.other,PAGE_ORIGIN.track],
        },
        [CHART_TYPES['control']]: {
            title: i18n.t('name', '行驶状态'),
            icon: <IconCustomize />,
            main: (
                <BaseLineChart
                    lines={getChartGroupData(blackBoxData, CHART_TYPES.control)}
                    computedTimestampList={computedTimestampList}
                />
            ),
            showMenu: [PAGE_ORIGIN.mix, PAGE_ORIGIN.device, PAGE_ORIGIN.alarmPlayBack,PAGE_ORIGIN.alarmUploadEvidence,PAGE_ORIGIN.other,PAGE_ORIGIN.track],
        },
    };

    Object.keys(chartConfig).forEach((key) => {
        // 图片播放模式不展示通道
        if (isPlayModeImage && key === CHART_TYPES['chl']) {
            return;
        }
        // 过滤租户参数不展示的chart图表和平台数据不展示的chart图表
        if (
            playBackBlackBoxPlayMode &&
            !showChartTab(fieldsType, key) &&
            key !== CHART_TYPES['chl']
        ) {
            return;
        } else if (!playBackBlackBoxPlayMode && !PLAT_LINES.includes(key)) {
            return;
        }
        // 服务器回放不会显示黑匣子曲线
        if (chartConfig[key].showMenu.includes(origin)) {
            chartTabNodes.push(
                <Tooltip
                    title={chartConfig[key].title}
                    placement="right"
                    key={key}
                >
                    <span
                        key={key}
                    onClick={() => handleChartTabClick(key as any)}
                        className={classNames(`${prefixCls}-chart-tab`, {
                            active: activeChart === key,
                        })}
                    >
                        {chartConfig[key].icon}
                    </span>
                </Tooltip>,
            );
            chartMainNodes.push(
                <div
                    key={key}
                    style={{
                        height: '100%',
                        display: activeChart === key ? 'block' : 'none',
                    }}
                >
                    {chartConfig[key].main}
                </div>,
            );
        }
    });

    // 图表标线
    const chartMarkLineNodes = [
        <MarkLine
            key="markline"
            style={{
                zIndex: 99,
                display: !isOffsetInViewPort(currentPlayOffset) ? 'none' : '',
                // height: 'calc(100% + 10px)'
            }}
            offset={currentPlayOffset - CTRL_PROCESS_LINE_OFFSET}
            tooltip={getToolTip(currentPlayOffset)}
        />,
    ];

    if (
        chartContainerHoverOffset !== null &&
        !cliping &&
        !currentSeekingRef.current
    ) {
        chartMarkLineNodes.push(
            <MarkLine
                hover
                key="marklin-hover"
                style={{ zIndex: 100, transition: 'all .2s' }}
                offset={chartContainerHoverOffset - CTRL_PROCESS_LINE_OFFSET}
                tooltip={getToolTip(chartContainerHoverOffset)}
            />,
        );
    }
    const handleStreamTypeChange = () => {
        const type = {
            [STREAM_TYPE.MINOR]: STREAM_TYPE.MAJOR,
            [STREAM_TYPE.MAJOR]: STREAM_TYPE.MINOR,
        };
        onStreamTypeChange?.(type[currentStreamType] as StreamType);
    };
    useEffect(() => {
        //主子码流模式且选择通道时展示
        setShowStreamTypeSwitchButton(
            playBackDisplayMode === PlayBackDisplayModeEnum.MIX &&
                activeChart === CHART_TYPES.chl,
        );
    }, [activeChart, playBackDisplayMode]);
    // 图表面板
    const chartPanelsNode = (
        <div
            className={classNames(`${prefixCls}-chart-panels-box`, {
                collapsed: chartCollapsed,
            })}
        >
            <div
                ref={chartContainerRef}
                style={{
                    position: 'relative',
                    height: chartHeight,
                    margin: `8px ${CTRL_REFERENCE_GRID}px 0`,
                }}
                className={classNames(`${prefixCls}-chart-panels`, {
                    collapsed: chartCollapsed,
                })}
                onClick={handleChartContainerClick}
            >
                {getCustomJsx(
                    getCustomChartMainNodes,
                    chartMainNodes,
                    chartConfig,
                )}
                {getCustomJsx(getCustomChartMarkLineNodes, chartMarkLineNodes)}
            </div>

            {showStreamTypeSwitchButton && (
                <StreamTypeSwitchButton
                    onClick={handleStreamTypeChange}
                    disabled={streamTypeSwitchButtonDisabled}
                />
            )}
        </div>
    );

    // 滚动条
    const scrollBarNode = (
        <div className={`${prefixCls}-scroll-wrapper`} ref={scrollContainerRef}>
            <ScrollBar
                offset={currentScrollOffsetStart}
                width={currentScrollWidth}
                onScroll={handleScroll}
            />
        </div>
    );

    // 颜色提示
    const colorTipsNode = (
        <Space size={24} className={`${prefixCls}-color-tips-node-box`}>
            {getCustomItems(getVideoTip, tipNodes).map(({ label, color }) => (
                <Badge color={color} text={label} key={color} />
            ))}
        </Space>
    );

    return (
        <div style={style} className={classNames(prefixCls, className)}>
            <Panel className={`${prefixCls}-top`}>
                <span
                    className={classNames(`${prefixCls}-play-trigger`, {
                        disabled: disableChangePlayState || cliping,
                    })}
                    onClick={handlePlayStateChange}
                >
                    {currentPlayStateIcons[
                        currentPlayState == 'stop' ? 'pause' : currentPlayState
                    ] || <IconPlay01Fill />}
                </span>
                {progressBarRefHoverOffset !== null &&
                    getSeekPop(progressBarRefHoverOffset)}
                {processBarNode}
            </Panel>
            <div className={`${prefixCls}-bottom`} >
                <div className={`${prefixCls}-bottom-left`}>
                    <Panel
                        onClick={() => {
                            setChartCollapsed(!chartCollapsed);
                            onChartCollapsed?.(!chartCollapsed);
                        }}
                        className={classNames(
                            `${prefixCls}-chart-collapse-btn`,
                            {
                                collapsed: chartCollapsed,
                            },
                        )}
                    >
                        <UpOutlined />
                    </Panel>
                    <Panel className={`${prefixCls}-chart-tabs`}>
                        <div
                            className={classNames(
                                `${prefixCls}-chart-tabs-inner`,
                                {
                                    collapsed: chartCollapsed,
                                },
                            )}
                        >
                            {getCustomJsx(
                                getCustomChartNodes,
                                chartTabNodes,
                                chartConfig,
                            )}
                        </div>
                    </Panel>
                </div>
                <div className={`${prefixCls}-bottom-right`}>
                    {
                        !hideChart ? (
                            <Panel className={`${prefixCls}-chart-wrapper`}>
                                <TimeLine data={computedTimestampList} />
                                {chartPanelsNode}
                            </Panel>
                        ) : null
                    }
                    <Panel className={`${prefixCls}-toolbar`}>
                        {scrollBarNode}
                        {playBackBlackBoxPlayMode && ![PAGE_ORIGIN.server].includes(origin) ? (
                            <div className={`${prefixCls}-color-lines`}>
                                <LineControl
                                    cliping={cliping}
                                    activeChart={activeChart}
                                />
                            </div>
                        ) : null}
                        <div
                            className={`${prefixCls}-color-tips ${
                                activeChart === CHART_TYPES.chl ||
                                checkBoxOptions?.length < 2
                                    ? `${prefixCls}-color-tips-no-check`
                                    : ''
                            }`}
                        >
                            <RspHorizontal align="middle" justify="space-between">
                                <ScaleControl
                                    level={scaleLevel}
                                    cliping={cliping}
                                    min={scaleLevels.min}
                                    max={scaleLevels.max}
                                    onScale={handleScale}
                                />

                                {colorTipsNode}
                            </RspHorizontal>

                        </div>
                    </Panel>
                </div>
            </div>
        </div>
    );
};

export default withSharePropsHOC<ControllerProps, DevicePlayBackTipShareProps>(
    Controller,
);
