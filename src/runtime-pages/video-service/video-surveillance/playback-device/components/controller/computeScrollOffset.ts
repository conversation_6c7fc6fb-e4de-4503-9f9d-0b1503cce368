// ========== 常量定义 ==========
/**
 * 播放点不在可视区域时的默认相对位置（居中）
 */
const DEFAULT_RELATIVE_POSITION = 0.5;

/**
 * 最小可视区域宽度比例（避免缩放等级100时可视区域为0）
 * 设置为0.5%，确保即使在最高缩放等级时也有最小的可视区域
 */
const MIN_SCALED_WIDTH_RATIO = 0.005;

/**
 * 计算滚动条偏移位置的纯函数
 * 
 * 核心原理：
 * 1. 缩放时保持播放点在可视区域中的相对位置不变
 * 2. 容器宽度变化时，按比例缩放可视区域并保持相对位置
 * 
 * 示例场景：
 * - 总时长：24小时（86400秒）
 * - 默认容器宽度：1200px
 * - 可变容器宽度：1800px
 * - 当前播放时间：12:00:00（43200秒，正好一半）
 * - 缩放级别：50%（显示50%的内容）
 */
export interface ComputeScrollOffsetParams {
    // 当前容器宽度（示例：1200px 或 1800px）
    containerWidth: number;
    // 上一次计算时的容器宽度（示例：1200px，null表示首次计算）
    prevContainerWidth: number | null;
    // 缩放级别 0-100（示例：50表示显示50%内容）
    scaleLevel: number;
    // 上一次的滚动偏移 [start, end]（示例：[300, 900]表示显示300px到900px的区域）
    prevScrollOffset: [number, number];
    // 当前播放时间（示例：43200秒 = 12:00:00）
    currentPlayTime: number;
    // 原始开始时间（示例：0秒 = 00:00:00）
    originStartTime: number;
    // 原始结束时间（示例：86400秒 = 24:00:00）
    originEndTime: number;
    // 缩放级别总数（示例：100）
    scaleLevelTotal: number;
}

export interface ComputeScrollOffsetResult {
    // 新的滚动偏移 [start, end]（示例：[300, 900]）
    scrollOffset: [number, number];
    // 更新后的容器宽度（示例：1200）
    updatedContainerWidth: number;
}

export function computeScrollOffset(params: ComputeScrollOffsetParams): ComputeScrollOffsetResult {
    const {
        containerWidth,
        prevContainerWidth,
        scaleLevel,
        prevScrollOffset,
        currentPlayTime,
        originStartTime,
        originEndTime,
        scaleLevelTotal,
    } = params;

    // 使用上一次的容器宽度，如果没有则使用当前宽度
    // 示例：首次计算时 prevContainerWidth = null，使用 containerWidth = 1200
    const effectivePrevWidth = prevContainerWidth || containerWidth;
    
    // 解构上一次的滚动偏移
    // 示例：[300, 900] 表示显示从300px到900px的内容
    const [start, end = containerWidth] = prevScrollOffset;

    // 计算新的可视区域宽度（缩放后的宽度）
    // 示例：缩放50%时，1200 * (100-50) / 100 = 600px
    const scaledWidth = calculateScaledWidth(containerWidth, scaleLevel, scaleLevelTotal);

    // 如果可视区域大于等于容器宽度，显示全部内容
    // 示例：缩放0%时，scaledWidth = 1200，显示全部
    // 注意：移除了 scaledWidth === 0 的判断，因为现在有最小宽度保证
    if (scaledWidth >= containerWidth) {
        return {
            scrollOffset: [0, containerWidth],
            updatedContainerWidth: containerWidth,
        };
    }

    // 计算播放点的相关位置信息
    // 示例：12:00:00时，position = 600px（在1200px容器的中间）
    const playTimeInfo = calculatePlayTimeInfo(
        currentPlayTime,
        originStartTime,
        originEndTime,
        containerWidth
    );

    // 计算播放点在可视区域中的相对位置
    // 示例：如果播放点在[300, 900]的600px处，相对位置 = (600-300)/600 = 0.5
    const relativePosition = calculateRelativePosition(
        playTimeInfo.position,
        start,
        end,
        containerWidth,
        effectivePrevWidth
    );

    // 根据相对位置计算新的可视区域
    // 示例：保持相对位置0.5，新区域 = [300, 900]
    const newOffset = calculateNewOffset(
        playTimeInfo.position,
        relativePosition,
        scaledWidth,
        containerWidth
    );

    return {
        scrollOffset: newOffset,
        updatedContainerWidth: containerWidth,
    };
}

// ========== 辅助函数 ==========

/**
 * 计算缩放后的可视区域宽度
 *
 * 公式：可视宽度 = 容器宽度 × (总级别 - 当前级别) ÷ 总级别
 * 但是会确保有一个最小宽度，避免缩放等级100时可视区域为0
 *
 * 示例计算：
 * - 容器1200px，缩放50%：1200 × (100-50) ÷ 100 = 600px
 * - 容器1200px，缩放75%：1200 × (100-75) ÷ 100 = 300px
 * - 容器1200px，缩放99%：1200 × (100-99) ÷ 100 = 12px
 * - 容器1200px，缩放100%：max(0, 1200 × 0.005) = 6px（最小宽度）
 * - 容器1800px，缩放50%：1800 × (100-50) ÷ 100 = 900px
 */
function calculateScaledWidth(
    containerWidth: number,
    scaleLevel: number,
    scaleLevelTotal: number
): number {
    // 计算基础缩放宽度
    const baseScaledWidth = (containerWidth * (scaleLevelTotal - scaleLevel)) / scaleLevelTotal;

    // 计算最小宽度
    const minWidth = containerWidth * MIN_SCALED_WIDTH_RATIO;

    // 返回基础宽度和最小宽度中的较大值，确保始终有可视区域
    return Math.max(baseScaledWidth, minWidth);
}

/**
 * 计算播放时间相关信息
 * 
 * 示例计算（24小时时间轴，容器1200px）：
 * - 00:00:00 (0秒)：percent = 0/86400 = 0，position = 0px
 * - 12:00:00 (43200秒)：percent = 43200/86400 = 0.5，position = 600px
 * - 18:00:00 (64800秒)：percent = 64800/86400 = 0.75，position = 900px
 * - 24:00:00 (86400秒)：percent = 86400/86400 = 1，position = 1200px
 */
function calculatePlayTimeInfo(
    currentPlayTime: number,
    originStartTime: number,
    originEndTime: number,
    containerWidth: number
): { percent: number; position: number } {
    // 计算时间百分比
    // 示例：(43200 - 0) / (86400 - 0) = 0.5
    const percent = (currentPlayTime - originStartTime) / (originEndTime - originStartTime);
    
    // 计算在容器中的绝对位置
    // 示例：1200 × 0.5 = 600px
    const position = containerWidth * percent;
    
    return { percent, position };
}

/**
 * 计算播放点在可视区域中的相对位置
 * 
 * 场景1：容器宽度不变（1200px → 1200px）
 * - 可视区域[300, 900]，播放点600px
 * - 相对位置 = (600 - 300) / (900 - 300) = 0.5
 * 
 * 场景2：容器变大（1200px → 1800px）
 * - 旧可视区域[300, 900]，缩放比例 = 1800/1200 = 1.5
 * - 缩放后区域[450, 1350]，播放点900px（600 × 1.5）
 * - 相对位置 = (900 - 450) / (1350 - 450) = 0.5
 */
function calculateRelativePosition(
    playPosition: number,
    prevStart: number,
    prevEnd: number,
    currentWidth: number,
    prevWidth: number
): number {
    // 容器宽度发生变化
    if (prevWidth !== currentWidth) {
        // 计算缩放比例
        // 示例：1800 / 1200 = 1.5
        const ratio = currentWidth / prevWidth;
        
        // 将旧的可视区域按比例缩放到新容器
        // 示例：[300, 900] × 1.5 = [450, 1350]
        const scaledStart = prevStart * ratio;
        const scaledEnd = prevEnd * ratio;
        const scaledViewWidth = scaledEnd - scaledStart;
        
        // 播放点在缩放后的可视区域内
        if (playPosition >= scaledStart && playPosition <= scaledEnd) {
            // 计算相对位置
            // 示例：(900 - 450) / 900 = 0.5
            return (playPosition - scaledStart) / scaledViewWidth;
        }
        
        // 播放点不在可视区域内，默认居中
        return DEFAULT_RELATIVE_POSITION;
    }
    
    // 容器宽度未变化
    const currentViewWidth = prevEnd - prevStart;
    
    // 播放点在当前可视区域内
    if (playPosition >= prevStart && playPosition <= prevEnd) {
        // 示例：(600 - 300) / 600 = 0.5
        return (playPosition - prevStart) / currentViewWidth;
    }
    
    // 播放点不在可视区域内，默认居中
    return DEFAULT_RELATIVE_POSITION;
}

/**
 * 根据相对位置计算新的可视区域偏移
 * 
 * 公式：起始位置 = 播放点位置 - (可视宽度 × 相对位置)
 * 
 * 示例计算：
 * - 播放点600px，可视宽度600px，相对位置0.5
 * - start = 600 - (600 × 0.5) = 300px
 * - end = 300 + 600 = 900px
 * 
 * 边界处理示例：
 * - 如果start = -100，调整为[0, 600]
 * - 如果end = 1300（超出1200），调整为[600, 1200]
 */
function calculateNewOffset(
    playPosition: number,
    relativePosition: number,
    scaledWidth: number,
    containerWidth: number
): [number, number] {
    // 计算新的起始位置
    // 示例：600 - (600 × 0.5) = 300
    let start = playPosition - (scaledWidth * relativePosition);
    let end = start + scaledWidth;
    
    // 边界处理：确保可视区域在容器范围内
    if (start < 0) {
        // 左边界越界，调整为从0开始
        start = 0;
        end = scaledWidth;
    }
    
    if (end > containerWidth) {
        // 右边界越界，调整为到容器末尾
        end = containerWidth;
        start = containerWidth - scaledWidth;
    }
    
    return [start, end];
}
