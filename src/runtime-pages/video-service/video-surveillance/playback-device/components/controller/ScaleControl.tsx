/*
 * @LastEditTime: 2024-11-09 17:09:57
 */
import React, { useLayoutEffect, useRef, useState } from 'react';
import './ScaleControl.less';
import classNames from 'classnames';
import {
    PAGE_CLS_PREFIX_CTRL,
    DEFAULT_CTRL_SCALE_LEVEL_MAX,
    CTRL_SCALE_LEVEL_STEP,
} from '../../constant';
import { i18n } from '@base-app/runtime-lib';
import {
    IconRefresh,
    IconReduceLine,
    IconAddLine,
} from '@streamax/poppy-icons';
import { Slider, Tooltip } from '@streamax/poppy';
// 缩放的步长
const SCALE_STEP = CTRL_SCALE_LEVEL_STEP;
const SCALE_STEP_BTN = 1;

export interface ScaleControlProps {
    // 当前缩放等级 0-99
    level: number;
    // 是否正在剪辑中
    cliping?: boolean;
    // 最小缩放等级
    min?: number;
    // 最大缩放等级
    max?: number;
    // 缩放事件，参数为缩放等级
    onScale: (level: number) => void;
}

const ScaleControl: React.FC<ScaleControlProps> = (props) => {
    const {
        level = 0,
        cliping = false,
        min = 0,
        max = DEFAULT_CTRL_SCALE_LEVEL_MAX,
        onScale,
    } = props;
    // slider缩放值
    const [sliderValue, setSliderValue] = useState<number>(0);

    // 监听外部传递的缩放等级
    useLayoutEffect(() => {
        // 确保外部传入的缩放等级在合理范围内
        const clampedLevel = Math.min(Math.max(level, min), max);
        setSliderValue(clampedLevel);
    }, [level, min, max]);

    // slider滑块改变缩放等级
    const sliderChange = (val: number) => {
        // 若操作后的值与当前值相同，则不处理
        if (val == sliderValue) return;
        setSliderValue(val);
        // 滑块拖放结束，发起onScale
        onScale(val);
    };

    const handleSliderAfterChange = (val: number) => {
        // onScale(val);
    };

    const handleZoomOut = () => {
        if (sliderValue <= min) return;
        const targetVal = sliderValue - SCALE_STEP_BTN;
        const val = targetVal <= min ? min : targetVal;
        setSliderValue(val);
        onScale(val);
    };

    const handleZoomIn = () => {
        if (sliderValue >= max) return;
        const targetVal = sliderValue + SCALE_STEP_BTN;
        const val = targetVal >= max ? max : targetVal;
        setSliderValue(val);
        onScale(val);
    };

    const handleResizeZoomIn = () => {
        if (min === sliderValue) return;
        setSliderValue(min);
        onScale(min);
    };

    return (
        <div className={classNames(`${PAGE_CLS_PREFIX_CTRL}-scale-container`)}>
            <a
                onClick={handleZoomOut}
                className={classNames(
                    `${PAGE_CLS_PREFIX_CTRL}-scale-control-btn-item`,
                    {
                        disabled: sliderValue <= min,
                    },
                )}
                title={i18n.t('action', '缩小')}
            >
                <IconReduceLine />
            </a>
            <div
                className={classNames(
                    `${PAGE_CLS_PREFIX_CTRL}-scale-slider-box`,
                )}
            >
                <Slider
                    step={SCALE_STEP}
                    min={min}
                    max={max}
                    tipFormatter={(v) => Math.round(v as number)}
                    // disabled={cliping}
                    onChange={sliderChange}
                    onAfterChange={handleSliderAfterChange}
                    value={sliderValue}
                />
            </div>
            <a
                onClick={handleZoomIn}
                className={classNames(
                    `${PAGE_CLS_PREFIX_CTRL}-scale-control-btn-item`,
                    {
                        disabled: sliderValue >= max,
                    },
                )}
                title={i18n.t('action', '放大')}
            >
                <IconAddLine />
            </a>
            <Tooltip title={i18n.t('action', '重置缩放')}>
                <a
                    onClick={handleResizeZoomIn}
                    className={classNames(
                        `${PAGE_CLS_PREFIX_CTRL}-scale-control-btn-item`,
                        `${PAGE_CLS_PREFIX_CTRL}-scale-control-btn-reset`,
                    )}
                >
                    <IconRefresh />
                </a>
            </Tooltip>
        </div>
    );
};

const MemoizedScaleControl = React.memo(ScaleControl);
export default MemoizedScaleControl;
