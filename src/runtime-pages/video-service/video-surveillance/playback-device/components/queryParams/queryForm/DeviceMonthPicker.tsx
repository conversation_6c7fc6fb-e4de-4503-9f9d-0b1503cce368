/* eslint-disable @typescript-eslint/no-unsafe-return */
import type { FC } from 'react';
import { useContext, useEffect, useRef, useState } from 'react';
import { DatePicker, message, Space } from '@streamax/poppy';
import { getAppGlobalData, i18n, mosaicManager, MosaicTypeEnum, utils } from '@base-app/runtime-lib';
import { useLocation } from '@base-app/runtime-lib/core';
import { IconCloseCircleLine, IconLoading, IconRetry, IconSuccessLine } from '@streamax/poppy-icons';
import { getDeviceDetail, getDeviceMonthquery, getDeviceVideoMonthQuery } from '@/service/device';
import { getGpsCalendarAlarm, getImageCalendar, ImageCalendarParams } from '@/service/gps';
import type { PlayMode, StoreType } from '../../../types';
import { DEVICE_PROTOCOL_TYPE, ON_LINE_STATE, PAGE_ORIGIN, PLAY_MODE, PLAYBACK_PATHNAME } from '../../../constant';
import moment from 'moment';
import cn from 'classnames';
import './deviceMonthPicker.less';
import { isNil, uniqueId } from 'lodash';
import { vehicleIsOnline } from '../../../utils';
import usePageOrigin from '../../../hooks/usePageOrigin';
import { Context } from '../../../store';
import { useDebounceFn, useGetState, useScroll, useSetState, useUpdateEffect } from '@streamax/hooks';
import { CONTROL_RANGE, isNeedControl } from '@/utils/flow';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { check28181 } from '@/utils/28181utils';

const { zeroTimeStampToFormatTime, getSummerTimeOffsetByTime } = utils.formator;

interface MonthPickerProps {
    vehicleInfo?: any;
    deviceInfo?: any;
    channel?: number;
    onChange?: any;
    value?: any;
    playMode?: PlayMode;
    disabled?: boolean;
    onReset?: (type: 'image' | 'video') => void;
}
const LoadingStateObj = {
    loading: 'loading',
    success: 'success',
    fail: 'fail',
};
enum StoreTypes {
    deviceStore = 1,
    serverStore,
}
type LoadingState = 'loading' | 'success' | 'fail';
const DeviceMonthPicker: FC<MonthPickerProps> = (props) => {
    const { deviceInfo, channel, value, onChange, playMode, vehicleInfo, disabled, onReset } = props;
    const { pathname } = useLocation();
    const pageOrigin = usePageOrigin();
    const { dispatch, exceedFlow, curSelectedDeviceState } = useContext(Context);
    const [dataPickLoading, setDataPickLoading] = useSetState<{
        videoLoading: LoadingState;
        imageLoading: LoadingState;
    }>({
        videoLoading: 'loading',
        imageLoading: 'loading',
    });
    const [monthMask, setMonthMask] = useState(true); // 总的loading 用于全部未加载完时 不允许切换月份
    /**
     * @description 1、存储上次查询时的设备状态
     * @description 2、用于判断日历下拉框打开时是否重新请求接口（需求来源于设备状态实时变更）
     */
    const latestQueryDeviceState = useRef<0 | 1>(0);

    const scroll = useScroll();

    useEffect(() => {
        if (datePickerOpen) {
            setDatePickerOpen(false);
        }
    }, [scroll]);

    // 当前所选月份
    const [selectDateMonth, setSelectDateMonth] = useState<string | undefined>();
    // 当前选择的日期
    const [selectDate, setSelectDate, getSelectDate] = useGetState<string | undefined>();
    // 设备月历数据
    const [deviceMonthInfo, setDeviceMonthInfo, getDeviceMonthInfo] = useGetState<any>({
        commonList: [],
        alarmList: [],
        imgList: [],
        deviceVideoList: [],
    });
    const [pickMode, setPickMode] = useState('date');
    const [datePickerOpen, setDatePickerOpen] = useState(false); // 下拉框展示需要手动控制不然无法触发重试请求
    const [isHoverFail, setIsHoverFail] = useSetState<{
        image?: Boolean;
        video?: Boolean;
    }>({
        image: false,
        video: false,
    }); // 是否hover过失败icon 用于区别展示重试icon

    const lastLockKey = useRef<string | null>();
    const resetTime = useRef<{
        startTime: string;
        endTime: string;
    }>();

    const getLastLockKey = () => {
        const lockKey = uniqueId();
        lastLockKey.current = lockKey;
        return lockKey;
    };
    useUpdateEffect(() => {
        if (
            dataPickLoading.videoLoading !== LoadingStateObj.loading &&
            dataPickLoading.imageLoading !== LoadingStateObj.loading
        ) {
            setMonthMask(false);
            dispatch({
                type: 'isMonthQueryDone',
                payload: true,
            });
        }
    }, [dataPickLoading]);

    const _changeDevice = (deviceInfo: any, channel?: number) => {
        // 每次切换设备就需要重置日历数据
        setDeviceMonthInfo({
            commonList: [],
            alarmList: [],
            imgList: [],
            deviceVideoList: [],
        });
        setDataPickLoading({
            imageLoading: "loading",
            videoLoading: "loading"
        });
        setMonthMask(true);
        dispatch({
            type: 'isMonthQueryDone',
            payload: false,
        });
        const searchDate =
            selectDateMonth ||
            zeroTimeStampToFormatTime(moment().utc().unix(), undefined, 'YYYY-MM');
        // 只有（设备回放&车辆在线&有设备&视频模式）||服务器回放 || 融合回放，才会去查询月历
        const pathname = location.pathname;
        // if (PAGE_ORIGIN.track === pageOrigin) {
        // }
        // 轨迹回放界面没有时间选择 需要展示模式选择
        // 28181设备，切换设备、通道时，不隐藏视频模式选择按钮
        if (!check28181(deviceInfo || {}) && !selectDate) {
            dispatch({
                type: 'playModeShow',
                payload: false,
            });
        }
        if (PAGE_ORIGIN.track === pageOrigin && deviceInfo.onlineState === ON_LINE_STATE.offline) {
            dispatch({
                type: 'playMode',
                payload: PLAY_MODE.image,
            });
        }
        if (
            // playMode === 'video' &&
            deviceInfo?.authId &&
            (PAGE_ORIGIN.device === pageOrigin ||
                PAGE_ORIGIN.other === pageOrigin ||
                // deviceInfo.onlineState === ON_LINE_STATE.online) ||
                pathname.includes(PLAYBACK_PATHNAME.serve) ||
                pathname.includes(PLAYBACK_PATHNAME.mix))
        )
            getDeviceMonthStatusQuery(searchDate, searchDate, getLastLockKey(), deviceInfo);
    };
    //防抖是防止更新更新设备数据频繁，造成重复向后端发起请求
    const { run: changeDevice } = useDebounceFn(_changeDevice, { wait: 30 });

    const onOpenChange = (open: boolean, onlineState: 0 | 1) => {
        if (open && latestQueryDeviceState.current !== onlineState) {
            changeDevice({ ...deviceInfo, onlineState });
            latestQueryDeviceState.current = onlineState;
        }
    };

    useUpdateEffect(() => {
        // 28181设备，选择了通道后，才查询月历，非28181设备，选择了设备即查询月历
        if (check28181(deviceInfo || {}) ? deviceInfo && channel : deviceInfo) {
            changeDevice(deviceInfo);
            latestQueryDeviceState.current = deviceInfo.onlineState;
        }
    }, [deviceInfo, channel]);

    // 自定义日历样式
    function dateRender(current: any) {
        const currentDate = moment(current).format('YYYY-MM-DD');
        const commonList = deviceMonthInfo.commonList;
        const alarmList = deviceMonthInfo.alarmList;
        const imgList = deviceMonthInfo.imgList || [];
        const deviceVideoList = deviceMonthInfo.deviceVideoList;
        return (
            <div
                className={cn('ant-picker-cell', {
                    green: commonList.indexOf(currentDate) > -1,
                    red: alarmList.indexOf(currentDate) > -1,
                    'white-border':
                        commonList?.indexOf(currentDate) <= -1 &&
                        imgList?.indexOf(currentDate) > -1,
                    // value无值时不用设置current样式
                    current: value ? currentDate === moment(value).format('YYYY-MM-DD') : '',
                })}
            >
                <div className="ant-picker-cell-inner">
                    <div
                        className={cn({
                            'image-border':
                                commonList?.indexOf(currentDate) <= -1 &&
                                imgList?.indexOf(currentDate) > -1,
                        })}
                    />
                    <div
                        className={cn({
                            triangle: deviceVideoList?.indexOf(currentDate) > -1,
                        })}
                    />
                    {current.date()}
                </div>
            </div>
        );
    }

    async function getDeviceMonthData(params: any, hasMessage: boolean = true) {
        const data = await getDeviceMonthquery(
            params,
            {
                _accessKey: `${localStorage.getItem('AUTH_TOKEN')}#${getAppGlobalData('APP_ID')}`,
            },
            hasMessage,
        );
        return data;
    }
    async function getDeviceVideoMonthData(params: any, hasMessage: boolean = true) {
        const data: any[] = await getDeviceVideoMonthQuery(
            params,
            {
                _accessKey: `${localStorage.getItem('AUTH_TOKEN')}#${getAppGlobalData('APP_ID')}`,
            },
            hasMessage,
        );
        return data;
    }
    const queryImageCalendarAlarm = async (imgReq: ImageCalendarParams) => {
        try {
            setDataPickLoading({
                imageLoading: 'loading',
            });
            const imgList: string[] = (await getImageCalendar(imgReq)) || [];
            setDeviceMonthInfo((devData: any) => {
                return {
                    ...devData,
                    imgList,
                };
            });
            setDataPickLoading({
                imageLoading: 'success',
            });

            const hasImage = imgList?.includes(getSelectDate() || '');
            if (imgList && imgList?.length && hasImage) {
                dispatch({
                    type: 'playModeDisabled',
                    payload: {
                        [PLAY_MODE.image]: false,
                    },
                });
            }
        } catch (error) {
            setDataPickLoading({
                imageLoading: 'fail',
            });
            dispatch({
                type: 'playModeDisabled',
                payload: {
                    [PLAY_MODE.image]: true,
                },
            });
        }
    };
    const queryDeviceVideoData = async (
        deviceServerReq: any,
        alarmReq: any,
        deviceInfo: any,
        isReset?: boolean,
    ) => {
        // 获取设备的视频日历信息-----将之前的提了出来，为了和图片请求做异步处理, 和增加请求有设备视频的日期
        try {
            // 离线设备也可以请求
            // 设备不在线 && 不是点击的重试就不进行多余的请求
            if (
                deviceInfo.onlineState !== ON_LINE_STATE.online &&
                pathname.includes(PLAYBACK_PATHNAME.device)
            ) {
                if (isNil(isReset)) {
                    throw new Error();
                }
            }
            if (!deviceServerReq.channels) {
                message.warn({
                    content: i18n.t('message', '设备无通道'),
                    key: 'noChannels',
                });
                throw new Error();
            }
            setDataPickLoading({
                videoLoading: 'loading',
            });
            let hasMessage = true;
            if (!isNil(isReset) && isReset) {
                hasMessage = !isReset;
                const deviceInfo = await getDeviceDetail({ authId: deviceServerReq.devId });
                const deviceOnline = vehicleIsOnline(deviceInfo); // 设备的在线状态和车辆一致的
                if (!deviceOnline) {
                    // 设备不在线情况
                    // throw new Error();
                }
            }
            const tempDeviceVideo = getDeviceVideoMonthData(deviceServerReq, hasMessage); // 用于展示是否有设备视频标识

            let targetDeviceVideo = await tempDeviceVideo;
            const commonDaysList = targetDeviceVideo
                ?.filter((item) => {
                    // fileTypes 1 普通录像 2报警录像
                    return item.fileTypes.includes(1) || item.fileTypes.includes(2);
                })
                ?.map((item) => item.date);

            if (targetDeviceVideo?.length && commonDaysList?.length) {
                targetDeviceVideo = targetDeviceVideo.filter(
                    (p: any) => commonDaysList.indexOf(p.date) > -1,
                ); // 防止两个接口返回数据不一致 兜个底
                targetDeviceVideo = targetDeviceVideo
                    .filter((i) => i.storeTypes.includes(StoreTypes.deviceStore))
                    .map((i) => i.date); // 过滤出只有设备视频的日期
            }

            setDeviceMonthInfo((devData: any) => {
                return {
                    ...devData,
                    commonList: commonDaysList || [],
                    deviceVideoList: targetDeviceVideo || [],
                };
            });

            // 获取报警信息的日历信息
            let alarmDaysList = (await getGpsCalendarAlarm(alarmReq)) || [];
            alarmDaysList = alarmDaysList.filter((p: string) => commonDaysList?.indexOf(p) > -1);
            setDeviceMonthInfo((devData: any) => {
                return {
                    ...devData,
                    alarmList: alarmDaysList || [],
                };
            });

            setDataPickLoading({
                videoLoading: 'success',
            });
            // 有视频 则直接取消视频模式的禁用
            if (commonDaysList && commonDaysList.length) {
                dispatch({
                    type: 'playModeDisabled',
                    payload: {
                        [PLAY_MODE.video]: false,
                    },
                });
            }
        } catch (error) {
            setDataPickLoading({
                videoLoading: 'fail',
            });
            dispatch({
                type: 'playModeDisabled',
                payload: {
                    [PLAY_MODE.video]: true,
                },
            });
            // setDataPickLoading(false);
        }
    };

    // 查询月历数据modify夏令时月历
    async function getDeviceMonthStatusQuery(
        sTime: string,
        eTime: string,
        lockKey: string,
        /** 新增原因：设备变更时需要重新加载日历，因此deviceInfo需从调用方传入，否则状态不是最新的 */
        deviceInfo: any,
        type?: 'image' | 'video',
        isReset?: boolean
    ) {
        // setDataPickLoading(true);
        resetTime.current = {
            startTime: sTime,
            endTime: eTime,
        };
        try {
            const deviceChannelList = deviceInfo?.deviceChannelList || [];
            const channelResult = check28181(deviceInfo || {}) ? channel : undefined;
            const channels = channelResult ? channelResult : deviceChannelList.map((p: any) => p.channelNo).join(',');
            const { protocolType, authId, deviceId } = deviceInfo || {};
            let storeType: StoreType = 'ALL',
                streamType = 'ALL';
            if (protocolType === DEVICE_PROTOCOL_TYPE.JT905 && !pathname.includes(PLAYBACK_PATHNAME.serve)) {
                storeType = 'MASTER';
                streamType = 'MAJOR';
            }
            if (pathname.includes(PLAYBACK_PATHNAME.mix)) {
                const exceedFlow = await isNeedControl(authId, CONTROL_RANGE.PLAY_BACK);
                if (exceedFlow) {
                    message.warn({
                        key: 'exceedFlowMessage',
                        content: i18n.t('message', '流量使用超额，仅能查看平台视频。'),
                    });
                }
                // 超出流量管控后查询服务器回放
                storeType = exceedFlow ? 'CENTER' : 'MIX'; // 融合回放
            } else if (pathname.includes(PLAYBACK_PATHNAME.serve)) {
                storeType = 'CENTER';
            }
            const userConfig = getAppGlobalData('APP_USER_CONFIG');
            // 用户时区
            let userTimeZone = (Number(userConfig?.timeZone) || 0) / 60;
            /**
             * 目标：平台-》用户 平台0时区，用户8时区
                平台 + 参数偏移量  = 用户
                平台 + 时区偏移量+ 用户夏令时偏移量（加） = 用户
                两式相减可得
                0 +  时区偏移量+ 用户夏令时偏移量（加）- 参数偏移量  = 0
                参数偏移量 = toZone - fromZone + 用户夏令时偏移量（加） = 9
             *
             */

            // 若开启了夏令时，用户时区需要加一个小时
            if (parseInt(userConfig.dst, 10) === 1) {
                const dstTime = getSummerTimeOffsetByTime(moment(`${sTime}-01`));
                userTimeZone += dstTime / 60;
            }
            // 平台时区
            const platformTimeZone = (Number(getAppGlobalData('PLATFORM_TIME_ZONE')) || 0) / 60;
            // 时区偏移量
            const timeZoneOffset = userTimeZone - platformTimeZone;
            const req = {
                devId: authId,
                channels,
                storeType,
                streamType,
                beginDate: sTime,
                endDate: eTime,
                timeZoneOffset,
            };
            if (lastLockKey.current !== lockKey) return;
            let gpsTimeZoneOffset = getAppGlobalData('APP_USER_CONFIG')?.timeZone || '0';
            // 若开启了夏令时，用户时区需要加一个小时
            if (parseInt(userConfig.dst, 10) === 1) {
                const dstTime = getSummerTimeOffsetByTime(moment(`${sTime}-01`));
                gpsTimeZoneOffset = Number(gpsTimeZoneOffset) + dstTime;
            }
            const summerList = getAppGlobalData('APP_USER_CONFIG')?.summerTimeList || [];
            const dst = summerList.map((i: {startTime: string,endTime: string}) => ({
                dstStartTime: i.startTime,
                dstEndTime: i.endTime,
            }));
            const monthLength = moment(eTime, 'YYYY-MM').daysInMonth();
            const alarmOrImageReq = {
                startDate: `${sTime}-01`,
                endDate: `${eTime}-${monthLength}`,
                vehicleId: vehicleInfo.vehicleId,
                timeZoneOffset: getAppGlobalData('APP_USER_CONFIG')?.timeZone ?? '0',
                dst,
                mosaicFlag: mosaicManager.checkMosaicEnable(MosaicTypeEnum.playback) ? 1 : 0
            };
            const imageReq = {
                ...alarmOrImageReq,
                deviceId,
            };
            if (isNil(type)) {
                queryDeviceVideoData(req, alarmOrImageReq, deviceInfo);
                queryImageCalendarAlarm(imageReq);
            } else {
                type === 'image' && queryImageCalendarAlarm(imageReq);
                type === 'video' &&
                    (deviceInfo.onlineState === ON_LINE_STATE.online || isReset) &&
                    queryDeviceVideoData(req, alarmOrImageReq, deviceInfo, isReset);
            }
        } catch (error) {
            // setDataPickLoading(false);
        }
    }

    // 切换月历
    function onPanelChange(value: any, mode: any) {
        if (mode !== 'date') {
            // 切换年/月的时候不需要mask遮挡
            setMonthMask(false);
            dispatch({
                type: 'isMonthQueryDone',
                payload: true,
            });
        } else {
            setMonthMask(true);
            dispatch({
                type: 'isMonthQueryDone',
                payload: false,
            });
        }
        // 如果是图片模式，则不需要查询月历
        // 如果切换月历面板不是日期面板，也不需要查询月历
        setPickMode(mode);
        // 清除原来的数据
        setDeviceMonthInfo({
            commonList: [],
            alarmList: [],
            // imgList: [],
            deviceVideoList: [],
        });
        if (mode !== 'date') return;
        // if (playMode === 'image' || mode !== 'date') return;
        const dateMonth = moment(value).format('YYYY-MM');
        setSelectDateMonth(dateMonth);
        getDeviceMonthStatusQuery(dateMonth, dateMonth, getLastLockKey(), deviceInfo, undefined, undefined);
    }

    // 设置不可选日期
    const disabledDate = (current: any) => {
        const halfYear = moment().subtract(6, 'months').format('YYYY-MM-DD');
        const today = moment().format('YYYY-MM-DD');
        const currentDay = moment(current).format('YYYY-MM-DD');
        // 图片模式可以看最近半年的
        // if (playMode === PLAY_MODE.image) {
        //     // return currentDay < halfYear || currentDay > today;
        //     return deviceMonthInfo.imgList.indexOf(currentDay) === -1;
        // }
        // 如果选择面板不是以日期为维度，则全部为可选
        if (pickMode !== 'date') {
            // --fix-- 禁用日期选择后还能继续切换
            return currentDay > today;
        }
        const allDays = (deviceMonthInfo.imgList || []).concat(deviceMonthInfo.commonList);
        // 视频模式只能选有视频的日期
        return allDays.indexOf(currentDay) === -1;
    };
    const renderLoadingText = (
        loading: LoadingState = 'loading',
        type: 'video' | 'image' = 'video',
    ) => {
        if (loading === LoadingStateObj.success) {
            return (
                <>
                    <div className="right-text">
                        <OverflowEllipsisContainer>
                            {i18n.t('name', '加载成功')}
                        </OverflowEllipsisContainer>
                    </div>
                    <IconSuccessLine />
                </>
            );
        }
        if (loading === LoadingStateObj.fail) {
            return (
                <>
                    <div className="right-text">
                        <OverflowEllipsisContainer>
                            {(PAGE_ORIGIN.device === pageOrigin ||
                                PAGE_ORIGIN.other === pageOrigin) &&
                            deviceInfo.onlineState !== ON_LINE_STATE.online
                                ? i18n.t('name', '设备离线')
                                : i18n.t('name', '加载失败')}
                        </OverflowEllipsisContainer>
                    </div>
                    <span
                        onMouseEnter={() =>
                            setIsHoverFail({
                                [type]: true,
                            })
                        }
                        onMouseLeave={() =>
                            setIsHoverFail({
                                [type]: false,
                            })
                        }
                    >
                        {isHoverFail[type] ? (
                            <a
                                onClick={() => {
                                    if (resetTime.current) {
                                        const { startTime, endTime } = resetTime.current;
                                        onReset && onReset(type);
                                        type &&
                                            getDeviceMonthStatusQuery(
                                                startTime,
                                                endTime,
                                                getLastLockKey(),
                                                deviceInfo,
                                                type,
                                                true,
                                            );
                                    }
                                }}
                            >
                                <IconRetry />
                            </a>
                        ) : (
                            <IconCloseCircleLine />
                        )}
                    </span>
                </>
            );
        }
        return (
            <>
                {i18n.t('name', '加载中')}
                <IconLoading spin />
            </>
        );
    };
    const handleOnChange = async (value: any) => {
        setDatePickerOpen(false);
        let isShow = false;
        if (value) isShow = true;

        const selectedDay = moment(value).format('YYYY-MM-DD');
        const tempData = getDeviceMonthInfo();

        const hasVideo = tempData.commonList?.includes(selectedDay);
        const hasImage = tempData.imgList?.includes(selectedDay);

        if (!hasVideo && hasImage) {
            // 无视频 有图片需要切换为图片模式
            dispatch({
                type: 'playMode',
                payload: PLAY_MODE.image,
            });
        }
        if (hasVideo) {
            dispatch({
                type: 'playMode',
                payload: PLAY_MODE.video,
            });
        }
        // 设备回放时，如果流量超出，则只允许图片播放
        if (PAGE_ORIGIN.device === pageOrigin || PAGE_ORIGIN.other === pageOrigin) {
            // 如果有流量超出了
            if (exceedFlow) {
                dispatch({
                    type: 'playMode',
                    payload: PLAY_MODE.image,
                });
            }
        }
        dispatch({
            type: 'playModeDisabled',
            payload: {
                [PLAY_MODE.video]: !hasVideo,
                [PLAY_MODE.image]: !hasImage,
            },
        });
        dispatch({
            type: 'playModeShow',
            payload: isShow,
        });
        onChange && onChange(value);
        setSelectDate(selectedDay);
    };
    const panelRender = (node: React.ReactNode) => {
        return (
            <div className="month-container">
                <div>{node}</div>
                <div
                    className={cn({
                        'month-mask': monthMask,
                    })}
                />
                <div className="video-image-data-container">
                    <div className="data-container">
                        <div className="left-text">
                            <OverflowEllipsisContainer>
                                {i18n.t('name', '视频数据')}
                            </OverflowEllipsisContainer>
                        </div>
                        <div>
                            <Space style={{ alignItems: 'center' }}>
                                {renderLoadingText(dataPickLoading.videoLoading, 'video')}
                            </Space>
                        </div>
                    </div>
                    <div className="data-container">
                        <div className="left-text">
                            <OverflowEllipsisContainer>
                                {i18n.t('name', '图片数据')}
                            </OverflowEllipsisContainer>
                        </div>
                        <div>
                            <Space style={{ alignItems: 'center' }}>
                                {renderLoadingText(dataPickLoading.imageLoading, 'image')}
                            </Space>
                        </div>
                    </div>
                </div>
                {PAGE_ORIGIN.mix === pageOrigin && (
                    <div className="data-example-container">
                        <div className="data-example">
                            <div className="example fill" />
                            <div className="example-text">
                                <OverflowEllipsisContainer>
                                    {i18n.t('name', '有视频')}
                                </OverflowEllipsisContainer>
                            </div>
                        </div>
                        <div className="data-example">
                            <div className="example fill">
                                <div className="triangle" />
                            </div>
                            <div className="example-text">
                                <OverflowEllipsisContainer>
                                    {i18n.t('name', '设备视频')}
                                </OverflowEllipsisContainer>
                            </div>
                        </div>
                        <div className="data-example">
                            <div className="example has-border" />
                            <div className="example-text">
                                <OverflowEllipsisContainer>
                                    {i18n.t('name', '有图片')}
                                </OverflowEllipsisContainer>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };
    return (
        <div className="device-month-picker-container">
            {/* @ts-ignore */}
            <DatePicker
                style={{ width: '100%' }}
                dropdownClassName="playback-month-picker"
                dateRender={dateRender}
                disabledDate={disabledDate}
                showToday={false}
                value={value}
                disabled={disabled}
                open={datePickerOpen}
                onOpenChange={(open: boolean) => onOpenChange(open, curSelectedDeviceState)}
                onPanelChange={onPanelChange}
                panelRender={panelRender}
                placeholder={i18n.t('message', '请选择日期')}
                onChange={handleOnChange}
                onBlur={() => {
                    setDatePickerOpen(false);
                }}
                onClick={() => {
                    setDatePickerOpen(true);
                }}
                getPopupContainer={(dom) => dom.parentElement as HTMLElement}
            />
            {/* 解决DatePicker的open变成受控后，disabled时点击也会触发onClick，等enable时也会变成打开 */}
            <div
                className={cn('device-month-picker-disabled', {
                    disabled: disabled,
                })}
            />
        </div>
    );
};

export default DeviceMonthPicker;
