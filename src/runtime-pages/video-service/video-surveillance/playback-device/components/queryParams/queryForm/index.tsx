import { useDebounceFn, useLatest, useUpdateEffect } from '@streamax/hooks';
import { Select, message, Tooltip, Badge } from '@streamax/poppy';
import {
    i18n,
    utils,
    getAppGlobalData,
    g_emmiter,
} from '@base-app/runtime-lib';
import { useLocation, useHistory } from '@base-app/runtime-lib/core';
import _, { pick, omit } from 'lodash';
import moment from 'moment';
import type { Moment } from 'moment';
import {
    useState,
    useEffect,
    useContext,
    useRef,
    useImperativeHandle,
    forwardRef,
    useMemo,
    useCallback,
} from 'react';
import type { FC } from 'react';
import DeviceMonthPicker from './DeviceMonthPicker';
import {
    CHANGE_PLAYMODE_MANUAL_MESSAGE_NAME,
    PLAY_MODE,
    PLAYBACK_PATHNAME,
    ON_LINE_STATE,
} from '../../../constant';
import { PAGE_ORIGIN } from '../../../constant';
import usePageOrigin from '../../../hooks/usePageOrigin';
import { Context } from '../../../store';
import type { ChangeParams } from '../../../types';
import { vehicleIsOnline } from '../../../utils';
import type { QueryParamsProps } from '../index';
import VehicleTreeSelect from '@/components/VehicleTreeSelect';
import useRTVehicle from '@/hooks/useRTData/useRTVehicle';
import { getVehicleDetailWithEnabledChannels } from '@/service/vehicle';
import { DeviceStateType } from '@/service/vehicle';
import { getCompleteDevice, getDeviceStates } from '@/utils/commonFun';
import { DEVICE_PRIMARY_TYPE } from '@/utils/constant';
import { CONTROL_RANGE, isNeedControl } from '@/utils/flow';
import './index.less';
import { OFFLINE_STATE, VEHICLE_STATES } from '@/const/vehicle';
import { getCustomJsx } from '@/utils/pageReuse';
import { check28181 } from '@/utils/28181utils';
import classNames from 'classnames';
import useTreeDataStore, {
   getTreeDataStore, NodeType,
    TreeData,
} from '@/modules/vehicle-tree/data/useTreeDataManager/useTreeDataStore';
import useRealtimeRTData, {
    VehicleDataModule,
} from '@/modules/realtime-monitor/fleet-vehicle-tree/data/useRealtimeRTData';
import { shallow } from 'zustand/shallow';
import useVehicleList from '@/modules/realtime-monitor/fleet-vehicle-tree/data/useVehicleList';
import { getRTDataStore, useRTDataStore } from '@/modules/vehicle-tree/data/realtime-data/useRTDataManager';
import { OperationTreeNode } from '@/modules/vehicle-tree/data/useTreeDataManager/useLimitTreeChildren';
import GenerateTreeTitleV2 from '@/components/VehicleTreeSelect/compontents/GenerateTreeTitleV2';
import { IconReturn } from '@streamax/poppy-icons';
import { uuid } from '@/runtime-lib/utils/general';
import { useUnmount } from 'ahooks';
import useTreeTitleStore from '@/components/VehicleTreeSelect/useTreeTitleStore';
import useInstanceId from '../../../hooks/useInstanceId';
import DeviceOnlineStatus from '../../DeviceOnlineStatus';
import useOnceFn from '@/hooks/useOnceFn';
const { timestampToZeroTimeStamp, zeroTimeStampToFormatTime } = utils.formator;
interface vehicleModel {
    vehicleId: string;
    vehicleNumber: string;
    deviceList: any[];
    [key: string]: any;
}
const QueryForm = (props: QueryParamsProps, ref) => {
    // 页面复用
    const {
        addLoopMethod,
        getLoopTime,
        getCustomFleets,
        getCustomVehicles,
        getCustomUnGroupedTitle,
        getVehicleNumber,
        getCustomFilter,
        onVisibleVehicleChange
    } = props;
    // 路由中的参数信息
    // @ts-ignore
    const location: any = useLocation();
    const queryInfo = location.query;
    const history: any = useHistory();
    const pageOrigin = usePageOrigin();
    const { dispatch, cliping, playMode, isMonthQueryDone } =
        useContext(Context);
    const vehicleTreeRef = useRef<any>();
    // 使用 useInstanceId 钩子，获取实例ID
    const { instanceId, clearInstanceId } = useInstanceId();
    // 所选车辆信息
    const [selectedVehicleInfo, setSelectedVehicleInfo] =
        useState<vehicleModel | null>(null);
    const latestSelectedVehicleRef = useLatest(selectedVehicleInfo);
    // 所选设备信息
    const [selectedDeviceInfo, setSelectedDeviceInfo] = useState<any>(null);
    // 所选通道信息
    const [selectedChannel, setSelectedChannel] = useState<number | undefined>(
        undefined,
    );
    const latestSelectedDeviceInfo = useLatest(selectedDeviceInfo);
    // 所选日期信息
    const [selectedDate, setSelectedDate] = useState<any>();
    // 是否是第一次渲染
    const [firstLoad, setFirstLoad] = useState<boolean>(true);
    // 当前所选设备
    const [currentDeviceValue, setCurrentDeviceValue] = useState<
        string | undefined
    >(undefined);
    const treeDataStore = getTreeDataStore(instanceId);
    const fleetTotalNumberMap = treeDataStore(
        (state) => state.fleetTotalNumberMap,
    );
    const reuseParam = addLoopMethod
        ? {
              getReuseData: addLoopMethod,
              getLoopTime,
          }
        : undefined;

    // 组件卸载时的清理已经在useInstanceId中处理
    useUnmount(()=>{
        // 清理工作已在useInstanceId中处理
        clearInstanceId();
    });

    const { loaded, fleetLoaded, loadFleets, fleetList } = treeDataStore(
        (state) =>
            pick(state, [
                'loaded',
                'fleetLoaded',
                'loadFleets',
                'refresh',
                'fleetList',
                'filteredVehicleList',
            ]),
        shallow,
    );
    const RTDataStore = getRTDataStore(instanceId)
    // 车辆回显开关，它用于控制车辆下拉框回显车辆的时机，确保是在下拉树车辆已经加载完成后再回显。
    // 车组和车辆是分开加载的，且车组未展开时，车辆并不会出现在treeData中，因此下方还有loadFleets相关代码，用于手动将车辆挂载到车站下。
    const [vehicleFeedback, setVehicleFeedback] = useState(false);
    const vehicleList = useVehicleList(instanceId);
    const vehicleStateConfig = RTDataStore((state) => state.stateConfig);
    const loadedKeys = treeDataStore((state) => state.loadedKeys);
    const _treeData = treeDataStore((state) => state.treeNodes);
    const treeData = vehicleFeedback ? _treeData : [];
    const vStatesMap = RTDataStore((state) => state.rtDataMap);
    const vehicleListMap = useTreeDataStore((state) => state.vehicleListMap);
    const rtDataLoaded = RTDataStore((state) => state.loaded) && loaded;
    const latestVehicleRef = useLatest(vehicleList);
    const latestSelectedDateRef = useLatest(selectedDate);
    const latestSelectedChannelRef = useLatest(selectedChannel);
    const latestStateConfigRef = useLatest(vehicleStateConfig);
    const vStatesMapRef = useLatest(vStatesMap);

    useEffect(() => {
        useTreeTitleStore.getState().updateState({ getVehicleNumber });
    }, [getVehicleNumber]);

    useEffect(() => {
        let vehicle;
        let fId = queryInfo.fleetId;
        const vehicleId = queryInfo.vehicleId;
        /**** 四种场景
         * 1、跳转到回放也，有车组id，就展开该车组id
         * 2、跳转到回放也，无车组id，取从map中找到的车辆的车组id
         * 3、当前页面播放，不带车辆车组id，不会展开车组
         * 4、单车属于多车组场景，判断走1和2
         * ****/
        if (rtDataLoaded) {
            vehicle = vehicleListMap[vehicleId];
            if(vehicle) {
                // 若没有传车组id 就使用查到的车辆信息的车组id
                if(!fId) {
                    fId = vehicle.fId;
                }
                treeDataStore.getState().loadFleetsManager.loadFleets([vehicle.fId]).then(() => {
                    setVehicleFeedback(true);
                });
            } else {
                setVehicleFeedback(true);
            }
        }
    }, [queryInfo.id, loaded, rtDataLoaded]);

    const convertTreeData2Node = useCallback(
        (item: TreeData) => {
            // 不允许选择车组
            if (item.type === 'fleet') {
                // @ts-ignore
                item.selectable = false;
                // @ts-ignore 是否有车辆，自适应展开宽度需要
                item.hasChild =
                    fleetTotalNumberMap[item.fId] > 0 ? true : false;
            } else {
                // item.vStates = vStatesMapRef[item?.vId] || [];
            }
            return Object.assign(
                {
                    title:
                        item.type === NodeType.OPERATION ? (
                            <OperationTreeNode baseInfo={item} instanceId={instanceId}  showToTop={false}/>
                        ) : (
                            <GenerateTreeTitleV2
                                vehicleTreeRef={vehicleTreeRef.current}
                                hasBracket={false}
                                baseInfo={item}
                                type={item.type}
                            instanceId={instanceId}/>
                        ),
                    showIcon: true,
                    switcherIcon: false,
                },
                item,
            );
        },
        [loadFleets, fleetTotalNumberMap],
    );
    const updateTreeDataNodeTitle = useCallback(
        (
            item: {
                title: JSX.Element;
                showIcon: boolean;
                switcherIcon: boolean;
            } & TreeData,
            data: TreeData,
        ) => {
            // @ts-ignore
            item.vOnlineNumber = data.vOnlineNumber;
            item.title =
                data.type === NodeType.OPERATION ? (
                    <OperationTreeNode baseInfo={data} instanceId={instanceId} showToTop={false} />
                ) : (
                    <GenerateTreeTitleV2
                        vehicleTreeRef={vehicleTreeRef.current}
                        hasBracket={false}
                        baseInfo={data}
                        type={data.type}
                     instanceId={instanceId}/>
                );
            // item.vStates = vStatesMapRef[item?.vId] || [];
            return item;
        },
        [loadFleets],
    );

    useRealtimeRTData(
        {
            ...reuseParam,
            reConvertTreeData2Node: convertTreeData2Node,
            reUpdateTreeDataNodeTitle: updateTreeDataNodeTitle,
        },
        {
            getCustomFleets,
            getCustomVehicles,
            getCustomUnGroupedTitle,
        },
        {
            vehicleDataModule: [VehicleDataModule.device],
            showToTop: false
        },
        instanceId
    );
    //添加防抖，防止上层逻辑重复触发
    const { run: _debouncedFormatQueryParams } = useDebounceFn(
        () => {
            formatQueryParams();
        },
        {
            wait: 30,
        },
    );
    useEffect(() => {
        if (!latestSelectedVehicleRef.current) return;
        const curVehicle = vehicleList.find(
            (item) => item.vId === latestSelectedVehicleRef.current?.vehicleId,
        );
        if (!curVehicle) return;
        /** 判断设备状态是否变化，变化则更新设备数据 */
        if (
            JSON.stringify(curVehicle.deviceList) !==
                JSON.stringify(latestSelectedVehicleRef.current.deviceList) &&
            latestSelectedDeviceInfo.current
        ) {
            const curDeviceId = latestSelectedDeviceInfo.current.deviceId;
            const curState = getDeviceStates(
                curVehicle.deviceList?.find(
                    (item) => item.deviceId === curDeviceId,
                ) as any,
                latestStateConfigRef.current,
            );
            dispatch({
                type: 'curSelectedDeviceState',
                payload: curState?.[0]?.stateId !== OFFLINE_STATE ? 1 : 0,
            });
            setSelectedVehicleInfo({
                ...latestSelectedVehicleRef.current,
                stateConfig: latestStateConfigRef.current,
                deviceList: getCompleteDevice(
                    latestSelectedVehicleRef.current.deviceList,
                    curVehicle.deviceList || [],
                ),
            });
        }
    }, [vehicleList]);

    useEffect(() => {
        // 手动切换了播放模式
        g_emmiter.on(CHANGE_PLAYMODE_MANUAL_MESSAGE_NAME, (newPlayMode) => {
            if (
                pageOrigin === PAGE_ORIGIN.alarmUploadEvidence ||
                pageOrigin === PAGE_ORIGIN.alarmPlayBack
            )
                return;
            if (newPlayMode === PLAY_MODE.image) {
                // 从视频模式切换到图片模式
                _debouncedFormatQueryParams();
            } else if (newPlayMode === PLAY_MODE.video) {
                // 从图片模式切换到视频模式,解决跳过来如果选择其他车辆，车辆id不会变，永远都是查询的跳转过来的车辆
                if (
                    queryInfo.from !== PAGE_ORIGIN.alarmUploadEvidence &&
                    queryInfo.from !== PAGE_ORIGIN.alarmPlayBack
                ) {
                    // setSelectedDate(undefined);
                } else {
                    _debouncedFormatQueryParams();
                }
                // formatQueryParams();
            }
        });
        return () => {
            g_emmiter.off(CHANGE_PLAYMODE_MANUAL_MESSAGE_NAME);
        };
    }, []);

    useEffect(() => {
        // 因为日期选择是所有情况都必须有的，并且是必填的，因此只需要   监听selectDateInfo来触发onChange事件
        _debouncedFormatQueryParams();
    }, [
        JSON.stringify(selectedVehicleInfo),
        JSON.stringify(selectedDeviceInfo),
        selectedDate,
        selectedChannel,
    ]);

    useEffect(() => {
        if (
            pageOrigin === PAGE_ORIGIN.alarmUploadEvidence ||
            pageOrigin === PAGE_ORIGIN.alarmPlayBack
        )
            return;
        // 切换播放播放模式也需要请求
        playMode && selectedDate && _debouncedFormatQueryParams();
    }, [playMode]);

    // 增加导出清除地图的操作的方法
    useImperativeHandle(ref, () => ({
        resetDate: () => {
            setSelectedDate(null);
        },
    }));
    // 格式化参数，将设置的参数抛给上层
    function formatQueryParams() {
        // 只有当所有的参数都通过校验都才触发onChange  已处理夏令时
        // 夏令时modify 设备回放日历选择
        // 28181设备，选中车辆、设备、通道、日期后再查询，其他设备保持设备选择后查询
        const condition28181 =
            latestSelectedVehicleRef.current &&
            latestSelectedDeviceInfo.current &&
            latestSelectedChannelRef.current &&
            latestSelectedDateRef.current;
        const conditionSimple =
            latestSelectedVehicleRef.current &&
            latestSelectedDeviceInfo.current &&
            latestSelectedDateRef.current;
        const condition =
            latestSelectedDeviceInfo.current &&
            check28181(latestSelectedDeviceInfo.current)
                ? condition28181
                : conditionSimple;
        if (condition) {
            const currentSelectedDate = latestSelectedDateRef.current;
            const startTime: number = timestampToZeroTimeStamp(
                moment(currentSelectedDate).startOf('day'),
                Number(getAppGlobalData('APP_USER_CONFIG')?.timeZone) || 0,
            );
            const endTime: number = timestampToZeroTimeStamp(
                moment(currentSelectedDate).endOf('day'),
                Number(getAppGlobalData('APP_USER_CONFIG')?.timeZone) || 0,
            );

            const params: ChangeParams = {
                vehicleInfo: pick(latestSelectedVehicleRef.current, [
                    'vehicleId',
                    'vehicleNumber',
                    'onlineState',
                    'deviceList',
                    'vehicleType',
                    'stateConfig',
                ]),
                deviceInfo: latestSelectedDeviceInfo.current,
                channel: latestSelectedChannelRef.current,
                startTime,
                endTime,
            };
            props?.onChange?.(params);
            // 当设置的参数满足所有条件时，清空路由中的vehicleId,fleetId,alarmTime,playTime信息
            if (
                queryInfo.from !== PAGE_ORIGIN.alarmUploadEvidence &&
                queryInfo.from !== PAGE_ORIGIN.alarmPlayBack
            ) {
                history.replace({
                    query: omit(queryInfo, [
                        'vehicleId',
                        'fleetId',
                        'deviceId',
                        'playTime',
                    ]),
                });
            }
        } else {
            props?.onChange?.(null);
        }
    }

    const getPrimaryDevice = (deviceList: any[]) => {
        const device =
            (deviceList || []).find(
                (p) => p.primaryType === DEVICE_PRIMARY_TYPE.main,
            ) || deviceList?.[0];
        return device;
    };

    // 选择车辆
    async function handleOnChangeVehicle(vehicleId: string) {
        // TODO 改造优化
        setSelectedDeviceInfo(null);
        setSelectedVehicleInfo(null);
        setSelectedDate(undefined);
        setSelectedChannel(undefined);
        if (!vehicleId && cliping) {
            dispatch({ type: 'cliping', payload: false });
            return;
        }
        const vehicleInfo = await getVehicleDetailWithEnabledChannels({
            vehicleId,
            fields: 'device,channel',
        });
        const vehicleOnline = vehicleIsOnline(vehicleInfo);
        // 只有在设备回放、融合回放下，在线的车辆才可以视频模式播放
        if (
            location.pathname.includes(PLAYBACK_PATHNAME.device) ||
            location.pathname.includes(PLAYBACK_PATHNAME.mix)
        ) {
            // 如果车辆离线，则设置为图片模式
            dispatch({
                type: 'playMode',
                payload: vehicleOnline ? PLAY_MODE.video : PLAY_MODE.image,
            });
        }
        dispatch({ type: 'vehicleOnlineState', payload: vehicleOnline });
        const curVehicle = latestVehicleRef.current.find(
            (item) => item.vId === vehicleId,
        );
        vehicleInfo.deviceList = getCompleteDevice(
            vehicleInfo.deviceList,
            curVehicle?.deviceList || [],
        );
        vehicleInfo.stateConfig = latestStateConfigRef.current;
        setSelectedVehicleInfo(vehicleInfo);

        const { deviceList } = vehicleInfo;

        const updateDeviceInfo = async (device: any) => {
            if (!device) {
                setSelectedDeviceInfo(null);
                dispatch({ type: 'curSelectedDeviceState', payload: 0 });
                return;
            }
            if (
                device.onlineState !== ON_LINE_STATE.online &&
                [PAGE_ORIGIN.mix, PAGE_ORIGIN.server].indexOf(pageOrigin) === -1
            ) {
                // 选中的设备是离线设备，且是在设备回放页面，需要设置为图片播放
                dispatch({
                    type: 'playMode',
                    payload: PLAY_MODE.image,
                });
            } else if (
                device.onlineState === ON_LINE_STATE.online &&
                [PAGE_ORIGIN.mix, PAGE_ORIGIN.server].indexOf(pageOrigin) === -1
            ) {
                await updateExceedFlow(device.authId);
            }
            dispatch({
                type: 'curSelectedDeviceState',
                payload: device.onlineState,
            });
            setSelectedDeviceInfo(device);
            setSelectedChannel(device.deviceChannelList?.[0]?.channelNo);
        };
        // 只有第一次加载的时候，才会通过路由中的参数进行回填
        if (firstLoad) {
            if (queryInfo?.deviceId) {
                // 如果路由中带了设备信息，并且设备存在，则默认选中所带的设备,否则默认选中第一个设备
                const device =
                    deviceList.find(
                        (d: any) => d.deviceId === queryInfo?.deviceId,
                    ) ||
                    getPrimaryDevice(deviceList) ||
                    null;
                if (device) {
                    updateDeviceInfo(device);
                    // 如果参数中带了报警时间，则需要默认选中相应的日期
                    if (queryInfo.alarmTime) {
                        const searchDate = zeroTimeStampToFormatTime(
                            queryInfo.alarmTime,
                            undefined,
                            'YYYY-MM-DD',
                        );
                        setSelectedDate(moment(searchDate));
                    }
                    // ft的Iframe嵌入会传入播放日期
                    if (queryInfo.playTime) {
                        const searchDate = zeroTimeStampToFormatTime(
                            queryInfo.playTime,
                            undefined,
                            'YYYY-MM-DD',
                        );
                        setSelectedDate(moment(searchDate));
                    }
                }
            } else {
                updateDeviceInfo(getPrimaryDevice(deviceList) || null);
            }
        } else {
            updateDeviceInfo(getPrimaryDevice(deviceList) || null);
            setSelectedDate(undefined);
        }
        setFirstLoad(false);
    }

    // 选择通道
    async function handleOnChangeChannel(value: any) {
        if (!value) {
            message.error(i18n.t('message', '请选择通道'));
            return;
        }
        setSelectedChannel(Number(value));
    }

    // 选择设备
    async function handleOnChangeDevice(value: any) {
        if (!value) {
            message.error(i18n.t('message', '请选择设备'));
            return;
        }

        const deviceId = value.split(',')[1];
        const deviceList = selectedVehicleInfo?.deviceList || [];
        const device = deviceList.find((d) => d.deviceId === deviceId);
        const isDevicePlayback =
            [PAGE_ORIGIN.mix, PAGE_ORIGIN.server].indexOf(pageOrigin) === -1;
        if (device.onlineState !== ON_LINE_STATE.online && isDevicePlayback) {
            // 选中的设备是离线设备，且是在设备回放页面，需要设置为图片播放
            dispatch({
                type: 'playMode',
                payload: PLAY_MODE.image,
            });
        } else if (
            device.onlineState === ON_LINE_STATE.online &&
            isDevicePlayback
        ) {
            await updateExceedFlow(device.authId);
        }

        dispatch({
            type: 'curSelectedDeviceState',
            payload: device.onlineState,
        });
        setSelectedDeviceInfo(device);
        setSelectedDate(undefined);
        // 选择设备时，默认选中第一个通道
        setSelectedChannel(device.deviceChannelList?.[0]?.channelNo);
    }
    const updateExceedFlow = async (authId: string) => {
        const exceedFlow = await isNeedControl(authId, CONTROL_RANGE.PLAY_BACK);
        // 选中的设备是在线设备，且是在设备回放页面，超出流量管控了，需要设置为图片播放
        if (exceedFlow) {
            dispatch({
                type: 'playMode',
                payload: PLAY_MODE.image,
            });
            dispatch({
                type: 'exceedFlow',
                payload: true,
            });
            message.warn({
                key: 'exceedFlowMessage',
                content: i18n.t('message', '流量使用超额，视频功能暂停使用。'),
            });
        } else {
            dispatch({
                type: 'exceedFlow',
                payload: false,
            });
        }
    };

    // 获取设备下拉框选项的key
    function getDeviceKey(device: any) {
        return `${device?.authId},${device?.deviceId},${device?.protocolType}`;
    }

    const onVehicleTreeLoad = () => {
        const { vehicleId, fleetId, alarmTime, playTime } = queryInfo;
        if (vehicleId && vehicleTreeRef.current) {
            vehicleTreeRef.current.positionVehicle(
                vehicleId,
                fleetId === 'undefined' ? null : fleetId
            );
            if (alarmTime) {
                const time = moment(
                    zeroTimeStampToFormatTime(
                        Number(alarmTime),
                        undefined,
                        'YYYY-MM-DD',
                    ),
                );
                setSelectedDate(time);
            } else if (playTime) {
                // FT iframe传入播放日期
                const time = moment(
                    zeroTimeStampToFormatTime(
                        Number(playTime),
                        undefined,
                        'YYYY-MM-DD',
                    ),
                );
                setSelectedDate(time);
            }
        }
    };
    const onTreeMounted = () => {
        // 由于做了数据加载完成才给设置treeData，所以有数据就代表加载完成了
        onVehicleTreeLoad();
    };

    const onceFn = useOnceFn(async () => {
        setTimeout(()=>onTreeMounted(), 0);
    });

    useEffect(() => {
        if(treeData.length && loaded) {
            onceFn();
        }
    }, [treeData.length, loaded]);
    useEffect(() => {
        if(selectedVehicleInfo && selectedVehicleInfo?.deviceList?.length > 1 && selectedDeviceInfo?.authId) {
            // 解决优化展示select数据还没有id闪动问题，fix-bug:96299
            setTimeout(() => {
                setCurrentDeviceValue(getDeviceKey(selectedDeviceInfo));
            }, 200);
        } else {
            setCurrentDeviceValue(undefined);
        }
    }, [selectedDeviceInfo?.authId]);

        const { run: onVisibleChange } = useDebounceFn(
            (treeData) => {
                const vIds = treeData
                    .filter((i) => i.type === 'vehicle')
                    .map((i) => i.data.id);
                onVisibleVehicleChange?.({
                    vIds,
                });
            },
            {
                wait: 500,
            },
        );

    const calcSelectDeviceStateInfo = useMemo(() => {
        const curState = getDeviceStates(
            selectedDeviceInfo,
            vehicleStateConfig,
        )?.[0];
        // 计算最新的车辆状态
        const onlineState =
            curState?.stateId === VEHICLE_STATES.OFFLINE_STATE ? 0 : 1;
        return {
            ...(selectedDeviceInfo || {}),
            onlineState,
        };
    }, [selectedDeviceInfo, vehicleStateConfig]);

    const getDeviceSelectOptions = (deviceList: any[]) => {
        return deviceList?.map((device: any, index: number) => {
            return (
                <Select.Option key={index} value={getDeviceKey(device)}>
                    <Tooltip
                        title={
                            device.deviceAlias
                                ? `${device.deviceAlias}(${device.deviceNo})`
                                : device.deviceNo
                        }
                        className="device-alias-option-tooltip-item"
                    >
                         <DeviceOnlineStatus vehicleId={latestSelectedVehicleRef.current!.vehicleId } deviceId={device.deviceId} />
                        {device.deviceAlias || device.deviceNo}
                    </Tooltip>
                </Select.Option>
            );
        });
    };

    // 通道选择下拉框
    const getChannelSelectOptions = (channelList: any[]) => {
        return channelList.map((channel: any, index: number) => {
            return (
                <Select.Option key={index} value={Number(channel.channelNo)}>
                    {channel.channelAlias}
                </Select.Option>
            );
        });
    };

    const calcMinWidth = () => {
        let backBtnWidth = '0px';
        if (location?.query?.showBackBtn) {
            backBtnWidth = '30px';
        }
        return `calc(100% - ${backBtnWidth})`;
    };

    const showQueryItemSmall = ()=>{
        if (location?.query?.hideOperateBtn){
            return false;
        }else {
            return selectedDeviceInfo ? check28181(selectedDeviceInfo) : false;
        }
    };

    return (
        <div
            className="page-playback-query-params-form"
        >
            {/*读取查询参数，展示返回按钮*/}
            {location?.query?.showBackBtn && <IconReturn style={{fontSize:16, marginRight: 14}} onClick={()=>{history.goBack();}} key="returnIcon" />}
            <div className="query-item-list" style={{width:calcMinWidth()}}>
                {
                    <div
                        className={classNames('query-item', {
                            'query-item-small': showQueryItemSmall(),
                        })}
                        style={location?.query?.hideOperateBtn ? { display:'none' }:{} }
                    >
                        {getCustomJsx(
                            props?.getTreeSelectBlock,
                            [
                                <VehicleTreeSelect
                                    key="playBlackTreeSelect"
                                    ref={vehicleTreeRef}
                                    vehicleId={selectedVehicleInfo?.vehicleId}
                                    onChange={handleOnChangeVehicle}
                                    disabled={
                                        cliping ||
                                        [
                                            PAGE_ORIGIN.alarmPlayBack,
                                            PAGE_ORIGIN.alarmUploadEvidence,
                                        ].includes(pageOrigin) ||
                                        (!isMonthQueryDone && selectedDeviceInfo)
                                    }
                                    treeData={treeData}
                                    // @ts-ignore
                                    loadFleets={loadFleets}
                                    loadedKeys={loadedKeys}
                                    vehicleStateConfig={vehicleStateConfig}
                                    fleetList={fleetList}
                                    vehicleList={vehicleList}
                                    instanceId={instanceId}
                                    getCustomFilter={getCustomFilter}
                                    getVehicleNumber={getVehicleNumber}
                                    treeDataStore={treeDataStore}
                                    onVisibleChange={onVisibleChange}
                                />,
                            ],
                            {
                                vehicleTreeRef,
                                vehicleId: selectedVehicleInfo?.vehicleId,
                                onChange: handleOnChangeVehicle,
                                disabled:
                                    cliping ||
                                    [PAGE_ORIGIN.alarmPlayBack, PAGE_ORIGIN.alarmUploadEvidence].includes(
                                        pageOrigin,
                                    ) ||
                                    (!isMonthQueryDone && selectedDeviceInfo),
                                treeData: treeData,
                                // @ts-ignore
                                loadFleets: loadFleets,
                                loadedKeys: loadedKeys,
                                vehicleStateConfig: vehicleStateConfig,
                                fleetList: fleetList,
                                vehicleList: vehicleList,
                                vStatesMap: vStatesMap,
                                onTreeMounted: onTreeMounted
                            },
                        )}
                    </div>
            }
            {/* 当设备数量大于1个时才展示设备选择框 */}
            {selectedVehicleInfo && selectedVehicleInfo?.deviceList?.length > 1 && (
                <div
                    className={classNames('query-item', {
                        'query-item-small': showQueryItemSmall()
                    })}
                >
                    <Select
                        style={{ width: '100%' }}
                        className="select-device"
                        dropdownClassName="select-device-drop"
                        placeholder={i18n.t('message', '请选择设备')}
                        value={currentDeviceValue}
                        disabled={!selectedVehicleInfo || cliping || !isMonthQueryDone}
                        onChange={handleOnChangeDevice}
                        getPopupContainer={(dom) => dom.parentElement as HTMLElement}
                    >
                        {getDeviceSelectOptions(selectedVehicleInfo.deviceList || [])}
                    </Select>
                </div>
            )}
            {/* 28181设备，显示通道选择框 */}
            {selectedDeviceInfo &&
                check28181(selectedDeviceInfo) &&
                selectedDeviceInfo.deviceChannelList?.length > 1 && (
                    <div
                        className={classNames('query-item', {
                            'query-item-small': showQueryItemSmall()
                        })}
                    >
                        <Select
                            style={{ width: '100%' }}
                            className="select-device"
                            dropdownClassName="select-device-drop"
                            placeholder={i18n.t('message', '请选择通道')}
                            value={selectedChannel}
                            disabled={!selectedDeviceInfo || cliping || !isMonthQueryDone}
                            onChange={handleOnChangeChannel}
                            getPopupContainer={(dom) => dom.parentElement as HTMLElement}
                        >
                            {getChannelSelectOptions(selectedDeviceInfo.deviceChannelList || [])}
                        </Select>
                    </div>
                )}
            <div
                className={classNames('query-item', {
                    'query-item-small': showQueryItemSmall()
                })}
            >
                <DeviceMonthPicker
                    value={selectedDate}
                    vehicleInfo={selectedVehicleInfo}
                    deviceInfo={calcSelectDeviceStateInfo}
                    channel={selectedChannel}
                    onChange={(v: Moment) => setSelectedDate(v)}
                    playMode={playMode}
                    disabled={!selectedDeviceInfo || cliping}
                    onReset={async (type: 'image' | 'video') => {
                        // if (type === 'video') {}
                    }}
                />
                </div>
            </div>
        </div>
    );
};

export default forwardRef(QueryForm);
