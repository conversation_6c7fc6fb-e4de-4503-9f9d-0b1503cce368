import React, {
    useContext,
    useEffect,
    useImperativeHandle,
    useRef,
    useState,
} from 'react';
import { exportExcel } from '@/service/import-export';
import { getTravelSimpleGps } from '@/service/track-playback';
import { useDebounceFn, useSize } from '@streamax/hooks';
import { message, Modal, Radio, Tooltip } from '@streamax/poppy';
import {
    IconFenceFill,
    IconFleet,
    IconFleetFill,
    IconTrajectory02,
    IconTrajectory02Fill,
} from '@streamax/poppy-icons';
import {
    Auth,
    getAppGlobalData,
    g_emmiter,
    i18n,
    utils,
} from '@base-app/runtime-lib';
import { useHistory, useLocation } from '@base-app/runtime-lib/core';
// @ts-ignore
import { getBaseAlarmList } from '@/service/alarm';
import { getAlarmName } from '@/utils/lang';
import { useInit, usePlay, useRenderGps, useRenderMarkers } from './mapHooks';
import { ALL_PLAYBACK_PAGE_CODE, PLAYBACK_PAGE_CODE } from '../../constant';
import AlarmChooseTool from '../AlarmChooseTool';
import classNames from 'classnames';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { MapOperateButtons, PlayBackMapReuse } from '@/types/pageReuse/playbackDevice';
import { getCustomJsx } from '@/utils/pageReuse';
import type { AlarmData, ChangeParams } from '../../types';
import { multiAuthCheck } from '../../utils';
import './index.less';
import { MapToolCustom } from '@/types/pageReuse/pageReuseBase';
/**列表页table定制复用 */
export type DevicePlayBackMapShareProps = MapOperateButtons;

const {
    formator: { zeroTimeStampToFormatTime, timestampToZeroTimeStamp },
} = utils;

export type MapProps = DevicePlayBackMapShareProps &  PlayBackMapReuse &
    MapToolCustom & {
        onTimeChange: (time: any) => void;
        getCurrentPlayTime: () => number;
        queryParams: ChangeParams | undefined;
        hoverTime: number | null;
        showTimeRange: number[];
        cliping: boolean;
        playMode: 'video' | 'image';
        setShowAlarmListDataStore: (
            AlarmData: AlarmData[],
            allAlarmList: AlarmData[],
        ) => void;
        currentPlayState: string;
    };

interface AlarmTypes {
    openState: boolean;
    alarmTypeList: number[];
}

export interface MapRefProps {
    removeToolBarActions: () => void;
}

const MapContainer = React.forwardRef<MapRefProps, MapProps>((props, ref) => {
    const {
        onTimeChange,
        getCurrentPlayTime,
        queryParams,
        hoverTime,
        showTimeRange,
        cliping,
        playMode,
        currentPlayState,
        setShowAlarmListDataStore,
        getMapOperateButtons,
        getUseMapLayerOptions,
        onMapInstance
    } = props;

    const history = useHistory();
    const location: any = useLocation();
    const { startTime, endTime, deviceInfo, vehicleInfo } = queryParams || {};
    const [lockActive] = useState<boolean>(true);
    const [traceActive] = useState<boolean>(true);
    const [gpsData, setGpsData] = useState<any[]>([]);
    const [warningPoints, setWarningPoints] = useState<any[]>([]);
    const [fenceVisible, setFenceVisible] = useState<boolean>(false);
    const [selectedRoleType, setSelectedRoleType] = useState<number>(1);
    const [alarmTypes, setAlarmTypes] = useState<AlarmTypes>({} as AlarmTypes);
    const {
        L,
        map,
        warningMarkers = [],
        isInitDone,
        mapSearchSelected,
        removeToolBarActions,
    } = useInit(getUseMapLayerOptions);
    const mapDomRef = useRef<any>();
    const { height, width } = useSize(mapDomRef) || { width: 0, height: 0 };


    useEffect(() => {
        if (onMapInstance) {
            onMapInstance(map);
        }
    }, [map, onMapInstance]);

    useEffect(() => {
        map?.invalidateSize();
    }, [height, width]);
    const filterAlarmData = () => {
        return warningPoints.filter((item: any) => {
            if (Object.keys(alarmTypes)?.length && alarmTypes.openState) {
                return (
                    alarmTypes.alarmTypeList.findIndex(
                        (type) => type == item.alarmType,
                    ) > -1
                );
            } else {
                return false;
            }
        });
    };

    // 增加导出清除地图的操作的方法
    useImperativeHandle(ref, () => ({
        removeToolBarActions: () => removeToolBarActions(),
    }));
    const { gpsLine, playbackLine } = useRenderGps({
        map,
        L,
        mapSearchSelected,
        props: {
            gpsData,
            hoverTime,
            cliping,
            onTimeChange,
        },
    });
    useRenderMarkers({
        map,
        L,
        warningMarkers,
        props: {
            warningPoints: filterAlarmData(),
            vehicleId: vehicleInfo?.vehicleId,
            deviceId: deviceInfo?.deviceId,
        },
    });

    useEffect(() => {
        if (playMode === 'video') {
            setShowAlarmListDataStore(filterAlarmData(), warningPoints);
        } else {
            setShowAlarmListDataStore([], []);
        }
    }, [warningPoints, alarmTypes, playMode]);

    const currentPlayTime = getCurrentPlayTime();
    usePlay({
        map,
        L,
        gpsLine,
        playbackLine,
        props: {
            gpsData: gpsData.sort((a: any, b: any) => a.gpsTime - b.gpsTime),
            traceActive,
            lockActive,
            currentPlayTime,
        },
        vehicleInfo,
        currentPlayState,
    });

    // 获取报警列表
    const getAlarmList = async () => {
        const vehicleId = vehicleInfo?.vehicleId;
        if (vehicleId && startTime && endTime) {
            const data = await getBaseAlarmList({
                vehicleId,
                startTime,
                endTime,
            }).catch(() => {
                setWarningPoints([]);
            }); //
            if (data) {
                data.forEach(async (item: any) => {
                    const { lat, lng } = { lat: item.lat, lng: item.lng };
                    const alarmTypeName = await getAlarmName(
                        item.alarmType,
                        true,
                    );
                    item.warningName = i18n.t(
                        'message',
                        // @ts-ignore
                        // eslint-disable-next-line @streamax/no-template-in-i18n
                        `${item.alarmType} error`,
                        alarmTypeName,
                    );
                    item.happenTime = zeroTimeStampToFormatTime(item.startTime);
                    item.lng = lng / 1e6;
                    item.lat = lat / 1e6;
                });
            }
            setWarningPoints(data || []);
        }
    };

    // 获取 gps 列表
    const getGpsData = async () => {
        const gpsStartTime = startTime;
        const gpsEndTime = endTime;
        // 102208 按产品要求gps的展示时间范围始终保持展示全天，不随时间轴的变化而改变
        // if (showTimeRange?.length) {
        //     gpsStartTime = showTimeRange[0];
        //     gpsEndTime = showTimeRange[1];
        // }
        const vehicleId = vehicleInfo?.vehicleId;
        if (vehicleId) {
            let gpsData: any = [];
            const data =
                (await getTravelSimpleGps({
                    vehicleId,
                    startTime: gpsStartTime,
                    endTime: gpsEndTime,
                }).catch(() => {
                    setGpsData([]);
                })) || [];
            data.forEach((item: string) => {
                const [time, valLng, valLat, speed, angle] = item
                    .split(',')
                    .map((val: string) => Number(val));
                const { lat, lng } = { lat: valLat, lng: valLng };
                if (lng && lat) {
                    gpsData.push({
                        lng,
                        lat,
                        speed: speed / 10,
                        angle,
                        gpsTime: time,
                    });
                }
            });
            gpsData = gpsData.filter((p: any) => !(p.lat == 0 && p.lng == 0));
            setGpsData(gpsData);
        }
    };

    const handleResize = () => {
        if (map) {
            map.invalidateSize();
        }
    };
    const carMarkerRef = useRef();
    useEffect(() => {
        if(playbackLine) {
            playbackLine.on('update', (e: any) => {
                carMarkerRef.current = e.latlng;
            });
        }
    }, [playbackLine]);

    const rebackCurrentPosition = () => {
        const gpsList = gpsData.sort((a: any, b: any) => a.gpsTime - b.gpsTime);
        if (gpsList && gpsList.length > 0) {
            // map.setZoom(12);
            // const passedLatLngArr: any = [];
            // const unpassedLatLngArr: any = [];
            // const currentTime = getCurrentPlayTime();
            // gpsList.forEach((item: any) => {
            //     const latlng = L?.latLng(
            //         item.lat / 1000000 || 0,
            //         item.lng / 1000000 || 0,
            //     );
            //     if (item.gpsTime <= currentTime) {
            //         passedLatLngArr.push(latlng);
            //     } else {
            //         unpassedLatLngArr.push(latlng);
            //     }
            // });
            // if (passedLatLngArr.length > 0) {
            //     unpassedLatLngArr.unshift(
            //         passedLatLngArr[passedLatLngArr.length - 1],
            //     );
            // }
            // let carPosition: any = null;
            // if (passedLatLngArr.length > 0) {
            //     carPosition = passedLatLngArr[passedLatLngArr.length - 1];
            // } else if (unpassedLatLngArr.length > 0) {
            //     carPosition = unpassedLatLngArr[0];
            // }
            // 设置地图中心
            if (carMarkerRef.current || gpsLine?.getLatLngs()?.[0]) {
                map?.setView(carMarkerRef.current ?? gpsLine?.getLatLngs()?.[0]);
                // if (
                //     passedLatLngArr.length == 0 ||
                //     unpassedLatLngArr.length < 2
                // ) {
                //     // 如果没有已行驶路线  直接设置车辆到对应经纬度
                //     map?.setView(carPosition);
                // } else {
                //     // @ts-ignore
                //     map?.setView(carMarker?.getLatLng?.());
                // }
            }
        } else {
            message.error(i18n.t('message', '没有查询到gps轨迹数据'));
        }
    };

    const handleSelectedRoleType = (e: any) => {
        const newType = e.target.value;
        setSelectedRoleType(newType);
    };

    const onFenceExport = () => {
        if (deviceInfo?.deviceId && startTime) {
            gpsData.length == 0
                ? message.warning(i18n.t('message', '无数据'))
                : setFenceVisible(true);
        } else {
            message.warning(i18n.t('message', '请选择设备及日期'));
        }
    };

    const fenceHandleOk = () => {
        let gpsStartTime = startTime;
        let gpsEndTime = endTime;
        if (showTimeRange) {
            gpsStartTime = showTimeRange[0];
            gpsEndTime = showTimeRange[1];
        }
        selectedRoleType == 1
            ? history.push(
                  `/fence/speed/add?vehicleId=${vehicleInfo?.vehicleId}&startTime=${gpsStartTime}&endTime=${gpsEndTime}`,
              )
            : history.push(
                  `/fence/time/add?vehicleId=${vehicleInfo?.vehicleId}&startTime=${gpsStartTime}&endTime=${gpsEndTime}`,
              );
    };

    const fetchData = () => {
        getAlarmList();
        getGpsData();
    };

    const { run: debounceFetchData } = useDebounceFn(fetchData, { wait: 500 });

    useEffect(() => {
        if (deviceInfo?.deviceId && startTime && isInitDone) {
            debounceFetchData();
        } else {
            setGpsData([]);
            setWarningPoints([]);
        }
    }, [deviceInfo?.deviceId, startTime, isInitDone]);

    useEffect(() => {
        removeToolBarActions();
    }, [deviceInfo?.deviceId, startTime, isInitDone]);

    useEffect(() => {
        const token = g_emmiter.on('playback.resize', handleResize);
        return () => {
            // @ts-ignore
            g_emmiter.off('playback.resize', token);
        };
    }, [map]);

    // 轨迹导出
    const handleExportTrack = () => {
        if (queryParams?.startTime) {
            const exportDate = timestampToZeroTimeStamp(queryParams?.startTime);
            exportExcel({
                serviceCode: 'ae64300c89cd4e21ab9c5258b9a61a26',
                isAsync: true,
                excelType: 'XLSX',
                fileName: `${vehicleInfo?.vehicleNumber}_${i18n.t(
                    'name',
                    '轨迹',
                )}_${exportDate}`,
                sheetQueryParams: [
                    {
                        sheetName: 'gpsTrajectory',
                        excelHeaders: [
                            {
                                columnName: 'gpsId',
                                title: i18n.t('name', '序号'),
                                isOption: false,
                                index: 0,
                            },
                            {
                                columnName: 'gpsTime',
                                title: i18n.t('name', '时间'),
                                isOption: false,
                                index: 1,
                            },
                            {
                                columnName: 'lng',
                                title: i18n.t('name', '经度'),
                                isOption: false,
                                index: 2,
                            },
                            {
                                columnName: 'lat',
                                title: i18n.t('name', '纬度'),
                                isOption: false,
                                index: 3,
                            },
                        ],
                        queryParam: {
                            param: {
                                vehicleIds: queryParams?.vehicleInfo?.vehicleId,
                                startTime,
                                endTime,
                                complexSort: 'orderBy gpsTime asc',
                                page: 1,
                                pageSize: 99999999,
                                timeZoneOffset: Number(
                                    getAppGlobalData('APP_USER_CONFIG')
                                        ?.timeZone,
                                ),
                            },
                        },
                    },
                ],
            })
                .then(() => {
                    message.success(
                        i18n.t(
                            'message',
                            '导出成功，请到个人中心中查看导出详情',
                        ),
                    );
                })
                .finally(() => {});
        } else {
            message.warning(i18n.t('message', '请选择设备及日期'));
        }
    };

    // 更新报警类型展示
    const onToolsValueChange = (toolValues: AlarmTypes) => {
        setAlarmTypes(toolValues);
    };
    const viewButton = () => {
        return (
            <Tooltip
                key={'viewButton'}
                placement="left"
                title={i18n.t('action', '查看车辆')}
            >
                <a
                    onClick={() =>
                        deviceInfo?.deviceId && rebackCurrentPosition()
                    }
                >
                    <IconFleetFill />
                </a>
            </Tooltip>
        );
    };
    const exportButton = () => {
        return (
            <Tooltip
                key={'exportButton'}
                placement="left"
                title={i18n.t('action', '导出轨迹')}
            >
                <a onClick={() => deviceInfo?.deviceId && handleExportTrack()}>
                    <IconTrajectory02Fill />
                </a>
            </Tooltip>
        );
    };

    const fenceButton = () => {
        const authCode = multiAuthCheck(
            ALL_PLAYBACK_PAGE_CODE.map((item) => `${item}@action:save.route`),
            'code',
        )
        return(
        <Auth code={ String(authCode) } key={'fenceButton'}>
            <Tooltip
                placement="left"
                title={i18n.t('action', '导出电子围栏')}
            >
                <a onClick={() => deviceInfo?.deviceId && onFenceExport()}>
                    <IconFenceFill />
                </a>
            </Tooltip>
        </Auth>
        )
    };
    const filterButton = () => {
        return (
            <a key={'filterButton'}>
                <AlarmChooseTool
                    disabled={!deviceInfo?.deviceId}
                    onToolsValueChange={onToolsValueChange}
                />
            </a>
        );
    };

    const mapOperateButtons =location?.query?.hideOperateBtn ?
        [
            viewButton(),
        ]:
        [viewButton(),
            exportButton(),
            fenceButton(),
            filterButton()
        ];

    return (
        <div className={classNames('map-wrapper', { cliping })}>
            <div id="map-dom" ref={mapDomRef} />
            <div
                className={`btns-wrapper  ${
                    !deviceInfo?.deviceId ? 'disabled' : ''
                }`}
            >
                {getCustomJsx(
                    getMapOperateButtons,
                    mapOperateButtons,
                    {
                        startTime,
                        endTime,
                        showTimeRange,
                        deviceInfo,
                        vehicleInfo,
                        onToolsValueChange,
                        onFenceExport,
                        handleExportTrack,
                        rebackCurrentPosition,
                    },
                )}

                <Modal
                    title={i18n.t('name', '电子围栏类型选择')}
                    okText={i18n.t('name', '下一步')}
                    visible={fenceVisible}
                    onOk={fenceHandleOk}
                    onCancel={() => setFenceVisible(false)}
                >
                    <p>{i18n.t('name', ' 将此轨迹导出为：')}</p>
                    <Radio.Group
                        onChange={handleSelectedRoleType}
                        defaultValue={1}
                    >
                        <Radio value={1}>{i18n.t('name', '限速线路')}</Radio>
                        <Radio value={2}>{i18n.t('name', '限时线路')}</Radio>
                    </Radio.Group>
                </Modal>
            </div>
        </div>
    );
});

export default MapContainer;
