import React, { useEffect, useRef, useState } from 'react';
import { omit } from 'lodash';
import moment from 'moment';
import classNames from 'classnames';
import {
    i18n,
    utils,
    getAppGlobalData,
    useSystemComponentStyle,
    StarryAbroadFormItem as AFormItem
} from '@base-app/runtime-lib';
import { useHistory, useLocation } from '@base-app/runtime-lib/core';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { videoCut } from '@/service/evidence';
import { handleAlarmRecord } from '@/service/alarm';
import {
    Modal,
    Row,
    Col,
    Form,
    Input,
    Button,
    TimePicker,
    Radio,
    message,
    Select,
    Badge,
    Segmented,
    Divider,
    Space,
    Tooltip,
    Switch,
    Checkbox,
} from '@streamax/poppy';
import VideoTimeLength from '../VideoTimeLength';
import {
    StreamTypeEnum,
    StoreTypeEnum,
    PickFrameEnum,
    getPriorityOptions,
    getStoreTypeMap,
    PLAY_MODE,
} from '../../constant';
import useStreamTypeAndTime from './useStreamTypeAndTime';
import type { Moment } from 'moment';
import './index.less';
import AllCheckbox from '@/components/AllCheckbox';
import { checkFieldSpace, getOffsetTimeByTimeStamp } from '@/utils/commonFun';
import { CONTROL_RANGE, isNeedControl } from '@/utils/flow';
import { PAGE_ORIGIN } from '../../playback-device/constant';
import usePageOrigin from '../../playback-device/hooks/usePageOrigin';
import { vehicleIsOnline } from '../../playback-device/utils';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import DefaultIconInformation from '@/components/DefaultIncoInformation';
import { check28181 } from '@/utils/28181utils';
import { PickFrameModelMap, checkboxOptions } from '@/utils/constant';
const { getLocalMomentByZeroTimeStamp } = utils.formator;

// 自适应下载码流类型
const ANYONE = 3;
// streamType为ANYONE下有效，0 主码流优先 1 子码流优先
const getStreamPriorityMap = {
    [StreamTypeEnum.MAJOR]: 0,
    [StreamTypeEnum.MINOR]: 1,
};

type PlayMode = keyof typeof PLAY_MODE;
interface Option {
    label: string;
    value: number;
}
interface DeviceItem {
    deviceId: string;
    authId: string;
    protocolType: number;
    deviceAbility: string;
}
interface VideoEditModel {
    visible: boolean;
    vehicleInfo: {
        vehicleId: string;
        vehicleNumber: string;
        deviceList: DeviceItem[];
    };
    selectedDate: string; // 日期
    channelList: any[];
    clipVideoTime: {
        startTime: number; // 开始时间戳，秒级
        videoLength?: number; // 视频时长，秒级 默认60
    };
    playMode?: PlayMode; // 图片播放、视频播放，默认视频
    playType?: 'server' | 'mixPlay' | 'device';
    streamType?: number;
    storeType?: number;
    havePickFrame: boolean;
    onCloseModal: () => void;
    deviceInfo?: DeviceItem;
    isDeviceAbilityAnalysis?: boolean; // 是否以设备能力进行解析码流。默认是
}

const DEFAULT_CLIP_TIME = 60 * 60; // 默认剪辑一分钟
const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const storeTypeMap = getStoreTypeMap();
const priorityOptions = getPriorityOptions();

const timeFormat = getAppGlobalData('APP_USER_CONFIG')?.timeFormat;
const { zeroTimeStampToFormatTime, timestampToZeroTimeStamp, getSummerTimeOffsetByTime } =
    utils.formator;
const fileTypes = [
    { fileType: 1, haveChannel: true },
    { fileType: 2, haveChannel: false },
    { fileType: 3, haveChannel: false },
    { fileType: 4, haveChannel: false },
];

const VideoEdit: React.FC<VideoEditModel> = (props) => {
    const {
        playMode = 'video',
        visible,
        vehicleInfo,
        selectedDate,
        channelList,
        streamType = StreamTypeEnum.MINOR,
        storeType,
        clipVideoTime,
        havePickFrame,
        onCloseModal,
        deviceInfo,
        playType,
        isDeviceAbilityAnalysis,
    } = props; // 页面来源
    const origin = usePageOrigin();
    const validatorVideoName = utils.validator.validateBaseVehicleNumber;
    const { vehicleId, vehicleNumber, deviceList = [] } = vehicleInfo;
    const deviceOnline = vehicleIsOnline(deviceInfo);
    // 如果外部有传入设备，则剪辑以外部传入的为准
    const { deviceId, authId, protocolType = 1, deviceAbility } = deviceInfo || deviceList[0] || {};
    const { startTime: initStartTime, videoLength: initVideoLength = DEFAULT_CLIP_TIME } =
        clipVideoTime;
    const defaultVideoTimeLength = Math.round(initVideoLength);

    const [moreSetting, setMoreSetting] = useState<boolean>(false);
    const [channelOptions, setChannelOptions] = useState<Option[]>([]);
    const [confirmLoading, setConfirmLoading] = useState(false);
    const [dstTime, setDistTime] = useState(0); //记录是否开启夏令时
    const [storeTypeOptions, setStoreTypeOptions] = useState(
        Object.values(storeTypeMap).slice(0, 2),
    );
    const [timeStartStep, setTimeStartStep] = useState<number>(10);
    const [autoDown, setAutoDown] = useState(true);

    const { isAbroadStyle } = useSystemComponentStyle();

    // 初始化是否完成
    // 第一次打开时，应该使用外部传入的存储器和码流类型
    // 后面再切换存储器时，按照默认设置进行选择
    const playerRef: any = useRef();
    const location: any = useLocation();
    const history: any = useHistory();
    const [form] = Form.useForm();
    const alarmSourceType = location?.query?.alarmSourceType;
    const isPlaybackServe = playType === 'server' || location.pathname.includes('playback-serve');
    
    const {
        unReport,
        streamType: currentStreamType,
        timeNodes,
        initFinish,
        streamTypeOptions,
        initStreamOptions,
        onVideoLengthChange,
        onStreamTypeChange,
        deviceAbilityMajor,
        deviceAbilityMinor,
        deviceAbilityPickFrame,
    } = useStreamTypeAndTime({
        playMode,
        deviceAbility,
        havePickFrame: !isPlaybackServe && havePickFrame,
        channelList,
        defaultVideoTimeLength,
        defaultStreamType: streamType,
        isDeviceAbilityAnalysis,
        alarmSourceType
    });
    

    // 确认
    const handleOk = async () => {
        setConfirmLoading(true);
        if (
            origin === PAGE_ORIGIN.device ||
            origin === PAGE_ORIGIN.other ||
            origin === PAGE_ORIGIN.alarmPlayBack ||
            origin === PAGE_ORIGIN.alarmUploadEvidence
        ) {
            let typeCode: number;
            if (origin === PAGE_ORIGIN.alarmUploadEvidence) {
                typeCode = CONTROL_RANGE.EVIDENCE_DOWNLOAD; // 证据下载
            } else {
                typeCode = CONTROL_RANGE.VIDEO_CUT; // 视频剪辑
            }
            const needControlCut = await isNeedControl(authId, typeCode);
            if (needControlCut) {
                message.warn(i18n.t('message', '流量使用超额，请处理流量限制。'));
            }
        }
        form.validateFields().then(async (values) => {
            const {
                evidenceName,
                priority,
                streamType,
                startTime,
                storeType,
                channelNoList,
                videoLength,
                pickFrameModel,
                networkType, // 网络模式
            } = values;
            if (
                origin === PAGE_ORIGIN.mix &&
                storeType !== 3 &&
                deviceOnline &&
                playType !== 'server'
            ) {
                // 融合回放、设备存储器、在线
                const needControlCut = await isNeedControl(authId, CONTROL_RANGE.VIDEO_CUT);
                if (needControlCut) {
                    message.warn(i18n.t('message', '流量使用超额，请处理流量限制。'));
                }
            }
            const startTimeStr = moment(startTime).format('HH:mm:ss');
            let startTimeStamp = moment
                .utc(moment(`${selectedDate} ${startTimeStr}`).format('YYYY-MM-DD HH:mm:ss'))
                .unix();
            // 反向计算未做偏移时的真正时间戳
            if (dstTime) {
                startTimeStamp = startTimeStamp - dstTime;
            }
            // 必须传0时区时间，公共方法内转化
            startTimeStamp = timestampToZeroTimeStamp(
                startTimeStamp,
                undefined,
                dstTime ? true : false,
            );
            const endTimeStamp = startTimeStamp + videoLength;
            const storageType = isPlaybackServe ? undefined : storeType;
            let pickFrameParam; // 当为设备上传页面和不是抽帧时，不传
            let streamTypeParam = streamType;
            let alarmParams: any;
            let evidenceType: number;

            if (videoLength > 60 * 60 + 59) {
                message.error(i18n.t('message', '剪辑时长超过1小时'));
                return;
            }
            if (location?.query?.alarmInfo) {
                alarmParams = JSON.parse(decodeURIComponent(location?.query?.alarmInfo));
            }
            if (alarmParams) {
                evidenceType = Number(location?.query?.alarmSourceType) === 2 ? 9 : 1;
            } else {
                evidenceType = isPlaybackServe ? 5 : 2;
            }

            if (streamType === StreamTypeEnum.PICK_FRAME) {
                    // 抽帧的高清标清，按pickFrameModel字段选择的来
                const haveMajor = streamTypeOptions.some(
                    ({ value }) => value === StreamTypeEnum.MAJOR,
                );
                const haveMinor = streamTypeOptions.some(
                    ({ value }) => value === StreamTypeEnum.MINOR,
                );
                pickFrameParam = {
                    pickFrame: PickFrameEnum.PickFrame,
                };
                // 有主码流选项，或者柱子码流选项都没有下载主码流，只有子码流选项，下载子码流
                const majorOrMinor = haveMajor || !haveMinor ? StreamTypeEnum.MAJOR : StreamTypeEnum.MINOR;
                // 有pickFrameModel字段，取该字段值，没有则取majorOrMinor
                // 选择的不是auto，则按选择的值传参，否则按原逻辑
                streamTypeParam = pickFrameModel !== PickFrameModelMap[0].value && pickFrameModel ? pickFrameModel : majorOrMinor;
            }

            const resultFileTypes = check28181(deviceInfo || {}) ? fileTypes.filter(i => i.fileType === 1) : fileTypes;

            const paramList = resultFileTypes.map(({ fileType, haveChannel }) => {
                const params: any = {
                    fileType,
                    streamType: (unReport && autoDown) || pickFrameModel === PickFrameModelMap[0].value ? ANYONE : streamTypeParam,
                    streamPriority:
                        (unReport && autoDown) || pickFrameModel === PickFrameModelMap[0].value
                            ? getStreamPriorityMap[streamTypeParam]
                            : undefined,
                    storageType,
                }; 
                if (haveChannel) {
                    params.channelNoList = channelNoList;
                }
                return params;
            });
            let param: any = {
                evidenceName,
                evidenceType,
                sourceType: '2',
                vehicleId,
                deviceId,
                authId,
                fileParam: {
                    priority,
                    startTime: startTimeStamp,
                    endTime: endTimeStamp,
                    protocolType,
                    networkType: networkType?.at(-1) || 0, // 3G/4G/5G >  WIFI  > 有线网络 具有优先级，不用全传
                    needScreenSnap: 0,
                    evtId: location?.query?.evtId || undefined,
                    paramList,
                    pickFrameParam,
                },
                alarmInfo: alarmParams ? { ...alarmParams } : undefined,
                prefixUrl: window.location.origin,
            };
            if (location?.query?.driverId) {
                param.driverId = location?.query?.driverId;
            }
            // FT iframe嵌入新增剪辑字段
            if (location.query.from === 'other') {
                param.fileParam.transparentParameter = location.query?.dparam;
            }
            videoCut(param)
                .then((rs) => {
                    onCloseModal?.();
                    if (alarmParams) {
                        message.success(i18n.t('message', '视频剪辑成功,已存放在证据列表'));
                    } else {
                        message.success(i18n.t('message', '视频剪辑成功,已存放在视频中心'));
                    }
                    // 从报警详情跳转过来的，剪辑成功后需要提交处理记录
                    if (location?.query?.alarmId && origin === PAGE_ORIGIN.alarmUploadEvidence) {
                        handleAlarmRecord([
                            {
                                alarmId: location?.query?.alarmId,
                                type: 'saas.search.alarm.handle.type.uploadevidence',
                                content: rs.evidenceId,
                            },
                        ]);
                        history.replace({
                            query: omit(location?.query, ['alarmId', 'alarmInfo']),
                        });
                    }
                })
                .finally(() => {
                    setConfirmLoading(false);
                });
        }).catch(() => {
            setConfirmLoading(false);
        });
    };
    const initMemoryOptions = (channel: number[]) => {
        const memoryOption = [];
        let masterFlag = false;
        let backupFlag = false;
        let centerFlag = false;
        let defaultMemoryFlag = false;
        channelList.map((val) => {
            if ((channel || []).indexOf(val.channel) > -1) {
                val.videoList.map((video: any) => {
                    if (video.storeType === 'MASTER') {
                        masterFlag = true;
                    } else if (video.storeType === 'BACKUP') {
                        backupFlag = true;
                    } else if (video.storeType === 'CENTER') {
                        centerFlag = true;
                    }
                });
                const defaultMemoryList =
                    val.videoList.filter(
                        (p: any) => p.storeType === playerRef.current?.currentStoreType,
                    ) || [];
                if (defaultMemoryList.length > 0) defaultMemoryFlag = true;
            }
        });
        masterFlag && memoryOption.push(storeTypeMap.MASTER);
        backupFlag && memoryOption.push(storeTypeMap.BACKUP);
        centerFlag && memoryOption.push(storeTypeMap.CENTER);
        setStoreTypeOptions(memoryOption);

        let memoryValue =
            backupFlag && (playerRef.current?.currentStoreType === 'BACKUP' || !masterFlag)
                ? StoreTypeEnum.BACKUP
                : StoreTypeEnum.MASTER;
        if (defaultMemoryFlag) {
            memoryValue =
                playerRef.current?.currentStoreType === 'BACKUP'
                    ? StoreTypeEnum.BACKUP
                    : StoreTypeEnum.MASTER;
        }
        if (centerFlag) memoryValue = StoreTypeEnum.CENTER;
        // 获取当前选中的存储器的值
        let defaultStoreType: number;
        if (memoryValue !== StoreTypeEnum.CENTER) {
            defaultStoreType = initFinish ? memoryValue : storeType || memoryValue;
        } else {
            defaultStoreType = memoryValue;
        }
        if (storeType && memoryOption.find((item) => item.value === storeType)) {
            defaultStoreType = storeType;
        }
        form.setFieldsValue({
            storeType: defaultStoreType,
        });
        initStreamOptions();
    };

    const formValuesChange = (values: any) => {
        // 如果是图片模式,则不需要重新计算码流类型数据
        if (playMode === PLAY_MODE.image) return;
        if (values.channelNoList && !location.pathname.includes('playback-serve')) {
            initMemoryOptions(values.channelNoList);
        }
    };

    const onStartTimeChange = (val: Moment | null) => {
        const timeStr = moment(val).format('HH:mm:ss');
        const timeStamp = timestampToZeroTimeStamp(moment(`${selectedDate} ${timeStr}`));
        form.setFieldsValue({
            startTime: moment(zeroTimeStampToFormatTime(timeStamp), `${timeFormat} hh:mm:ss`),
        });
    };

    const addOrReduceTime = (type: 'add' | 'reduce') => {
        const step = type === 'add' ? timeStartStep : -timeStartStep;
        const timeStr = moment(form.getFieldValue('startTime')).format('HH:mm:ss');
        const startTimeStamp =
            timestampToZeroTimeStamp(moment(`${selectedDate} ${timeStr}`)) + step;
        // 源时间基础上查询是否有夏令时偏移
        setDistTime(getSummerTimeOffsetByTime(moment(`${selectedDate} ${timeStr}`)));
        const startTime = getLocalMomentByZeroTimeStamp(startTimeStamp);
        form.setFieldsValue({ startTime });
    };

    useEffect(() => {
        if (visible) {
            setConfirmLoading(false);
            const formateDate = moment(selectedDate).format(timeFormat);
            const roundStartTime = Math.round(initStartTime) || 0;
            // 源时间基础上查询是否有夏令时偏移
            setDistTime(getSummerTimeOffsetByTime(roundStartTime));
            form.setFieldsValue({
                evidenceName: `${vehicleNumber}-${formateDate}-${i18n.t('name', '视频')}`,
            });
            // 设置通道数据
            const channels = channelList.map((val: any) => {
                return {
                    label: val.channelAlias || ((i18n.t('name', '通道') + val.channel) as string),
                    value: val.channel as number,
                };
            });
            setChannelOptions(channels || []);
            form.setFieldsValue({
                channelNoList: (channels || []).map((val) => val.value),
                storeType: storeType || StoreTypeEnum.MASTER,
                startTime: getLocalMomentByZeroTimeStamp(roundStartTime),
                videoLength: defaultVideoTimeLength,
                streamType: streamType || StreamTypeEnum.MAJOR,
            });

            // 视频模式
            if (playMode === PLAY_MODE.video) {
                initMemoryOptions((channels || []).map((val) => val.value));
            }
        }
    }, [visible]);

    useEffect(() => {
        form.setFieldsValue({
            streamType: currentStreamType || StreamTypeEnum.MAJOR,
        });
    }, [currentStreamType]);

    const checkSpace = (rule: any, value: string) => {
        return checkFieldSpace(value, i18n.t('message', '视频名称不能为空'));
    };

    return (
        <Modal
            title={i18n.t('action', '视频剪辑')}
            visible={visible}
            maskClosable={false}
            width={460}
            zIndex={1002}
            okText={i18n.t('action', '确定')}
            cancelText={i18n.t('action', '取消')}
            className="video-clip-modal"
            confirmLoading={confirmLoading}
            onOk={handleOk}
            onCancel={() => onCloseModal?.()}
        >
            <div className="video-clip-con">
                <div className="video-clip-form">
                    <Form layout="vertical" form={form} onValuesChange={formValuesChange}>
                        <Row>
                            <Col span={24}>
                                <AFormItem
                                    name="evidenceName"
                                    label={i18n.t('name', '视频名称')}
                                    rules={[
                                        { required: true, validator: checkSpace },
                                        { validator: validatorVideoName },
                                    ]}
                                >
                                    <Input
                                        allowClear
                                        maxLength={50}
                                        placeholder={i18n.t('message', '请输入视频名称')}
                                    />
                                </AFormItem>
                            </Col>
                            <Col span={24}>
                                <FormItem
                                    label={i18n.t('name', '开始时间')}
                                    rules={[{ required: true }]}
                                >
                                    <FormItem name="startTime" className="start-time-wrap">
                                        <TimePicker
                                            className="start-time-picker-wrap"
                                            size={isAbroadStyle ? 'large' : 'middle'}
                                            placeholder={i18n.t('message', '开始时间')}
                                            allowClear={false}
                                            style={{ width: '100%' }}
                                            format="HH:mm:ss"
                                            onChange={onStartTimeChange}
                                        />
                                    </FormItem>
                                    <div className="time-step-control">
                                        <div className="time-step-btns">
                                            <div
                                                className="time-step-add"
                                                onClick={() => addOrReduceTime('add')}
                                            >
                                                <UpOutlined />
                                            </div>
                                            <div
                                                className="time-step-reduce"
                                                onClick={() => addOrReduceTime('reduce')}
                                            >
                                                <DownOutlined />
                                            </div>
                                        </div>
                                    </div>
                                </FormItem>
                            </Col>
                            <Col span={24}>
                                <FormItem
                                    name="videoLength"
                                    label={i18n.t('name', '视频时长')}
                                    rules={[{ required: true }]}
                                >
                                    <VideoTimeLength
                                        isAbroadStyle={isAbroadStyle}
                                        timeNodes={timeNodes}
                                        streamType={currentStreamType}
                                        onChange={onVideoLengthChange}
                                    />
                                </FormItem>
                            </Col>
                            <Col span={24}>
                                <AFormItem
                                    name="streamType"
                                    label={
                                        <div className="stream-type-wrap">
                                            <Space>
                                                <span>{i18n.t('name', '视频类型')}</span>
                                                {unReport && (
                                                    <Tooltip
                                                        title={i18n.t(
                                                            'message',
                                                            '所选类型没有视频，自动下载其他类型的视频',
                                                        )}
                                                    >
                                                        <a>
                                                            <DefaultIconInformation />
                                                        </a>
                                                    </Tooltip>
                                                )}
                                            </Space>
                                            {unReport && (
                                                <div>
                                                    <Space>
                                                        <span>
                                                            {i18n.t('message', '自适应下载')}
                                                        </span>
                                                        <Switch
                                                            checked={autoDown}
                                                            onChange={(checked: boolean) =>
                                                                setAutoDown(checked)
                                                            }
                                                            size="small"
                                                        />
                                                    </Space>
                                                </div>
                                            )}
                                        </div>
                                    }
                                    rules={[
                                        {
                                            required: true,
                                            message: i18n.t('message', '视频类型不能为空'),
                                        },
                                    ]}
                                >
                                    <Segmented
                                        block
                                        //@ts-ignore
                                        onChange={(value: number) =>
                                            onStreamTypeChange(Number(value))
                                        }
                                        options={streamTypeOptions.map((item) => ({
                                            ...item,
                                            label: <span title={item.label}>{item.label}</span>,
                                            icon: <Badge color={item.color} />,
                                        }))}
                                    />
                                </AFormItem>
                            </Col>
                            <Col span={24}>
                                {currentStreamType === StreamTypeEnum.PICK_FRAME && deviceAbilityMajor && deviceAbilityMinor && 
                                    <AFormItem
                                        name="pickFrameModel"
                                        label={i18n.t('name', '延时录像模式')}
                                        rules={[
                                            {
                                                required: true,
                                            }
                                        ]}
                                        initialValue={PickFrameModelMap[0].value}
                                    >
                                        <Radio.Group>
                                            {PickFrameModelMap.map((item) => (
                                                <Radio value={item.value} key={item.value}>
                                                    <OverflowEllipsisContainer>
                                                        {item.label}
                                                    </OverflowEllipsisContainer>
                                                </Radio>
                                            ))}
                                        </Radio.Group>
                                    </AFormItem>
                                }
                            </Col>
                            <Col span={24} style={{ textAlign: 'right' }}>
                                <a onClick={() => setMoreSetting(!moreSetting)}>
                                    {i18n.t('name', '高级设置')}
                                    {moreSetting ? <UpOutlined /> : <DownOutlined />}
                                </a>
                            </Col>
                        </Row>
                        <div className={classNames('more-form', { 'form-show': moreSetting })}>
                            <Divider style={{ margin: '15px 0' }} />
                            <Row>
                                {isPlaybackServe ? null : (
                                    <Col span={24}>
                                        <FormItem
                                            name="storeType"
                                            label={i18n.t('name', '存储器类型')}
                                            rules={[{ required: true }]}
                                        >
                                            <RadioGroup>
                                                {storeTypeOptions.map((item, i) => {
                                                    return (
                                                        <span title={item.label} key={i}>
                                                            <Radio
                                                                key={item.value}
                                                                value={item.value}
                                                            >
                                                                <OverflowEllipsisContainer
                                                                    maxWidth={
                                                                        380 /
                                                                            (storeTypeOptions?.length ||
                                                                                1) -
                                                                        storeTypeOptions?.length *
                                                                            20
                                                                    }
                                                                >
                                                                    {item.label}
                                                                </OverflowEllipsisContainer>
                                                            </Radio>
                                                        </span>
                                                    );
                                                })}
                                            </RadioGroup>
                                        </FormItem>
                                    </Col>
                                )}
                                <Col span={24} className="all-checkbox">
                                    <FormItem
                                        name="channelNoList"
                                        rules={[
                                            {
                                                required: true,
                                                message: i18n.t('message', '下载通道不能为空'),
                                            },
                                        ]}
                                        label={
                                            <>
                                                <span style={{ marginRight: 28 }}>
                                                    {i18n.t('name', '下载通道')}
                                                </span>
                                            </>
                                        }
                                    >
                                        <AllCheckbox
                                            options={channelOptions}
                                            label={<span>{i18n.t('state', '全选')}</span>}
                                        />
                                    </FormItem>
                                </Col>
                                <Col span={24}>
                                    <FormItem
                                        rules={[{ 
                                            required: true,
                                            message: i18n.t('message', '网络模式不能为空'),
                                         }]}
                                        initialValue={checkboxOptions.map(item => item.value)}
                                        name="networkType"
                                        label={
                                            <>
                                                <span style={{ marginRight: 28 }}>
                                                    {i18n.t('name', '网络模式')}
                                                </span>
                                            </>
                                        }
                                    >
                                        <Checkbox.Group
                                            options={checkboxOptions}
                                        />
                                    </FormItem>
                                </Col>
                                <Col span={24}>
                                    <FormItem
                                        name="priority"
                                        initialValue={0}
                                        rules={[{ required: true }]}
                                        label={i18n.t('name', '优先级')}
                                    >
                                        <RadioGroup>
                                            {priorityOptions.map((item) => {
                                                return (
                                                    <span title={item.label} key={item.value}>
                                                        <Radio key={item.value} value={item.value}>
                                                            <OverflowEllipsisContainer>
                                                                {item.label}
                                                            </OverflowEllipsisContainer>
                                                        </Radio>
                                                    </span>
                                                );
                                            })}
                                        </RadioGroup>
                                    </FormItem>
                                </Col>
                            </Row>
                        </div>
                    </Form>
                </div>
            </div>
        </Modal>
    );
};

export default VideoEdit;
