import { getAppGlobalData, i18n, MosaicTypeEnum, utils } from '@base-app/runtime-lib';
import { geoPositionAnalysisV2 } from '@/service/geo';
import { createParkImageTask, getTravelImage } from '@/service/track-playback';
import { getEvidenceByAlarm } from '@/service/evidence';
import type { TravelInfo } from './type';
import { evidenceFormatData } from '@/utils/evidenceFormatData';
import { calcDuration, FileType, getAllVideos } from '@/utils/commonFun';
import { createVideoDom, destoryVideoDom } from '@/components/HistoryPlayer/commonFun';
import moment from 'moment';
import { escapeHtml } from '@/utils/util';

const { zeroTimeStampToFormatTime } = utils.formator;
const { timeFormat } = getAppGlobalData('APP_USER_CONFIG');
export const TIME_FORMAT = `${timeFormat.replace(/-?YYYY-?/, '')} HH:mm`;
export const TIME_FORMAT_WITH_SECOND = `${timeFormat.replace(/-?YYYY-?/, '')} HH:mm:ss`;
const ALARM_ICON = require('@/assets/icons/icon_direction.svg');
const EYES_ICON = require('@/assets/icons/icon_eyes.svg');
const ADDR_ICON = require('@/assets/icons/icon_address.svg');

export const getInitTimeRange = () => [
    moment().startOf('day').subtract(0, 'days'),
    moment().endOf('day'),
];
const {
    formator: { formatByte },
} = utils;
// 此刻
export const _NOW = 'now';
export const TRACK_PLAYBACK_ALARM_OPTION = `TRACK_PLAYBACK_ALARM_OPTION_${
    getAppGlobalData('APP_USER_INFO').tenantId
}`;
export const defaultTravelData: TravelInfo = {
    speedData: [],
    gpsData: [],
    parkData: [],
    alarmData: [],
    travelData: [],
    totalMileage: 0,
    totalTime: 0,
    showLastGps: false,
    noTravelData: true,
    isAllTravelInfo: true,
    hasRealtimeTravel: false,
    isIncrementData: false,
};

// 转换英里和公里数
const convertMileage = (value: number | string): number => {
    if (typeof value !== 'number' && typeof value !== 'string') {
        return value;
    }
    const userConfig = getAppGlobalData('APP_USER_CONFIG');
    const rate = 0.6214; // 0.6214MP等于1km
    if (userConfig.mileageUnit == 1) {
        return value as number;
    } else {
        return (value as number) * rate;
    }
};
const contextImage = (e) => {
    if (
        (e.target.tagName.toLowerCase() === 'img' || e.target.tagName.toLowerCase() === 'video') &&
        e.target?.closest('.disable-right-mouse-click') instanceof HTMLSpanElement
    ) {
        e.preventDefault(); // 阻止默认的右键菜单显示
    }
};
(window as any).contextImage = contextImage;

// 向下取整，保留一位小数
export const getMiles = (miles: number) => {
    const mileage = (Math.floor(convertMileage(miles) * 10) / 10).toFixed(1);
    if (Number(mileage) > 0) return mileage;
    return '<0.1';
};

export const getTime = (time: number, timeFormat: string = TIME_FORMAT, datumStamp?: number): string => {
    return zeroTimeStampToFormatTime(time, undefined, timeFormat, undefined, datumStamp);
};

const getTimeData = (time: number) => {
    if (typeof time !== 'number') {
        // eslint-disable-next-line no-param-reassign
        time = 0;
    }
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor((time % 3600) % 60);
    const format = (num: number) => {
        if (num <= 9) {
            return `0${num}`;
        }
        return num;
    };
    return {
        hours: format(hours),
        minutes: format(minutes),
        seconds: format(seconds),
    };
};

export const formateSeconds = (
    time: number,
    type: 'standard' | 'minutes' | 'seconds' = 'standard',
) => {
    const { hours, minutes, seconds } = getTimeData(time);
    const lang: string = getAppGlobalData('APP_LANG');
    const splitChar = /^zh_/.test(lang) ? '' : ' ';
    const getUnit = (zhUnit: string, otherUnit: string) => {
        return /^zh_/.test(lang) ? zhUnit : otherUnit;
    };
    const hourStr = Number(hours)
        ? `${Number(hours)}${getUnit(i18n.t('name', '时'), 'h')}${splitChar}`
        : '';
    const minuteStr =
        Number(hours) || Number(minutes)
            ? `${Number(minutes)}${getUnit(i18n.t('name', '分'), 'm')}${splitChar}`
            : '';
    const secondStr = `${Number(seconds)}${getUnit(i18n.t('name', '秒'), 's')}`;
    let timeStr = '';
    switch (type) {
        case 'standard':
            timeStr = `${hours}:${minutes}:${seconds}`;
            break;
        case 'minutes':
            timeStr = `${hourStr}${minuteStr}`;
            timeStr = timeStr || `<1${getUnit(i18n.t('name', '分'), 'm')}`;
            break;
        case 'seconds':
            timeStr = `${hourStr}${minuteStr}${secondStr}`;
        default:
            break;
    }
    return timeStr;
};
const getMediaSource = (file?: MediaSource, existH264?: boolean, hasWatermark?: boolean) => {
    if (!file || !Object.keys(file).length || !(file.url || existH264)) {
        return '';
    }
    const domId = `alarm-video-${file.alarmId}`;
    return `
        <div class="img-box">
            ${
                file?.fileSize || existH264
                    ? `
                    ${
                        existH264
                            ? `
                            <div style="height: 100%" id="${domId}"></div>
                            <div class="img-info">${formatByte(file?.fileSize as number)}
                                <span style="float: right;">
                                    ${file?.duration}
                                </span>
                            </div>
                        `
                            : ''
                    }
                    ${
                        !existH264
                            ? `
                            <span class="disable-right-mouse-click">
                                <video src="${
                                    file.url
                                }" controls onloadedmetadata="getVideoDuration()" ${hasWatermark ? 'oncontextmenu="contextImage(event)"' : null} id="track-playback-alarmvideo"></video>
                            </span>
                            <div class="img-info">
                                ${formatByte(file.fileSize as number)}
                                <span style="float: right;" id="track-playback-alarmvideolength">00:00</span>
                            </div>
                        `
                            : ''
                    }
                `
                    : getImg(file.url, hasWatermark)
            }
        </div>
    `;
};
const getImg = (url: string | null, hasWatermark: boolean) => {
    if (url) {
        return `
            <span class="disable-right-mouse-click">
                <div
                    class="info-image"
                    onclick="showTravelImage('${url}')"
                >

                    <img src=${url} ${hasWatermark ? 'oncontextmenu="contextImage(event)"' : null}/>

                    <div>
                        <img src="${EYES_ICON}" />
                        ${i18n.t('action', '预览')}
                    </div>
                </div>
            </span>
        `;
    }
    return '';
};
interface MediaSource {
    url: string | null;
    fileSize: number | null;
    type: 'alarm' | 'park';
    alarmId: string;
    duration: string;
}
const getContent = (
    haswatermark: boolean,
    html: string,
    address?: string,
    file?: MediaSource,
    existH264?: boolean,
    fileData?: any,
) => {
    const { startTime, vehicleInfo, driverList } = fileData || {};
    const escapeDeviceNo = escapeHtml(vehicleInfo.deviceNo) || '-';
    const escapeDriverName = driverList && driverList.length ? escapeHtml(driverList[0]['driverName']) : '-'
    const newHtml = `
        <div class="track-playback-map-info-panel" >
            ${file?.type === 'alarm' ? getMediaSource(file, existH264, haswatermark) : getImg(file?.url, haswatermark)}
            ${html}
            ${
                file?.type === 'alarm'
                    ? `<div class="info-driver" title=" ${escapeDeviceNo} | ${escapeDriverName}">
                        ${escapeDeviceNo} | ${escapeDriverName}
                    </div>`
                    : ''
            }
            <div class="info-address" title="${address || i18n.t('message', '地址解析中...')}">
                <img src="${ADDR_ICON}" />
                ${address ? address : i18n.t('message', '地址解析中...')}
            </div>
            ${
                file?.type === 'alarm'
                    ? `<div class="info-time">
                          ${getTime(startTime)}
                      </div>`
                    : ''
            }
        </div>
    `;
    return newHtml;
};

const getInfoMark = (params: any) => {
    const { lng, lat, deviceId, authId, startTime, html, icon, type, alarmId, haswatermark, L } = params;
    const marker = L?.marker([lat, lng], {
        icon,
        zIndexOffset: type === 'alarm' ? 20 : 10, // 报警点图标大小不同，偏移量不通
    });
    const domId = `alarm-video-${alarmId}`;
    const popup = L?.popup({
        offset: [0, 0],
        closeButton: false,
    }).setContent(getContent(haswatermark, html));
    marker.bindPopup(popup);
    marker.on('popupopen', async () => {
        Promise.all([
            type === 'alarm'
                ? getEvidenceByAlarm({
                      alarmIds: alarmId,
                      fields: 'vehicle,file_url,driver',
                  }).catch(() => {})
                : getTravelImage({
                      deviceId,
                      startTime: Number(startTime) - 5,
                      endTime: Number(startTime) + 5,
                  }).catch(() => {}),
            geoPositionAnalysisV2({
                lat: lat * 10 ** 6,
                lng: lng * 10 ** 6,
            }).catch(() => {}),
        ])
            .then(([fileData, addrData]: any) => {
                if (Array.isArray(fileData)) {
                    // eslint-disable-next-line no-param-reassign
                    fileData = fileData[0];
                }
                const { fileList = [], url = '' } = fileData || {};
                const { existH264 } = evidenceFormatData(fileData);
                const imageUrl = url;
                let mediaData: MediaSource = {
                    url,
                    type: 'park', // 停车点展示图片getTravelImage
                } as MediaSource;
                if (!imageUrl && authId) {
                    createParkImageTask({
                        captureTime: Number(startTime) - 5,
                        authId,
                    }).catch(() => {});
                }
                let playChannel = undefined;
                if (fileList.length && type === 'alarm') {
                    // 报警点 优先展示顺序，视频>图片>不展示；getEvidenceByAlarm
                    const videoData = fileList.find(
                        (item: { fileType: number }) => item.fileType === FileType.VIDEO,
                    );
                    const imageData = fileList.find(
                        (item: { fileType: number }) => item.fileType === FileType.IMAGE,
                    );
                    // 计算时长
                    const duration = calcDuration(fileData, existH264);
                    // const videos = getVideosByFileType(fileList || [], existH264);
                    const videoSize = getAllVideos(fileList).reduce(
                        (total: any, item: any) => total + item.fileSize,
                        0,
                    );
                    mediaData = {
                        url: videoData?.url || imageData?.url || '',
                        fileSize: videoSize || '',
                        type: 'alarm',
                        alarmId,
                        duration,
                    };
                    playChannel = videoData?.channelNo;
                }
                popup.on('contentupdate', () => {
                    createVideoDom(
                        domId,
                        fileData,
                        MosaicTypeEnum.playback,
                        playChannel
                    );
                });
                popup.setContent(
                    getContent(
                        haswatermark,
                        html,
                        addrData?.address || i18n.t('message', '暂无地址'),
                        mediaData,
                        existH264,
                        fileData,
                    ),
                );
            })
            .catch(() => {
                popup.setContent(getContent(haswatermark, html, i18n.t('message', '地址解析失败')));
            });
    });
    marker.on('popupclose', () => {
        popup.off('contentupdate');
        destoryVideoDom(domId);
    });
    marker.on('mouseover', () => {
        marker.bindPopup(popup).openPopup();
    });
    return marker;
};

export const getMapPanel = (data: any, type: 'alarm' | 'park' = 'alarm', map: any, L: any) => {
    if (type === 'park') {
        const { startTime, endTime, isStart, isEnd } = data;
        let html = `
            <h4
                class="info-title"
                title="${i18n.t('name', '停车时长')}: ${formateSeconds(
            endTime - startTime,
            'seconds',
        )}"
            >
                ${i18n.t('name', '停车时长')}: ${formateSeconds(endTime - startTime, 'seconds')}
            </h4>
            <div class="info-time">
                ${getTime(startTime)} ~ ${getTime(endTime)}
            </div>
        `;
        if (isStart || isEnd) {
            html = `<div class="info-title">${getTime(startTime)}</div>`;
        }
        const marker = getInfoMark({
            ...data,
            type,
            html,
            L,
            icon: L?.divIcon({
                className: 'park-point',
                iconSize: [16, 16],
                iconAnchor: [8, 8],
            }),
        });
        return marker;
    } else {
        const { alarmName, alarmId } = data;
        const escapeAlarmName = escapeHtml(alarmName);
        const html = `
            <h4
                class="info-title alarm-info-title"
                onclick="openAlarmDetailPage('${alarmId}')"
                title="${escapeAlarmName}"
            >
                ${escapeAlarmName}
            </h4>
        `;
        const marker = getInfoMark({
            ...data,
            type,
            html,
            L,
            icon: L?.icon({
                iconUrl: ALARM_ICON,
                iconSize: [20, 20],
                iconAnchor: [10, 20],
            }),
        });
        return marker;
    }
};

export const stopEventPropagation = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
};
