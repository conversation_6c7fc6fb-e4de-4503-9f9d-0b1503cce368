import { <PERSON><PERSON>, Col, Form, Row, Tooltip } from '@streamax/poppy';
import {
    i18n,
    StarryAbroadFormItem as AFormItem,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import './index.less';
import TimeCascadeSelect from '../TimeCascadeSelect';
import DriverSelect from '@/components/RequestSelect/DriverSelect';
import type { TravelDataItem, OptionItem, AppendFormData, TimeValues } from '../../type';
import { inTimeRange } from '@/utils/time';
import { useEffect, useState } from 'react';
import { IconInformationFill } from '@streamax/poppy-icons';
import { _NOW } from '../../constant';
import { postQueryDriverList } from '@/service/driver';
interface Props {
    loading: boolean;
    travelData?: TravelDataItem[]; // 历史行程
    selectedTravelData?: TravelDataItem; // 选中的行程
    onCancel: () => void;
    onOk: (values: AppendFormData) => void;
}
export default ({ loading, travelData, selectedTravelData, onCancel, onOk }: Props) => {
    const [startOptions, setStartOptions] = useState<OptionItem[]>([]);
    const [endOptions, setEndOptions] = useState<OptionItem[]>([]);
    const [initOption, setInitOption] = useState<OptionItem[]>([]);
    const [form] = Form.useForm();
    useEffect(() => {
        initOptions();
        initTimeData();
    }, [selectedTravelData, travelData?.length]);
    const initOptions = () => {
        const startOptions =
            travelData?.map((item) => {
                return {
                    label: item.travelStartTime,
                    value: item.travelStartTime,
                };
            }) || [];
        setStartOptions(startOptions);
        const endOptions =
            travelData?.map((item) => {
                return {
                    labelText: !item.endFlag ? i18n.t('name', '此刻') : '',
                    label: !item.endFlag ? i18n.t('name', '此刻') : '',
                    value: item?.travelEndTime || _NOW,
                };
            }) || [];
        setEndOptions(endOptions);
    };
    const initTimeData = async () => {
        // 设置开始时间结束时间（过滤掉30天之前的数据）
        if (!selectedTravelData) {
            if (travelData && travelData.length) {
                const initData = travelData[0];
                setInitOption([
                    {
                        label: initData.driverName,
                        value: initData.driverId,
                    },
                ]);
                if (inTimeRange(initData.travelStartTime)) {
                    const values = {
                        startValue: initData.travelStartTime,
                        endValue: initData?.travelEndTime || _NOW,
                    };
                    // 处理删除掉的司机
                    const flag = await filterDriverFlag(initData.driverId);
                    const flagDelete = await filterDriverDeleteFlag(initData.driverName);
                    if (flag && flagDelete) {
                        form.setFieldsValue({ driverId: initData.driverId, time: values });
                    } else {
                        setInitOption([]);
                        form.setFieldsValue({ driverId: undefined, time: values });
                    }
                }
            }
        } else {
            // 重新查找一次被选中的数据
            const initSelectData =
                travelData?.find(
                    (item) => item.travelStartTime === selectedTravelData.travelStartTime,
                ) || null;
            const initData = initSelectData || selectedTravelData;
            setInitOption([
                {
                    label: initData.driverName,
                    value: initData.driverId,
                },
            ]);
            if (inTimeRange(initData.travelStartTime)) {
                const values = {
                    startValue: initData.travelStartTime,
                    endValue: initData?.travelEndTime || _NOW,
                };
                // 处理删除掉的司机
                const flag = await filterDriverFlag(initData.driverId);
                const flagDelete = await filterDriverDeleteFlag(initData.driverName);
                if (flag && flagDelete) {
                    form.setFieldsValue({
                        driverId: initData?.driverId ? initData?.driverId : undefined,
                        time: values,
                    });
                } else {
                    setInitOption([]);
                    form.setFieldsValue({
                        driverId: undefined,
                        time: values,
                    });
                }
            }
        }
    };
    const filterDriverFlag = async (driverId: string) => {
        if (!driverId) return false;
        const data =
            (await postQueryDriverList({
                page: 1,
                pageSize: 20,
                driverIds: driverId,
                driverName: undefined,
                needDriverPicture: false,
            })) || [];
        const flag = data?.list?.find((item: any) => item?.driverId == driverId) || false;
        return !!flag;
    };
    const filterDriverDeleteFlag = async (driverName: string) => {
        if (!driverName) return false;
        const data = await postQueryDriverList({
            companyIds: { includeSubFleet: 0 },
            includeSubFleet: 0,
            page: 1,
            pageSize: 20,
            driverName: driverName?.trim(),
            needDriverPicture: false,
        });
        const flag = !!(data && data.list && data.list.length);
        return flag;
    };
    const onCancelFn = () => {
        onCancel && onCancel();
    };
    const onOkFn = async () => {
        try {
            const verify = await form.validateFields();
            onOk && onOk(verify);
        } catch (error) {
            console.warn(error);
        }
    };
    const checkTimeValues = (_: any, value: TimeValues) => {
        if (!value || !value.startValue || !value.endValue) {
            return Promise.reject(i18n.t('message', '请选择驾驶时间'));
        }
        return Promise.resolve();
    };
    return (
        <div className="bind-driver-wrapper">
            <div className="bind-driver-title">
                {i18n.t('name', '绑定司机')}
                <Tooltip title={i18n.t('message', '部分数据更新可能会有延迟')}>
                    <span>
                        <StarryAbroadIcon>
                            <IconInformationFill className="bind-driver-title-name-icon" />
                        </StarryAbroadIcon>
                    </span>
                </Tooltip>
            </div>
            <div className="bind-driver-content">
                <Form
                    form={form}
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    initialValues={{ remember: true }}
                    layout="vertical"
                    onFinish={() => {}}
                    autoComplete="off"
                >
                    <Row>
                        <Col span={24}>
                            <AFormItem
                                label={i18n.t('name', '司机')}
                                name="driverId"
                                rules={[
                                    { required: true, message: i18n.t('message', '请选择司机') },
                                ]}
                            >
                                <DriverSelect
                                    hunt={true}
                                    initOption={initOption?.filter((item) => item.value !== '0')}
                                />
                            </AFormItem>
                        </Col>
                        <Col span={24}>
                            <Form.Item
                                label={i18n.t('name', '驾驶时间')}
                                name="time"
                                rules={[{ required: true, validator: checkTimeValues }]}
                            >
                                <TimeCascadeSelect
                                    startOptions={startOptions}
                                    endOptions={endOptions}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </div>
            <div className="bind-driver-footer">
                <Button onClick={onCancelFn}>{i18n.t('action', '取消')}</Button>
                <Button type="primary" loading={loading} onClick={onOkFn}>
                    {i18n.t('action', '确定')}
                </Button>
            </div>
        </div>
    );
};
