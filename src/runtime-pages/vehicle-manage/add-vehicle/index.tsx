import React, { useEffect, useState, useRef } from 'react';
import { Form, Input, Button, Space, Select, message, Row, Col, Divider } from '@streamax/poppy';
import {
    StarryAbroadIcon,
    StarryAbroadLRLayout,
    StarryBreadcrumb,
    StarryCard,
    StarryInfoBlock
} from '@base-app/runtime-lib';
import {
    i18n,
    utils,
    getAppGlobalData,
    RouterPrompt,
    StarryStorage,
} from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import CompanyRequest from '../../../service/company';
import { useSubmitFn, useAsyncEffect } from '@streamax/hooks';
import {
    getVehicleDetail,
    addVehicleV2,
    editVehicleV2,
    editVehicleBaseInfo,
    fetchEnumPage,
} from '../../../service/vehicle';
import { fetchApplicationConfig } from '../../../service/application';
import DeviceInfo from './device-info';
import { VEHICLE_COLOR } from '../constant';
import { EnumDomainType } from '@/types/company';
import type { SourceFleetType } from '@/types/company';
import { useLocalStorageState, useUpdate } from 'ahooks';
import { cloneDeep, isEqual } from 'lodash';

//@ts-ignore
// import { FleetTreeSelect } from '@base-app/runtime-lib';
import { FleetItem } from '@base-app/runtime-lib';
import { VEHICLE_STATE } from '../types';
import { getDissociateDeviceList } from '@/service/device';
import type { OptionItemProps } from '@/utils/constant';
import FleetTreeSelect from '@/components/AuthFleetSelectShow';
import { getAuthFleetList } from '@/utils/commonFun';
import AuthTip from '@/components/AuthTip';
import { getTenantDetail } from '@/service/tenant';
import type { TenantDetail } from '@/service/tenant';
import './index.less';
import {IconAdd02Line} from "@streamax/poppy-icons";

interface fleetItem {
    [propName: number | string]: any;
}
export interface DissociateDeviceItem {
    deviceId: string;
    deviceNo: string;
}
// vehicle/simple/update编辑基础信息时未选择车辆类型时需要传-1标识
const NO_VEHICLE_TYPE = '-1';
const TextArea = Input.TextArea;
const Vehicle = (props: any) => {
    const storage = StarryStorage();
    const validateBaseVehicleNumber = utils.validator.validateBaseVehicleNumber;
    const history = useHistory();

    const [groupNumber, setGroupNumber] = useState<number>(1);
    const [deviceNumber, setDeviceNumber] = useState<number>(1);
    const [plateColor, setPlateColor] = useState<number>();

    const [primaryDeviceIndex, setPrimaryDeviceIndex] = useState<number>(0);
    const [selectedDevice, setSelectedDevice] = useState<string[]>([]);
    const [fleetList, setFleetList] = useState<FleetItem | null>(null);
    const fleetTreeEleRef = useRef<fleetItem>({});
    const [when, setWhen] = useState(true);
    const [form] = Form.useForm();
    const [vehicleInfo, setVehicleInfo] = useLocalStorageState('add-vehicle-base-info', '{}');
    const [filled, setFilled] = useState(false);
    const [optionList, setOptionList] = useState<OptionItemProps[]>([]); // 游离设备列表
    const [showAuthTip, setShowAuthTip] = useState(false);
    const [tenantInfo, setTenantInfo] = useState<TenantDetail>({} as TenantDetail);
    const [vehicleTypeOptions, setVehicleTypeOptions] = useState([]);
    const update = useUpdate();
    const { vehicleId, editBase } = props.location.query;
    useAsyncEffect(async () => {
        // 优先请求设备
         const list = await fetchApplicationConfig({
             appId: getAppGlobalData('APP_ID'),
             keys: 'vehicle.device.max',
         });
        const dNum = list?.[0]?.value > 0 ? parseInt(list?.[0]?.value, 10) : 1;
         setDeviceNumber(dNum);
         fetchVehicleType();
        // 从激活设备页回退时回填已填写的数据
        if (history.action === 'POP') {
            let info: any;
            try {
                info = vehicleInfo ? JSON.parse(vehicleInfo) : {};
            } catch (error) {
                message.error(i18n.t('message', '回填数据失败'));
            }
            let fleetIds = (info.fleetIds || []).filter((item: any) => !!item); // 去掉null影响
            if (!fleetIds.length) {
                // 解决空数组时不渲染下拉选择框
                fleetIds = [undefined];
            }
            if (!filled) {
                const newDeviceId = await storage.getItem('newDeviceId');
                let deviceList = cloneDeep(info?.deviceList || []).filter(
                    (item: any) => item?.deviceId !== undefined,
                );
                if (newDeviceId) {
                    dNum > 1
                        ? deviceList.push({ deviceId: newDeviceId })
                        : (deviceList = [{ deviceId: newDeviceId }]); // 回填新增的设备
                } else if (!deviceList.length) {
                    deviceList.push(null);
                }
                setFilled(true);
                const devicecIds = info?.deviceList?.map(
                    (item: { deviceId: string }) => item?.deviceId,
                );
                devicecIds && devicecIds.push(newDeviceId);
                setPrimaryDeviceIndex(info?.primaryDeviceIndex);
                setSelectedDevice(devicecIds);
                if (deviceList.length === 1) {
                    setPrimaryDeviceIndex(0);
                }
                setTimeout(() => {
                    form.setFieldsValue({
                        ...info,
                        fleetIds,
                        deviceList,
                    });
                    setVehicleInfo('{}');
                });
                setTimeout(() => {
                    storage.removeItem('newDeviceId');
                }, 300);
            }
        }
    }, []);
    const fetchVehicleType = async () => {
        const { list } = await fetchEnumPage({
            appId: getAppGlobalData('APP_ID'),
            enumType: 'vehicleType',
            pageSize: 1e8,
        });
        setVehicleTypeOptions((list || [] ).map(item => {
            return {
                value: Number(item.enumCode),
                label: i18n.t(
                    `@i18n:@vehicleTypeEnum__${item.enumCode}`,
                    item.enumName,
                )
            };
        }));
    };

    useAsyncEffect(async () => {
        const deviceList = form.getFieldValue('deviceList');
        const formDeviceIds = (deviceList || []).map(
            (item: { deviceId: string }) => item?.deviceId,
        );
        if (deviceList && !isEqual(selectedDevice, formDeviceIds)) {
            const newDeviceId = await storage.getItem('newDeviceId');
            setSelectedDevice([...formDeviceIds]);
            if (newDeviceId) {
                setSelectedDevice([...formDeviceIds, newDeviceId]);
            }
        }
    }, [selectedDevice]);

    // 查询游离设备列表
    const getDeviceList = async () => {
        const list = getDissociateDeviceList();
        const options = (await list).map((item: DissociateDeviceItem) => {
            return {
                label: item.deviceNo,
                value: item.deviceId,
                disabled: selectedDevice?.includes(item.deviceId),
            };
        });
        setOptionList(options);
    };

    const fetchApplicationConfigRequest = async () => {
        const number = await fetchApplicationConfig({
            appId: getAppGlobalData('APP_ID'),
            keys: 'vehicle.fleet.max',
        });
        setGroupNumber(number[0]?.value > 0 ? parseInt(number[0]?.value, 10) : 1);
    };
    const onFormValuesChange = (changedValues: any) => {
        if (changedValues['fleetIds']) {
            for (const key in fleetTreeEleRef.current) {
                if (fleetTreeEleRef.current[key]) {
                    fleetTreeEleRef.current[key].loadData();
                }
            }
        }
    };
    useEffect(() => {
        fetchInitData();
        fetchApplicationConfigRequest();
        getDeviceList();
        // 新增时获取租户信息的最大设备信息
        !vehicleId &&
            getTenantDetail().then((data) => {
                setTenantInfo(data);
            });
    }, []);

    const fetchInitData = () => {
        // 编辑从接口获取数据
        if (vehicleId) {
            (async () => {
                const vehicle = await getVehicleDetail({
                    vehicleId,
                    fields: 'fleet,device,channel',
                    countFlag: true,
                });
                // 确保主设备在第一个
                const deviceList = [];
                const paramry = vehicle?.deviceList.find(
                    (item: { primaryType: number }) => item.primaryType === 1,
                );
                paramry && deviceList.push(paramry);
                vehicle?.deviceList.forEach((item: { primaryType: number }) => {
                    if (item.primaryType !== 1) {
                        // 辅助设备
                        deviceList.push(item);
                    }
                });
                const { fleetList, authDisable } = getAuthFleetList(vehicle);
                setShowAuthTip(authDisable);
                form.setFieldsValue({
                    ...vehicle,
                    fleetIds: (fleetList || []).map((fleet: any) => {
                        return {
                            fleetIds: fleet.fleetId,
                            includeSubFleet: 0,
                        };
                    }),
                    deviceList,
                });

                setPlateColor(vehicle.vehicleColor);
                if (!fleetList?.length) {
                    form.setFieldsValue({
                        fleetIds: [undefined],
                    });
                }
                onFormValuesChange({
                    fleetIds: (fleetList || [])
                        .map((fleet: any) => fleet.fleetId)
                        .map((fleetId: string) => {
                            return {
                                fleetIds: fleetId,
                                includeSubFleet: 0,
                            };
                        }),
                });
            })();
        } else {
            form.setFieldsValue({
                fleetIds: [undefined],
                deviceList: [{}],
            });
            onFormValuesChange({ fleetIds: [] });
        }
    };

    const [submit, submitLoading] = useSubmitFn(async (values: any) => {
        const { deviceList } = values;

        const fleetIdStrings: string[] = [];
        values.fleetIds.forEach((item: { fleetIds: string }) => {
            fleetIdStrings.push(item.fleetIds);
        });
        deviceList?.forEach((item: { primaryType: number }, index: number | undefined) => {
            if (index === primaryDeviceIndex) {
                item.primaryType = 1; // 主设备
            } else {
                item.primaryType = 2; // 辅助设备
            }
        });

        // eslint-disable-next-line no-param-reassign
        const requestParams = {
            ...values,
            fleetIds: fleetIdStrings.join(','),
            vehicleId,
            bindingDeviceList: deviceList
            // requestSource: 1,
        };
        delete requestParams.deviceList;
        // 编辑
        if (vehicleId && editBase === '1') {
            requestParams.vehicleType = values.vehicleType ?? NO_VEHICLE_TYPE;
            await editVehicleBaseInfo(requestParams);
        } else if (vehicleId) {
            const bindingDeviceList: any = [];
            requestParams.bindingDeviceList.forEach(
                (item: { deviceId: string; primaryType: string; deviceAlias: string }) => {
                    bindingDeviceList.push({
                        deviceId: item.deviceId,
                        primaryType: item.primaryType,
                        deviceAlias: item.deviceAlias,
                    });
                },
            );
            await editVehicleV2({
                vehicleId,
                ...requestParams,
                bindingDeviceList,
            });
        } else {
            await addVehicleV2({
                ...requestParams,
                vehicleState: VEHICLE_STATE.START, // 新增默认启用车辆
            });
        }
        setWhen(false);
        message.success(i18n.t('message', '成功'));
        history.goBack();
    });
    const onColorChange = (value: any) => {
        setPlateColor(value);
    };
    // 获取车组列表
    const fetchGroupsDataSource = (key?: number) => {
        return async () => {
            const idItems = form.getFieldValue('fleetIds');
            const ids = idItems.map((item: { fleetIds: string }) => item?.fleetIds);
            ids.splice(key, 1); // 删除当前项的id,下一步会把所有id项禁用，不禁用当前项(修改时可再次选中当前项，不改变)
            let list = fleetList;
            if (!fleetList) {
                list = await CompanyRequest.getList({
                    domainType: EnumDomainType.touchFleet,
                    ...utils.general.getSortParam(),
                });
                setFleetList(list);
            }

            const flatData: FleetItem[] = list.map((x: SourceFleetType) => {
                return {
                    key: x['key'],
                    title: x['title'],
                    value: x['value'],
                    parentId: x['parentId'],
                    disabled: ids.includes(x['id']),
                };
            });
            return flatData;
        };
    };
    // 跳转到激活设备页面前保存表单数据
    const saveFormValue = () => {
        setWhen(false);
        const value = JSON.stringify({ ...form.getFieldsValue(), primaryDeviceIndex });
        setVehicleInfo(value);
        setWhen(false);
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能不会保存您所做的更改。',
                )}
            />
            <StarryCard className="base-data-vehicle-manage-add-edit-vehicle">
                <Form
                    layout="vertical"
                    form={form}
                    onValuesChange={onFormValuesChange}
                    onFinish={submit}
                >
                    <StarryInfoBlock title={i18n.t('name', '基本信息')}>
                        <Row gutter={80} style={{ width: '830px' }}>
                            <Col span={12}>
                                <Form.Item
                                    label={i18n.t('name', '车牌号码')}
                                    name="vehicleNumber"
                                    rules={[
                                        {
                                            required: true,
                                            whitespace: true,
                                            message: i18n.t(
                                                'message',
                                                '请输入车牌号码',
                                            ),
                                        },
                                        {
                                            validator:
                                                validateBaseVehicleNumber,
                                        },
                                    ]}
                                    groupColProps={{ span: 12 }}
                                >
                                    <Input
                                        allowClear
                                        maxLength={50}
                                        placeholder={i18n.t(
                                            'message',
                                            '请输入车牌号码',
                                        )}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label={i18n.t('name', '车牌颜色')}
                                    name="vehicleColor"
                                >
                                    <Select
                                        options={VEHICLE_COLOR}
                                        placeholder={i18n.t(
                                            'message',
                                            '请选择车牌颜色',
                                        )}
                                        onChange={onColorChange}
                                        value={plateColor}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label={i18n.t('name', '车辆类型')}
                                    name="vehicleType"
                                >
                                    <Select
                                        options={vehicleTypeOptions}
                                        placeholder={i18n.t(
                                            'message',
                                            '请选择车辆类型',
                                        )}
                                        allowClear
                                        showSearch
                                        filterOption={(
                                            inputValue: string,
                                            option: any,
                                        ) => {
                                            return (
                                                (option.label || '')
                                                    .toLowerCase()
                                                    .indexOf(
                                                        (
                                                            inputValue as string
                                                        ).toLowerCase(),
                                                    ) !== -1
                                            );
                                        }}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label={i18n.t('name', '描述')}
                                    name="description"
                                >
                                    <TextArea
                                        allowClear
                                        maxLength={500}
                                        showCount
                                        rows={5}
                                        placeholder={i18n.t(
                                            'message',
                                            '不多于500字',
                                        )}
                                        // @ts-ignore
                                        countformatter={(
                                            count: number,
                                            maxLength?: number,
                                        ) => {
                                            return `${count}/${maxLength}`;
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <div className="poppy-form-item-label">
                                    <label className="poppy-form-item-required">
                                        {i18n.t('name', '归属车组')}
                                        <AuthTip show={showAuthTip} />
                                    </label>
                                </div>
                                <Form.List
                                    name="fleetIds"
                                    rules={[
                                        {
                                            validator(_: any, value: any) {
                                                if (
                                                    !value ||
                                                    form.getFieldValue(
                                                        'fleetIds',
                                                    ).length < 0
                                                ) {
                                                    return Promise.reject(
                                                        new Error(
                                                            i18n.t(
                                                                'message',
                                                                '请选择归属车组',
                                                            ),
                                                        ),
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        },
                                    ]}
                                >
                                    {(fields, { remove, add }) => (
                                        <Space direction="vertical">
                                            {fields.map(
                                                (
                                                    { key, name, ...restField },
                                                    index: number,
                                                ) => {
                                                    const fleetItem = (
                                                        <FleetTreeSelect
                                                            allowClear={false}
                                                            ref={(node: any) =>
                                                                (fleetTreeEleRef.current[
                                                                    key
                                                                ] = node)
                                                            }
                                                            onInitDone={update}
                                                            showSearch
                                                            style={{
                                                                width: '340px',
                                                            }}
                                                            getDataSource={fetchGroupsDataSource(
                                                                key,
                                                            )}
                                                        />
                                                    );
                                                    return (
                                                        <Space
                                                            size="small"
                                                            align="baseline"
                                                            key={key}
                                                        >
                                                            <Form.Item
                                                                {...restField}
                                                                name={name}
                                                                key={key}
                                                                required={false}
                                                                rules={[
                                                                    {
                                                                        required:
                                                                            true,
                                                                        message:
                                                                            i18n.t(
                                                                                'message',
                                                                                '请选择归属车组',
                                                                            ),
                                                                    },
                                                                ]}
                                                                groupColProps={{
                                                                    span: 12,
                                                                }}
                                                            >
                                                                {fleetItem}
                                                            </Form.Item>
                                                            <div
                                                                style={{
                                                                    display:
                                                                        groupNumber ===
                                                                        1
                                                                            ? 'none'
                                                                            : 'block',
                                                                }}
                                                                className="add-or-substract-fleet"
                                                            >
                                                                {index === 0 &&
                                                                fields.length <
                                                                    groupNumber ? (
                                                                    <Button
                                                                        type="primary"
                                                                        onClick={() =>
                                                                            add()
                                                                        }
                                                                    >
                                                                        +
                                                                    </Button>
                                                                ) : (
                                                                    <Button
                                                                        disabled={
                                                                            fleetTreeEleRef
                                                                                .current[
                                                                                key
                                                                            ]
                                                                                ?.getAuth
                                                                        }
                                                                        onClick={() => {
                                                                            remove(
                                                                                index,
                                                                            );
                                                                        }}
                                                                    >
                                                                        -
                                                                    </Button>
                                                                )}
                                                            </div>
                                                        </Space>
                                                    );
                                                },
                                            )}
                                        </Space>
                                    )}
                                </Form.List>
                            </Col>
                        </Row>
                    </StarryInfoBlock>
                    {editBase !== '1' && (
                        <>
                            <Divider />
                            <StarryInfoBlock title={i18n.t('name', '设备信息')}>
                                <Form.List
                                    name="deviceList"
                                    rules={[
                                        {
                                            validator(_: any, value: any) {
                                                if (
                                                    !value ||
                                                    form.getFieldValue(
                                                        'deviceList',
                                                    ).length < 0
                                                ) {
                                                    return Promise.reject(
                                                        new Error(
                                                            i18n.t(
                                                                'message',
                                                                '请添加设备信息',
                                                            ),
                                                        ),
                                                    );
                                                }
                                                return Promise.resolve();
                                            },
                                        },
                                    ]}
                                >
                                    {(fields, { add, remove }) => {
                                        return (
                                            <>
                                                <Space
                                                    direction="vertical"
                                                    size={0}
                                                    style={{ width: '100%' }}
                                                >
                                                    {fields.map(
                                                        // @ts-ignore
                                                        (
                                                            {
                                                                key,
                                                                name,
                                                                fieldKey,
                                                                ...restField
                                                            },
                                                            index: number,
                                                        ) => (
                                                            <DeviceInfo
                                                                isEdit={
                                                                    vehicleId
                                                                        ? true
                                                                        : false
                                                                }
                                                                remove={remove}
                                                                add={add}
                                                                index={index}
                                                                key={key}
                                                                name={name}
                                                                fieldKey={
                                                                    fieldKey
                                                                }
                                                                restField={
                                                                    restField
                                                                }
                                                                value={
                                                                    form.getFieldValue(
                                                                        'deviceList',
                                                                    )[index]
                                                                }
                                                                hasColor={
                                                                    plateColor
                                                                }
                                                                deviceNumber={
                                                                    deviceNumber
                                                                }
                                                                fieldsLength={
                                                                    fields.length
                                                                }
                                                                primaryDeviceIndex={
                                                                    primaryDeviceIndex
                                                                }
                                                                setPrimaryDeviceIndex={
                                                                    setPrimaryDeviceIndex
                                                                }
                                                                selectedDevice={
                                                                    selectedDevice
                                                                }
                                                                setSelectedDevice={
                                                                    setSelectedDevice
                                                                }
                                                                saveFormValue={
                                                                    saveFormValue
                                                                }
                                                                optionList={
                                                                    optionList
                                                                }
                                                                setOptionList={
                                                                    setOptionList
                                                                }
                                                                tenantInfo={
                                                                    tenantInfo
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </Space>
                                                {deviceNumber > 1 &&
                                                fields.length < deviceNumber ? (
                                                    <div
                                                        className={'add-button'}
                                                        onClick={() => {
                                                            add();
                                                        }}
                                                    >
                                                        <StarryAbroadIcon>
                                                            <IconAdd02Line />
                                                        </StarryAbroadIcon>
                                                        <span
                                                            className={
                                                                'add-button-text'
                                                            }
                                                        >
                                                            {i18n.t(
                                                                'name',
                                                                '添加',
                                                            )}
                                                        </span>
                                                    </div>
                                                ) : null}
                                            </>
                                        );
                                    }}
                                </Form.List>
                            </StarryInfoBlock>
                        </>
                    )}
                    <StarryAbroadLRLayout>
                        <Button
                            onClick={() => {
                                history.goBack();
                            }}
                        >
                            {i18n.t('action', '取消')}
                        </Button>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={submitLoading}
                        >
                            {i18n.t('action', '保存')}
                        </Button>
                    </StarryAbroadLRLayout>
                </Form>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default Vehicle;
