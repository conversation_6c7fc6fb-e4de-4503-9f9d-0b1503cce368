import { forwardRef, useContext, useRef } from 'react';
//@ts-ignore
import { StarryModal as Modal, Action, MosaicTypeEnum, mosaicManager } from '@base-app/runtime-lib';
import {
    StarryAbroadIcon,
    evidenceUtils,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import VideoCard from './VideoCard';
import {
    IconArrow02Right,
    IconArrow02Left,
    IconClear,
    IconInformation as TipsIcon,
    IconNullData as NullIcon,
    IconRequest as DeleteConfirmIcon,
    IconSearch02,
    IconSwitch02Fill,
} from '@streamax/poppy-icons';
import {
    Input,
    Tree,
    Button,
    Select,
    ProForm,
    Form,
    Spin,
    message,
    ConfigProvider,
    List,
    Tooltip,
    Container,
} from '@streamax/poppy';
import { useState, useEffect } from 'react';
import InfoBack from '@/components/InfoBack';
import { CaretDownOutlined } from '@ant-design/icons';
import Icon from '@streamax/poppy-icons/lib/Icon';
import { debounce, head } from 'lodash';
import './ManualClear.less';
import {
    i18n,
    Auth,
    utils,
    getAppGlobalData,
    useUrlSearchStore,
    g_emmiter,
} from '@base-app/runtime-lib';
import moment from 'moment';
import alarmApi from '@/service/alarm';
import evidenceApi from '@/service/evidence';
import companyApi from '@/service/company';
import { getVehiclerListByPage } from '@/service/vehicle';
import clearTaskApi from '@/service/clear-task';
import DateRange from '@/components/DateRange';
import { getSpecialVehicle } from '@/service/vehicle';
import classNames from 'classnames';
import { useAppList } from '@/hooks';
import {
    FileType,
    getAllVideos,
    getInitTimeRange,
    getOffsetInfo,
    getVideosByFileType,
} from '@/utils/commonFun';
import { evidenceFormatData } from '@/utils/evidenceFormatData';
import { useAsyncEffect, useRequest } from '@streamax/hooks';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import DisableRightMouseClickImage from '@/components/DisableRightMouseClickImage';
import type { ItemConfig } from '@/components/UserSearchVideoDownloadStore';
import { getCustomItems, getCustomJsx } from '@/utils/pageReuse';
import type {
    DataClearCustom,
    ListPageQueryForm,
} from '@/types/pageReuse/pageReuseBase';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { ProtocolTypesEnum } from '@/types';
import type { SearchModuleNameType } from '@/components/MultiSearch';
import MultiSearch from '@/components/MultiSearch';
import { getParentIds } from '@/hooks/useRTData/util';
import { getPickerRangeTWM } from '@/utils/date-picker-config';
import { RspCardLayout, RspDrawerTemplate } from '@streamax/responsive-layout';
import { aspectRatioImage } from '@base-app/runtime-lib';
import { VideoSource } from '@/utils/evidence';
import { renderEvidenceCardType } from '@/runtime-lib/modules/Evidence/utils';


const {
    formator: {
        formatByte,
        secondsToTime,
        timestampToZeroTimeStamp,
        zeroTimeStampToFormatTime,
        getLocalMomentByZeroTimeStamp,
    },
} = utils;
const { QueryForm } = ProForm;

interface TreeNode {
    key: string;
    title: string;
    children?: TreeNode[];
    disabled?: boolean;
    selected?: boolean;
    has?: boolean;
}

interface ReqParams {
    page: number;
    pageSize: number;
    fleetIds: string;
    vehicleIds: string;
    timeRange: any[];
    alarmTypes?: string;
    sourceTypes: string;
    cleanStatus: number;
    // 查询是否包含子组，当组不为空有效，默认0，0-否，1-是
    includeSubFleet: 1 | 0;
    states?: any;
}

const EVIDENCE_TYPES_PARAM = '1,2,5,8,9'; // 查询、清理 - 证据类型的参数
const CLEAN_STATUS = 0; // 证据清理状态：0-未清理，1-已清理
const VIDEO_FILE_TYPES = '2,5,8';

const filter = (list: TreeNode[], title: string | number): TreeNode[] => {
    if (title === '') {
        return list;
    }
    const re = [];
    const listLength = list.length;
    for (let index = 0; index < listLength; index += 1) {
        /** 父级满足条件，子级全保留 */
        if (list[index].title.includes(`${title}`)) {
            re.push(list[index]);
            // @ts-ignore
        } else if (list[index].children && list[index].children.length) {
            // @ts-ignore
            list[index].children = filter(list[index].children, title);
            // @ts-ignore
            if (list[index].children.length > 0) {
                re.push(list[index]);
            }
        }
    }
    return re;
};
export type QueryFormShareProps = ListPageQueryForm & DataClearCustom;


const ManualClear = (props: QueryFormShareProps) => {
    /**定制项 */
    const { getQueryForm, getCardDetail, getClearBtn, injectSearchList } = props;
    /**end */
    const { renderEmpty } = useContext(ConfigProvider.ConfigContext);
    mosaicManager.useMosaicInit();
    const { AspectRatioImage } = aspectRatioImage.AspectRatioImageV1;

    const treeRef = useRef<any>(null);
    // 证据来源
    const EVIDENCE_TYPES = [
        {
            label: i18n.t('name', '手动'),
            value: VideoSource.video,
        },
        {
            label: i18n.t('name', '证据自动上传'),
            value: VideoSource.evidence,
        },
    ];
    const APP_ID = getAppGlobalData('APP_ID');
    const initTimeRange = getInitTimeRange(7);
    const searchStore = useUrlSearchStore();
    const [appList, setAppList] = useState([]);
    const [appId, setAppId] = useState(
        Number(searchStore.get().AppId) || APP_ID,
    );
    const [carGroupName, setCarGroupName] = useState('');
    const [treeData, setTreeData] = useState<TreeNode[]>([]);
    const [treeDataList, setTreeDataList] = useState<TreeNode[]>([]);
    const [treeRenderData, setTreeRenderData] = useState<TreeNode[]>([]);
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const [selectCarGroupId, setSelectCarGroupId] = useState('');
    const [selectedKeys, setSelectedKeys] = useState<string | undefined>(
        undefined,
    );
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [queryParams, setQueryParams] = useState<ReqParams>({
        page: 1,
        pageSize: 20,
        fleetIds: '',
        vehicleIds: '',
        timeRange: [...initTimeRange],
        sourceTypes: '',
        cleanStatus: CLEAN_STATUS,
        includeSubFleet: 1,
    });
    const [listData, setListData] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [size, setSize] = useState<string | number>('0.00 KB');
    const [alarmTypes, setAlarmTypes] = useState<any>([]);
    const [firstFetch, setFirstFetch] = useState(true);
    const [vehicles, setVehicles] = useState([]);
    const [authVehicles, setAuthVehicles] = useState([]);
    const [hasNextPage, setHasNextPage] = useState<boolean>(false);
    const [treeHeight, setTreeHeight] = useState(0);
    const { inSaaS } = useAppList({}, [0]);
    const containerRef = useRef<any>(null);
    const drawerRef = useRef<any>(null);
    const { isAbroadStyle } = useSystemComponentStyle();

    // TODO异步竞态临时解决 后面框架出一个异步竞态的解决方案
    const requestIdRef = useRef(0); //
    
    useEffect(() => {
        const {
            page,
            pageSize,
            alarmTypes,
            cleanStatus = CLEAN_STATUS,
            startTime,
            endTime,
            states,
            appId,
            fleetIds,
            vehicleIds,
            includeSubFleet,
            searchCarGroupName,
            ...values
        } = searchStore.get();
        const searchList = {
            ...values,
            page: Number(page) || 1,
            pageSize: Number(pageSize) || 20,
            alarmTypes: alarmTypes && Number(alarmTypes),
            cleanStatus: cleanStatus && Number(cleanStatus),
            timeRange:
                startTime && endTime
                    ? [
                        getLocalMomentByZeroTimeStamp(Number(startTime)) ||
                        null,
                        getLocalMomentByZeroTimeStamp(Number(endTime)) ||
                        null,
                    ]
                    : initTimeRange,
            vehicleIds: vehicleIds,
            fleetIds,
            includeSubFleet: (includeSubFleet && Number(includeSubFleet)) || 1,
        };
        searchCarGroupName &&
            onChange({
                target: { value: searchCarGroupName },
            });
        form.setFieldsValue(searchList);
        setSelectCarGroupId(fleetIds);
        setQueryParams(searchList);
        fetchList();
        if (containerRef.current) {
            let treeHeight = getOffsetInfo(containerRef.current).height || 0;
            if (isAbroadStyle) {
                // 海外风格高度问题需要减去65
                treeHeight = treeHeight - 65;
            }
            setTreeHeight(treeHeight);
        }
    }, []);

    const appIdChange = (evidenceId: any) => {
        searchStore.set({
            ...searchStore.get(),
            appId: evidenceId,
        });
        setAppId(evidenceId);
        setSelectedKeys('');
        setSelectCarGroupId('');
    };
    const treeFilter = (val: any) => {
        setCarGroupName(val);
    };
    const debounceChange = debounce(treeFilter, 2000);

    const onChange = (e: any) => {
        debounceChange(e.target.value);
    };
    // 获取证据列表
    const fetchList = async () => {
        const currentRequestId = ++requestIdRef.current;
        if (!queryParams.fleetIds) {
            setListData([]);
            setHasNextPage(false);
            setSize(0);
            setTotal(0);
            return;
        }
        setLoading(true);
        let startTime;
        let endTime;
        if (queryParams.timeRange) {
            startTime = timestampToZeroTimeStamp(
                queryParams.timeRange[0],
                undefined,
                false,
                queryParams.timeRange[0],
            );
            endTime = timestampToZeroTimeStamp(
                queryParams.timeRange[1],
                undefined,
                false,
                queryParams.timeRange[0],
            );
        } else {
            startTime = timestampToZeroTimeStamp(
                initTimeRange[0],
                undefined,
                false,
                initTimeRange[0],
            );
            endTime = timestampToZeroTimeStamp(
                initTimeRange[1],
                undefined,
                false,
                initTimeRange[0],
            );
        }
        const params = {
            ...queryParams,
            startTime,
            endTime,
            fields: 'driver,file,alarm,vehicle,file_url', // 'file,alarm,vehicle',
            evidenceTypes: EVIDENCE_TYPES_PARAM,
            states: 3,
        };
        const {
            page,
            pageSize,
            fleetIds,
            vehicleIds,
            alarmTypes,
            sourceTypes,
            includeSubFleet,
            cleanStatus,
        } = params;
        searchStore.set({
            startTime,
            endTime,
            page,
            pageSize,
            fleetIds,
            vehicleIds,
            cleanStatus,
            alarmTypes,
            sourceTypes,
            includeSubFleet,
            searchCarGroupName: carGroupName,
        });
        // 未分组车组
        if ((params.fleetIds as any) == '-1') {
            params.fleetIds = '';
            if (!params.vehicleIds) {
                params.vehicleIds = vehicles.map((p: any) => p.value).join(',');
            }
        }
        // @ts-ignore
        delete params.timeRange;
        const mosaicFlags = mosaicManager.getMosaicFlags(params.evidenceTypes, (type) => {
            const isVideo = VIDEO_FILE_TYPES.includes(type);
            return isVideo ? MosaicTypeEnum.videoStore : MosaicTypeEnum.alarm;
        });
        if (injectSearchList) {
            setLoading(true);
            const { list, hasNextPage = false, dataSize } = await injectSearchList(params).finally(() => {
                setLoading(false);
            });
            setListData(list.map((i: any) => {
                return i;
            }));
            setHasNextPage(hasNextPage);
            setSize(
                formatByte(dataSize.totalSize || 0),
            );
            setTotal(dataSize.total || 0);
            return;
        }
        evidenceApi
            .getPageList({
                ...params,
                mosaicFlags,
                fields: `${params.fields},noTotal,videoChannel`,
            })
            .then(async (rs: any) => {
                if (currentRequestId === requestIdRef.current) {
                      const list = rs.list || [];
                      const data = list.map((i: any) => {
                          return i;
                      });
                    setListData(data);
                    setHasNextPage(rs?.hasNextPage);
                    // 获取统计数据
                    const dataSize = await evidenceApi.getFileTotalSize({
                        ...params,
                    });
                    setSize(
                        formatByte(dataSize.totalSize || 0),
                    );
                    const totalNum = dataSize.total || 0;
                    setTotal(totalNum);
                }

            })
            .catch(() => {
                setListData([]);
                setHasNextPage(false);
                setSize(0);
                setTotal(0);
            })
            .finally(() => {
                setLoading(false);
            });
    };
    const searchHandler = (params: ReqParams) => {
        g_emmiter.emit('clear.manage.search');
        setQueryParams({
            ...queryParams,
            ...params,
            page: 1,
            fleetIds: selectCarGroupId,
        });
    };

    const pageChange = (pageArow: string) => {
        if (pageArow === 'pre') {
            setQueryParams({
                ...queryParams,
                page: queryParams.page - 1,
                fleetIds: selectCarGroupId,
            });
        } else {
            setQueryParams({
                ...queryParams,
                page: queryParams.page + 1,
                fleetIds: selectCarGroupId,
            });
        }
    };
    const queryFormItem = [
        {
            label: i18n.t('name', '车辆'),
            name: 'vehicleIds',
            field: Select,
            itemProps: {},
            fieldProps: {
                allowClear: true,
                options: [...vehicles],
                showSearch: true,
                filterOption: (input: any, option: any) => {
                    return (
                        option.label
                            .toLowerCase()
                            .indexOf(input.toLowerCase()) >= 0
                    );
                },
                placeholder: i18n.t('message', '请选择车牌号码'),
            },
        },
        {
            label: i18n.t('name', '报警类型'),
            name: 'alarmTypes',
            field: Select,
            itemProps: {},
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择报警类型'),
                options: alarmTypes,
            },
        },
        {
            label: i18n.t('name', '证据来源'),
            name: 'sourceTypes',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择证据来源'),
                options: EVIDENCE_TYPES,
            },
        },
        {
            label: i18n.t('name', '时间范围'),
            name: 'timeRange',
            colSize: 2,
            field: DateRange,
            itemProps: {
                initialValue: initTimeRange,
            },
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                    // value: 31 * 84600,
                    // unitOfTime: 'seconds',
                },
                pickerProps: {
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    ranges: getPickerRangeTWM(),
                },
            },
        },
    ];
    const queryItems: ItemConfig[] = getCustomItems(
        getQueryForm,
        queryFormItem,
        { selectCarGroupId, selectedKeys },
    );
    // 创建清除任务
    const createClearTask = () => {
        const vehicleIds = form.getFieldsValue().vehicleIds;
        let operateDetail: string;
        let operationDetailModelCode: string;
        const logParams: any[] = [];
        if (vehicleIds) {
            const vehicle: any = vehicles.find(
                (item: any) => item.value === vehicleIds,
            ); // label
            logParams.push({ data: vehicle?.label });
            operateDetail = 'cleanVehicles';
            operationDetailModelCode = vehicle?.label;
        } else {
            const fleet: any = treeDataList.find(
                (item: any) => item.id === selectCarGroupId,
            ); // title
            logParams.push({ data: fleet?.title });
            operateDetail = 'cleanFleet';
            operationDetailModelCode = fleet?.title;
        }
        Modal.confirm({
            centered: true,
            title: i18n.t('name', '清除确认'),
            content: i18n.t('message', '您确认清除数据吗？删除后不可恢复！'),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <Icon component={DeleteConfirmIcon as any} />,
            onOk: () => {
                const params: any = {
                    taskName: `${i18n.t(
                        'name',
                        '证据清理',
                    )}${queryParams.timeRange[0].format(
                        'YY-MM-DD HH:mm:ss',
                    )}~${queryParams.timeRange[1].format('YY-MM-DD HH:mm:ss')}`,
                    taskType: '2', // 任务类型：1-自动生成,2-手动触发
                    // dataType: '1',
                    condition: {
                        startTime: timestampToZeroTimeStamp(
                            queryParams.timeRange[0],
                        ),
                        endTime: timestampToZeroTimeStamp(
                            queryParams.timeRange[1],
                        ),
                        fleetIds:
                            (queryParams.fleetIds as any) == '-1'
                                ? ''
                                : queryParams.fleetIds,
                        vehicleIds: queryParams.vehicleIds,
                        evidenceTypes: EVIDENCE_TYPES_PARAM,
                        alarmTypes: queryParams.alarmTypes,
                        states: queryParams.states,
                        includeSubFleet: queryParams.includeSubFleet,
                        sourceTypes: queryParams.sourceTypes,
                    },
                    logParams,
                    operationModelCode: 'dataManualCleaning',
                    operationTypeCode: 'edit',
                    operationDetailTypeCode: operateDetail,
                    operationDetailModelCode,
                };
                clearTaskApi.createClearTask(params)?.then((res) => {
                    message.success(i18n.t('message', '操作成功'));
                    setTimeout(() => {
                        clearTaskApi
                            .getCleanList({ cleanIds: res })
                            .then((data) => {
                                const { state } = data?.[0] || {};
                                if (state === 3) {
                                    fetchList();
                                } else {
                                    message.warn(
                                        i18n.t(
                                            'message',
                                            '正在清理中，请稍后刷新页面',
                                        ),
                                    );
                                }
                            });
                    }, 2000);
                });
            },
        });
    };
    // 重置
    const handleReset = () => {
        g_emmiter.emit('clear.manage.search');
        setSize(0);
        setTotal(0);
        searchHandler({
            ...form.getFieldsValue(),
        });
    };
    // 获取应用列表
    useEffect(() => {
        if (APP_ID) {
            return;
        }
        clearTaskApi
            .getAppList({
                page: 1,
                pageSize: 999999999, // 需要查询全部，接口只支持分页查询，所以pageSize需要很大
            })
            .then((rs: any) => {
                const list = (rs.list || []).map((item: any) => ({
                    label: i18n.t(
                        `@i18n:@app__${item.applicationId}`,
                        item.applicationName,
                    ),
                    value: item.applicationId,
                }));
                setAppList(list);
                if (list.length > 0) {
                    setAppId(
                        Number(searchStore.get().appId) || list[0]['value'],
                    );
                }
            });
    }, []);
    // 根据选择的应用拉取车组树数据
    useAsyncEffect(async () => {
        const { tenantId, userId } = getAppGlobalData('APP_USER_INFO');
        if (appId === null || appId === undefined || appId === '') {
            return;
        }
        const treeData1: any = await companyApi.getTree({
            appId,
            domainType: 1,
            ...utils.general.getSortParam(),
        });
        let treeList = treeData1 || [];
        // 未分组车辆
        // const res = await clearTaskApi.getAuthVehilceList({
        const res = await getSpecialVehicle({
            appId,
            userId,
            // isInclude: 1,
            page: 1,
            pageSize: 1e8,
        });
        if (res && res.length) {
            const treeData = [
                {
                    key: '-1',
                    title: i18n.t('name', '未分组车组'),
                },
            ];
            const list = res.map((item: any) => {
                return {
                    label: item.vehicleNumber,
                    value: item.vehicleId,
                };
            });
            setAuthVehicles(list);
            treeList = treeList.concat(treeData);
        }
        setTreeData(treeList);
        const dataList = await companyApi.getList({
            appId,
            domainType: 1,
        });
        setTreeDataList(dataList);
        // 获取告警类型
        alarmApi
            // .getAuthorityCategory({ appId })
            .getAuthAlarmTypes({
                tenantId,
                page: 1,
                pageSize: 1e5,
                state: 1,
            })
            .then((rs) => {
                setAlarmTypes(rs);
            });
    }, [appId]);

    // 树筛选
    useEffect(() => {
        const data: TreeNode[] = filter(
            JSON.parse(JSON.stringify(treeData)),
            carGroupName,
        );
        setTreeRenderData(data);
        const result = filterSearchFleet(JSON.parse(JSON.stringify(treeData)));
        let parentIds: string[] = [];
        result.forEach((fleet: any) => {
            if (fleet.parentId) {
                parentIds = parentIds.concat(findAllParentIds(fleet.parentId));
            }
        });
        if (data.length > 0) {
            // setSelectCarGroupId(data[0]['key']);
            if (carGroupName !== '') {
                setExpandedKeys([...new Set(parentIds)]);
            } else {
                const expandKeys = treeData.map((item) => item.key);
                setExpandedKeys(expandKeys);
            }
        }
        if (data.length > 0 && firstFetch) {
            // const defaultSelect = data[0]['key'] || '';
            // setSelectCarGroupId(defaultSelect);
            setQueryParams({
                ...queryParams,
                // fleetIds: defaultSelect,
            });
            setFirstFetch(false);
        }
    }, [treeData, carGroupName]);
    const getUnGroupVehicle = () => {
        const { userId } = getAppGlobalData('APP_USER_INFO');
        getSpecialVehicle({
            appId,
            userId,
            page: 1,
            pageSize: 1e8,
        }).then((res) => {
            const list = res.map((item: any) => {
                return {
                    label: item.vehicleNumber,
                    value: item.vehicleId,
                };
            });
            setVehicles(list);
        });
    };
    useEffect(() => {
        fetchList();
    }, [queryParams]);
    useEffect(() => {
        if (selectedKeys !== undefined) {
            form && form.setFieldsValue({ vehicleIds: null });
        }
        if (selectCarGroupId == null || selectCarGroupId == '') {
            setVehicles([]);
            // 未选中清空右侧列表
            searchHandler({
                ...form.getFieldsValue(),
            });
            return;
        }
        if (selectCarGroupId == '-1') {
            if (authVehicles && authVehicles.length) {
                setVehicles(authVehicles);
                return;
            } else {
                getUnGroupVehicle();
                return;
            }
        }
        getVehiclerListByPage(
            {
                fleetIds: selectCarGroupId,
                includeSubFleet: 1,
                page: 1,
                pageSize: 1e5,
            },
            true,
        )
            .then((rs: any) => {
                // console.log(rs);
                if (rs && rs.list) {
                    const result = rs.list.map((i: any) => {
                        return {
                            value: i.vehicleId,
                            label: i.vehicleNumber,
                        };
                    });
                    setVehicles(result);
                    searchHandler({
                        ...form.getFieldsValue(),
                    });
                }
            })
            .catch(() => {
                setVehicles([]);
            });
    }, [selectCarGroupId]);

    // 查找所有符合的车组
    function filterSearchFleet(data: any[]) {
        let result: any[] = [];
        data.forEach((fleet) => {
            if (fleet.title.includes(carGroupName)) {
                result.push(fleet);
            }
            if (fleet.children?.length > 0) {
                result = result.concat(filterSearchFleet(fleet.children));
            }
        });
        return result;
    }

    // 查找某个车组的所有父级车组
    function findAllParentIds(id: string | number): any[] {
        let result = [];
        const fleet: any = treeDataList.find((item: any) => item.id === id);
        if (fleet) {
            result.push(fleet.key);
            if (fleet.pId) {
                result = result.concat(findAllParentIds(fleet.pId));
            }
        }
        return result;
    }

    // 获取证据名称
    const getEvidenceName = (data: any) => {
        if (!data?.evidenceName) return '';
        const evidenceName = i18n.t(
            `@i18n:@alarmType__${data?.alarmInfo?.alarmType}`,
            data?.evidenceName,
        );
        return evidenceName;
    };

    const renderTime = (item: Record<string, any>) => {
        if (item.evidenceType === 1 || item.evidenceType === 9) {
            return item.happenTime
                ? zeroTimeStampToFormatTime(item.happenTime)
                : '-';
        } else {
            return item.startTime
                ? zeroTimeStampToFormatTime(item.startTime)
                : '-';
        }
    };

    const getMultiSearchValue = (value: any) => {
        if (!value) {
            setSelectedKeys('');
            setSelectCarGroupId('');
        }
    };
    // 根据key找到对应的title
    function findTitleByKey(data: TreeNode[], key: string | undefined): string | null {
        for (const item of data) {
            if (item.key === key) {
                return item.title;
            }
            if (item.children && item.children.length > 0) {
                const result = findTitleByKey(item.children, key);
                if (result) {
                    return result;
                }
            }
        }
        return null;
    }
    
    const listDom =
        listData.length === 0 ? (
            <div style={{ width: '100%' }}> {renderEmpty('List')}</div>
        ) : (
            <RspCardLayout>
                {listData.map((item) => {
                    const {
                        fileList = [],
                        vehicleInfo,
                        videoChannelList,
                        evidenceType
                    } = item || {};
                    const { videoList, videoPriority } =
                        evidenceUtils.getVideoList({
                            fileList: fileList,
                            channelPriority: videoChannelList,
                            isN9M:
                                vehicleInfo?.protocolType ===
                                ProtocolTypesEnum.N9M,
                            isForceH264: false,
                            evidenceType: evidenceType
                        });
                    const firstVideo = head(videoList) as any;
                    const existH264 = firstVideo?.fileType === FileType.H264;
                    const imageList = evidenceUtils.getImageList(
                        fileList,
                        videoPriority,
                    );
                    const tooltip = (
                        <Tooltip title={getEvidenceName(item)}>
                            <OverflowEllipsisContainer>
                                {getEvidenceName(item)}
                            </OverflowEllipsisContainer>
                        </Tooltip>
                    );
                    // type=1 手动证据 证据名称 or type=2 自动证据 报警类型
                    // type=9 平台报警证据
                    const evidenceAction =
                        item.evidenceType === 1 || item.evidenceType === 9 ? (
                            <Action
                                code="@base:@page:task.clear@action:tab.clear:view.evidence"
                                url="/evidence/detail"
                                target="blank"
                                params={{
                                    evidenceId: item.evidenceId,
                                    fromList: '1',
                                }}
                                needWidthPage
                                fellback={tooltip}
                            >
                                {tooltip}
                            </Action>
                        ) : (
                            <Action
                                code="@base:@page:task.clear@action:tab.clear:view.video"
                                url="/video/detail"
                                target="blank"
                                params={{
                                    evidenceId: item.evidenceId,
                                    fromList: '1',
                                    type: 'video',
                                }}
                                needWidthPage
                                fellback={tooltip}
                            >
                                {tooltip}
                            </Action>
                        );

                    const detail = (
                        <div className="base-info">
                            <p className="info-title text-overflow-ellipsis">
                                {evidenceAction}
                            </p>
                            <p className="text-overflow-ellipsis primary-text">
                                <Tooltip title={item.vehicleInfo.vehicleNumber}>
                                    <OverflowEllipsisContainer>
                                        {item.vehicleInfo.vehicleNumber}
                                    </OverflowEllipsisContainer>
                                </Tooltip>
                            </p>
                            <p className="secondary-text">
                                <span className='secondary-text-title'>
                                    {item.evidenceType === 1 ||
                                        item.evidenceType === 9
                                        ? i18n.t('name', '报警时间')
                                        : i18n.t('name', '视频时')}
                                    ：
                                </span>
                                <OverflowEllipsisContainer>
                                    {renderTime(item)}
                                </OverflowEllipsisContainer>
                            </p>
                            <p className="secondary-text">
                                <span className='secondary-text-title'>{i18n.t('name', '产生时间')}：</span>
                                <OverflowEllipsisContainer>
                                    {item.createTime
                                    ? zeroTimeStampToFormatTime(
                                        item.createTime,
                                    )
                                    : '-'}
                                </OverflowEllipsisContainer>
                            </p>
                        </div>
                    );
                    const isVideo = VIDEO_FILE_TYPES.includes(item.evidenceType);
                    const mosaicSourcecode = isVideo ? MosaicTypeEnum.videoStore : MosaicTypeEnum.alarm;
                    const renderEvidenceCardTypeObject = renderEvidenceCardType(firstVideo, imageList, item.fileChannelAuth);
                    return (
                        <List.Item key={item.evidenceId}>
                            <div className="list-item" key={item.evidenceId}>
                                {renderEvidenceCardTypeObject.image ? (
                                    <div className="img-wrapper">
                                        <DisableRightMouseClickImage>
                                            <AspectRatioImage
                                                style={{width: '100%', height: '100%'}}
                                                preview={false} 
                                                src={imageList?.[0]?.url} 
                                            />
                                        </DisableRightMouseClickImage>
                                    </div>
                                ) : null}
                                {renderEvidenceCardTypeObject.video && (
                                    <div className="video-wrapper">
                                        <VideoCard
                                            existH264={existH264}
                                            firstVideo={firstVideo}
                                            evidenceData={item}
                                            mosaicSourcecode={mosaicSourcecode}
                                        />
                                    </div>
                                )}
                                {renderEvidenceCardTypeObject.noData && (
                                    <div className="default-img-block">
                                        <NullIcon />
                                    </div>
                                )}
                                {getCustomJsx(getCardDetail, [detail], {
                                    item,
                                })}
                            </div>
                        </List.Item>
                    );
                })}
            </RspCardLayout>
        );
    const renderNode = (nodeData: any) => {
        return (
            <div
                className="text-overflow-ellipsis"
                style={{ maxWidth: '135px' }}
                title={nodeData.title}
            >
                {nodeData.title}
            </div>
        );
    };

    // 选择搜索结果
    async function onSelectSearchResult(
        e: React.MouseEvent,
        module: SearchModuleNameType,
        data: any,
    ) {
        let id = '';
        if (module === 'fleet') {
            id = data?.fleetId;
        } else if (module === 'vehicle') {
            id = data?.vehicleId;
        }
        const parentIds = getParentIds(id, treeDataList, {
            idKey: 'key',
            parentIdKey: 'parentId',
        });
        setExpandedKeys(expandedKeys.concat(parentIds));
        setSelectedKeys(id.toString());
        setSelectCarGroupId(id.toString());
        treeRef.current?.scrollTo({ key: id, offset: 30 });
    }

    const clearBtn = (
        <Button
            type="primary"
            disabled={listData.length === 0}
            onClick={() => {
                createClearTask();
            }}
            icon={
                <IconClear
                    className="clear-icon"
                    style={{ lineHeight: '25px' }}
                />
            }
        >
            {i18n.t('action', '清理数据')}
        </Button>
    );
    const { Left, Right } = RspDrawerTemplate;
    return (
        <div className="system-data-clear">
            <RspDrawerTemplate breakpoint='lg'>
                <Left drawerTrigger={<div className="switch-container">
                    {
                        findTitleByKey(treeRenderData, selectedKeys || selectCarGroupId) || i18n.t('name', '选择车组')
                    }
                    <IconSwitch02Fill className='switch-icon' />
                </div>} drawerProps={{ className: 'data-clear-drawer', width: 400 }} ref={drawerRef}>
                    <div className="left">
                        {/* 在应用里面不需要展示应用下拉框 */}
                        {inSaaS && (
                            <div className="top">
                                <Select
                                    placeholder={i18n.t('name', '应用')}
                                    className="select"
                                    value={appId}
                                    onChange={(val) => {
                                        appIdChange(val);
                                    }}
                                >
                                    {appList.map((app) => {
                                        return (
                                            <Select.Option
                                                key={app['value']}
                                                value={app['value']}
                                            >
                                                {app['label']}
                                            </Select.Option>
                                        );
                                    })}
                                </Select>
                            </div>
                        )}
                        <div className="bottom">
                            <MultiSearch
                                mountedLoad={false}
                                placeholder={i18n.t('message', '请输入车组名称')}
                                // @ts-ignore
                                onSelect={onSelectSearchResult}
                                onChange={getMultiSearchValue}
                                className="search-input"
                                searchModule={['fleet']}
                            />
                            <div
                                className="system-data-clear-tree-container"
                                ref={containerRef}
                            >
                                <Tree
                                    ref={treeRef}
                                    // blockNode
                                    className="cargroup-tree"
                                    // selectable
                                    showLine={{ showLeafIcon: false }}
                                    showIcon={false}
                                    switcherIcon={<CaretDownOutlined />}
                                    expandedKeys={expandedKeys}
                                    height={treeHeight}
                                    // @ts-ignore
                                    selectedKeys={[selectCarGroupId]}
                                    treeData={treeRenderData}
                                    onSelect={(selectedKeys: any[]) => {
                                        setSelectedKeys(selectedKeys.toString());
                                        // @ts-ignore
                                        setSelectCarGroupId(
                                            selectedKeys.length > 0
                                                ? selectedKeys.pop()
                                                : '',
                                        );
                                        drawerRef.current.closeDrawer();
                                        // setSelectCarGroupId(selectedKeys);
                                    }}
                                    onExpand={(newExpandedKeys: any[]) => {
                                        setExpandedKeys(newExpandedKeys);
                                    }}
                                    titleRender={renderNode}
                                />
                            </div>
                        </div>
                    </div>
                </Left>
                <Right>
                    <div className="right">
                        <Container className="form-search-container">
                            <QueryForm
                                layout="vertical"
                                items={queryItems}
                                collapseCacheKey={'@base:@page:task.clear'}
                                form={form}
                                onSearch={(reqParams) => {
                                    const timeRange = form.getFieldValue('timeRange');
                                    if (!timeRange) {
                                        form.setFieldsValue({
                                            ...form.getFieldsValue(),
                                            timeRange: initTimeRange,
                                        });
                                    }
                                    searchHandler(reqParams);
                                }}
                                onReset={handleReset}
                            />
                        </Container>

                        <div className="operate">
                            <Auth code="@base:@page:task.clear@action:tab.clear:clear">
                                {getCustomJsx(getClearBtn, [clearBtn], {
                                    formData: form.getFieldsValue(),
                                    queryParams,
                                    listData,
                                    vehicles,
                                    treeDataList,
                                    selectCarGroupId,
                                    fetchList
                                })}
                            </Auth>
                            <InfoBack
                                style={{ marginTop: 24 }}
                                title={i18n.t(
                                    'message',
                                    '包含证据 {total} 条；共计占用存储空间：{size}',
                                    {
                                        total: <span data-primary-color>{total}</span>,
                                        size: <span data-primary-color>{size}</span>,
                                    },
                                )}
                            />
                        </div>
                        <Spin spinning={loading}>
                            <div className="image-list">{listDom}</div>
                            <div
                                className={`alarm-list-pagination-box ${listData.length === 0
                                    ? 'alarm-list-pagination-box-hidden'
                                    : ''
                                    }`}
                            >
                                <div
                                    className={classNames(
                                        'alarm-list-pagination-box-item',
                                        {
                                            'alarm-list-pagination-box-item-disabled':
                                                queryParams.page == 1,
                                        },
                                    )}
                                    onClick={() =>
                                        queryParams.page == 1
                                            ? () => { }
                                            : pageChange('pre')
                                    }
                                    title={i18n.t('message', '上一页')}
                                >
                                    <StarryAbroadIcon>
                                        <IconArrow02Left />
                                    </StarryAbroadIcon>
                                </div>
                                <div
                                    className={`alarm-list-pagination-box-item current-page`}
                                >
                                    {queryParams.page}
                                </div>
                                <div
                                    onClick={() =>
                                        hasNextPage ? pageChange('next') : () => { }
                                    }
                                    className={classNames(
                                        'alarm-list-pagination-box-item',
                                        {
                                            'alarm-list-pagination-box-item-disabled':
                                                !hasNextPage,
                                        },
                                    )}
                                    title={i18n.t('message', '下一页')}
                                >
                                    <StarryAbroadIcon>
                                        <IconArrow02Right />
                                    </StarryAbroadIcon>
                                </div>
                            </div>
                        </Spin>
                    </div>
                </Right>
            </RspDrawerTemplate>
        </div>
    );
};

export default withSharePropsHOC<QueryFormShareProps>(ManualClear);
