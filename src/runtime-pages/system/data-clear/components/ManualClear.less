//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';
@card-top-height: 200px;
.system-data-clear {
    display: flex;
    &>div>div:first-child {
        gap: 24px !important;
        &::abroad {
            gap: 24px !important;
        }
    }
    .left {
        width: 280px;
        // height: 779px;
        min-height: 100%;
        padding-right: 24px;
        background: @starry-bg-color-container;
        border: 1px solid @starry-border-level-1-color;
        .poppy-tree {
            height: calc(100vh - 340px);
            min-height: 460px;
        }
        .poppy-tree-treenode {
            width: 100%;
            .poppy-tree-node-content-wrapper-normal {
                flex: 1;
            }
        }
        .poppy-tree .poppy-tree-node-content-wrapper {
            width: 100%;
        }
        //覆盖树选中状态背景色

        .poppy-tree .poppy-tree-node-content-wrapper.poppy-tree-node-selected {
            color: @primary-color;
            // background-color: rgba(89, 126, 247, 0.15);
        }
        .top {
            width: 278px;
            height: 64px;
            padding: 16px;
            background: @starry-bg-color-container;
            .select {
                width: 100%;
            }
        }
        .bottom {
            height: 100%;
            padding: 16px;
            padding-top: 24px;
            .search-input {
                width: 100%;
                margin-bottom: 16px;
            }
            .cargroup-tree {
                overflow-y: auto;
            }
        }
    }
    .left::abroad {
        background: @starry-bg-color-container;
        border: 0;
        border-radius: @border-radius-16;
    }
    .right {
        flex: 1;
        .operate {
            .clear-icon {
                height: 20px;
                line-height: 20px;
                vertical-align: text-bottom;
            }
        }
        .alarm-list-pagination-box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 24px 0 32px;
            &-item {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                padding: 10px;
                border-radius: @border-radius-8;
                cursor: pointer;
                transition: 0.5s;
                &:hover {
                    color: @primary-color;
                }
            }
            .current-page {
                display: flex;
                align-items: center;
                justify-content: center;
                color: @primary-color;
                border: 1px solid @primary-color;
                border-color: @primary-color;
                border-radius: @border-radius-8;
            }
            &-item-disabled {
                color: @starry-text-color-disabled;
                cursor: not-allowed;
            }
        }
        .alarm-list-pagination-box-hidden {
            display: none;
        }
        .image-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            .poppy-list-item {
                padding: 0;
            }
            .list-item {
                display: flex;
                flex: 1px;
                flex-direction: column;
                height: 346px;
                // margin-bottom: 24px;
                overflow: hidden;
                background: @starry-bg-color-container;
                border: 1px solid @starry-border-level-2-color;
                border-radius: @border-radius-4;
                overflow: hidden;
                &::abroad{
                    border-radius: @border-radius-12;
                }
                .flex-space-between {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }
                .default-img-block {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: @card-top-height;
                    color: @grey-100;
                    font-size: 38px;
                    background: @grey-900;
                    &::abroad{
                        border-radius: @border-radius-12;
                    }
                }
                .img-wrapper,
                .video-wrapper {
                    position: relative;
                    width: 100%;
                    height: @card-top-height;
                    background: @grey-900;
                    &::abroad{
                        border-radius: @border-radius-12 @border-radius-12 0 0;
                    }
                    /* .video-info {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        height: 36px;
                        padding: 0 16px;
                        color: #fff;
                        background: @starry-bg-color-container;
                    } */
                }
                .base-info {
                    display: flex;
                    flex: 1;
                    flex-direction: column;
                    justify-content: space-between;
                    padding: 16px 16px;
                    p {
                        margin-bottom: 0;
                        font-weight: 400;
                        font-size: 14px;
                    }
                    .info-title {
                        font-weight: 600;
                        font-size: 16px;
                    }
                    .secondary-text {
                        display: flex;
                        align-items: center;
                        flex-wrap: nowrap;
                        color: @starry-text-color-secondary;
                        .secondary-text-title {
                            flex-shrink: 0;
                        }
                    }
                }
            }
            .list-item::abroad {
                border: 0;
            }
        }
        .pagination {
            float: right;
        }
        .poppy-picker-range {
            width: 100%;
        }
        .poppy-new-picker-range {
            width: 100%;
        }
    }
    .switch-container {
        font-weight: 700;
        font-size: 20px;
        .switch-icon {
            margin-left: 8px;
            font-size: 16px;
            &::abroad {
                font-size: 20px;
            }
        }
    }
}
.list-item::abroad {
    border-radius: @border-radius-12;
}
.form-search-container {
    padding-bottom: 0 !important;
}
.primary-text::abroad {
    color: @starry-text-color-primary;
}
.system-data-clear-tree-container::abroad {
    height: 100%;
    margin-top: 8px;
}

.data-clear-drawer {
    .left {
        width: 100%;
        padding: 0;
        border-right: none;
    }
}
