/*
 * @LastEditTime: 2024-12-18 10:34:06
 */

import { escapeHtml } from "@/utils/util";

const renderCarHtml = (
    angle: number,
    vehicleInfo: {
        vehicleId: string;
        vehicleNumber: string;
    },
    selectedId?: string,
): string => {
    const isSelected = vehicleInfo.vehicleId === selectedId;
    const escapeVehicleNumber = escapeHtml(vehicleInfo.vehicleNumber);
    const showVehicleNumberContent = isSelected
        ? `<div class="vehicle-name ${isSelected && 'selected-vehicle-name'}" title=${
              escapeVehicleNumber
          }>${escapeVehicleNumber}</div>`
        : '';
    const vehicle = `
        <div class="vehicle-wrapper">
            <div 
                class='vehicle-icon'
            >
                <span role="img" class="bg-border ${
                    isSelected && 'selected-bg-border'
                }" style="transform: ${
        isSelected ? 'translate(-50%, -50%)' : ''
    } rotate(${angle}deg)">
                    <svg width="${isSelected ? '36px' : '32px'}" height="${
        isSelected ? '90px' : '72px'
    }" viewBox="0 0 34 58" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                        <g id="监控" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="a" transform="translate(-64.000000, -85.000000)">
                                <g id="icon_bg" transform="translate(64.000000, 85.000000)">
                                    <rect id="矩形备份-5" x="0" y="0" width="34" height="58"></rect>
                                    <path class="${
                                        isSelected ? 'border-path' : 'no-border-path'
                                    }" d="M17,13 C25.836556,13 33,20.163444 33,29 C33,32.3260464 31.9851253,35.4150562 30.2481513,37.9742539 L30.2505971,37.9735015 L17,57 L3.76803896,38.0002611 C2.02202026,35.4367644 1,32.337735 1,29 C1,20.163444 8.163444,13 17,13 Z" id="形状结合备份-8"></path>
                                </g>
                            </g>
                        </g>
                    </svg>
                </span>
                <span role="img" class="bg ${isSelected && 'selected-bg'}" style="transform: ${
        isSelected ? 'translate(-50%, -50%)' : ''
    } rotate(${angle}deg)">
                    <svg width="${isSelected ? '28px' : '28px'}" height="${
        isSelected ? '76px' : '68px'
    }" viewBox="0 0 34 58" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                        <g id="监控" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                            <g id="a" transform="translate(-64.000000, -85.000000)">
                                <g id="icon_bg" transform="translate(64.000000, 85.000000)">
                                    <rect id="矩形备份-5" x="0" y="0" width="34" height="58"></rect>
                                    <path d="M17,13 C25.836556,13 33,20.163444 33,29 C33,32.3260464 31.9851253,35.4150562 30.2481513,37.9742539 L30.2505971,37.9735015 L17,57 L3.76803896,38.0002611 C2.02202026,35.4367644 1,32.337735 1,29 C1,20.163444 8.163444,13 17,13 Z" id="形状结合备份-8" fill=${'#09AB69'}></path>
                                </g>
                            </g>
                        </g>
                    </svg>
                </span>
                <img 
                    class='car ${isSelected && 'selected-car'}' 
                    src=${require('@/assets/images/icon_map_car_current.png')}
                />
                <img 
                    class='angle-border ${isSelected && 'selected-angle-border'}' 
                    src=${require('@/assets/icons/icon_wireframe.svg')}
                    style="transform: translate(-50%, -50%) rotate(${angle}deg)"
                />
            </div>
            ${showVehicleNumberContent}
        </div>
    `;
    return vehicle;
};

const getCarIcon = (
    angle: number,
    vehicleInfo: { vehicleId: string; vehicleNumber: string },
    L: any,
    selectedId?: string,
) => {
    const icon: any = L?.divIcon({
        className: 'region-vehicle-find-car-icon',
        html: renderCarHtml(angle, vehicleInfo, selectedId),
        iconAnchor: [28, 28],
        iconSize: [28, 28],
    });
    return icon;
};

export default getCarIcon;
