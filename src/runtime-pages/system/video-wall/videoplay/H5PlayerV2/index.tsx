/**
 * @description 与H5Player相同，此组件是使用SDK2.x版本
 */
import { useRef, useEffect, useState } from 'react';
import PlayerGroup from './components/PlayerGroup/index';
import PlayerContextProvider from '../H5Player/store';
import { message } from '@streamax/poppy';
import { useLocation, useHistory } from '@base-app/runtime-lib/core';
import { getAppGlobalData, i18n, mosaicManager, MosaicTypeEnum, FlOW_BU_TYPE } from '@base-app/runtime-lib';
import { getcarListPOST as fetchVehicleList } from '@/service/videowall';
import { OnlineState, ChannelEnableState } from '../H5Player/types';
import type { PlayerGroupRef } from './components/PlayerGroup/index';
import type { PlayerOption, VehicleInfo, DeviceInfo, ChannelInfo } from '../H5Player/types';
import { COM_CLS_PREFIX } from '../H5Player/constant';
import '../H5Player/index.less';
import DisableRightMouseClick from '@/components/DisableRightMouseClick';
import useMosaic from '@/hooks/useMosaic';
import { useSafeState, useUpdateEffect } from 'ahooks';
import { getBaseURL } from '@/utils/h5sdk';
import { LIVE_VIDEO_RECONNECT_INTERVAL } from '@/utils/constant';
import useLiveDecoderType from '@/hooks/useLiveDecoderType';
import { fetchChannelType } from '@/utils/commonFun';


export enum PatrolChannelTypeEnum {
    CHANNEL = 1,
    CHANNEL_TYPE
}

const PAGE_SIZE = 1000;
export default () => {
    const location: any = useLocation();
    const history = useHistory();
    const { channelNumber, pollingTime, channelList, fleetList, vehicleIdList, patrolChannelType=PatrolChannelTypeEnum.CHANNEL,channelTypeList } =
        location?.query || {};
    // 防止组件卸载触发返回
    const [isSafeGoBack, setIsSafeGoBack] = useSafeState(false);
    const compMountRef = useRef(false);

    const player = useRef<PlayerGroupRef | null>(null);

    // 根据车组请求车辆分页查询,当前页
    const page = useRef(1);
    // 根据车组请求车辆分页查询，是否结束
    const requestFinish = useRef(false);
    // 根据车辆ID请求车辆的标识，只请求一次
    const vehicleRequestedFlag = useRef(false);
    // 播放通道列表
    const playerList = useRef<PlayerOption[]>([]);
    const [decoderPromise] = useLiveDecoderType();
    // 仓内通道数据
    const cabinChannelListRef = useRef<{
        deviceId: string;
        cabinChannels: number[];
    }[]>();


    useEffect(() => {
        init();
        return () => {
            requestFinish.current = true;
            compMountRef.current = true;
        };
    }, []);

    useUpdateEffect(() => {
        if (isSafeGoBack) {
            history.goBack();
            setIsSafeGoBack(false);
        }
    }, [isSafeGoBack]);

    async function init() {
        const playerOptionList = await handleData();
        playerList.current = [...playerList.current, ...playerOptionList];
        // 1.没有请求完，获取的播放通道数大于等于用户设置的通道数
        // 2.请求完，通道数>0
        const decoderTypeObj = await decoderPromise;
        if (
            (!requestFinish.current && playerList.current.length >= channelNumber) ||
            (requestFinish.current && playerList.current.length > 0)
        ) {
            player.current?.load({
                config: {
                    baseURL: getBaseURL(),
                    // mse解码 1mse 0 wasm 2webCodecs
                    decoderType: (decoderTypeObj.multiLiveDecoderType || 1) as any,
                    // 打码类型  0 模糊 1方块
                    mosaicType: 1,
                    // mosaic
                    mosaic: mosaicManager.getVideoMosaicConfigValue(MosaicTypeEnum.live),
                    reconnectTime: LIVE_VIDEO_RECONNECT_INTERVAL,
                    headers: {
                        _busCustom: FlOW_BU_TYPE.STARRY_LIVE
                    }
                },
                headerConfig: {
                    _tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
                    _appId: getAppGlobalData('APP_ID'),
                    _token: window.localStorage.getItem('AUTH_TOKEN') || '',
                },
                playerOptionList: playerList.current,
                cabinChannelList: cabinChannelListRef.current
            });
            if (!requestFinish.current) {
                update();
            }
            return;
        }
        if (!requestFinish.current) {
            init();
        } else {
           !compMountRef.current &&
               message.error(i18n.t('message', '所选车组所属车辆已全部下线，请重新选择'));
            setIsSafeGoBack(true);
        }
        player.current?.load({
            config: {
                baseURL: getBaseURL(),
                // mse解码 1mse 0 wasm
                decoderType: (decoderTypeObj.multiLiveDecoderType || 1) as any,
                // 打码类型  0 模糊 1方块
                mosaicType: 1,
                // mosaic
                mosaic: mosaicManager.getVideoMosaicConfigValue(MosaicTypeEnum.live),
                reconnectTime: LIVE_VIDEO_RECONNECT_INTERVAL,
                headers: {
                    _busCustom: FlOW_BU_TYPE.STARRY_LIVE
                }
            },
            headerConfig: {
                _tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
                _appId: getAppGlobalData('APP_ID'),
                _token: window.localStorage.getItem('AUTH_TOKEN') || '',
            },
            playerOptionList: playerOptionList,
            cabinChannelList: cabinChannelListRef.current
        });
    }

    async function update() {
        const playerOptionList = await handleData();
        if (playerOptionList.length != 0) {
            playerList.current = [...playerList.current, ...playerOptionList];
            player.current?.update(playerList.current);
            if (!requestFinish.current) {
                update();
            }
        }
    }

    // 批量处理获取设备的仓内通道列表
    const getDevicesCabinChannels = async (allDeviceList: { deviceId: string; authId: string }[]) => {
        // 将设备列表按1000个一组进行分块
        const CHUNK_SIZE = 1000;
        const deviceChunks = [];
        
        // 手动分块，避免引入额外依赖
        for (let i = 0; i < allDeviceList.length; i += CHUNK_SIZE) {
            deviceChunks.push(allDeviceList.slice(i, i + CHUNK_SIZE));
        }
        
        // 并行请求每个分块的数据
        const chunkPromises = deviceChunks.map(chunk => 
            fetchChannelType(chunk.map(d => d.deviceId).join(','))
        );
        
        // 等待所有请求完成并合并结果
        const chunkResults = await Promise.all(chunkPromises);
        
        // 将所有结果扁平化为一个数组
        const cabinChannelList = chunkResults.flat();
        
        return cabinChannelList.map(i => ({
            deviceId: i.authId,
            cabinChannels: i.cabinChannels,
        }));
    };

    async function handleData() {
        const vehicleList = await getVehicleList();
        // 设备列表
        const allDeviceList: { deviceId: string; authId: string }[] = [];
        // 用户所勾选的通道
        const checkedChannels: number[] = channelList.map((c: string) => Number(c));
        const result: PlayerOption[] = [];
        
        vehicleList.forEach((vehicle: VehicleInfo) => {
            const { deviceList, vehicleNumber } = vehicle;
            deviceList.forEach((device: DeviceInfo) => {
                const { authId, deviceChannelList, onlineState, deviceId } = device;
                allDeviceList.push({
                    deviceId,
                    authId
                });
                // 判断设备是否在线
                if (onlineState === OnlineState.online) {
                    deviceChannelList.forEach((deviceChannel: ChannelInfo & { channelTypeList?: { channelTypeId: string; channelTypeName: string; }[] }) => {
                        const { channelNo, enable, channelAlias, channelTypeList: deviceChannelTypes } = deviceChannel;
                        // 判断通道类型来确定使用哪种过滤方式
                        let shouldIncludeChannel = false;
                        if (Number(patrolChannelType) === PatrolChannelTypeEnum.CHANNEL) {
                            // 按通道号过滤：此通道被用户勾选并且可用
                            shouldIncludeChannel = checkedChannels.includes(channelNo) && enable === ChannelEnableState.enable;
                        } else if (Number(patrolChannelType) === PatrolChannelTypeEnum.CHANNEL_TYPE) {
                            // 按通道类型过滤：检查用户选择的通道类型是否在该通道支持的通道类型列表中
                            if (deviceChannelTypes && deviceChannelTypes.length > 0 && enable === ChannelEnableState.enable) {
                                // 判断用户选择的通道类型是否与该通道支持的通道类型有交集
                                shouldIncludeChannel = channelTypeList.some(type => deviceChannelTypes?.map(item=>item.channelTypeId).includes(type));
                            }
                        }
                        if (shouldIncludeChannel) {
                            result.push({
                                devId: authId,
                                channel: channelNo,
                                channelAlias,
                                vehicleNumber,
                                key: `${authId}-${channelNo}`,
                                status: 'idle',
                            });
                        }
                    });
                }
            });
        });
        const cabinChannelList = await getDevicesCabinChannels(allDeviceList);
        cabinChannelListRef.current = cabinChannelList;
        return result;
    }

    async function getVehicleList() {
        const promiseList = [];
        let vehicleIds = '';
        let fleets = [];
        try {
            vehicleIds = (JSON.parse(vehicleIdList) || []).join(',');
            fleets = JSON.parse(fleetList);
        } catch (error) {
            // empty
            return [];
        }
        // 根据车辆ID查询车辆
        let vehiclePromise = Promise.resolve({
            list: [],
            total: 0,
        });
        if (vehicleIds && !vehicleRequestedFlag.current) {
            vehiclePromise = fetchVehicleList({
                fields: 'device,channel,channelAllInfo',
                pageSize: 1e8,
                vehicleIds,
                vehicleState: 1,
            });
            vehicleRequestedFlag.current = true;
        }
        // 根据车组ID查询车辆
        let fleetPromise = Promise.resolve({
            list: [],
            total: 0,
        });
        if (fleets.length > 0) {
            fleetPromise = fetchVehicleList({
                fields: 'device,channel,channelAllInfo',
                fleetList: JSON.parse(fleetList),
                pageSize: PAGE_SIZE,
                page: page.current,
                vehicleState: 1,
            });
        }
        const vRes = await vehiclePromise;
        const fRes = await fleetPromise;
        if (fRes.total) {
            if (fRes.total > PAGE_SIZE * page.current) {
                page.current = page.current + 1;
            } else {
                requestFinish.current = true;
            }
        } else {
            requestFinish.current = true;
        }

        // 得到所有的车辆
        const vehicleList: VehicleInfo[] = (vRes?.list || []).concat(fRes?.list || []);
        const result: VehicleInfo[] = [];
        // 去重
        vehicleList.forEach((item: VehicleInfo) => {
            if (!result.find((i) => i.vehicleId === item.vehicleId)) {
                item?.onlineState === OnlineState.online && result.push(item);
            }
        });
        return result;

        // const result = Promise.all(promiseList).then((res) => {
        //     // 得到所有的车辆
        //     const vehicleList: VehicleInfo[] = (res[0]?.list || []).concat(res[1]?.list || []);
        //     const result: VehicleInfo[] = [];
        //     // 去重
        //     vehicleList.forEach((item: VehicleInfo) => {
        //         if (!result.find((i) => i.vehicleId === item.vehicleId)) {
        //             item?.onlineState === OnlineState.online && result.push(item);
        //         }
        //     });
        //     return result;
        // });
        // return result;
    }

    return (
        <PlayerContextProvider>
            <div className={COM_CLS_PREFIX}>
                <div className={`${COM_CLS_PREFIX}-header`} />
                <div className={`${COM_CLS_PREFIX}-wrapper`}>
                    <DisableRightMouseClick>
                        <PlayerGroup
                            ref={player}
                            layoutCount={Number(channelNumber)}
                            pollingTime={Number(pollingTime) * 1000}
                        />
                    </DisableRightMouseClick>
                </div>
            </div>
        </PlayerContextProvider>
    );
};
