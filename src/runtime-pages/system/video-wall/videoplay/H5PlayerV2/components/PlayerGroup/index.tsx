import React, {
    useImperativeHandle,
    forwardRef,
    useEffect,
    useContext,
    useRef,
    useState,
} from 'react';
import useDeviceChannelData from './hooks/useDeviceChannelData';
import type { ForwardRefRenderFunction } from 'react';
import type { PlayerOption, LoadPlayerParams } from '../../../H5Player/types';
import { Context } from '../../../H5Player/store';
import BaseVideo from '../../../H5Player/components/BaseVideo';
import ChannelLayout from '@/components/StarryPlayer/components/ChannelLayout';
// @ts-ignore
import { i18n, utils, useV2H5SDKAbility } from '@base-app/runtime-lib';
import {
    PLAYER_STATUS,
    ENLARGE_STATE,
    STREAM_TYPE,
    COM_CLS_PREFIX_PLAYER_GROUP,
    getNeedMessageErrorCode
} from '../../../H5Player/constant';
import PlayerLayout from '../../../H5Player/layout/PlayerLayout';
import type { ChannelBorderColorType } from '../../../H5Player/layout/PlayerLayout';
import Reload from '../../../H5Player/components/Reload';
import CloseVideo from '../../../H5Player/components/CloseVideo';
import ControllerBar from '../../../H5Player/layout/ControllerBar';
import Volume from '../../../H5Player/components/Volume';
import FullScreen from '../../../H5Player/components/FullScreen';
import Enlarge from '../../../H5Player/components/Enlarge';
import Screenshot from '../../../H5Player/components/Screenshot';
import Clarity from '../../../H5Player/components/Clarity';
import { StarryPlayer } from '@base-app/runtime-lib';

const { PlayAspectRatio } = StarryPlayer.components;
import useOperationPlayer from './hooks/useOperationPlayer';
import './index.less';
import { useAsyncEffect, useDebounceFn } from '@streamax/hooks';
import Pagination from '../../../H5Player/components/Pagination';
import SwitchPollingStatus from '../../../H5Player/components/SwitchPollingStatus';
import { IconPlay02Fill } from '@streamax/poppy-icons';
// @ts-ignore
import { setChannelTitle } from '@base-app/runtime-lib';
import { globalMessageMention } from '@/utils/commonFun';

// 通道loading等待时间
const LOADING_WAIT_TIME = 3000;

export interface PlayerProps {
    // 布局通道数量
    layoutCount?: number;
    // 轮询时间
    pollingTime?: number;
}

type Config = Omit<LoadPlayerParams['config'], 'ip' | 'port' | 'wasmPath' | 'httpProtocol' | 'aspectRatio'> & {
    reconnectTime?: number;
};
type ExtendLoadPlayerParams = Omit<LoadPlayerParams, 'config'> & {config: Config};
const { videoAspectRatio } = utils;

export interface PlayerGroupRef {
    // 播放视频方法
    load: (params: Omit<ExtendLoadPlayerParams, 'config'> & {config: Config}) => void;
    // 更新播放列表和总页数
    update: (playerOptionList: PlayerOption[]) => void;
    // 停止播放
    stop: () => void;
}

export interface BaseVideoInfo {
    dom: HTMLElement;
    playerChannelInfo: PlayerChannelInfo;
}

export interface PlayerChannelInfo extends PlayerOption {
    key: string;
}

const PlayerGroup: ForwardRefRenderFunction<PlayerGroupRef, PlayerProps> = (props, ref) => {
    const {
        layoutCount: defaultLayoutCount,
        volume,
        deviceChannelInfoMap,
        volumeDeviceChannel,
        activeDeviceChannel,
        currentPage,
        playerStatus,
        switchPollingStatus,
        deviceChannelFullScreen,
        playAspectRatio,
        dispatch,
    } = useContext(Context);

    const { layoutCount, pollingTime } = props;
    const deviceChannels = useDeviceChannelData();
    const baseVideos = useRef<Record<string, BaseVideoInfo>>({});
    const playerLayouts = useRef<Record<string, HTMLElement>>({});
    const rootDom = useRef<HTMLDivElement>(null);
    const { players, destroy, updateChannelInfo, selectDeviceChannel } = useOperationPlayer({
        baseVideos: baseVideos.current,
        initPlayer,
        deviceChannels,
        playerLayouts: playerLayouts.current,
        rootDom: rootDom.current as HTMLDivElement,
    });
    const [renderCover, setRenderCover] = useState<JSX.Element | null>(null);
    const channelLoadingWaitMap = useRef<Record<string, number>>({});

    // 播放器配置
    const playerConfig = useRef<{
        config: ExtendLoadPlayerParams['config'];
        headerConfig: ExtendLoadPlayerParams['headerConfig'];
    } | null>(null);

    useEffect(() => {
        const cancel = setChannelTitle('.starry-player-channel-layout-tile', '.player-wrapper');
        return cancel;
    }, []);

    useAsyncEffect(async () => {
        // 初始化设置画面比例
        dispatch({ 
            type: 'setPlayAspectRatio', 
            payload: await videoAspectRatio.getVideoAspectRatio() 
        });
    }, []);

    // 每次分页信息改变的时候，需要将之前的播放器销毁
    useEffect(() => {
        for (const key in baseVideos.current) {
            // @ts-ignore
            destroy(baseVideos.current[key]?.key);
        }
    }, [currentPage]);

    // 更新布局数量
    useEffect(() => {
        layoutCount && dispatch({ type: 'setLayoutCount', payload: layoutCount });
    }, [layoutCount]);

    // 更新轮询时间
    useEffect(() => {
        pollingTime && dispatch({ type: 'setPollingTime', payload: pollingTime });
    }, [pollingTime]);

    /**
     * 根据传入的数据，获取组装仓内通道数据
     * @param options 传入播放器的配置参数
     * @returns 仓内通道列表
     */
    const getCabinChannel = (options: LoadPlayerParams) => {
        const { cabinChannelList } = options;
        if (!cabinChannelList?.length) return [];
        const list: string[] = [];
        cabinChannelList.forEach((item) => {
            item.cabinChannels.forEach((channel) => {
                list.push(`${item.deviceId}-${channel}`);
            });
        });
        return list;
    };
    // 对外暴露方法
    useImperativeHandle(ref, () => ({
        load: (params: ExtendLoadPlayerParams) => {
            const { config, headerConfig, playerOptionList } = params;
            playerConfig.current = { config, headerConfig };
            const totalPage = Math.ceil(
                playerOptionList.length / (layoutCount || defaultLayoutCount),
            );
            dispatch({
                type: 'initStateConfig',
                payload: {
                    playerOptionList,
                    totalPage: totalPage,
                    loaded: true,
                    cabinChannel: getCabinChannel(params)
                },
            });
        },
        update: (playerOptionList: PlayerOption[]) => {
            const totalPage = Math.ceil(
                playerOptionList.length / (layoutCount || defaultLayoutCount),
            );
            dispatch({
                type: 'initStateConfig',
                payload: {
                    playerOptionList,
                    totalPage: totalPage,
                },
            });
        },
        stop: () => destroy(),
    }));


    // copy 自starry-player直通播放器，实现视频墙播放器隐私保护蒙层展示功能
    // 直通播放器接入NovaPlayer时，统一实现接入
    /** @description 直通视频每一个通道都是一个播放器，触发错误时，会短时间触发多次，因此使用防抖来规避 */
    const { run: debounceGlobalMessage } = useDebounceFn(globalMessageMention, {
        wait: 500,
    });
    // 创建播放器
    const createLivingPlayer = async (dom: HTMLElement, playerChannelInfo: PlayerChannelInfo) => {
        const { channel, streamType, devId, key, mosaic, status } = playerChannelInfo;
        // 若设备是暂停使用的，则设置通道状态为暂停使用，不初始化播放器
        if (status === PLAYER_STATUS.stopUse) {
            updateChannelInfo(key, {
                status: PLAYER_STATUS.stopUse,
            });
            return;
        }
        const { SPlayer } = await useV2H5SDKAbility.loadV2H5SDK() as any;
        const { headers = {}, ...restConfig } = playerConfig.current?.config || {};
        SPlayer.config = restConfig;
        SPlayer.defaultHeaders = playerConfig.current?.headerConfig;
        
        const reqParams = {
            streamType: deviceChannelInfoMap[key]?.streamType || streamType || STREAM_TYPE.MINOR,
            // 业务操作 id，可用于操作留痕记录等
            optId: '123',
            wasmPath: '/player',
            // 打码类型  0 模糊 1方块
            mosaicType: 1,
            mosaic,
            decoderType: 1,
            deviceInfos: [
                { devId, channels: String(channel) },
            ],
            headers: { ...headers }
        };
        updateChannelInfo(key, {
            status: PLAYER_STATUS.loading,
        });
        players.current[key] = SPlayer.createLivePlayer([dom], reqParams);
        const currentPlayer = players.current[key];
            players.current[key].load();
        if (currentPlayer) {
            // 设置默认画面比例
            const aspectRatioValue = await videoAspectRatio.getVideoAspectRatio();
            players.current[key]?.pluginMap.aspectRatio.set(aspectRatioValue || 'origin');
            // 加载成功事件
            players.current[key].hooks.afterLoaded.tap('auto play', (player: any) => {
                player.play();
            });
            // 开始播放事件
            players.current[key].hooks.afterPlay.tap('begin play', () => {
                updateChannelInfo(key, {
                    status: PLAYER_STATUS.playing,
                    errorMsg: '',
                });
                if (players.current[key]) {
                    const [devId, channel] = key.split('-');
                    if (key === volumeDeviceChannel) {
                        players.current[key].volume = [{ devId, channel: Number(channel), volume }];
                    } else {
                        players.current[key].volume = [{ devId, channel: Number(channel), volume: 0 }];
                    }
                }
            });
            // 播放错误事件
            players.current[key].hooks.onError.tap('play error', (errorData: any) => {
                // eslint-disable-next-line no-console
                const { errorInfo = {}, errorType, subErrorType } = errorData;
                console.error('====【sdk error】====', errorData);
                // 确保触发中的hooks是当前播放器，而不是上一次播放器触发的hooks
                if (players.current[key] !== currentPlayer) return;
                updateChannelInfo(key, {
                    status: PLAYER_STATUS.error,
                    enlarge: ENLARGE_STATE.close,
                });

                /** sdk说二级错误编码按照如下方式取 */
                let subErrorCode = (window as any).playerjs?.errorCode
                ?.sdkErrorCode[errorType];
                // 这两个错误码返回的errorType是NETWORK_ERROR，需要展示定制文案
                if(['NO_GPU_RESOURCE', 'VIDEO_OVER_LOAD'].includes(subErrorType)){
                    subErrorCode = subErrorType;
                    dispatch({
                        type: 'setGpuError',
                        payload: true,
                    });
                }

                /** @description 根据业务要求，只对部分错误进行消息提示 */
                debounceGlobalMessage(subErrorCode, getNeedMessageErrorCode());
            });
            // 播放器销毁事件
            players.current[key].hooks.afterDestroy.tap('play destroy', () => {
                if (players.current[key] !== currentPlayer) return;
                updateChannelInfo(key, {
                    ...(deviceChannelInfoMap[key] || {}),
                    status: PLAYER_STATUS.stop,
                    enlarge: ENLARGE_STATE.close,
                });
                delete players.current[key];
            });
            // SDK断线重连
            players.current[key]?.hooks.startReconnect.tap('start reconnecting', () => {
                // 断线重连时，展示loading动画
                if (players.current[key] !== currentPlayer) return;
                updateChannelInfo(key, {
                    status: PLAYER_STATUS.loading,
                    errorMsg: '',
                });
            });
            players.current[key]?.hooks.reconnectSuccess.tap('reconnect success', () => {
                if (players.current[key] !== currentPlayer) return;
                updateChannelInfo(key, {
                    status: PLAYER_STATUS.playing,
                    errorMsg: '',
                });
            });
            players.current[key].hooks?.onLoading?.tap(
                'loading',
                (list: any[]) => {
                    if (players.current[key] !== currentPlayer) return;
                    list.forEach((item: any) => {
                        const { network, percent } = item;
                        if (percent < 100) {
                            // 此通道处于loading中
                            const currentTime = Date.now();
                            if (!channelLoadingWaitMap.current[key]) {
                                channelLoadingWaitMap.current[key] = currentTime;
                            }
                            if (currentTime - channelLoadingWaitMap.current[key] > LOADING_WAIT_TIME) {
                                updateChannelInfo(key, {
                                    status: PLAYER_STATUS.loading,
                                    internetSpeed: network
                                });
                            }
                        } else {
                            channelLoadingWaitMap.current[key] = 0;
                            updateChannelInfo(key, {
                                status: PLAYER_STATUS.playing,
                                internetSpeed: network
                            });
                        }
                    });
                },
            );
        }
    };

    // 初始化播放器
    function initPlayer(dom: HTMLElement, playerChannelInfo: PlayerChannelInfo) {
        if (dom) {
            destroy(playerChannelInfo.key);
            createLivingPlayer(dom, playerChannelInfo);
        }
    }

    // 渲染重新加载
    function renderReload(key: string) {
        // 只要是报错或者停止状态就展示重新加载按钮
        const playerStatus = deviceChannelInfoMap[key]?.status;
        if ([PLAYER_STATUS.stop, PLAYER_STATUS.error].includes(playerStatus)) {
            return (
                <span className="reload-icon">
                    <Reload deviceChannel={key} />
                </span>
            );
        }
        return null;
    }

    //视频全部重新播放
    function getRenderCover() {
        if (playerStatus === 'stop') {
            return (
                <div className="cover-wrapper">
                    <div className="reload-wrapper">
                        <Reload iconText={i18n.t('name', '视频已关闭')} icon={<IconPlay02Fill />} />
                    </div>
                </div>
            );
        }

        return null;
    }

    useEffect(() => {
        setRenderCover(getRenderCover());
    }, [playerStatus]);

    // Dom加载完成
    function handleOnLoaded(dom: HTMLElement, playerChannelInfo: PlayerChannelInfo) {
        const { key } = playerChannelInfo;
        // 将播放器的dom容器存起来
        baseVideos.current[key] = {
            dom,
            playerChannelInfo,
        };
        loadPlayersDebounceFn();
    }

    // Dom销毁
    function handleOnDestory(playerChannelInfo: PlayerChannelInfo) {
        const { key } = playerChannelInfo;

        if (baseVideos.current[key]) {
            delete baseVideos.current[key];
            destroy(key);
        }
        loadPlayersDebounceFn();
    }

    const { run: loadPlayersDebounceFn } = useDebounceFn(
        () => {
            loadPlayers();
        },
        {
            wait: 100,
        },
    );

    function loadPlayers() {
        for (const key in baseVideos.current) {
            const { dom, playerChannelInfo } = baseVideos.current[key];
            initPlayer(dom, playerChannelInfo);
        }
    }

    function fullScreenStateChange() {
        if (!deviceChannelFullScreen) {
            dispatch({
                type: 'setPollingStatus',
                payload: switchPollingStatus,
            });
        }
    }

    const generateFileName = (vehicleData: any) => {
        const { vehicleNumber = '', channelAlias, channelNo } = vehicleData || {};
        const now = parseInt((Date.now() / 1000) as unknown as string);
        const name = `${vehicleNumber}_${now}_${channelAlias || channelNo || ''}`;
        // 操作系统文件名称不支持的特殊字符按需求替换成空
        return name.replace(/[\/:*?"<>|\\]/g, '');
     }

    const toolBarContent = (deviceChannel: string) => (
        <>
            <Clarity deviceChannel={deviceChannel} />
            <Screenshot deviceChannel={deviceChannel} generateFileName={generateFileName} />
            <Enlarge deviceChannel={deviceChannel} />
            <FullScreen
                deviceChannel={deviceChannel}
                fullScreenStateChange={fullScreenStateChange}
            />
            <CloseVideo deviceChannel={deviceChannel} />
        </>
    );

    function handleOnClickPlayerLayout(key: string) {
        selectDeviceChannel(key, deviceChannelInfoMap[key]?.enlarge === ENLARGE_STATE.open);
    }

    function handleOnDoubleClick(
        e: React.MouseEvent<HTMLDivElement, MouseEvent>,
        currentDeviceChannel: string,
    ) {
        e.stopPropagation();
        dispatch({
            type: 'setActionInfo',
            payload: {
                actionName: 'fullScreen',
                actionDeviceChannel: currentDeviceChannel,
            },
        });
    }

    return (
        <div className={COM_CLS_PREFIX_PLAYER_GROUP} ref={rootDom}>
            <div className="layout-wrapper">
                {renderCover}
                <ChannelLayout supportResponsive={true}>
                    {deviceChannels.map((item: PlayerOption) => {
                        const { channel, devId, vehicleNumber, key } = item;
                        let borderColor: ChannelBorderColorType = 'none';
                        volumeDeviceChannel === key && (borderColor = 'green');
                        activeDeviceChannel === key && (borderColor = 'primary');
                        return (
                            <PlayerLayout
                                key={key}
                                vehicleNumber={vehicleNumber}
                                borderColor={borderColor}
                                toolBarContent={toolBarContent(key)}
                                onDoubleClick={(e) => handleOnDoubleClick(e, `${devId}-${channel}`)}
                                onClick={() => handleOnClickPlayerLayout(key)}
                                onLoaded={(dom) => (playerLayouts.current[key] = dom)}
                            >
                                <BaseVideo
                                    channel={channel}
                                    device={devId}
                                    onLoaded={(dom) => handleOnLoaded(dom, { ...item, key })}
                                    onDestory={() => handleOnDestory({ ...item, key })}
                                    key={key}
                                    coverContent={renderReload(key)}
                                />
                            </PlayerLayout>
                        );
                    })}
                </ChannelLayout>
            </div>
            <ControllerBar
                centerContent={<Pagination />}
                rightContent={
                    <>
                        <SwitchPollingStatus />
                        <PlayAspectRatio 
                            style={{}} 
                            onPlayAspectRatioChange={(value) => {
                                videoAspectRatio.setVideoAspectRatio(value as any);
                            }}
                            dispatch={dispatch}
                            isLive={true}
                            playAspectRatio={playAspectRatio}
                            disabledWidgets={[]}
                        />
                        <Volume />
                        <FullScreen />
                        <CloseVideo />
                    </>
                }
            />
        </div>
    );
};

export default forwardRef<PlayerGroupRef, PlayerProps>(PlayerGroup);
