/*
 * @LastEditTime: 2025-04-15 13:35:20
 */
import { FlOW_BU_TYPE } from "@/runtime-lib";

// 播放器状态
export type PlayerStatus = 'idle' | 'loading' | 'pause' | 'playing' | 'stop' | 'error' | 'stopUse';
// 码流类型
export type StreamType = 'MINOR' | 'MAJOR' | 'CENTER';
// 布局类型
export type LayoutType = 'tile' | 'highlight';

export type PollingStatus = 0 | 1;
export type EnlargeStatus = 0 | 1;

// 车辆/设备在线状态
export enum OnlineState {
    online = 1,
    outline = 0,
}

export enum ChannelEnableState {
    enable = 1,
    unable = 0,
}

// 通道基本信息
export interface ChannelBaseInfo {
    devId: string;
    channel: number;
}

// 播放器通道配置信息
export interface PlayerOption {
    devId: string;
    streamType?: StreamType;
    channel: number;
    channelAlias?: string;
    vehicleNumber: string;
    mosaic?: 1 | 2;
    key: string;
    status: PlayerStatus;
}

// 设备通道状态信息
export interface DeviceChannelStateInfo {
    // 单通道状态
    status: PlayerStatus;
    // 错误信息
    errorMsg?: string;
    // 其他
    [key: string]: any;
}

export interface LoadPlayerParams {
    config: {
        ip: string;
        port: number;
        wasmPath: string;
        httpProtocol: string;
        decoderType: 0 | 1;
        mosaicType: 0 | 1;
        mosaic: 0 | 2;
        aspectRatio: string;
        baseURL: string;
        _busCustom?: FlOW_BU_TYPE;
    };
    headerConfig: {
        _tenantId: string;
        _appId: string | number;
        _token: string;
    };
    playerOptionList: PlayerOption[];
    // 舱内通道
    cabinChannelList?: {deviceId: string, cabinChannels: number[]}[] | undefined;
}

// 通道信息
export interface ChannelInfo {
    channelAlias: string;
    channelNo: number;
    enable: ChannelEnableState;
    peripheralType: number;
}

// 设备信息
export interface DeviceInfo {
    deviceChannelList: ChannelInfo[];
    deviceId: string;
    deviceNo: string;
    onlineState: OnlineState;
    protocolType: number;
    vehicleId: string;
    authId: string;
}

// 车辆信息
export interface VehicleInfo {
    onlineState: OnlineState;
    vehicleNumber: string;
    vehicleId: string;
    deviceList: DeviceInfo[];
}
