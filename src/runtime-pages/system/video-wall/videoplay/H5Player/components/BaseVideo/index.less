@import '~@streamax/poppy-themes/starry/index.less';

.video-wall-h5player-basevideo {
    position: relative;
    width: 100%;
    height: 100%;
    background: #0D0D0D;
    &-sdk-player {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
    }
    &-notice-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
        .loading-icon {
            color: @primary-color;
            font-size: 34px;
        }
    }
    &-cover-content-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        background: #0D0D0D;
    }
    .loading-icon{
        position: absolute;
        left: 50%;
        top: 50%;
        z-index: 10;
        transform: translate(-50%, -50%);
    }
    &-error-msg {
        color: rgba(255, 255, 255, 0.65);
        position: absolute;
        z-index: 10;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
    }
    &-gpu-error-msg{
        z-index: 100;
        background: #0D0D0D;
    }
    &-screen-shot {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        object-fit: contain;
        transform: translate(-50%, -50%);
    }
}
