/**
 * @description 基础单通道播放器dom节点组件，主要负责渲染挂载H5SDK player的dom节点和错误信息等
 */

import React, { useContext, useEffect, useRef, useState } from 'react';
import cn from 'classnames';
import { useDeepCompareEffect } from '@streamax/hooks';
import { IconLoading } from '@streamax/poppy-icons';
import { i18n } from '@base-app/runtime-lib';
import { Context } from '../../store';
import { PLAYER_STATUS } from '../../constant';
import './index.less';

const clsPrefix = `video-wall-h5player-basevideo`;
const IC_GPU_ERROR = require('@/assets/icon-status-conceal.svg');


export interface BaseVideoProps {
    // 设备号
    device: string;
    // 通道号
    channel: number;
    // 额外覆盖展示信息
    coverContent?: React.ReactNode;
    // 加载完回调，返回用于挂载SDK Player的节点
    onLoaded?: (dom: HTMLDivElement) => void;
    onDestory?: () => void;
    className?: string;
}

const BaseVideo: React.FC<BaseVideoProps> = (props) => {
    const { device, channel, coverContent, onLoaded, onDestory, className } = props;

    const [loading, setLoading] = useState(false);
    const [showErrorMsg, setShowErrorMsg] = useState(false);
    const [showFlowControl, setShowFlowControl] = useState(false);
    // 隐私保护蒙层展示
    const [showPrivacyProtection, setPrivacyProtection] = useState(false);
    const [errorMsg, setErrorMsg] = useState<string | undefined>('');
    const [showScreenshot, setShowScreenshot] = useState(false);

    const playerMountDom = useRef<HTMLDivElement>(null);

    const { deviceChannelInfoMap, gpuError, cabinChannel } = useContext(Context);

    useEffect(() => {
        onLoaded?.(playerMountDom.current as HTMLDivElement);
        return () => {
            onDestory?.();
        };
    }, []);

    useDeepCompareEffect(() => {
        const channelInfo = deviceChannelInfoMap[`${device}-${channel}`];
        if (channelInfo) {
            const { status, errorMsg, screenshot } = channelInfo;
            setLoading(status === PLAYER_STATUS.loading);
            setShowErrorMsg(status === PLAYER_STATUS.error);
            setShowFlowControl(status === PLAYER_STATUS.stopUse);
            setErrorMsg(errorMsg);
            setShowScreenshot(status === PLAYER_STATUS.stop && !!screenshot);
        }
    }, [JSON.stringify(deviceChannelInfoMap), channel, device]);

    useEffect(() => {
        if (gpuError) {
            const channelStr = `${device}-${channel}`;
            if (cabinChannel.includes(channelStr)) {
                setPrivacyProtection(true);
            } else {
                setPrivacyProtection(false);
            }
        } else {
            setPrivacyProtection(false);
        }
    }, [gpuError, cabinChannel, channel, device]);

    return (
        <div className={cn(clsPrefix, className)}>
            <div className={`${clsPrefix}-sdk-player`} ref={playerMountDom} />
            <div className={`${clsPrefix}-notice-wrapper`}>
                {loading && <IconLoading spin className="loading-icon" />}
                {showErrorMsg && (
                    <span className={`${clsPrefix}-error-msg`}>
                        {errorMsg || i18n.t('message', '加载失败')}
                    </span>
                )}
                {showFlowControl && (
                    <span className={`${clsPrefix}-error-msg`}>
                        {i18n.t('message', '设备流量超额，暂停使用')}
                    </span>
                )}
                {/* 隐私保护蒙层，层级最高，覆盖在其他信息上 */}
                {
                    showPrivacyProtection && (
                        <span className={`${clsPrefix}-error-msg ${clsPrefix}-gpu-error-msg`}>
                            <img src={IC_GPU_ERROR} />
                            <p className={`${clsPrefix}-gpu-error-msg-text`}>{i18n.t('message', '隐私保护')}</p>
                        </span>
                    )
                }
            </div>
            {showScreenshot && deviceChannelInfoMap[`${device}-${channel}`].screenshot && (
                <img
                    className={`${clsPrefix}-screen-shot`}
                    src={deviceChannelInfoMap[`${device}-${channel}`].screenshot}
                />
            )}
        </div>
    );
};

export default BaseVideo;
