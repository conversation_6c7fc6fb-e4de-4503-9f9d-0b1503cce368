import type { PlayerStatus, StreamType, PollingStatus, EnlargeStatus } from './types';
import { i18n } from '@/runtime-lib';

// 播放器状态
export const PLAYER_STATUS: Record<PlayerStatus, PlayerStatus> = {
    idle: 'idle',
    loading: 'loading',
    pause: 'pause',
    playing: 'playing',
    stop: 'stop',
    error: 'error',
    stopUse: 'stopUse',
};

export const POLLING_STATE: Record<'open' | 'stop', PollingStatus> = {
    open: 1,
    stop: 0,
};

// 电子放大状态
export const ENLARGE_STATE: Record<'open' | 'close', EnlargeStatus> = {
    open: 1,
    close: 0,
};

// 码流类型
export const STREAM_TYPE: Record<StreamType, StreamType> = {
    MAJOR: 'MAJOR', // 主码流
    MINOR: 'MINOR', // 子码流
    CENTER: 'CENTER',
};

// 组件类名前缀
export const COM_CLS_PREFIX = 'video-wall-h5player';
export const COM_CLS_PREFIX_TEMPLATE = `${COM_CLS_PREFIX}-template`;
export const COM_CLS_PREFIX_CONTROLLER_BAR = `${COM_CLS_PREFIX}-controller-bar`;
export const COM_CLS_PREFIX_TOOL_BAR = `${COM_CLS_PREFIX}-tool-bar`;
export const COM_CLS_PREFIX_PLAYER_LAYOUT = `${COM_CLS_PREFIX}-player-layout`;
export const COM_CLS_PREFIX_PLAYER_GROUP = `${COM_CLS_PREFIX}-player-group`;
export const COM_CLS_PREFIX_WIDGET = `${COM_CLS_PREFIX}-widget`;
export const COM_CLS_PREFIX_TOOLBAR = `${COM_CLS_PREFIX}-toolbar`;
export const COM_CLS_PREFIX_CHANNEL_LAYOUT = `${COM_CLS_PREFIX}-channel-layout`;


export const getNeedMessageErrorCode = () => {
    const NEED_MESSAGE_ERRORCODE: Record<string, string> = {
        NO_GPU_RESOURCE: i18n.t('message', '服务器资源异常，暂不支持视频/图片显示马赛克'),
        VIDEO_OVER_LOAD: i18n.t('message', '服务器资源异常，暂不支持视频/图片显示马赛克'),
    };
    return NEED_MESSAGE_ERRORCODE;
};

