import type { PlayAspectRatio } from '@/runtime-lib/components/StarryPlayer/types';
import type { PlayerStatus, StreamType, PlayerOption, PollingStatus } from '../types';

type ActionInfo = {
    // 操作名称
    actionName: string;
    // 操作通道
    actionDeviceChannel?: string;
    // 操作负载
    actionPayload?: any;
};

export interface State {
    // 所有的设备通道列表
    playerOptionList: PlayerOption[];
    // 播放器配置
    config: Record<string, any> | null;
    // 轮询状态 0关闭 1开启
    pollingStatus: PollingStatus;
    // 轮询开关状态 0关闭 1开启
    switchPollingStatus: PollingStatus;
    // 轮询时间【秒】
    pollingTime: number;
    // 播放器状态
    playerStatus: PlayerStatus;
    // 视频墙通道数量
    layoutCount: number;
    // 音量
    volume: number;
    // 画面比例
    playAspectRatio: PlayAspectRatio;
    // 当前正在播放的页码
    currentPage: number;
    // 总页数
    totalPage: number;
    // 播放音源的通道
    volumeDeviceChannel: string | null;
    // 当前选中的设备通道信息
    activeDeviceChannel: string | null;
    // 用户手动停止播放的设备通道列表
    stopDeviceChannelList: string[];
    // 全屏状态
    fullscreen: boolean;
    // 全屏设备通道
    deviceChannelFullScreen: boolean;
    // 设备通道的状态信息
    deviceChannelInfoMap: {
        [deviceChannel: string]: {
            // 单通道状态
            status: PlayerStatus;
            // 码流类型
            streamType?: StreamType;
            // 电子放大的状态
            enlarge?: 0 | 1;
            // 错误信息
            errorMsg?: string;
            //截图
            screenshot?: string;
            // 其他
            [key: string]: any;
        };
    };
    // 操作动作相关数据
    actionInfo?: ActionInfo;
    // 设置方法
    dispatch?: (action: { type: string; payload: any }) => void;
    loaded: boolean;
    // 舱内通道
    cabinChannel: string[];
    // 是否有GPU不足报错
    gpuError: boolean;
}

const initialState: State = {
    playerOptionList: [],
    config: null,
    pollingStatus: 1,
    switchPollingStatus: 1,
    playerStatus: 'idle',
    pollingTime: 10 * 1000,
    layoutCount: 9,
    volume: 0.5,
    playAspectRatio: 'origin',
    currentPage: 1,
    totalPage: 1,
    fullscreen: false,
    deviceChannelFullScreen: false,
    volumeDeviceChannel: null,
    activeDeviceChannel: null,
    stopDeviceChannelList: [],
    deviceChannelInfoMap: {},
    loaded: false,
    cabinChannel: [],
    gpuError: false,
};

export default initialState;
