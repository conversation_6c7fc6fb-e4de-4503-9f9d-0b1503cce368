import type { Reducer } from 'react';
import type { State } from './state';
import type { DeviceChannelStateInfo } from '../types';

type TranslateType<T> = {
    [K in keyof T]: {
        type: K;
        payload: T[K];
    };
}[keyof T];

export type PayloadMap = {
    setVolume: State['volume'];
    setPlayAspectRatio: State['playAspectRatio'];
    setLayoutCount: State['layoutCount'];
    setPollingTime: State['pollingTime'];
    setPollingStatus: State['pollingStatus'];
    setSwitchPollingStatus: State['switchPollingStatus'];
    setPlayerOptionList: State['playerOptionList'];
    setPlayerStatus: State['playerStatus'];
    setConfig: State['config'];
    setCurrentPage: State['currentPage'];
    setActionInfo: State['actionInfo'];
    setDeviceChannelFullScreen: State['deviceChannelFullScreen'];
    setFullScreen: State['fullscreen'];
    setVolumeDeviceChannel: State['volumeDeviceChannel'];
    setActiveDeviceChannel: State['activeDeviceChannel'];
    setStopDeviceChannelList: State['stopDeviceChannelList'];
    setGpuError: State['gpuError']; // gpuError
    initStateConfig: {
        playerOptionList: State['playerOptionList'];
        totalPage: State['totalPage'];
        loaded?: State['loaded'];
        cabinChannel?: State['cabinChannel'];
    };
    updateChannelInfo: {
        deviceChannel: string;
        info: Partial<DeviceChannelStateInfo>;
    };
    updateAllChannelInfo: State['deviceChannelInfoMap'];
};

export type Action = TranslateType<PayloadMap>;

const reduce: Reducer<State, Action> = (prevState, action) => {
    const { type, payload } = action;
    switch (type) {
        case 'setVolume':
            return {
                ...prevState,
                volume: payload,
            };
        case 'setPlayAspectRatio':
            return {
                ...prevState,
                playAspectRatio: payload,
            };
        case 'setLayoutCount':
            return {
                ...prevState,
                layoutCount: payload,
            };
        case 'setPollingTime':
            return {
                ...prevState,
                pollingTime: payload,
            };
        case 'setPollingStatus':
            return {
                ...prevState,
                pollingStatus: payload,
            };
        case 'setSwitchPollingStatus':
            return {
                ...prevState,
                switchPollingStatus: payload,
            };
        case 'setPlayerStatus':
            return {
                ...prevState,
                playerStatus: payload,
            };
        case 'setPlayerOptionList':
            return {
                ...prevState,
                playerOptionList: payload,
            };
        case 'setConfig':
            return {
                ...prevState,
                config: payload,
            };
        case 'setCurrentPage':
            return {
                ...prevState,
                currentPage: payload,
            };
        case 'initStateConfig':
            return {
                ...prevState,
                ...payload,
            };
        case 'setActionInfo':
            return {
                ...prevState,
                actionInfo: payload,
            };
        case 'setDeviceChannelFullScreen':
            return {
                ...prevState,
                deviceChannelFullScreen: payload,
            };
        case 'setFullScreen':
            return {
                ...prevState,
                fullscreen: payload,
            };
        case 'setVolumeDeviceChannel':
            return {
                ...prevState,
                volumeDeviceChannel: payload,
            };
        case 'setActiveDeviceChannel':
            return {
                ...prevState,
                activeDeviceChannel: payload,
            };
        case 'setStopDeviceChannelList':
            return {
                ...prevState,
                stopDeviceChannelList: payload,
            };
        case 'setGpuError':
            return {
                ...prevState,
                gpuError: payload,
            };
        case 'updateChannelInfo':
            // 更新通道信息
            const nextChannelInfoMap = { ...prevState.deviceChannelInfoMap };
            const { deviceChannel, info } = payload;
            nextChannelInfoMap[deviceChannel] = {
                ...nextChannelInfoMap[deviceChannel],
                ...info,
            };
            return {
                ...prevState,
                deviceChannelInfoMap: nextChannelInfoMap,
            };
        case 'updateAllChannelInfo':
            // 更新通道信息
            return {
                ...prevState,
                deviceChannelInfoMap: payload,
            };
        default:
            return prevState;
    }
};

export default reduce;
