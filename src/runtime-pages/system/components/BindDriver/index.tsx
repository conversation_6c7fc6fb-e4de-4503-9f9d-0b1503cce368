import { useState } from 'react';

import { Button, Form, Modal, Select, Checkbox, Image, message, Popconfirm } from '@streamax/poppy';
import { i18n, StarryAbroadFormItem as AFormItem, useSystemComponentStyle } from '@base-app/runtime-lib';
import {
    deletePicture,
    postQueryDriverList,
    picQuality,
    addPicture,
    DriverPageItem,
    requestFaceBindDriver,
    IFaceBindDriver,
} from '@/service/driver';
import { fetchApplicationConfig } from '@/service/application';
import {
    abnornalFaceDelete,
    abnornalFaceHandle,
    abnornalAlarmDelete,
    abnornalAlarmHandle,
    AbnornalFace as AbnornalFaceInt,
} from '@/service/system';
import { aspectRatioImage } from '@base-app/runtime-lib';

import usePhotoNumber from '@/hooks/usePhotoNumber';
import { useAsyncEffect } from '@streamax/hooks';
import './index.less';
import InfoBack from '@/components/InfoBack';
import { PICTURE_SUCCESS_CODE } from '../../constant';
import { orderBy } from 'lodash';
import DisableRightMouseClickImage from '@/components/DisableRightMouseClickImage';
import { fetchFilesDetailPost } from '@/service/fms';
import { RspCardLayout, RspGridLayout, useResponsiveShow } from '@streamax/responsive-layout';
const FMS = 1;
export interface AbnornalFace extends AbnornalFaceInt {
    pictureId: string;
    pictureUrl?: string;
    driverName?: string;
    picFrom: number;
    faceLocation: string;
    score: number;
}

export const FALL_SRC = require('@/assets/images/image_fail.png');
const DRIVER_NO_PHOTO = require('@/assets/images/icon_nopicture.svg');

const PopconfirmButton = (props: {
    text: string;
    primary: boolean;
    callback: () => Promise<void>;
    preSave: () => Promise<void>;
    placement: 'topRight' | 'top';
}) => {
    const { text, primary, callback, preSave, placement } = props;
    const [visible, setVisible] = useState(false);
    const [confirmLoading, setConfirmLoading] = useState(false);

    const clickHandler = async () => {
        setConfirmLoading(true);
        try {
            const configs: { configKey: string; value: string }[] = await fetchApplicationConfig({
                keys: 'driver.binding.dynamic,driver.face.compare.plateform,driver.dynamic.compare.face.ibutton,driver.face.compare.device',
            });
            // 无数据默认关闭
            let faceOpen;
            try {
                const dynamic = (configs || []).find(
                    (p: any) => p.configKey === 'driver.binding.dynamic',
                )?.value;
                const plateFace = (configs || []).find(
                    (p: any) => p.configKey === 'driver.face.compare.plateform',
                )?.value;
                const deviceFace = (configs || []).find(
                    (p: any) => p.configKey === 'driver.face.compare.device',
                )?.value;
                const iButtonFace = (configs || []).find(
                    (p: any) => p.configKey === 'driver.dynamic.compare.face.ibutton',
                )?.value;

                const plateFaceValue = (plateFace && JSON.parse(plateFace)) || {};
                const deviceFaceValue = (deviceFace && JSON.parse(deviceFace)) || {};
                const iButtonFaceValue = (iButtonFace && JSON.parse(iButtonFace)) || {};
                const dynamicValue = (dynamic && JSON.parse(dynamic)) || {};
                faceOpen =
                    dynamicValue.isOpen &&
                    (plateFaceValue.isOpen || deviceFaceValue.isOpen || iButtonFaceValue.isOpen)
                        ? true
                        : false;
            } catch (error) {
                console.error(error);
                setConfirmLoading(false);
            }
            // 查询人脸验证开关是否关闭。关闭时才需要弹出确认提示，开启时直接提交
            if (faceOpen) {
                setConfirmLoading(true);
                await submitHandler();
                return;
            }
            try {
                await preSave();
            } catch (error) {
                console.error(error);
                setConfirmLoading(false);
                return;
            }
            setVisible(true);
        } catch (error) {
            setConfirmLoading(false);
        }
    };
    const submitHandler = async () => {
        setConfirmLoading(true);
        try {
            await callback();
            setVisible(false);
            setConfirmLoading(false);
        } catch (error) {
            setVisible(false);
            setConfirmLoading(false);
        }
    };
    return (
        <Popconfirm
            title={
                <p style={{ maxWidth: '300px' }}>
                    {i18n.t('name', '人脸验证开关未开启，待办绑定到司机可能会造成人脸照片丢失')}
                </p>
            }
            visible={visible}
            placement={placement || 'top'}
            onConfirm={submitHandler}
            onCancel={() => {
                setConfirmLoading(false);
                setVisible(false);
            }}
        >
            <Button
                type={primary ? 'primary' : 'default'}
                loading={confirmLoading}
                onClick={clickHandler}
            >
                {text}
            </Button>
        </Popconfirm>
    );
};

type BindDriverProps = {
    visible: boolean;
    selectedItems: AbnornalFace[];
    recordId: string;
    onCancel: () => void;
    onOk: () => void;
    title?: string;
    destroyOnClose?: boolean;
    className?: string;
    allPictureIds?: string[];
    pageFrom: 'faceAbnormal' | 'driverAlarm';
};

export default (props: BindDriverProps) => {
    const { visible, selectedItems, recordId, onOk, onCancel, pageFrom, allPictureIds } = props;
    const [driverList, setDriverList] = useState<DriverPageItem[]>([]);
    const [searchValue, setSearchValue] = useState('');
    const [checkedList, setCheckedList] = useState<string[]>([]); //绑定到司机勾选list
    const [currentDriver, setCurrentDriver] = useState<DriverPageItem>({} as DriverPageItem);
    const [form] = Form.useForm();
    const { getPhotoNumber } = usePhotoNumber();
    const [maxPhotoNumber, setMaxPhotoNumber] = useState(0);
    const [noPicQuality, setNoPicQuality] = useState<string[]>([]); // 记录图片没有通过校验
    const preSave = form.validateFields;

    const { AspectRatioImage } = aspectRatioImage.AspectRatioImageV1;
    const saveData = async (clear?: boolean) => {
        let deleteFun = abnornalFaceDelete;
        let handleFun = abnornalFaceHandle;
        if (pageFrom === 'driverAlarm') {
            deleteFun = abnornalAlarmDelete;
            handleFun = abnornalAlarmHandle;
        }
        const value: {
            driver: string;
            todoPhoto: string[];
            driverPhoto: string[];
        } = await form.validateFields();
        /**
         *
         * 1.检测质量
         * 2.删除未勾选的司机照片
         * 3.添加勾选的待办照片，且变更待办状态
         * 4?. 清理未勾选的待办照片
         *
         * */

        const { driverPhoto: selectDriverPhoto, todoPhoto: selectTodoPhoto = [], driver } = value;

        const unSelectDriverPhoto = currentDriver.pictureList.filter(
            (item) => !selectDriverPhoto.includes(item.pictureId),
        );
        const unSelectTodoPhoto = selectedItems.filter(
            (item) => !selectTodoPhoto.includes(item.pictureId),
        );

        const pictureList = selectedItems
            .filter((item) => selectTodoPhoto.includes(item.pictureId))
            .map((item) => ({
                pictureId: item.pictureId,
                pictureUrl: item.pictureUrl as any,
                picFrom: item.picFrom,
                faceLocation: item.faceLocation ? JSON.parse(item.faceLocation) : undefined,
            }));
        const picQualityRes = selectTodoPhoto.length ? await picQuality({ pictureList }) : [];
        if (
            picQualityRes &&
            picQualityRes.filter((item) => item.resultCode != 3120000 && item.resultCode != 3120106)
                .length
        ) {
            message.error(i18n.t('name', '算法识别异常'));
            return;
        }

        const requestParam : IFaceBindDriver = {
            recordId: recordId,
            handleType: pageFrom === 'driverAlarm' ? 1 : 0,
            handlePictureIds: '',
            handleDriverId: driver,
            addDriverPictureReqDTO: undefined,
            deleteDriverPictureReqDTO: undefined,
        }

        if (unSelectDriverPhoto.length){
            requestParam.deleteDriverPictureReqDTO = {
                driverId: driver,
                pictureIds: unSelectDriverPhoto.map((item) => item.pictureId).join(','),
            }
        };

        if(selectTodoPhoto.length) {
            requestParam.addDriverPictureReqDTO = {
                driverId: driver,
                pictureList: picQualityRes?.map((item: any) => ({
                    pictureId: item.picId,
                    pictureSource: FMS, //人脸待办照片类型
                    score: item.score,
                    resultCode: item.resultCode,
                })),
            }
            requestParam.handlePictureIds = selectTodoPhoto.join(',')

        };

        if (selectTodoPhoto.length === 0 && allPictureIds?.length) {
            requestParam.handlePictureIds = allPictureIds?.join(',')
        }

        // 解决133425问题，合并之前的删除、添加、处理接口为一个接口
        await requestFaceBindDriver(requestParam)

        clear &&
            unSelectTodoPhoto.length &&
            (await deleteFun({
                recordIds: recordId,
            }));

        message.success(i18n.t('name', '操作成功'));
        setCheckedList([]);
        setCurrentDriver({} as DriverPageItem);
        // 绑定成功
        onOk();
    };
    //@ts-ignore
    const formChange = (changedValues: any, { driverPhoto = [], todoPhoto = [] }) => {
        setCheckedList([...driverPhoto, ...todoPhoto]);
    };

    const modalCancel = () => {
        setCheckedList([]);
        setCurrentDriver({} as DriverPageItem);
        onCancel();
    };
    const selectSearch = (value: string) => {
        if (searchValue.length < 50) {
            setSearchValue(value);
        } else {
            if (value.length < 50) {
                setSearchValue(value);
            }
        }
    };

    const driverChange = async (value: any, option: DriverPageItem) => {
        /*
         * 保存司机详情 option
         * 计算需要勾选几个
         */
        option.pictureList = orderBy(option.pictureList, ['score'], ['desc']);
        // 记录没有通过校验的图片id
        setNoPicQuality(
            option.pictureList
                .filter((item: any) => item.resultCode != PICTURE_SUCCESS_CODE)
                ?.map((item) => item.pictureId),
        );
        try {
            const fileIds: string[] = [];
            const fileUuids: string[] = [];
            let list = [];
            const listMap: Record<string, string> = {};
            //后端不再返回pictureUrl，需要通过id去请求拿取
            (option.pictureList || []).forEach(item => {
                if(item.pictureSource == 0) {
                    fileIds.push(item.pictureId);
                }else{
                    fileUuids.push(item.pictureId);
                }
            });
            if(fileIds.length) {
                list = await fetchFilesDetailPost({ fileIds: fileIds.join(',') });
            }
            if(fileUuids.length) {
                const res = await fetchFilesDetailPost({ fileUuids: fileUuids.join(',') });
                list = [...list, ...res];
            }
            (list || []).forEach((item) => {
                listMap[item.fileId] = item;
                listMap[item.fileUuid] = item;
            });
            option.pictureList.forEach((item) => {
                if (item.pictureId) {
                    const file = listMap[item.pictureId]?.urls[0];
                    item.pictureUrl = file?.url;
                }
            });
        } catch (error) {}
        setCurrentDriver(option);
        // 用外层cardTable 勾选的项计算
        // 切换driver保证每次都从上往下一次勾选最多项  需先重置一下
        form.resetFields(['driverPhoto', 'todoPhoto']);
        const driverPictureList =
            option.pictureList?.map((item) => ({ ...item, type: 'direver' })) || [];
        const todoPictureList = selectedItems?.map((item) => ({ ...item, type: 'todo' }));
        const scoreTopList = orderBy(
            [...driverPictureList, ...todoPictureList],
            ['score'],
            ['desc'],
        );
        const scoreMaxPhoto = scoreTopList.slice(0, maxPhotoNumber);
        form.setFieldsValue({
            driverPhoto: scoreMaxPhoto
                ?.filter((item) => item.type == 'direver')
                ?.map((item) => item.pictureId),
            todoPhoto: scoreMaxPhoto
                ?.filter((item) => item.type == 'todo')
                .map((item) => item.pictureId),
        });
        setCheckedList(scoreMaxPhoto?.map((item) => item.pictureId));
    };
    useAsyncEffect(async () => {
        if (!visible) return;
        const maxNumber = await getPhotoNumber();
        setMaxPhotoNumber(maxNumber);

        form.setFieldsValue({
            todoPhoto: selectedItems
                .filter((item, index) => index < maxNumber)
                .map((item) => item.pictureId),
        });
        const { list } = await postQueryDriverList({
            page: 1,
            pageSize: 9999999,
            needDriverPicture: true, //这里绑定司机需要图片信息bug 62649
        });
        setDriverList(list);
    }, [visible]);
    const isResponsive = useResponsiveShow({
        xs: true,
        sm: false,
        md: false,
        lg: false,
        xl: false,
        xxl: false,
    });
    const { isAbroadStyle } = useSystemComponentStyle();
    const minCardWidth = isAbroadStyle ? 131 : 120;
    return (
        <Modal
            centered
            visible={visible}
            title={i18n.t('name', '绑定到司机')}
            destroyOnClose
            className="bind-to-driver-modal"
            onCancel={modalCancel}
            footer={[
                <Button onClick={modalCancel}> {i18n.t('name', '取消')}</Button>,
                <PopconfirmButton
                    text={i18n.t('name', '保存')}
                    primary
                    callback={async () => {
                        await saveData();
                    }}
                    preSave={preSave}
                    placement="top"
                />,
            ]}
        >
            <Form className='bind-to-driver-modal-form' form={form} onValuesChange={formChange} layout="vertical" preserve={false}>
                {selectedItems?.length ? (
                    <Form.Item
                        name={'todoPhoto'}
                        label={i18n.t('name', '待办照片')}
                        initialValue={[]}
                    >
                        <Checkbox.Group>
                            <div className="modal-img-wraper">
                                 <RspCardLayout minWidth={isResponsive?520:minCardWidth}>
                                {selectedItems.map((item) => {
                                    return (
                                        <Checkbox
                                            disabled={
                                                checkedList.length >= maxPhotoNumber &&
                                                !checkedList.includes(item.pictureId)
                                            }
                                            value={item.pictureId}
                                            key={item.pictureId}
                                        >
                                            <DisableRightMouseClickImage>
                                                <AspectRatioImage
                                                    preview={false}
                                                    src={item.pictureUrl}
                                                    fallback={FALL_SRC}
                                                />
                                            </DisableRightMouseClickImage>
                                            <svg className="score-tip-bg" width={32} height={32}>
                                                <path fill="#52C41A" d="M32 32L0 0 20 0 32 12z" />
                                            </svg>
                                            <span className="score-tip-title"> {item.score}</span>
                                        </Checkbox>
                                    );
                                })}
                                    </RspCardLayout>
                            </div>
                        </Checkbox.Group>
                    </Form.Item>
                ) : (
                    <InfoBack
                        className="bind-driver-info-back"
                        title={i18n.t(
                            'message',
                            '未知司机图片中未找到符合人脸库要求照片，可直接绑定司机，变更车辆驾驶关系',
                        )}
                    />
                )}
                <AFormItem
                    name={'driver'}
                    label={i18n.t('name', '绑定司机')}
                    rules={[
                        {
                            required: true,
                        },
                    ]}
                >
                    <Select
                        showSearch
                        onSearch={selectSearch}
                        searchValue={searchValue}
                        placeholder={i18n.t('message', '请选择司机')}
                        // @ts-ignore
                        options={driverList}
                        filterOption={(input, option) => {
                            return (
                                option?.driverName.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            );
                        }}
                        onChange={driverChange as any}
                        fieldNames={{
                            label: 'driverName',
                            value: 'driverId',
                        }}
                    />
                </AFormItem>
                {
                    <Form.Item
                        name={'driverPhoto'}
                        label={i18n.t('name', '司机照片')}
                        initialValue={[]}
                        hidden={!Boolean(Object.keys(currentDriver).length)}
                    >
                        <Checkbox.Group>
                            <div className="modal-img-wraper">
                                {currentDriver?.pictureList?.length ? (
                                      <RspCardLayout
                                    minWidth={isResponsive ? 520 : minCardWidth}
                                    >
                                        {        currentDriver.pictureList?.map(
                                            (item) => {
                                                return (
                                                    <Checkbox
                                                        disabled={
                                                            (checkedList.length >=
                                                                maxPhotoNumber &&
                                                                !checkedList.includes(
                                                                    item.pictureId,
                                                                )) ||
                                                            noPicQuality.includes(
                                                                item.pictureId,
                                                            )
                                                        }
                                                        value={item.pictureId}
                                                        key={item.pictureId}
                                                    >
                                                        <DisableRightMouseClickImage>
                                                            <AspectRatioImage
                                                                preview={false}
                                                                src={
                                                                    item.pictureUrl
                                                                }
                                                                fallback={
                                                                    FALL_SRC
                                                                }
                                                            />
                                                        </DisableRightMouseClickImage>
                                                        <svg
                                                            className="score-tip-bg"
                                                            width={32}
                                                            height={32}
                                                        >
                                                            <path
                                                                fill={
                                                                    noPicQuality.includes(
                                                                        item.pictureId,
                                                                    )
                                                                        ? '#FF4D4F'
                                                                        : '#52C41A'
                                                                }
                                                                d="M32 32L0 0 20 0 32 12z"
                                                            />
                                                        </svg>
                                                        <span className="score-tip-title">
                                                            {item.score}
                                                        </span>
                                                    </Checkbox>
                                                );
                                            },
                                        )}
                                </RspCardLayout>
                                    ) : (
                                        <div className="modal-img-no-img">
                                            <div className='modal-img-no-img-inner'>
                                                 <div className="no-img-box">
                                                <svg
                                                    viewBox="0 0 46 46"
                                                    className="design-iconfont"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        clipRule="evenodd"
                                                        d="M14.6981 12.417C16.139 14.1209 15.9506 16.6883 14.2764 18.1511C12.6022 19.6199 10.0768 19.4214 8.63592 17.7145C7.19406 16.0135 7.38247 13.4461 9.05666 11.9823C10.7309 10.5185 13.2563 10.713 14.6981 12.417ZM42.3333 35.1882L11.6666 35.4064L11.7146 35.3507L3.66663 35.4065L14.2418 23.2031L18.281 27.7294L26.8538 17.7793L42.3333 35.1882Z"
                                                        fillOpacity=".25"
                                                    />
                                                </svg>
                                            </div>
                                            <span>
                                                {i18n.t('message', '暂无照片')}
                                            </span>
                                           </div>
                                        </div>
                                    )}
                            </div>
                        </Checkbox.Group>
                    </Form.Item>
                }
                <div className="introduce-img">
                    <span>{i18n.t('name', '说明')}: &nbsp;</span>
                    <span>
                        <span
                            title={i18n.t(
                                'message',
                                '待办照片及司机照片最多只能选择 {maxPhotoNumber} 张',
                                {
                                    maxPhotoNumber: maxPhotoNumber,
                                },
                            )}
                        >
                            1.
                            {i18n.t(
                                'message',
                                '待办照片及司机照片最多只能选择 {maxPhotoNumber} 张',
                                {
                                    maxPhotoNumber: maxPhotoNumber,
                                },
                            )}
                        </span>
                        <span title={i18n.t('message', '未选的司机照片将从人脸库移除')}>
                            2.
                            {i18n.t('message', '未选的司机照片将从人脸库移除')}
                        </span>
                    </span>
                </div>
            </Form>
        </Modal>
    );
};
