@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.bind-to-driver-modal {
    .modal-img-wraper {
        label > span:nth-of-type(2) {
            display: block; 
            width: 100%; 
            .disable-right-mouse-click{
                display: block; 
                width: 100%; 
                .poppy-image{
                    width: 100%;
                }
            }
        }
        width: 100%;
        .poppy-checkbox-wrapper {
            position: relative;
            width: 100%;
            margin-left: unset !important;
            .poppy-checkbox {
                position: absolute;
                top: 5px;
                left: 5px;
                z-index: 10;
            }
            .poppy-checkbox + span {
                padding: 0;
            }
        }
        .modal-img-no-img {
            display: flex;
            &-inner {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .no-img-box {
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
                width: 166px;
                height: 90px;
                border: 1px solid @starry-border-level-2-color;
                background:@starry-bg-color-secondarycontainer;
                 svg {
                    height:46px ;
                    width: 46px;
                    fill: @starry-text-color-disabled ;
            }
            }

            span {
                margin-top: 12px;
                color: rgb(185, 185, 187);
            }
        }
    }
    .introduce-img {
        color: @starry-text-color-secondary;
        font-weight: 400;
        font-size: 12px;
        line-height: 19px;
        & span {
            display: block;
            span {
                display: block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}
.face-abnormal-page{
    .search-input-pro-result{
        .module-item{
            .module-title{
                display: none;
            }
            .module-list{
                margin-top: 8px;
                height: calc(100% - 16px);
            }
        }
    }
}
.bind-to-driver-modal{
    .poppy-checkbox-group{
        width: 100%;
    }
    .starry-responsive-custom-col{
        height: 90px;
        .poppy-checkbox-wrapper{
            height: 90px;
            .disable-right-mouse-click{
                height: 90px;
            }
        }
    }
}
