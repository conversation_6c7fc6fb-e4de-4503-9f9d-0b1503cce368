@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.face-detail-page {
    .turn-page-wrap {
        .poppy-btn {
            padding: 4px 8px;
        }
        .img-clear-button-no-focus:focus{
            color: rgba(0,0,0,.65);
            border: 1px solid #d9d9d9;
        }
    }
    .bind-driver-info-back {
        width: 100%;
        margin-bottom: 16px;
    }
    .img-wrap {
        width: 100%;
        height: 100%;
        background: #0d0d0d;
    }
}
.bind-to-driver-modal {
    .modal-img-wraper {
        flex-wrap: wrap;
        width: 100%;
        label > span:nth-of-type(2) {
            display: block; 
            width: 100%; 
            .disable-right-mouse-click{
                display: block; 
                width: 100%; 
                .poppy-image{
                    width: 100%;
                }
            }
        }
        .poppy-checkbox-wrapper {
            position: relative;
            width: 100%;
            .poppy-checkbox {
                position: absolute;
                top: 5px;
                left: 5px;
                z-index: 10;
            }
            .poppy-checkbox + span {
                padding: 0;
            }
        }
        .modal-img-no-img {
            display: flex;
            &-inner {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
                .no-img-box {
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
                width: 166px;
                height: 90px;
                border: 1px solid @starry-border-level-2-color;
                background:@starry-bg-color-secondarycontainer;
                 svg {
                    height:46px ;
                    width: 46px;
                    fill: @starry-text-color-disabled ;
            }
            }

            span {
                margin-top: 12px;
                color: rgb(185, 185, 187);
            }
        }
    }
    .introduce-img {
        color: @starry-text-color-secondary;
        font-weight: 400;
        font-size: 12px;
        line-height: 19px;
        & span {
            display: block;
            span {
                display: block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
}
