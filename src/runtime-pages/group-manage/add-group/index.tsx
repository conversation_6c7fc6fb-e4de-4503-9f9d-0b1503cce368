import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Space, message } from '@streamax/poppy';
import { i18n, utils, RouterPrompt } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import { StarryBreadcrumb, StarryCard } from '@base-app/runtime-lib';
import { addCompany, getCompanyDetail, editCompany } from '../../../service/company';
import { useSubmitFn } from '@streamax/hooks';
import { fleetHasAuthDisable } from '@/utils/commonFun';
import AuthTip from '@/components/AuthTip';
//@ts-ignore
import FleetTreeSelect from '@/components/AuthFleetSelectShow';
// import { groupIllegalCharacter } from '@/utils/commonFun';
// import { FleetTreeSelect } from '@base-app/runtime-lib';
const validateBaseData = utils.validator.validateBaseData;
const { TextArea } = Input;
// 新增车组的非法字符需要去掉|
const Group = (props: any) => {
    const { fleetId } = props.location.query;
    const [fleetInfo, setFleetInfo] = useState<any>({});
    // const [companylist, setCompanylist] = useState<any[]>([]);
    const [when, setWhen] = useState(true);
    const [form] = Form.useForm();
    const validatorIllegalCharacter = utils.validator.illegalCharacter;
    const [authDisable, setAuthDisable] = useState(false);
    const history = useHistory();

    useEffect(() => {
        (async () => {
            // 编辑从接口获取数据
            if (fleetId) {
                const rs = await getCompanyDetail({ fleetId });
                setFleetInfo(rs);
                setAuthDisable(fleetHasAuthDisable(rs.parentId, rs.parentName));
                form.setFieldsValue({
                    ...rs,
                    parentId: {
                        fleetIds: rs.parentId,
                        includeSubFleet: 0,
                    },
                });
            }
        })();
    }, []);
    const [submit, submitLoading] = useSubmitFn(async (values: any) => {
        const params = { ...values, parentId: values.parentId?.fleetIds };
        if (fleetId) {
            await editCompany({
                ...params,
                fleetId,
            });
        } else {
            await addCompany(params);
        }
        setWhen(false);
        message.success(i18n.t('message', '成功'));
        form.resetFields();
        history.goBack();
    });
    // 2.15.5版本，对添加编辑车组名称的特殊字符限制，放开|\/,?字符的校验，后续版本再做统一，暂时只调整车组名称
    const groupIllegalCharacter = (rule: any, value: string) => {
        if (value && /[*‘’''“”""<>]/g.test(value)) {
            return Promise.reject(i18n.t('message', '非法字符'));
        }
        return Promise.resolve();
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />

            <StarryCard title={i18n.t('title', '基本信息')}>
                <Form
                    layout="vertical"
                    form={form}
                    style={{
                        width: '340px',
                    }}
                    onFinish={submit}
                >
                    <Form.Item
                        label={i18n.t('name', '车组名称')}
                        name="fleetName"
                        rules={[
                            {
                                required: true,
                                whitespace: true,
                                message: i18n.t('message', '车组名称不能为空'),
                            },
                            {
                                validator: validateBaseData,
                            },
                        ]}
                    >
                        <Input
                            allowClear
                            maxLength={50}
                            placeholder={i18n.t('message', '请输入车组名称')}
                        />
                    </Form.Item>
                    {(!fleetId || fleetInfo.parentId) && (
                        <Form.Item
                            label={
                                <>
                                    {i18n.t('name', '上级车组')}
                                    <AuthTip show={authDisable} />
                                </>
                            }
                            name="parentId"
                            rules={[
                                {
                                    required: true,
                                    message: i18n.t('message', '请选择上级车组'),
                                },
                            ]}
                        >
                            <FleetTreeSelect
                                allowClear={false}
                                showSearch={false}
                                placeholder={i18n.t('message', '请选择上级车组')}
                                style={{ width: '340px' }}
                            />
                        </Form.Item>
                    )}

                    <Form.Item
                        label={i18n.t('naem', '备注')}
                        name="description"
                        rules={[
                            {
                                validator: validatorIllegalCharacter,
                            },
                        ]}
                    >
                        <TextArea
                            allowClear
                            maxLength={500}
                            showCount
                            rows={5}
                            placeholder={i18n.t('message', '不多于500字')}
                            // @ts-ignore
                            countFormatter={(count: number, maxLength?: number) => {
                                return `${count}/${maxLength}`;
                            }}
                        />
                    </Form.Item>
                    <Form.Item>
                        <Space size="large">
                            <Button
                                onClick={() => {
                                    history.goBack();
                                }}
                            >
                                {i18n.t('action', '取消')}
                            </Button>
                            <Button type="primary" htmlType="submit" loading={submitLoading}>
                                {i18n.t('action', '保存')}
                            </Button>
                        </Space>
                    </Form.Item>
                </Form>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default Group;
