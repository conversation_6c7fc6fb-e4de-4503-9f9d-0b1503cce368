import { useGetState, useUpdateEffect } from '@streamax/hooks';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { Button, Space, Tooltip, Form, Table, Progress } from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import type { TableProps } from '@streamax/poppy/lib/table';
import { IconDeleteFill } from '@streamax/poppy-icons';
import {
    i18n,
    utils,
    Auth,
    useUrlSearchStore,
    getAppGlobalData,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
// @ts-ignore
import { FleetTreeSelect } from '@base-app/runtime-lib';
import cn from 'classnames';
import {
    ListDataContainer,
    OverflowEllipsisContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import moment from 'moment';
import React, { useState, useRef, useContext, useEffect } from 'react';
import DateRange from '../../../../components/DateRange';
import { fetchBaseData } from '../../../../utils/filterFetch';
import SearchInput from '../../../evidence-list/components/SearchInput';
import type { DeleteType } from '../../index';
import { PageTypeContext } from '../../index';
import { getUserMdFormat } from '../../utils';
import AuthDriverShow from '@/components/AuthDriverShow';
import AuthFleetShow from '@/components/AuthFleetShow';
import useFormUrlSearchStore from '@/hooks/useFormUrlSearchStore';
import type { ListPageTableBase, ListPageQueryForm, Instances, ListItemCheckboxProps } from '@/types/pageReuse/pageReuseBase';
import { disabledAfterDate, getInitTimeRange } from '@/utils/commonFun';
import { getVideoPageProcessList } from '@/utils/evidencePageList';
import { getCustomItems, runCustomFun, getCustomJsx, getTableIconBtns } from '@/utils/pageReuse';
import './index.less';
import { useLockAppIdHeaders } from '@/utils/lockAppId';
import useEvidenceDoingPercent from "@/hooks/useEvidenceDoingPercent";
import { LoadingOutlined } from '@ant-design/icons';
import { unionWith } from 'lodash';
import calcHeight from '@/utils/calcStickyHeight';
import useHideTableStickyScroll from '@/hooks/useHideTableStickyScroll';
import { getPickerRangeTWM } from '@/utils/date-picker-config';


/**列表页table定制复用 */
export type VideoLibraryDoingShareProps = ListPageTableBase & ListPageQueryForm & Instances & ListItemCheckboxProps;

const VideoLibraryDoing = (props: DeleteType & VideoLibraryDoingShareProps & TableProps) => {
    /**定制项 */
    const {
        getColumns,
        onSelectRows,
        getTableLeftRender,
        injectSearchList,
        getIconBtns,
        getQueryForm,
        getInstances,
        tableRowSelection,
        getColumnSetting,
        getListItemCheckboxProps,
    } = props;
    /**end */
    const searchStore = useUrlSearchStore();
    const { params: formQuery, setSearchStore } = useFormUrlSearchStore('page', 'pageSize', [
        'DoingStartTime',
        'DoingEndTime',
        'DoingIncludeSubFleet',
    ]);
    const { pageType } = useContext(PageTypeContext);
    const [form] = Form.useForm();
    const [selectedRows, setSelectedRows] = useState<React.Key[]>([]);
    const [order, setOrder] = useState<string | undefined>(
        formQuery.DoingComplexSort ? formQuery.DoingComplexSort : null,
    );
    const [dataSource, setDataSource, getDataSource] = useGetState<any[]>([]);

    const tableRef = useRef<any>();
    const initTimeRange = getInitTimeRange(7); // 默认7天内
    const { timestampToZeroTimeStamp, getLocalMomentByZeroTimeStamp } = utils.formator;
    const config = getAppGlobalData('APP_USER_CONFIG');

    const headers = useLockAppIdHeaders();

    const {
        // ...values,
        DoingVehicleSearchType,
        DoingVehicleSearchValue,
        DoingFleetIds,
        DoingIncludeSubFleet,
        DoingPage,
        DoingPageSize,
        DoingStartTime,
        DoingEndTime,
    } = formQuery;


    const getEvidenceIds = () => {
        return getDataSource().filter(i => !i.percent || i.percent < 100);
    };

    const PAGINATION_HEIGHT = 64;
    const { percentList } = useEvidenceDoingPercent({ getEvidenceIds });
    const { showSticky } = useHideTableStickyScroll({
        paginAtionHight: PAGINATION_HEIGHT,
        dataSource: dataSource,
    });
    useEffect(() => {
        const dataSource = getDataSource();
        if (!dataSource || !dataSource.length || !percentList || !percentList.length) return;
        setDataSource([...dataSource.map(item => {
            const target = percentList.find(i => i.evidenceId === item.evidenceId);
            if (target) {
                item.percent = (target?.dlPercent || target?.dlPercent === 0) ? Math.floor(target.dlPercent * 100) : undefined;
            }
            return item;
        })]);
        tableRef.current?.forceUpdate();
    }, [percentList]);

    useEffect(() => {
        const queryValue = {
            // ...values,
            vehicle: {
                type: DoingVehicleSearchType || 'vehicle',
                value: DoingVehicleSearchValue,
            },
            time:
                DoingStartTime && DoingEndTime
                    ? [
                          getLocalMomentByZeroTimeStamp(DoingStartTime) || null,
                          getLocalMomentByZeroTimeStamp(DoingEndTime) || null,
                      ]
                    : initTimeRange,
            company: { fleetIds: DoingFleetIds, includeSubFleet: DoingIncludeSubFleet ?? 1 },
        };
        form.setFieldsValue(queryValue);
    }, []);
    useEffect(() => {
        tableRef.current?.loadDataSource();
    }, [order]);

    const columns = [
        {
            title: i18n.t('name', '视频名称'),
            dataIndex: 'evidenceName',
            key: 'evidenceName',
            width: 200,
            fixed: 'left',
            ellipsis: { showTitle: false },
        },
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            key: 'vehicleNumber',
            width: 200,
            ellipsis: { showTitle: false },
        },
        pageType === 'video-library'
            ? {
                  title: i18n.t('name', '司机姓名'),
                  dataIndex: 'driverName',
                  key: 'driverName',
                  width: 200,
                  ellipsis: { showTitle: false },
                  render: (_: unknown, row: any) => {
                      // montageDriverList 这种类型未过滤权限，可直接传入不用其他限制入参
                      return (
                          <StarryAbroadOverflowEllipsisContainer>
                              <AuthDriverShow driverList={row?.montageDriverList} />
                          </StarryAbroadOverflowEllipsisContainer>
                      );
                  },
              }
            : null,
        {
            title: i18n.t('name', '设备编码'),
            dataIndex: 'deviceNo',
            key: 'deviceNo',
            width: 200,
            ellipsis: { showTitle: false },
            render: (text: string, record: any) => {
                const { deviceNo, deviceAlias } = record?.vehicleInfo || {};
                const name = deviceAlias || deviceNo || '-';
                const title = deviceAlias ? `${deviceAlias}(${deviceNo})` : deviceNo;
                return <Tooltip title={title}>{name}</Tooltip>;
            },
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetName',
            key: 'fleetName',
            calcWidth: () => 200,
            width: 280,
            ellipsis: { showTitle: false },
            render: (_: unknown, row: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow fleetList={row?.vehicleInfo?.fleetList} />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '下载进度'),
            dataIndex: 'percent',
            key: 'percent',
            width: 200,
            ellipsis: { showTitle: false },
            render: (_: any, row: any) => {
                return row.percent || row.percent === 0 ?
                    <Progress percent={row.percent || 0} size="small" /> : 
                    (<span className='doing-progress-loading'>
                        <LoadingOutlined/>
                        {i18n.t('message', '加载中...')}
                    </span>)
            },
        },
        {
            title: i18n.t('name', '产生时间'),
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: { showTitle: false },
            calcWidth: () => 165,
            width: 180,
            sorter: true,
            defaultSortOrder:
                formQuery.DoingComplexSort &&
                (formQuery.DoingComplexSort == 'create_time asc' ? 'ascend' : 'descend'),
            render: (text: number) => {
                const time = utils.formator.zeroTimeStampToFormatTime(text);
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {time}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '视频时间'),
            dataIndex: 'startTime',
            key: 'startTime',
            ellipsis: { showTitle: false },
            calcWidth: () => 180,
            width: 220,
            render: (text: number, record: any) => {
                const time =
                    utils.formator.zeroTimeStampToFormatTime(
                        text,
                        undefined,
                        getUserMdFormat(config?.timeFormat),
                    ) +
                    ' - ' +
                    utils.formator.zeroTimeStampToFormatTime(
                        record.endTime,
                        undefined,
                        'HH:mm:ss',
                        undefined,
                        // @ts-ignore
                        record.startTime,
                    );
                return (
                        <StarryAbroadOverflowEllipsisContainer>{time}</StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            key: 'operate',
            ellipsis: true,
            fixed: 'right',
            width: 180,
            calcWidth: () => utils.general.calcTableOperateWidth(1),
            render: (text: any, row: any) => {
                return (
                    <Space size={20} key="operateSpace">
                        <Auth
                            key="deleteButton"
                            code={
                                pageType === 'device-upload'
                                    ? '@base:@page:device.upload@action:delete.task'
                                    : '@base:@page:video.library@action:batch.delete.task'
                            }
                        >
                            <Tooltip title={i18n.t('action', '删除')}>
                                <a onClick={() => deleteRecords([row], false)}>
                                    <IconDeleteFill />
                                </a>
                            </Tooltip>
                        </Auth>
                    </Space>
                );
            },
        },
    ].filter((item) => item);
    const items: QueryFormProps['items'] = [
        {
            name: 'vehicle',
            field: SearchInput,
            fieldProps: {
                searchOptions: ['vehicle'],
            },
        },
        {
            label: i18n.t('name', '归属车组'),
            name: 'company',
            field: FleetTreeSelect,
            fieldProps: {
                // showSubFleetContent: true,
                placeholder: i18n.t('message', '请选择归属车组'),
            },
        },
        {
            label: i18n.t('name', '产生时间'),
            name: 'time',
            // field: RangePicker,
            field: DateRange,
            colSize: 2,
            itemProps: {
                initialValue: initTimeRange,
            },
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    allowClear: true,
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    ranges: getPickerRangeTWM(),
                    disabledDate: disabledAfterDate,
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];
    const queryItems = getCustomItems(getQueryForm, items);

    const handleTableChange: TableProps['onChange'] = (pagination, filters, sorter) => {
        //@ts-ignore
        const { order = 'descend' } = sorter;
        const orderEnum = {
            descend: 'desc',
            ascend: 'asc',
            null: 'desc',
        };
        setOrder(`create_time ${orderEnum[order]}`);
    };
    const fetchData = async (values: any) => {
        const { vehicle, company, time, page, pageSize } = values;
        let startTime, endTime;
        if (time) {
            startTime = timestampToZeroTimeStamp(time[0]);
            endTime = timestampToZeroTimeStamp(time[1]);
        } else {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                time: initTimeRange,
            });
            startTime = timestampToZeroTimeStamp(initTimeRange[0]);
            endTime = timestampToZeroTimeStamp(initTimeRange[1]);
        }
        setSearchStore({
            DoingVehicleSearchType: vehicle?.type,
            DoingVehicleSearchValue: vehicle?.value,
            DoingFleetIds: company?.fleetIds,
            DoingIncludeSubFleet: company?.includeSubFleet,
            DoingPage: page,
            DoingPageSize: pageSize,
            DoingStartTime: startTime,
            DoingEndTime: endTime,
        });
        if (injectSearchList) {
            setSelectedRows([]);
            const injectListData = await injectSearchList({...values, complexSort: order || 'create_time desc'});
            setDataSource(injectListData?.list || []);
            return injectListData;
        }
        let ids;
        if (vehicle && vehicle.value && vehicle.value.replace(/\s/g, '')) {
            ids = await fetchBaseData(vehicle);
            if (!ids || !ids.length) {
                return new Promise((resolve) => {
                    resolve({
                        total: 0,
                        list: [],
                    });
                });
            }
        }

        const params = {
            ...values,
            ...company,
            [`${vehicle ? vehicle.type : 'device'}Ids`]: ids,
            fields: 'vehicle,montage_driver',
            states: '2',
            evidenceTypes: pageType === 'device-upload' ? '6' : '2,5,8',
            startTime,
            endTime,
            time: undefined,
            page: Number(page),
            pageSize: Number(pageSize),
        };

        if (order) {
            params.complexSort = order;
        } else {
            params.complexSort = 'create_time desc';
        }
        setSearchStore({
            ...searchStore.get(),
            DoingComplexSort: params.complexSort,
        });
        setSelectedRows([]);
        const listData = await getVideoPageProcessList(params, headers);
        setDataSource(listData.list);
        return listData;
    };
    const deleteRecords = async (records: Record<string, any>, batchDelete = false) => {
        await props.deleteRecords(records, batchDelete);
        setSelectedRows([]);
        tableRef.current?.loadDataSource();
    };
    const batchDeleteButton = () => {
        return (
            <Auth
                key="batchDeleteButton"
                code={
                    pageType === 'device-upload'
                        ? '@base:@page:device.upload@action:batch.delete.task'
                        : '@base:@page:video.library@action:batch.delete.task'
                }
            >
                <Button
                    icon={<IconDeleteFill />}
                    disabled={selectedRows.length === 0}
                    onClick={() => deleteRecords(selectedRows, true)}
                >
                    {i18n.t('action', '批量删除')}
                </Button>
            </Auth>
        );
    };
    const getRowSelection = () => {
        const rowSelection: TableProps['rowSelection'] = {
            onChange: (selectedKeys: React.Key[], selectedRows: any[]) => {
                setSelectedRows(selectedRows);
            },
            selectedRowKeys: selectedRows.map((i: any) => i.evidenceId),
        };
        // 定制 getCheckboxProps 
        if(typeof getListItemCheckboxProps === 'function') {
            rowSelection.getCheckboxProps = getListItemCheckboxProps;
        }
        if (tableRowSelection) {
            return tableRowSelection() ? rowSelection : undefined;
        }
        return rowSelection;
    };

    useUpdateEffect(() => {
        runCustomFun(onSelectRows, selectedRows);
    }, [selectedRows]);

    runCustomFun(getInstances, {
        form,
        table: {
            ...tableRef.current,
            reload: tableRef.current?.loadDataSource,
        },
    });
    // @ts-ignore
    const newColumns = getCustomItems(getColumns, columns)?.map(item => {
        if (item.width) return item;
        else {
            return {
                ...item,
                width: 180,
            };
        }
    });

    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting(newColumns, {
        storageKey: '@base:@page:video.library.doing',
        disabledKeys: ['evidenceName', 'vehicleNumber', 'deviceNo', 'createTime'],
        ...getColumnSetting?.(),
    });
    return (
        <ListDataContainer
            loadDataSourceOnMount={false}
            queryForm={{
                items: queryItems,
                form,
            }}
            ref={tableRef as any}
            toolbar={{
                extraLeft: <Space>{getCustomJsx(getTableLeftRender, [batchDeleteButton()])}</Space>,
                // @ts-ignore
                extraIconBtns: getTableIconBtns(getIconBtns, [
                    <TableColumnSetting key="setting" {...tableColumnSettingProps} />,
                ]),
            }}
            // @ts-ignore
            getDataSource={fetchData}
            listRender={() => {
                return (
                    <Table
                        className={cn(!showSticky ? 'video-list-doing-table-scroll' : '','video-doing-table')}
                        columns={tableColumns}
                        dataSource={getDataSource()}
                        rowKey="evidenceId"
                        rowSelection={getRowSelection()}
                        onChange={handleTableChange}
                        scroll={{ x: '100%' }}
                        sticky={{
                            offsetScroll: PAGINATION_HEIGHT,
                            offsetHeader: calcHeight(), // 距离container顶部的高度
                            getContainer: () => {
                                return document.querySelector('#root') as HTMLElement;
                            },
                        }}
                    />
                );
            }}
        />
    );
};

export default withSharePropsHOC<DeleteType, VideoLibraryDoingShareProps>(VideoLibraryDoing);
