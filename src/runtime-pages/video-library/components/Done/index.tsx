import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { useAsyncEffect, useCreation, useLockFn, useUpdateEffect } from '@streamax/hooks';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import {
    Affix,
    Button,
    Checkbox,
    ConfigProvider,
    Container,
    Form,
    message,
    ProForm,
    ProTable,
    Space,
    Spin,
    Tooltip,
} from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import type { TableProps } from '@streamax/poppy/lib/table';
import {
    IconAddFill,
    IconDeleteFill,
    IconDownloadFill,
    IconHandleFill,
    IconMailboxFill,
    IconQrCodeFill,
    IconVideo02Fill,
} from '@streamax/poppy-icons';
import IconApplication from '@streamax/poppy-icons/lib/icons/IconApplication';
import IconList from '@streamax/poppy-icons/lib/icons/IconList';
// @ts-ignore
import {
    Action,
    Auth,
    FleetTreeSelect,
    g_emmiter,
    getAppGlobalData,
    i18n,
    StarryAbroadOverflowEllipsisContainer,
    StarryStorage,
    useUrlSearchStore,
    utils,
    mosaicManager,
    MosaicTypeEnum,
} from '@base-app/runtime-lib';
import { Link } from '@base-app/runtime-lib/core';
import cn from 'classnames';
import moment from 'moment';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { RspCardLayout, RspHorizontal } from '@streamax/responsive-layout';
import DateRange from '../../../../components/DateRange';
import EvidenceRequest, { adjustEvidenceDetailData } from '../../../../service/evidence';
import { fetchBaseData } from '../../../../utils/filterFetch';
import CodeModal from '@/components/EvidenceTools/components/code-modal';
import SearchInput from '../../../evidence-list/components/SearchInput';
import Create from '../../create';
import type { DeleteType } from '../../index';
import { PageTypeContext } from '../../index';
import { getEvidenceDownloadTooltip, getUserMdFormat } from '../../utils';
import Card from '../Card';
import MarkModal from '../MarkModal';
import AuthDriverShow from '@/components/AuthDriverShow';
import AuthFleetShow from '@/components/AuthFleetShow';
import MailModal from '@/components/EvidenceTools/components/mail-modal';
import LabelSelect from '@/components/LabelSelect';
import useFormUrlSearchStore from '@/hooks/useFormUrlSearchStore';
import LabelEllipsis from '@/runtime-pages/alarm-center/alarm-list/components/LabelEllipsis';
import type { LabelRecordType } from '@/service/label';
import { getLabelListMap } from '@/service/label';
import type {
    Instances,
    ListItemCheckboxProps,
    ListPageQueryForm,
    ListPageTableBase,
    ListPlaySpeed,
} from '@/types/pageReuse/pageReuseBase';
import { disabledAfterDate, FileChannelAuthEnum, getAllVideos, getInitTimeRange, getVideosByFileType, noChannelAuth } from '@/utils/commonFun';
import { evidenceFormatData } from '@/utils/evidenceFormatData';
import { getCustomItems, getCustomJsx, getTableIconBtns, runCustomFun } from '@/utils/pageReuse';
import './index.less';
import getWaterMask from '@/utils/getWaterMask';
import { isNil } from 'lodash';
import { useLockAppIdHeaders } from '@/utils/lockAppId';
import { PlaySpeed, PlaySpeedMap, StorePageCode } from '@/components/UserSearchVideoDownloadStore/type';
import UserSearchVideoDownloadStore from '@/components/UserSearchVideoDownloadStore';
import { VideoLibraryList } from '@/types/pageReuse/videoLibrary';
import { getPickerRangeTWM } from '@/utils/date-picker-config';
import useHideTableStickyScroll from '@/hooks/useHideTableStickyScroll';
import {FileCleanStateEnum} from "@/modules/evidence/types";

/**列表页table定制复用 */
export type VideoLibraryDoneShareProps = ListPageTableBase &
    ListPageQueryForm &
    Instances &
    ListItemCheckboxProps &
    ListPlaySpeed &
    VideoLibraryList;

export type SearchPage = {
    page: number;
};
export type SearchReset = {
    reset: boolean;
};

interface EvidenceQueryParams {
    fileTypes: string;
    evidenceId: string;
    watermarkFlag?: number;
    watermarkValue?: string[];
    playSpeed?: PlaySpeed;
    mosaicVideo?: boolean;
}

const { QueryForm } = ProForm;
const MAX_SELECTED_NUMBER = 5; // 最多选择查询标签个数
// @ts-ignore
const { timestampToZeroTimeStamp, getLocalMomentByZeroTimeStamp, formatByte } =
    utils.formator;

const VideoLibraryDone = (
    props: DeleteType & VideoLibraryDoneShareProps & TableProps,
) => {
    /**定制项 */
    const {
        getColumns,
        onSelectRows,
        getTableLeftRender,
        injectSearchList,
        getIconBtns,
        getQueryForm,
        getInstances,
        tableRowSelection,
        getColumnSetting,
        getListItemCheckboxProps,
        getDownloadSetRender,
        getDefaultLayoutType,
    } = props;
    /**end */
    const config = getAppGlobalData('APP_USER_CONFIG');
    const { params: formQuery, setSearchStore } = useFormUrlSearchStore(
        'page',
        'pageSize',
        ['startTime', 'endTime', 'includeSubFleet'],
    );
    const { pageType } = useContext(PageTypeContext);
    const { renderEmpty } = useContext(ConfigProvider.ConfigContext);
    const [layoutType, setLayoutType] = useState<'list' | 'card'>(
        formQuery.layoutType || 'card',
    );
    const [list, setList] = useState<any[]>([]);
    const [page, setPage] = useState<number>(1);
    const [pageSize] = useState<number>(20);
    const [queryParams, setQueryParams] = useState<any>({});
    const [showCode, setShowCode] = useState(false);
    const [showMail, setShowMail] = useState(false);
    const [loading, setLoading] = useState(false);
    const [alarmCheck, setAlarmCheck] = useState(false);
    const [evidenceId, setEvidenceId] = useState<number>(0);
    const [sortName, setSortName] = useState<any>('create_time');
    const [sortType, setSortType] = useState<any>('descend');
    const [form] = Form.useForm();
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [showMarkModal, setShowMarkModal] = useState(false);
    const searchStore = useUrlSearchStore();
    const [hasNextPage, setHasNextPage] = useState<boolean>();
    const [playSpeed, setPlaySpeed] = useState<PlaySpeed>(PlaySpeed['1X']);
    const hasMosaic = mosaicManager.useMosaicConfig(MosaicTypeEnum.videoStore);

    const PAGINATION_HEIGHT = 64;
    const { showSticky } = useHideTableStickyScroll({
        paginAtionHight: PAGINATION_HEIGHT,
        dataSource: list,
    });
    const fromList = 1;
    // 未禁用的列表项
    const enableList = useMemo(() => {
        if (typeof getListItemCheckboxProps !== 'function') {
            return list;
        }
        return list.filter((item) => !getListItemCheckboxProps(item)?.disabled);
    }, [list, getListItemCheckboxProps]);
    const headers = useLockAppIdHeaders();


    // 解决多次调用性能问题，重复渲染
    const queryItems = useCreation(() => {
        const tempQueryItems: QueryFormProps['items'] = [
            {
                name: 'vehicle',
                field: SearchInput,
                fieldProps: {
                    searchOptions: ['vehicle'],
                },
            },
            {
                label: i18n.t('name', '归属车组'),
                name: 'fleet',
                field: FleetTreeSelect,
                fieldProps: {
                    // showSubFleetContent: true,
                    placeholder: i18n.t('message', '请选择归属车组'),
                },
            },
            {
                label: i18n.t('name', '标签'),
                name: 'label',
                field: LabelSelect,
                colSize: 1,
                fieldProps: {
                    addAuthCode: '@base:@page:video.library@action:add.label',
                    maxSelectedNumber: MAX_SELECTED_NUMBER,
                },
            },
            {
                label: i18n.t('name', '产生时间'),
                name: 'time',
                // field: RangePicker,
                field: DateRange,
                colSize: 2,
                itemProps: {
                    initialValue: initTimeRange,
                },
                fieldProps: {
                    maxInterval: {
                        value: 31,
                        unitOfTime: 'days',
                    },
                    pickerProps: {
                        allowClear: true,
                        showTime: {
                            hideDisabledOptions: true,
                            defaultValue: [
                                moment('00:00:00', 'HH:mm:ss'),
                                moment('23:59:59', 'HH:mm:ss'),
                            ],
                        },
                        separator: '~  ',
                        ranges: getPickerRangeTWM(),
                        disabledDate: disabledAfterDate,
                        style: {
                            width: '100%',
                        },
                    },
                },
            },
        ];
        const queryItems: QueryFormProps['items'] = getCustomItems(
            getQueryForm,
            tempQueryItems,
        );
        return queryItems;
    }, []);

    useEffect(() => {
        if (pageType === 'device-upload') {
            setLayoutType('list');
        }
    }, [pageType]);

    useAsyncEffect(async () => {
        if (getDefaultLayoutType) {
            const type = await getDefaultLayoutType?.('card');
            setLayoutType(type);
        }
        const { page: historyPage } = searchStore.get();
        const {
            vehicleSearchType,
            vehicleSearchValue,
            startTime,
            endTime,
            label,
            fleetIds,
            includeSubFleet,
            ...value
        } = formQuery;
        //@ts-ignore
        const labelList =
            label && (typeof label == 'string' ? label.split() : label);
        const searchList = {
            ...value,
            vehicle: {
                type: vehicleSearchType || 'vehicle',
                value: vehicleSearchValue,
            },
            label: labelList,
            time:
                startTime && endTime
                    ? [
                        getLocalMomentByZeroTimeStamp(startTime) || null,
                        getLocalMomentByZeroTimeStamp(endTime) || null,
                    ]
                    : initTimeRange,
            fleet: { fleetIds, includeSubFleet: includeSubFleet ?? 1 },
        };

        form.setFieldsValue(searchList);

        const params = {
            fleetIds: searchList.fleet?.fleetIds,
            includeSubFleet: searchList.fleet?.includeSubFleet,
            startTime,
            endTime,
            labelIds: labelList?.toString(),
            page,
        };
        if (historyPage) {
            params.page = Number(historyPage);
            setPage(Number(historyPage));
        } else {
            setPage(1);
        }
        setQueryParams(params);
    }, []);
    const viewCode = async (evidenceId: number, fileChannelAuth: FileChannelAuthEnum) => {
        if(noChannelAuth(fileChannelAuth)) {
            return;
        }
        const detailData = await adjustEvidenceDetailData({
            evidenceId: evidenceId,
            fields: 'all',
            accessLog: fromList,
            mosaicFlag: hasMosaic ? 1 : 0,
        });
        const vehicleId = detailData?.vehicleInfo?.vehicleId;
        if (!vehicleId) {
            const info = i18n.t('message', '车辆不存在');
            message.error(info);
            throw info;
        } else {
            setShowCode(true);
            setEvidenceId(evidenceId);
        }
    };
    const viewMail = async (evidenceId: number, fileChannelAuth: FileChannelAuthEnum) => {
        if(noChannelAuth(fileChannelAuth)) {
            return;
        }
        const detailData = await adjustEvidenceDetailData({
            evidenceId: evidenceId,
            fields: 'all',
            accessLog: fromList,
            mosaicFlag: hasMosaic ? 1 : 0,
        });
        const vehicleId = detailData?.vehicleInfo?.vehicleId;
        if (!vehicleId) {
            const info = i18n.t('message', '车辆不存在');
            message.error(info);
            throw info;
        } else {
            setShowMail(true);
            setEvidenceId(evidenceId);
        }
    };

    const exportData = async (id: any, type: string, playSpeed?: PlaySpeed, fileChannelAuth?: FileChannelAuthEnum) => {
        if(noChannelAuth(fileChannelAuth)) {
            return;
        }
        const { waterFlag, customerIp, account, entryName, formatTime } =
            await getWaterMask();
        let evidenceQueryParams: EvidenceQueryParams = {
            fileTypes: type,
            evidenceId: id,
            playSpeed,
            mosaicVideo: hasMosaic,
        };
        // 若水印开启，则传入水印相关参数
        if (waterFlag) {
            evidenceQueryParams = {
                ...evidenceQueryParams,
                watermarkFlag: waterFlag,
                watermarkValue: [
                    entryName,
                    `${customerIp} ${formatTime}`,
                    account,
                ],
            };
        }
        await EvidenceRequest.exportFileAsync(
            {
                serviceCode: 'c182d4926be44c0683fbd8f0928a4751',
                fileName: playSpeed
                    ? `evidence_${moment().unix()}_${PlaySpeedMap[playSpeed]}`
                    : `evidence_${moment().unix()}`,
                isAsync: true,
                evidenceQueryParams,
            },
            headers,
        );
        message.success(
            i18n.t('message', '导出成功，请到个人中心中查看导出详情'),
        );
    };
    const initTimeRange = getInitTimeRange(7); // 默认7天内

    // 根据fileList计算当前视频是何种码流类型
    const findStreamType = (
        startTime: number,
        fileList: {
            startTime: number;
            streamType: number;
            fileType: number;
        }[],
    ) => {
        let streamType: 'MINOR' | 'MAJOR' = 'MINOR'; // 默认是子码流
        const TYPE_VIDEO = [13, 1]; // 13: H264或H265，1: mp4
        const TYPE_MAJOR = 1; // 主码流
        // 由于支持H264播放，需要优先查找H264，没有才会查找mp4
        const videoList = fileList.filter((p) =>
            TYPE_VIDEO.includes(p.fileType),
        );
        const listWithStartTime = videoList.filter(
            (p) => Number(p.startTime) === startTime,
        );
        const streamTypeList = new Set(
            listWithStartTime.map((p) => p.streamType),
        );
        if (streamTypeList.size === 1 && streamTypeList.has(TYPE_MAJOR)) {
            // 如果只有主码流
            streamType = 'MAJOR';
        }
        return streamType;
    };

    const columns = [
        {
            title: i18n.t('name', '视频名称'),
            dataIndex: 'evidenceName',
            key: 'evidenceName',
            ellipsis: { showTitle: true },
            fixed: 'left',
            width: 200,
            render: (text: any, record: any) => {
                if (pageType === 'device-upload') {
                    return <span>{text}</span>;
                } else {
                    return (
                        <Action
                            code="@base:@page:video.library@action:video.detail"
                            url="/video/detail"
                            target="blank"
                            params={{
                                evidenceId: record.evidenceId,
                                fromList: fromList,
                                type: 'video',
                            }}
                            fellback={text}
                        >
                            <StarryAbroadOverflowEllipsisContainer>
                                {text}
                            </StarryAbroadOverflowEllipsisContainer>
                        </Action>
                    );
                }
            },
        },
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            ellipsis: { showTitle: true },
            key: 'vehicleNumber',
            width: 250,
        },
        pageType === 'video-library'
            ? {
                title: i18n.t('name', '司机姓名'),
                dataIndex: 'driverName',
                key: 'driverName',
                width: 200,
                ellipsis: { showTitle: true },
                render: (_: unknown, row: any) => {
                    return (
                        <StarryAbroadOverflowEllipsisContainer>
                            <AuthDriverShow
                                driverList={row?.montageDriverList}
                            />
                        </StarryAbroadOverflowEllipsisContainer>
                    );
                },
            }
            : null,
        {
            title: i18n.t('name', '归属车组'),
            // dataIndex: 'vehicleInfo.vehicleNumber',
            // key: 'vehicleInfo.vehicleNumber',
            dataIndex: 'fleetName',
            key: 'fleetName',
            calcWidth: () => 200,
            width: 280,
            ellipsis: { showTitle: true },
            render: (_: unknown, row: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow
                            fleetList={row?.vehicleInfo?.fleetList}
                        />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '视频大小'),
            dataIndex: 'totalSize',
            key: 'totalSize',
            width: 180,
            ellipsis: { showTitle: true },
            render: (text: number, record: any) => {
                // @ts-ignore
                const { existH264 } = evidenceFormatData(record);
                const videos = getVideosByFileType(record.fileList, existH264);
                const videoSize = getAllVideos(record.fileList).reduce(
                    (total: any, item: any) => total + item.fileSize,
                    0,
                );
                // 视频证据才显示大小；反之显示0kb
                return videos.length > 0 ? formatByte(videoSize) : '0KB';
            },
        },
        {
            title: i18n.t('name', '标签'),
            dataIndex: 'labels',
            ellipsis: { showTitle: false },
            calcWidth: () => 200,
            width: 200,
            render: (text: string[] | undefined) => (
                <LabelEllipsis list={text || []} />
            ),
        },
        {
            title: i18n.t('name', '操作人'),
            dataIndex: 'createUserAccount',
            key: 'createUserAccount',
            ellipsis: { showTitle: true },
            width: 180,
        },
        {
            title: i18n.t('name', '产生时间'),
            dataIndex: 'createTime',
            key: 'createTime',
            sorter: true,
            sortDirections: ['ascend', 'descend', null],
            ellipsis: { showTitle: true },
            calcWidth: () => 165,
            render: (text: number) =>
                utils.formator.zeroTimeStampToFormatTime(text),
            width: 180,
        },
        // modify夏令时
        {
            title: i18n.t('name', '完成时间'),
            dataIndex: 'finishTime',
            key: 'finishTime',
            // sorter: true,
            ellipsis: { showTitle: true },
            calcWidth: () => 165,
            width: 180,
            render: (text: number) =>
                utils.formator.zeroTimeStampToFormatTime(text),
        },
        {
            title: i18n.t('name', '视频时间'),
            dataIndex: 'startTime',
            key: 'startTime',
            calcWidth: () => 180,
            width: 220,
            ellipsis: { showTitle: true },
            render: (text: number, record: any) => {
                const time =
                    utils.formator.zeroTimeStampToFormatTime(
                        text,
                        undefined,
                        getUserMdFormat(config?.timeFormat),
                    ) +
                    ' - ' +
                    utils.formator.zeroTimeStampToFormatTime(
                        record.endTime,
                        undefined,
                        'HH:mm:ss',
                        undefined,
                        // @ts-ignore
                        record.startTime,
                    );
                return (
                    <Tooltip title={time}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {time}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            key: 'operate',
            ellipsis: true,
            width: 180,
            fixed: 'right',
            calcWidth: () =>
                utils.general.calcTableOperateWidth(
                    pageType === 'device-upload' ? 1 : 4,
                ),
            render: (text: any, row: any) => {
                if (pageType === 'device-upload') {
                    const vehicleInfo = row.vehicleInfo;
                    const vehicleId = vehicleInfo && vehicleInfo.vehicleId;
                    const deviceId = vehicleInfo && vehicleInfo.authId;
                    const devId = row.deviceId;
                    const fleetId =
                        vehicleInfo &&
                        vehicleInfo.fleetList[0] &&
                        vehicleInfo.fleetList[0].fleetId;
                    const startTime = row.startTime;
                    const streamType = findStreamType(
                        Number(startTime),
                        row.fileList,
                    );
                    return (
                        <Space key="operateSpace" size={20}>
                            <Auth
                                key="lookVideoButton"
                                code="@base:@page:task.device.upload@action:video1"
                            >
                                <Link
                                    to={`/playback-serve?vehicleId=${vehicleId}&deviceId=${deviceId}&devId=${devId}&fleetId=${fleetId}&alarmTime=${startTime}&streamType=${streamType}`}
                                >
                                    <Tooltip
                                        title={i18n.t('action', '查看视频')}
                                    >
                                        <IconVideo02Fill />
                                    </Tooltip>
                                </Link>
                            </Auth>
                        </Space>
                    );
                } else {
                    const tooltipText = getEvidenceDownloadTooltip({ playSpeed, hasMosaic });
                    return (
                        <Space key="operateSpace" size={20}>
                            {
                                row.fileCleaned ===FileCleanStateEnum.CLEAN ?
                                    null:
                                    <Auth
                                        key="downloadVideoButton"
                                        code="@base:@page:video.library@action:dowload"
                                    >

                                        <Tooltip
                                            title={tooltipText}
                                        >
                                            <a
                                                onClick={() =>
                                                    exportData(
                                                        row.evidenceId,
                                                        '1,2,3',
                                                        playSpeed,
                                                        row.fileChannelAuth
                                                    )
                                                }
                                            >
                                                <IconDownloadFill />
                                            </a>
                                        </Tooltip>
                                    </Auth>
                            }
                            {
                                row.fileCleaned ===FileCleanStateEnum.CLEAN ?
                                    null:
                                    <Auth
                                        key="qrCodeShareButton"
                                        code="@base:@page:video.library@action:qrcode"
                                    >
                                        <Tooltip title={i18n.t('action', '二维码分享')}>
                                            <a
                                                onClick={() => {
                                                    viewCode(row.evidenceId, row.fileChannelAuth);
                                                }}
                                            >
                                                {/* <a disabled> */}
                                                <IconQrCodeFill />
                                            </a>
                                        </Tooltip>
                                    </Auth>
                            }
                            {
                                row.fileCleaned ===FileCleanStateEnum.CLEAN ?
                                    null:
                                    <Auth
                                        key="emailShareButton"
                                        code="@base:@page:video.library@action:view.mail"
                                    >
                                        <Tooltip title={i18n.t('action', '邮件分享')}>
                                            <a
                                                onClick={() => {
                                                    viewMail(row.evidenceId, row.fileChannelAuth);
                                                }}
                                            >
                                                {/* <a disabled> */}
                                                <IconMailboxFill />
                                            </a>
                                        </Tooltip>
                                    </Auth>
                            }
                            <Auth
                                key="deleteButton"
                                code="@base:@page:video.library@action:batch.delete.task"
                            >
                                <Tooltip title={i18n.t('action', '删除')}>
                                    <a
                                        onClick={() =>
                                            deleteRecords([row], false)
                                        }
                                    >
                                        <IconDeleteFill />
                                    </a>
                                </Tooltip>
                            </Auth>
                        </Space>
                    );
                }
            },
        },
    ].filter((item) => item);

    const getRealTimeLabelData = (
        labelIdList: string[],
        labelListMap: Record<string, LabelRecordType>,
    ) => {
        // 用于获取实时的标签，达到正确显示，这样是避免删除标签后还展示出来了，原因是后端标签的关联删除是异步的
        const labels: string[] = [];
        const labelIds: string[] = [];
        (labelIdList || []).forEach((id) => {
            if (labelListMap[id]) {
                labels.push(
                    i18n.t(`@i18n:@label__${id}`, labelListMap[id].labelName),
                );
                labelIds.push(id);
            }
        });
        return { labels, labelIds };
    };

    const query = useLockFn(async () => {
        if (!pageType) {
            return;
        }
        const mosaicFlag = mosaicManager.checkMosaicEnable(MosaicTypeEnum.videoStore) ? 1 : 0;

        const params = {
            ...queryParams,
            page,
            pageSize,
            fields: 'noTotal,driver,file,alarm,vehicle,file_url,montage_driver,label', // 'file,alarm,vehicle',
            evidenceTypes: pageType === 'device-upload' ? '6' : '2,5,8',
            states: 3,
            mosaicFlag,
        };
        if (sortType) {
            params.complexSort = `${sortName} ${sortType.replace(/end/, '')}`;
        }
        if (!params.startTime || !params.endTime) {
            params.startTime = utils.formator.timestampToZeroTimeStamp(
                initTimeRange[0].startOf('day'),
            );
            params.endTime = utils.formator.timestampToZeroTimeStamp(
                initTimeRange[1].endOf('day'),
            );
        }
        const values = form.getFieldsValue();
        const { vehicle, fleet, label } = values;
        params.labelIds = label?.join();
        setSearchStore({
            vehicleSearchType: vehicle?.type,
            vehicleSearchValue: vehicle?.value,
            fleetIds: fleet?.fleetIds,
            includeSubFleet: fleet?.includeSubFleet ?? 1,
            label,
            startTime: queryParams.startTime,
            endTime: queryParams.endTime,
            complexSort:
                sortType && `${sortName} ${sortType.replace(/end/, '')}`,
        });
        searchStore.set({
            page,
        });
        if (injectSearchList) {
            setLoading(true);
            const { list, hasNextPage } =
            (await injectSearchList({ ...params, ...values })) || {};
            setList(list);
            setHasNextPage(hasNextPage);
            // 重置选择项
            setSelectedRows([]);
            setLoading(false);
            return;
        }
        let ids: string;
        if (vehicle && vehicle.value && vehicle.value.replace(/\s/g, '')) {
            ids = await fetchBaseData(vehicle, headers);
            if (ids) {
                params[`${vehicle.type}Ids`] = ids;
            } else {
                setList([]);
                setHasNextPage(false);
                return;
            }
        }
        setLoading(true);
        try {
            const { list: resList, hasNextPage } =
                await EvidenceRequest.getPageList(params, headers);
            if (resList.length) {
                const labelListMap = await getLabelListMap(headers);
                (resList || []).forEach(
                    ({ evidenceId, labelIdList = [] }: any) => {
                        const { labels, labelIds } = getRealTimeLabelData(
                            labelIdList,
                            labelListMap,
                        );
                        const index = resList.findIndex(
                            (evidence: any) =>
                                evidence.evidenceId === evidenceId,
                        );
                        if (index !== -1) {
                            resList[index].labels = labels;
                            resList[index].labelIdList = labelIds;
                        }
                    },
                );
            }
            setList(resList);
            setHasNextPage(hasNextPage);
        } catch (error) {
            console.error(error);
            setList([]);
            setHasNextPage(false);
        }
        // 重置选择项
        setSelectedRows([]);
        setLoading(false);
        // 创建一个新的事件
        const event = new Event('resize');
        // 触发resize事件，触发Affix重新计算，【100744】当筛选数据较少时，分页的固定位置需要重新计算
        window.dispatchEvent(event);
    });
    const handleSearch = async (values: any) => {
        const { fleet, time, label } = values;
        let startTime, endTime;
        if (time) {
            startTime = timestampToZeroTimeStamp(time[0]);
            endTime = timestampToZeroTimeStamp(time[1]);
        } else {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                time: initTimeRange,
            });
            startTime = timestampToZeroTimeStamp(initTimeRange[0]);
            endTime = timestampToZeroTimeStamp(initTimeRange[1]);
        }
        const params = {
            fleetIds: fleet?.fleetIds,
            includeSubFleet: fleet?.includeSubFleet,
            startTime,
            endTime,
            labelIds: (label || []).join(',') || null,
            page: 1,
        };
        g_emmiter.emit('evidence.manage.search', params);
        setPage(1);
        setQueryParams(params);
    };
    const pageChange = (type: 'pre' | 'next') => {
        const currentPage = type == 'pre' ? page - 1 : page + 1;
        setPage(currentPage);
        // 重置选择项
        setSelectedRows([]);
    };
    const handleTableChange = (_: any, __: any, sorter: any) => {
        const { field, order } = sorter;
        if (field) {
            const name = field.replace(
                /[A-Z]/g,
                (r: any) => `_${r.toLowerCase()}`,
            );
            setSortName(name);
            setSortType(order);
        }
    };
    const deleteRecords = async (
        records: Record<string, any>,
        batchDelete = false,
    ) => {
        await props.deleteRecords(records, batchDelete);
        setSelectedRows([]);
        query();
    };

    useUpdateEffect(() => {
        query();
    }, [page, pageSize, queryParams, sortName, sortType, pageType]);

    const onFinishMark = (type: 'fail' | 'success') => {
        setShowMarkModal(false);
        if (type === 'success') {
            query();
        }
    };

    const addVideoButton = () => {
        return (
            <Auth
                key="addVideoButton"
                code={
                    pageType === 'device-upload'
                        ? '@base:@page:device.upload@action:done.create'
                        : '@base:@page:video.library@action:video.create'
                }
            >
                <Create
                    type={pageType}
                    havePickFrame={
                        pageType === 'device-upload'
                            ? true
                            : // 有马赛克则不展示延时录像
                            !hasMosaic
                    }
                >
                    <Button type="primary" icon={<IconAddFill />}>
                        {i18n.t('name', '添加')}
                    </Button>
                </Create>
            </Auth>
        );
    };
    const batchDeleteButton = () => {
        return (
            <Auth
                key="batchDeleteButton"
                code={
                    pageType === 'device-upload'
                        ? '@base:@page:device.upload@action:batch.delete.task'
                        : '@base:@page:video.library@action:batch.delete.task'
                }
            >
                <Button
                    icon={<IconDeleteFill />}
                    disabled={selectedRows.length === 0}
                    onClick={() => deleteRecords(selectedRows, true)}
                >
                    {i18n.t('action', '批量删除')}
                </Button>
            </Auth>
        );
    };

    const batchMarkButton = () => {
        return pageType === 'video-library' ? (
            <Auth
                key="batchMarkButton"
                code={'@base:@page:video.library@action:batch.mark'}
            >
                <Button
                    icon={<IconHandleFill />}
                    disabled={selectedRows.length === 0}
                    onClick={() => setShowMarkModal(true)}
                >
                    {i18n.t('action', '视频打标')}
                </Button>
            </Auth>
        ) : null;
    };

    const leftButton = (
        <RspHorizontal>
            {getCustomJsx(getTableLeftRender, [
                addVideoButton(),
                batchDeleteButton(),
                batchMarkButton(),
            ])}
        </RspHorizontal>
    );
    // 切换展示方式清空选择
    useEffect(() => {
        setSelectedRows([]);
        setSearchStore({
            ...formQuery,
            layoutType,
        });
    }, [layoutType]);

    useEffect(() => {
        enableList.length > 0 && selectedRows.length === enableList.length
            ? setAlarmCheck(true)
            : setAlarmCheck(false);
    }, [selectedRows, enableList]);
    const listBtn = (
        <a
            key="showTable"
            title={i18n.t('name', '列表')}
            onClick={() => {
                setLayoutType('list');
            }}
            className={cn('layout-type-button', {
                selected: layoutType === 'list',
            })}
        >
            <IconList />
        </a>
    );
    const cardBtn = (
        <a
            key="showCard"
            title={i18n.t('name', '卡片')}
            onClick={() => {
                setLayoutType('card');
            }}
            className={cn('layout-type-button', {
                selected: layoutType === 'card',
            })}
        >
            <IconApplication />
        </a>
    );
    const cardCheckAll = () => {
        return (
            <Checkbox
                key="cardCheckAll"
                checked={alarmCheck}
                onChange={handleCardCheckAll}
                disabled={!list.length}
            >
                {i18n.t('name', '全选')}
            </Checkbox>
        );
    };
    const handleCardCheckAll = (e: any) => {
        const { checked } = e.target;
        checked ? setSelectedRows(enableList) : setSelectedRows([]);
    };
    const storeOnSave = (data: Record<any, any>) => {
        if (data?.playSpeed) {
            setPlaySpeed(data.playSpeed);
        }
    };
    const storeOnInit = (data: Record<any, any>) => {
        if (data?.playSpeed) {
            setPlaySpeed(data.playSpeed);
        }
    };
    const downloadButton = () => {
        return (
            <UserSearchVideoDownloadStore
                page={StorePageCode.VideoList}
                onSave={storeOnSave}
                onInit={storeOnInit}
            />
        );
    };
    const downloadSet = () => {
        return (
            <>
                {getCustomJsx(getDownloadSetRender, [downloadButton()], {
                    playSpeed,
                    page: StorePageCode.VideoList,
                })}
            </>
        );
    };

    const toolBar = (
        <div className="switch-layout-container">
            <RspHorizontal justify="space-between" align="middle">
                {layoutType === 'card' && leftButton}
                {pageType == 'video-library' && (
                    <Space size="middle">
                        {getTableIconBtns(getIconBtns, [
                            cardCheckAll(),
                            downloadSet(),
                            listBtn,
                            cardBtn,
                        ])}
                    </Space>
                )}
            </RspHorizontal>
        </div>
    );

    const evidence = (list || []).find((p) => p.evidenceId == evidenceId) || {};
    const cardList = (
        <div className="card-container">
            <Spin spinning={loading}>
                {!list.length && renderEmpty('List')}
                <RspCardLayout minWidth={280}>
                    {list.map((item: any, index: number) => {
                        return (
                            <Card
                                key={item.evidenceId || index}
                                data={item}
                                exportData={exportData}
                                playSpeed={playSpeed}
                                checkbox={{
                                    ...(getListItemCheckboxProps?.(item) || {}),
                                    checked: selectedRows.find(
                                        (i: any) =>
                                            i.evidenceId === item.evidenceId,
                                    ),
                                    onChange: (checked: boolean) => {
                                        const index = selectedRows.findIndex(
                                            (i: any) =>
                                                i.evidenceId ===
                                                item.evidenceId,
                                        );
                                        const newRows = [...selectedRows];
                                        if (checked && index === -1) {
                                            newRows.push(item);
                                        } else if (!checked && index !== -1) {
                                            newRows.splice(index, 1);
                                        }
                                        setSelectedRows(newRows);
                                    },
                                }}
                            />
                        );
                    })}
                </RspCardLayout>
            </Spin>
        </div>
    );
    /***定制查询**/
    const resetData = () => {
        setQueryParams(() => {
            return {
                ...queryParams,
                page: 1,
            };
        });
        setPage(1);
    };
    const reloadData = (resetParams: SearchReset = { reset: false }) => {
        const { reset } = resetParams;
        if (reset) {
            resetData();
            return;
        }
        query();
    };
    const loadDataSource = (newParams: SearchPage = {} as SearchPage) => {
        const page = newParams?.page || queryParams.page;
        setQueryParams(() => {
            return {
                ...queryParams,
                ...newParams,
                page,
            };
        });
        setPage(page);
    };
    const getRowSelection = () => {
        const rowSelection: TableProps['rowSelection'] = {
            onChange: (selectedKeys: React.Key[], selectedRows: any[]) => {
                setSelectedRows(selectedRows);
            },
            selectedRowKeys: selectedRows.map((i: any) => i.evidenceId),
        };
        // 定制 getCheckboxProps
        if (typeof getListItemCheckboxProps === 'function') {
            rowSelection.getCheckboxProps = getListItemCheckboxProps;
        }
        if (tableRowSelection) {
            return tableRowSelection() ? rowSelection : undefined;
        }
        return rowSelection;
    };

    useUpdateEffect(() => {
        runCustomFun(onSelectRows, selectedRows);
    }, [selectedRows]);
    runCustomFun(getInstances, {
        form,
        table: {
            loadDataSource,
            reload: reloadData,
            reset: resetData,
        },
    });

    const showPageNext = isNil(hasNextPage)
        ? list.length == pageSize
        : hasNextPage;

    // 处理行业复用表格列没有传递宽度给一个默认值180px
    const newColumns =
        pageType === 'video-library'
            ? // @ts-ignore
            getCustomItems(getColumns, columns, list)?.map((item) => {
                if (item.width) return item;
                else {
                    return {
                        ...item,
                        width: 180,
                    };
                }
            })
            : // @ts-ignore
            (getCustomItems(getColumns, columns, list) || [])
                .filter((item) => item.dataIndex !== 'labels')
                ?.map((item) => {
                    if (item.width) return item;
                    else {
                        return {
                            ...item,
                            width: 180,
                        };
                    }
                });

    /***定制查询end**/
    return (
        <div className="video-library-done">
            <Container className="query-form-container">
                <QueryForm
                    items={
                        pageType === 'video-library'
                            ? queryItems
                            : queryItems
                                .slice(0, 2)
                                .concat(queryItems.slice(3, 4))
                    }
                    form={form as any}
                    onSearch={handleSearch}
                    layout="vertical"
                    onReset={() => {
                        setTimeout(() => {
                            handleSearch(form.getFieldsValue());
                        }, 500);
                    }}
                />
            </Container>
            <Container noStyle={layoutType === 'card'}>
                <div className="card-top-area">
                    {layoutType === 'card' && toolBar}
                </div>
                {layoutType === 'card' && cardList}
                {layoutType === 'list' && (
                    <ProTable
                        aroundBordered
                        columns={newColumns}
                        dataSource={list}
                        rowKey={'evidenceId'}
                        onChange={handleTableChange}
                        className={cn('video-done-table', {
                            'video-list-table-scroll': !showSticky,
                        })}
                        loading={loading}
                        scroll={{ x: '100%' }}
                        toolbar={{
                            // @ts-ignore
                            iconBtns: getTableIconBtns(getIconBtns, [
                                'reload',
                                'column-setting',
                                ...(pageType == 'video-library'
                                    ? [downloadSet(), listBtn, cardBtn]
                                    : []),
                            ]),
                            onReload: query,
                            // rightRender: () => toolBar,
                            leftRender: () => leftButton,
                            columnSetting: {
                                // @ts-ignore
                                storageKey:
                                    pageType == 'video-library'
                                        ? '@base:@page:video.library.done'
                                        : '@base:@page:task.device.upload.done',
                                disabledKeys: [
                                    'evidenceName',
                                    'vehicleNumber',
                                    'totalSize',
                                    'createUserAccount',
                                    'createTime',
                                    'operate',
                                ],
                                ...getColumnSetting?.(),
                            },
                        }}
                        rowSelection={getRowSelection()}
                        sticky={{
                            offsetScroll: PAGINATION_HEIGHT,
                            offsetHeader: 112, // 距离container顶部的高度
                        }}
                    />
                )}
                <Affix offsetBottom={0}>
                    <div
                        className={`video-library-pagination-box ${
                            layoutType === 'card'
                                ? 'video-library-pagination-box-has-container'
                                : ''
                        }`}
                    >
                        <div
                            className={cn('video-library-pagination-box-item', {
                                'video-library-pagination-box-item-disabled':
                                    page == 1,
                            })}
                            onClick={() =>
                                page == 1 ? () => {
                                } : pageChange('pre')
                            }
                            title={i18n.t('message', '上一页')}
                        >
                            <LeftOutlined />
                        </div>
                        <div
                            className={cn(
                                'video-library-pagination-box-item',
                                'current-page',
                            )}
                        >
                            {page}
                        </div>
                        <div
                            onClick={() =>
                                showPageNext ? pageChange('next') : () => {
                                }
                            }
                            className={cn('video-library-pagination-box-item', {
                                'video-library-pagination-box-item-disabled':
                                    isNil(hasNextPage)
                                        ? list.length != pageSize
                                        : !hasNextPage,
                            })}
                            title={i18n.t('message', '下一页')}
                        >
                            <RightOutlined />
                        </div>
                    </div>
                </Affix>
            </Container>
            <MailModal
                visible={showMail}
                close={() => setShowMail(false)}
                //@ts-ignore
                evidenceId={evidenceId}
                type="video"
            />
            <CodeModal
                visible={showCode}
                close={() => setShowCode(false)}
                evidenceId={evidenceId}
                data={evidence}
                type="video"
            />
            <MarkModal
                visible={showMarkModal}
                // @ts-ignore
                selectedRows={selectedRows.map(
                    ({ evidenceId, labelIdList }) => ({
                        evidenceId,
                        labelIdList,
                    }),
                )}
                onFinish={onFinishMark}
            />
        </div>
    );
};
export default withSharePropsHOC<DeleteType, VideoLibraryDoneShareProps>(
    VideoLibraryDone,
);
