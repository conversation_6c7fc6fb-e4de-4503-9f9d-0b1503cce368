import { useState, useRef, useEffect } from 'react';
import classNames from 'classnames';
import { Auth, g_emmiter, i18n, utils, StarryAbroadIcon, MosaicTypeEnum, mosaicManager, H5VideoWithStatus } from '@base-app/runtime-lib';
import { IconMore, IconDownloadFill, IconQrCodeFill, IconMailboxFill } from '@streamax/poppy-icons';
import Icon from '@streamax/poppy-icons/lib/Icon';
import { Dropdown, Menu, Tooltip, Checkbox } from '@streamax/poppy';
// @ts-ignore
import { Action } from '@base-app/runtime-lib';
import LabelEllipsis from '@/runtime-pages/alarm-center/alarm-list/components/LabelEllipsis';
// @ts-ignore
// @ts-ignore
import { evidenceFormatData } from '@/utils/evidenceFormatData';
import { H5Video } from '@/components/HistoryPlayer/Video';
// @ts-ignore
import { ReactComponent as NoData } from '@/assets/icons/nodata.svg';
import { ReactComponent as <PERSON><PERSON>lean } from '@/assets/icons/icon_cleaned_dark.svg';
import { FileChannelAuthEnum, FileType, getAllVideos, getVideosByFileType, noChannelAuth } from '@/utils/commonFun';
import ImageView from '../../../evidence-list/components/ImageView';
import CodeModal from '@/components/EvidenceTools/components/code-modal';
import MailModal from '@/components/EvidenceTools/components/mail-modal';
import type { CheckboxProps } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { PickFrameEnum, StreamTypeEnum } from '../../constant';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { PageCardButtons } from '@/types/pageReuse/pageReuseBase';
import { VideoCardInfoItem } from '@/types/pageReuse/alarmList';
import { getCustomJsx } from '@/utils/pageReuse';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import AuthDriverShow from '@/components/AuthDriverShow';
import useStreamTypeMap from '../../hooks/useStreamTypeMap';
import './index.less';
import { PlaySpeed, PlaySpeedMap } from '@/components/UserSearchVideoDownloadStore/type';
import SpeedDownloadButton from '@/components/UserSearchVideoDownloadStore/component/SpeedDownloadButton';
import { getEvidenceDownloadTooltip } from '@/runtime-pages/video-library/utils';
import { aspectRatioImage } from '@base-app/runtime-lib';
import {FileCleanStateEnum} from "@/modules/evidence/types";
import { renderEvidenceCardType } from '@/runtime-lib/modules/Evidence/utils';

/**定制类型项 */
export type VideoLibraryCardShareProps = PageCardButtons & VideoCardInfoItem;

type CheckBoxType = Omit<CheckboxProps, 'onChange'> & { onChange: (checked: boolean) => void };

interface PropsType {
    data: any;
    exportData?: (id: string, type: string, playSpeed?: PlaySpeed, fileChannelAuth?: FileChannelAuthEnum) => Promise<void>;
    checkbox?: CheckBoxType;
    playSpeed?: PlaySpeed;
}

const TYPE = 'video';
const {
    formator: { formatByte, zeroTimeStampToFormatTime },
} = utils;

const VideoLibraryCard = (props: PropsType & VideoLibraryCardShareProps) => {
    /**定制项 */
    const { getCardButtons, getCardShareMenu, getVideoCardInfoItem } = props;
    /**end */
    const { AspectRatioImage } = aspectRatioImage.AspectRatioImageV1;

    const { data, checkbox, playSpeed, exportData } = props;
    const { fileChannelAuth } = data;
    const { onChange, ...resetCheckboxProps } = checkbox || {};
    const [showImages, setShowImages] = useState(false);
    const [showCode, setShowCode] = useState(false);
    const [showMail, setShowMail] = useState(false);
    const [isPlay, setIsPlay] = useState(false);
    const [loadedFinish, setLoadedFinish] = useState<boolean>(false); // 是否加载完成
    // @ts-ignore
    const { existH264 } = evidenceFormatData(data);
    const videoRef: any = useRef();
    const historyPlayerRef = useRef<any>(null);
    const [streamTypeMap] = useStreamTypeMap();
    const hasMosaic = mosaicManager.useMosaicConfig(MosaicTypeEnum.videoStore);

    useEffect(() => {
        g_emmiter.on('evidence.manage.search', () => {
            handleEnd();
        });
        return () => {
            g_emmiter.off('evidence.manage.search');
            videoRef.current && (videoRef.current.currentTime = 0);
        };
    }, []);

    const pause = () => {
        setIsPlay(false);
        if (existH264) {
            historyPlayerRef.current?.pause();
        } else {
            videoRef.current?.pause();
        }
    };
    const play = () => {
        setIsPlay(true);
        if (existH264) {
            historyPlayerRef.current?.play();
        } else {
            videoRef.current?.play();
        }
    };
    const handleEnd = () => {
        setIsPlay(false);
        setLoadedFinish(true);
        if (existH264) {
            historyPlayerRef.current?.stop();
        } else if (videoRef.current) {
            videoRef.current.currentTime = 0;
            pause();
        }
    };

    const viewImage = () => {
        setShowImages(true);
    };
    const viewCode = () => {
        if(noChannelAuth(fileChannelAuth)) {
            return;
        }
        setShowCode(true);
    };
    const viewMail = () => {
        if(noChannelAuth(fileChannelAuth)) {
            return;
        }
        setShowMail(true);
    };
    const downLoadButton = () => {
        const speed = playSpeed || PlaySpeed['1X'];
        const tooltipText = getEvidenceDownloadTooltip({ playSpeed: speed, hasMosaic });

        return exportData ? (
            <SpeedDownloadButton
                key="downLoadButton"
                tooltip={tooltipText}
                playSpeed={playSpeed || PlaySpeed['1X']}
                onClick={() => exportData && exportData(data.evidenceId, '1,2,3', playSpeed, data.fileChannelAuth)}
            />
        ) : null;
    };
    const qrCodeShareMenu = () => {
        return (
            <Auth code={'@base:@page:video.library@action:qrcode'} key="qrCodeShareButton">
                <span>
                    <Menu.Item key={1}>
                        <a onClick={viewCode}>
                            <StarryAbroadIcon>
                                <IconQrCodeFill />
                            </StarryAbroadIcon>
                            {i18n.t('action', '二维码分享')}
                        </a>
                    </Menu.Item>
                </span>
            </Auth>)
    };
    const emailShareMenu = () => {
        return (
            <Auth code={'@base:@page:video.library@action:view.mail'} key="emailShareButton">
                <span>
                    <Menu.Item key={2}>
                        <a onClick={viewMail}>
                            <StarryAbroadIcon>
                                <IconMailboxFill />
                            </StarryAbroadIcon>
                            {i18n.t('action', '邮件分享')}
                        </a>
                    </Menu.Item>
                </span>
            </Auth>
        )
    };
    const shareButtons = () => {
        const btns = data.fileCleaned === FileCleanStateEnum.CLEAN ? [] : [qrCodeShareMenu(), emailShareMenu()]
        let menuList: any = getCustomJsx(getCardShareMenu,btns, data) || [];
        // 兼容返回不是数组的情况
        if (!Array.isArray(menuList)) {
            menuList = [menuList];
        }
        if (menuList?.some(item => item)) {
            return (
                <Dropdown
                    key="shareButtons"
                    placement="bottomRight"
                    overlayClassName="share-dropdown"
                    overlay={
                        <Menu>
                            {getCustomJsx(
                                getCardShareMenu,
                                btns,
                                data,
                            )}
                        </Menu>
                    }
                >
                    <a className="more">
                        <IconMore />
                    </a>
                </Dropdown>
            );
        }
        return null;
    };

    const { vehicleInfo, labels = [] } = data;
    const { fileList, ...restData } = data;
    const imgs = (fileList || []).filter((p: any) => p.fileType == FileType.IMAGE && p.url);
    const videos = getVideosByFileType(fileList || [], existH264);
    const firstVideo = videos[0];
    let videoStartTime = data.startTime; // 第一段视频开始时间
    let videoEndTime = data.endTime; // 第一段视频结束时间
    if (videos.length > 0) {
        // 计算第一段视频开始结束时间
        const startVideo = videos.sort((a: any, b: any) => a.startTime - b.startTime)[0];
        const endVideo = videos.sort((a: any, b: any) => b.endTime - a.endTime)[0];
        videoStartTime = startVideo.startTime;
        videoEndTime = endVideo.endTime;
    }
    // 计算时长
    const name =
        data.sourceType === 1
            ? i18n.t(`@i18n:@alarmType__${data.alarmType}`, data.evidenceName)
            : data.evidenceName;
    const getDriverList = data?.montageDriverList;

    const videoName = (
        <div className="video-library-name-label" key="videoName">
            <div className="name" title={name} style={{ maxWidth: labels.length ? '50%' : '' }}>
                <Action
                    code="@base:@page:video.library@action:video.detail"
                    url="/video/detail"
                    target="blank"
                    params={{
                        evidenceId: data.evidenceId,
                        fromList: 1,
                        type: 'video',
                    }}
                    fellback={name}
                >
                    {name}
                </Action>
            </div>
            <div className="label">
                <LabelEllipsis list={labels} itemPosition="right" defaultContent={null} />
            </div>
        </div>
    );
    const vehicleDriver = (
        <div className="info-item-vehicle-driver" key="vehicleDriver">
            <span
                className="info-item-vehicle-number"
                title={vehicleInfo ? vehicleInfo.vehicleNumber : ''}
            >
                {vehicleInfo ? vehicleInfo.vehicleNumber : ''}
            </span>
            <span className="info-item-vehicle-driver-split" style={{ display: 'inline-block' }}>
                |
            </span>
            <OverflowEllipsisContainer
                style={{ maxWidth: '44%', marginTop: '-10px', height: '1.2em' }}
            >
                {/* montageDriverList 这种类型未过滤权限，可直接传入不用其他限制入参 */}
                <AuthDriverShow driverList={getDriverList} />
            </OverflowEllipsisContainer>
        </div>
    );
    const videoTime = (
        <div className="info-item" key="videoTime">
            <span className="item-name">{i18n.t('name', '视频时间')}：</span>
            {/* modify夏令时 */}
            <Tooltip
                className="item-value"
                title={
                    zeroTimeStampToFormatTime(videoStartTime) +
                    ' - ' +
                    // @ts-ignore
                    zeroTimeStampToFormatTime(
                        videoEndTime,
                        undefined,
                        undefined,
                        undefined,
                        // @ts-ignore
                        videoStartTime,
                    )
                }
            >
                {zeroTimeStampToFormatTime(videoStartTime)} -{' '}
                {zeroTimeStampToFormatTime(
                    videoEndTime,
                    undefined,
                    undefined,
                    undefined,
                    // @ts-ignore
                    videoStartTime,
                )}
            </Tooltip>
        </div>
    );
    const createTime = (
        <div className="info-item-create-time" key="createTime">
            <OverflowEllipsisContainer>
                <span className="item-name">{i18n.t('name', '产生时间')}：</span>
                <span className="item-value">{zeroTimeStampToFormatTime(data.createTime)}</span>
            </OverflowEllipsisContainer>
        </div>
    );
    const renderEvidenceCardTypeObject = renderEvidenceCardType(firstVideo, imgs, data.fileChannelAuth);
    return (
        <div className="video-library-card">
            {/* Checkbox组件 */}
            {checkbox ? (
                <div className="card-checkbox-bg-container">
                    <Checkbox
                        className="card-checkbox"
                        onChange={(e: CheckboxChangeEvent) =>
                            onChange && onChange(e.target.checked)
                        }
                        {...resetCheckboxProps}
                    />
                </div>
            ) : null}
            <div className={classNames('image-container', { play: isPlay })}>
                {
                    data.fileCleaned === FileCleanStateEnum.CLEAN ? (
                            <div className="center-icon">
                                <Icon component={FileClean} />
                                <div className="center-icon-label">{i18n.t('message', '证据被清理')}</div>
                            </div>
                    ):
                    (
                        <>
                            {(renderEvidenceCardTypeObject.video || existH264) ? (
                                <>
                                    <H5VideoWithStatus
                                        ref={historyPlayerRef}
                                        evidenceData={data}
                                        mosaicSourcode={MosaicTypeEnum.videoStore}
                                        fileType={firstVideo?.fileType}
                                        videoInfo={firstVideo}
                                        hasProgress
                                        isPreView
                                        hasFileSize
                                        existH264={existH264}
                                        streamTypeMap={streamTypeMap}
                                    />
                                </>
                            ) : null}
                            {renderEvidenceCardTypeObject.image && !existH264 ? (
                                <AspectRatioImage
                                    preview={false}
                                    src={imgs[0].url}
                                    onClick={viewImage}
                                    style={{ cursor: 'pointer' }}
                                />
                            ) : null}
                            {renderEvidenceCardTypeObject.noData && !existH264 ? (
                                <div className="center-icon">
                                    <Icon component={NoData} />
                                    <div className="center-icon-label">{i18n.t('message', '暂无数据')}</div>
                                </div>
                            ) : null}
                        </>
                    )
                }

            </div>
            <div className="video-library-info">
                {getCustomJsx(
                    getVideoCardInfoItem,
                    [videoName, vehicleDriver, videoTime, createTime],
                    { ...restData },
                )}
            </div>
            <div className="btns">
                {getCustomJsx(getCardButtons, [
                        data.fileCleaned !== FileCleanStateEnum.CLEAN && downLoadButton(),
                        shareButtons()
                    ].filter(Boolean)
                    , data)}
            </div>
            <ImageView
                vehicleData={data}
                visible={showImages}
                list={imgs}
                onClose={() => setShowImages(false)}
            />
            <CodeModal
                visible={showCode}
                close={() => setShowCode(false)}
                evidenceId={data.evidenceId}
                data={data}
                type={TYPE}
            />
            <MailModal
                visible={showMail}
                close={() => setShowMail(false)}
                evidenceId={data.evidenceId}
                type={TYPE}
                emailShare="1"
            />
        </div>
    );
};
export default withSharePropsHOC<PropsType, VideoLibraryCardShareProps>(VideoLibraryCard);
