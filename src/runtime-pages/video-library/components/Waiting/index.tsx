import { useLockFn } from '@streamax/hooks';
import { useUpdateEffect } from '@streamax/hooks';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { Space, Button, message, Tooltip, Form, Table } from '@streamax/poppy';
import type { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import type { TableProps } from '@streamax/poppy/lib/table';
import { IconTop, IconDeleteFill } from '@streamax/poppy-icons';
import Icon from '@streamax/poppy-icons/lib/Icon';
import {
    i18n,
    utils,
    Auth,
    useUrlSearchStore,
    getAppGlobalData,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
// @ts-ignore
import { FleetTreeSelect } from '@base-app/runtime-lib';
import {
    ListDataContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import cn from 'classnames';
import moment from 'moment';
import React, { useState, useRef, useContext, useEffect } from 'react';
import DateRange from '../../../../components/DateRange';
import { updateEvidencePriority } from '../../../../service/evidence';
import { fetchBaseData } from '../../../../utils/filterFetch';
import SearchInput from '../../../evidence-list/components/SearchInput';
import type { DeleteType } from '../../index';
import { PageTypeContext } from '../../index';
import { getUserMdFormat } from '../../utils';
import { ReactComponent as IconInformation } from '@/assets/icons/icon_implement_fill.svg';
import AuthDriverShow from '@/components/AuthDriverShow';
import AuthFleetShow from '@/components/AuthFleetShow';
import useFormUrlSearchStore from '@/hooks/useFormUrlSearchStore';
import { updateTaskForce } from '@/service/task';
import calcHeight from '@/utils/calcStickyHeight';
import useHideTableStickyScroll from '@/hooks/useHideTableStickyScroll';
import type {
    ListPageTableBase,
    ListPageQueryForm,
    Instances,
} from '@/types/pageReuse/pageReuseBase';
import { disabledAfterDate } from '@/utils/commonFun';
import { getVideoPageProcessList } from '@/utils/evidencePageList';
import {
    getCustomItems,
    runCustomFun,
    getCustomJsx,
    getTableIconBtns,
} from '@/utils/pageReuse';
import { useLockAppIdHeaders } from '@/utils/lockAppId';
import { getPickerRangeTWM } from '@/utils/date-picker-config';
import './index.less'
/**列表页table定制复用 */
export type VideoLibraryWaitingShareProps = ListPageTableBase &
    ListPageQueryForm &
    Instances &
    ListItemCheckboxProps;
// @ts-ignore
const { timestampToZeroTimeStamp, getLocalMomentByZeroTimeStamp } =
    utils.formator;

const VideoLibraryWaiting = (
    props: DeleteType & VideoLibraryWaitingShareProps & TableProps,
) => {
    /**定制项 */
    const {
        getColumns,
        onSelectRows,
        getTableLeftRender,
        injectSearchList,
        getIconBtns,
        getQueryForm,
        getInstances,
        tableRowSelection,
        getColumnSetting,
        getListItemCheckboxProps,
    } = props;
    /**end */
    const config = getAppGlobalData('APP_USER_CONFIG');
    const { params: formQuery, setSearchStore } = useFormUrlSearchStore(
        'page',
        'pageSize',
        ['waitStartTime', 'waitEndTime', 'waitIncludeSubFleet'],
    );
    const searchStore = useUrlSearchStore();
    const [form] = Form.useForm();
    const { pageType } = useContext(PageTypeContext);
    const [selectedEvidences, setSelectedEvidences] = useState<any[]>([]);
    const [order, setOrder] = useState<string | undefined>(
        formQuery.WaitComplexSort ? formQuery.WaitComplexSort : null,
    );
    const [list, setList] = useState<any[]>([]);
    const tableRef = useRef<any>();

    const headers = useLockAppIdHeaders();

    const deleteRecords = async (
        records: Record<string, any>,
        batchDelete = false,
    ) => {
        await props.deleteRecords(records, batchDelete);
        setSelectedEvidences([]);
        tableRef.current?.loadDataSource();
    };
    const top = async (evidenceId: number, evidence: any) => {
        await updateEvidencePriority([
            {
                evidenceId,
                priority: 10,
                logParams: [{ data: evidence.evidenceName }],
                operationModelCode: 'video',
                operationTypeCode: 'edit',
                operationDetailTypeCode: 'top',
            },
        ]);
        message.success(i18n.t('message', '置顶成功'));
        tableRef.current?.loadDataSource();
    };
    const initTimeRange = [
        moment().subtract(6, 'days').startOf('day'),
        moment().endOf('day'),
    ];

    const {
        waitVehicleSearchType,
        waitVehicleSearchValue,
        waitFleetIds,
        waitIncludeSubFleet,
        waitPage,
        waitPageSize,
        waitStartTime,
        waitEndTime,
        ...value
    } = formQuery;
    const PAGINATION_HEIGHT = 64;
    const { showSticky } = useHideTableStickyScroll({
        paginAtionHight: PAGINATION_HEIGHT,
        dataSource: list,
    });
    useEffect(() => {
        form.setFieldsValue({
            ...value,
            vehicle: {
                type: waitVehicleSearchType || 'vehicle',
                value: waitVehicleSearchValue,
            },
            time:
                waitStartTime && waitEndTime
                    ? [
                          getLocalMomentByZeroTimeStamp(waitStartTime) || null,
                          getLocalMomentByZeroTimeStamp(waitEndTime) || null,
                      ]
                    : initTimeRange,
            company: {
                fleetIds: waitFleetIds,
                includeSubFleet: waitIncludeSubFleet ?? 1,
            },
        });
    }, []);
    useEffect(() => {
        tableRef.current?.loadDataSource();
    }, [order]);

    // 强制执行返回错误码

    // 二级状态码展示
    enum enforceSubStateCode {
        WAITING_ENFORCEMENT = 1, // 等待强制执行
        EXECUTION_PROGRESS = 2, // 置顶强制执行执行中
        TOP_FORCED_SUCCESSFULLY = 3, // 置顶强制成功
        TOP_FORCED_FAILED = 4, // 置顶强制失败
        PAUSE = 5, // 暂停
        FAIL = 6, // 取消
    }

    // 证据类型
    enum evidenceTypeCode {
        VIDEO_CLIP = 2, // 视频剪辑
        ALARM_EVENTS = 7, // 报警事件
        PLATFORM_ALARM_EVENTS = 9, // 平台报警事件
    }

    // 强制任务执行操作
    const enforce = useLockFn(async (evidenceId: string, evidence: any) => {
        const params = {
            evidenceId,
            logParams: [{ data: evidence.evidenceName }],
            operationModelCode: 'video',
            operationTypeCode: 'edit',
            operationDetailTypeCode: 'enforce',
        };
        await updateTaskForce(params);
        message.success(i18n.t('message', '操作成功'));
        //处理之后刷新表格
        tableRef.current.loadDataSource();
    });

    /**
     * 1.evidenceType为上述类型中的“是”，才能置顶强制执行  没有
     * 2.sourceType为2，才能置顶执行；  没有
     * 3.subState为空或者不是1（等待）和3（成功），才能置顶置顶  置灰
     *上述三个条件均满足的情况下，才能满足置顶强制执行；否则就置灰或者没有按钮
     * @param row
     * @returns
     */
    const enforceStrategy = (row: any) => {
        const showTypes = [
            evidenceTypeCode.VIDEO_CLIP,
            evidenceTypeCode.ALARM_EVENTS,
            evidenceTypeCode.PLATFORM_ALARM_EVENTS,
        ];
        const notShowStates = [
            enforceSubStateCode.WAITING_ENFORCEMENT,
            enforceSubStateCode.TOP_FORCED_SUCCESSFULLY,
        ];
        if (
            showTypes.includes(row.evidenceType) &&
            (row.subState === null || !notShowStates.includes(row.subState))
        ) {
            return (
                <Auth code="@base:@page:video.library@action:enforce.task">
                    <Tooltip title={i18n.t('name', '强制执行')}>
                        <a
                            onClick={() => {
                                enforce(row.evidenceId, row);
                            }}
                        >
                            <Icon component={IconInformation} />
                        </a>
                    </Tooltip>
                </Auth>
            );
        }
        return null;
    };
    // 强制执行过程中不可删除
    const deleteDisable = (record: any) => {
        if (!record?.subState) return false;
        return ![
            enforceSubStateCode.TOP_FORCED_SUCCESSFULLY,
            enforceSubStateCode.TOP_FORCED_FAILED,
        ].includes(record?.subState);
    };
    const getDefaultSortOrder = () => {
        if(formQuery.WaitComplexSort) {
            if(formQuery.WaitComplexSort == 'create_time asc') {
                return 'ascend';
            }
            if(formQuery.WaitComplexSort == 'create_time desc') {
                return 'descend';
            }
        }
    };
    const columns = [
        {
            title: i18n.t('name', '视频名称'),
            dataIndex: 'evidenceName',
            key: 'evidenceName',
            ellipsis: { showTitle: false },
            fixed: 'left',
            width: 200,
        },
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            key: 'vehicleNumber',
            ellipsis: { showTitle: false },
            width: 200,
        },
        pageType === 'video-library'
            ? {
                  title: i18n.t('name', '司机姓名'),
                  dataIndex: 'driverName',
                  key: 'driverName',
                  ellipsis: { showTitle: false },
                  width: 200,
                  render: (_: unknown, row: any) => {
                      // montageDriverList 这种类型未过滤权限，可直接传入不用其他限制入参
                      return (
                          <StarryAbroadOverflowEllipsisContainer>
                              <AuthDriverShow
                                  driverList={row?.montageDriverList}
                              />
                          </StarryAbroadOverflowEllipsisContainer>
                      );
                  },
              }
            : null,
        {
            title: i18n.t('name', '设备编码'),
            dataIndex: 'deviceNo',
            key: 'deviceNo',
            width: 200,
            ellipsis: { showTitle: false },
            render: (text: string, record: any) => {
                const { deviceNo, deviceAlias } = record?.vehicleInfo || {};
                const name = deviceAlias || deviceNo || '-';
                const title = deviceAlias
                    ? `${deviceAlias}(${deviceNo})`
                    : deviceNo;
                return <Tooltip title={title}>{name}</Tooltip>;
            },
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetName',
            key: 'fleetName',
            calcWidth: () => 200,
            width: 280,
            ellipsis: { showTitle: false },
            render: (_: unknown, row: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow
                            fleetList={row?.vehicleInfo?.fleetList}
                        />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '产生时间'),
            dataIndex: 'createTime',
            key: 'createTime',
            sorter: true,
            calcWidth: () => 165,
            width: 180,
            defaultSortOrder: getDefaultSortOrder(),
            ellipsis: { showTitle: false },
            render: (text: number) => {
                const time = utils.formator.zeroTimeStampToFormatTime(text);
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {time}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '视频时间'),
            dataIndex: 'startTime',
            key: 'startTime',
            ellipsis: { showTitle: false },
            calcWidth: () => 180,
            width: 220,
            render: (text: number, record: any) => {
                const time =
                    utils.formator.zeroTimeStampToFormatTime(
                        text,
                        undefined,
                        getUserMdFormat(config?.timeFormat),
                    ) +
                    ' - ' +
                    utils.formator.zeroTimeStampToFormatTime(
                        record.endTime,
                        undefined,
                        'HH:mm:ss',
                        undefined,
                        // @ts-ignore
                        record.startTime,
                    );
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {time}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            key: 'operate',
            ellipsis: true,
            width: 180,
            fixed: 'right',
            calcWidth: () => utils.general.calcTableOperateWidth(3),
            render: (_: any, row: { evidenceId: number }) => {
                return (
                    <Space size={20} key="operateSpace">
                        <Auth
                            key="topingButton"
                            code={
                                pageType === 'device-upload'
                                    ? '@base:@page:device.upload@action:top.task'
                                    : '@base:@page:video.library@action:top.task'
                            }
                        >
                            <Tooltip title={i18n.t('name', '置顶')}>
                                <a
                                    onClick={() => {
                                        top(row.evidenceId, row);
                                    }}
                                >
                                    <IconTop />
                                </a>
                            </Tooltip>
                        </Auth>
                        {/* 强制置顶只在视频库 */}
                        {pageType === 'video-library'
                            ? enforceStrategy(row)
                            : null}
                        {pageType === 'video-library' ? (
                            <Auth
                                key="deleteButton"
                                code="@base:@page:video.library@action:batch.delete.task"
                            >
                                <Tooltip title={i18n.t('name', '删除')}>
                                    {deleteDisable(row) ? null : (
                                        <a
                                            onClick={() => {
                                                deleteRecords([row], false);
                                            }}
                                        >
                                            <IconDeleteFill />
                                        </a>
                                    )}
                                </Tooltip>
                            </Auth>
                        ) : (
                            <Auth
                                key="deleteButton"
                                code="@base:@page:device.upload@action:delete.task"
                            >
                                <Tooltip title={i18n.t('name', '删除')}>
                                    <a
                                        onClick={() => {
                                            deleteRecords([row], false);
                                        }}
                                    >
                                        <IconDeleteFill />
                                    </a>
                                </Tooltip>
                            </Auth>
                        )}
                    </Space>
                );
            },
        },
    ].filter((item) => item);
    const items: QueryFormProps['items'] = [
        {
            name: 'vehicle',
            field: SearchInput,
            fieldProps: {
                searchOptions: ['vehicle'],
            },
        },
        {
            label: i18n.t('name', '归属车组'),
            name: 'company',
            field: FleetTreeSelect,
            fieldProps: {
                // showSubFleetContent: true,
                placeholder: i18n.t('message', '请选择归属车组'),
            },
        },
        {
            label: i18n.t('name', '产生时间'),
            name: 'time',
            // field: RangePicker,
            field: DateRange,
            colSize: 2,
            itemProps: {
                initialValue: initTimeRange,
            },
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    allowClear: true,
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    ranges: getPickerRangeTWM(),
                    disabledDate: disabledAfterDate,
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];
    const queryItems = getCustomItems(getQueryForm, items);
    const handleTableChange: TableProps['onChange'] = (
        pagination,
        filters,
        sorter,
    ) => {
        //@ts-ignore
        const { order } = sorter;
        const orderEnum = {
            descend: 'desc',
            ascend: 'asc',
        };
        setOrder(order ? `create_time ${orderEnum[order]}` : undefined);
    };

    const fetchData = async (values: any) => {
        const { vehicle, company, time, page, pageSize } = values;
        let startTime, endTime;
        if (time) {
            startTime = timestampToZeroTimeStamp(time[0]);
            endTime = timestampToZeroTimeStamp(time[1]);
        } else {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                time: initTimeRange,
            });
            startTime = timestampToZeroTimeStamp(initTimeRange[0]);
            endTime = timestampToZeroTimeStamp(initTimeRange[1]);
        }
        setSearchStore({
            waitVehicleSearchType: vehicle?.type,
            waitVehicleSearchValue: vehicle?.value,
            waitFleetIds: company?.fleetIds,
            waitIncludeSubFleet: company?.includeSubFleet,
            waitPage: page,
            waitPageSize: pageSize,
            waitStartTime: startTime,
            waitEndTime: endTime,
        });
        if (injectSearchList) {
            setSelectedEvidences([]);
            return injectSearchList({ ...values, complexSort: order || 'priority desc,update_time desc' });
        }
        let ids;
        if (vehicle && vehicle.value && vehicle.value.replace(/\s/g, '')) {
            ids = await fetchBaseData(vehicle);
            if (!ids || !ids.length) {
                return new Promise((resolve) => {
                    resolve({
                        total: 0,
                        list: [],
                    });
                });
            }
        }
        const params = {
            ...values,
            ...company,
            [`${vehicle ? vehicle.type : 'device'}Ids`]: ids,
            fields: 'vehicle,montage_driver',
            states: '1',
            evidenceTypes: pageType === 'device-upload' ? '6' : '2,5,8',
            startTime,
            endTime,
            time: undefined,
            page: Number(page),
            pageSize: Number(pageSize),
        };
        if (order) {
            params.complexSort = order;
        } else {
            params.complexSort = 'priority desc,update_time desc';
        }
        setSearchStore({
            ...searchStore.get(),
            WaitComplexSort: params.complexSort,
        });
        setSelectedEvidences([]);
        const result = await getVideoPageProcessList(params, headers);
        setList(result.list);
        return result;
    };
    const handleRowSelectChange = (
        _: any,
        selectedRows: React.SetStateAction<any[]>,
    ) => {
        setSelectedEvidences(selectedRows);
    };

    const batchDeleteButton = () => {
        return (
            <Auth
                key="batchDeleteButton"
                code={
                    pageType === 'device-upload'
                        ? '@base:@page:device.upload@action:batch.delete.task'
                        : '@base:@page:video.library@action:batch.delete.task'
                }
            >
                <Button
                    onClick={() => {
                        deleteRecords(selectedEvidences, true);
                    }}
                    disabled={selectedEvidences.length === 0}
                >
                    <IconDeleteFill />
                    {i18n.t('action', '批量删除')}
                </Button>
            </Auth>
        );
    };
    const getRowSelection = () => {
        const rowSelection: TableProps['rowSelection'] = {
            selectedRowKeys: selectedEvidences.map(
                (evidence) => evidence.evidenceId,
            ),
            onChange: handleRowSelectChange,
            getCheckboxProps: (record: any) => {
                if (deleteDisable(record)) {
                    return { disabled: true };
                } else {
                    return { disabled: false };
                }
            },
        };
        // 定制 getCheckboxProps
        if (typeof getListItemCheckboxProps === 'function') {
            rowSelection.getCheckboxProps = getListItemCheckboxProps;
        }
        if (tableRowSelection) {
            return tableRowSelection() ? rowSelection : undefined;
        }
        return rowSelection;
    };
    useUpdateEffect(() => {
        runCustomFun(onSelectRows, selectedEvidences);
    }, [selectedEvidences]);

    runCustomFun(getInstances, {
        form,
        table: {
            ...tableRef.current,
            reload: tableRef.current?.loadDataSource,
        },
    });
    // @ts-ignore
    const newColumns = getCustomItems(getColumns, columns)?.map((item) => {
        if (item.width) return item;
        else {
            return {
                ...item,
                width: 180,
            };
        }
    });
    const { tableColumns, tableColumnSettingProps } =
        TableColumnSetting.useSetting(newColumns, {
            storageKey: '@base:@page:video.library.waiting',
            disabledKeys: [
                'evidenceName',
                'vehicleNumber',
                'deviceNo',
                'createTime',
                'operate',
            ],
            ...getColumnSetting?.(),
        });
    return (
        <ListDataContainer
            loadDataSourceOnMount={false}
            queryForm={{
                items: queryItems,
                form,
            }}
            ref={tableRef as any}
            toolbar={{
                extraLeft: (
                    <Space>
                        {getCustomJsx(getTableLeftRender, [
                            batchDeleteButton(),
                        ])}
                    </Space>
                ),
                // @ts-ignore
                extraIconBtns: getTableIconBtns(getIconBtns, [
                    <TableColumnSetting
                        key="setting"
                        {...tableColumnSettingProps}
                    />,
                ]),
            }}
            // @ts-ignore
            getDataSource={fetchData}
            listRender={(data) => {
                return (
                    <Table
                        className={
                           cn(!showSticky ? 'video-list-waiting-table-scroll' : '','video-waiting-table')
                        }
                        scroll={{ x: '100%' }}
                        columns={tableColumns}
                        dataSource={data}
                        rowKey="evidenceId"
                        rowSelection={getRowSelection()}
                        onChange={handleTableChange}
                        sticky={{
                            offsetHeader: calcHeight(), // 距离container顶部的高度
                            offsetScroll: PAGINATION_HEIGHT,
                            getContainer: () => {
                                return document.querySelector('#root') as HTMLElement;
                            },
                        }}
                    />
                );
            }}
        />
    );
};
export default withSharePropsHOC<DeleteType, VideoLibraryWaitingShareProps>(
    VideoLibraryWaiting,
);
