import React, { useState, useRef, useContext, useEffect } from 'react';
import {
    i18n,
    utils,
    Auth,
    useUrlSearchStore,
    getAppGlobalData,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
// @ts-ignore
import { FleetTreeSelect } from '@base-app/runtime-lib';
import SearchInput from '../../../evidence-list/components/SearchInput';
import { fetchBaseData } from '../../../../utils/filterFetch';
import { QueryFormProps } from '@streamax/poppy/lib/pro-form/query-form';
import { Space, Button, message, Tooltip, Form, Table } from '@streamax/poppy';
import { SyncOutlined, DeleteOutlined } from '@ant-design/icons';
import { IconDeleteFill } from '@streamax/poppy-icons';
import { retry } from '../../../../service/evidence';
import { DeleteType } from '../../index';
import { PageTypeContext } from '../../index';
import cn from 'classnames';
import useFormUrlSearchStore from '@/hooks/useFormUrlSearchStore';
import {
    ListDataContainer,
    OverflowEllipsisContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import {ListPageTableBase, ListPageQueryForm, Instances, ListItemCheckboxProps} from '@/types/pageReuse/pageReuseBase';
import type { TableProps } from '@streamax/poppy/lib/table';
import { getCustomItems, runCustomFun, getCustomJsx, getTableIconBtns } from '@/utils/pageReuse';
import { useUpdateEffect } from '@streamax/hooks';
import AuthDriverShow from '@/components/AuthDriverShow';
import AuthFleetShow from '@/components/AuthFleetShow';
import { getVideoPageProcessList } from '@/utils/evidencePageList';
import DateRange from '@/components/DateRange';
import moment from 'moment';
import { disabledAfterDate, getInitTimeRange } from '@/utils/commonFun';
import { getUserMdFormat } from '../../utils';
import { useLockAppIdHeaders } from '@/utils/lockAppId';
import { getPickerRangeTWM } from '@/utils/date-picker-config';
import {getFailReason} from "@/utils/getFailReason";
import calcHeight from '@/utils/calcStickyHeight';
import useHideTableStickyScroll from '@/hooks/useHideTableStickyScroll';
import './index.less';
import {isNil} from "lodash";

/**列表页table定制复用 */
export type VideoLibraryFailShareProps = ListPageTableBase & ListPageQueryForm & Instances & ListItemCheckboxProps;

type logParamsProp = {
    id?: number;
    data: string;
};
const VideoLibraryFail = (props: DeleteType & VideoLibraryFailShareProps & TableProps) => {
    /**定制项 */
    const {
        getColumns,
        onSelectRows,
        getTableLeftRender,
        injectSearchList,
        getIconBtns,
        getQueryForm,
        getInstances,
        tableRowSelection,
        getColumnSetting,
        getListItemCheckboxProps,
    } = props;
    /**end */
    const config = getAppGlobalData('APP_USER_CONFIG');
    const { params: formQuery, setSearchStore } = useFormUrlSearchStore('page', 'pageSize', [
        'FailIncludeSubFleet',
        'FailStartTime',
        'FailEndTime',
    ]);
    const [form] = Form.useForm();
    const searchStore = useUrlSearchStore();
    const { pageType } = useContext(PageTypeContext);
    const [selectedEvidences, setSelectedEvidences] = useState<any[]>([]);
    const [order, setOrder] = useState<string | undefined>(
        formQuery.FailComplexSort ? formQuery.FailComplexSort : null,
    );
    const [list, setList] = useState<any[]>([]);
    const tableRef = useRef<any>();
    const initTimeRange = getInitTimeRange(7); // 默认7天内
    const {
        FailVehicleSearchType,
        FailVehicleSearchValue,
        FailFleetIds,
        FailIncludeSubFleet,
        FailPage,
        FailPageSize,
        FailStartTime,
        FailEndTime,
        ...value
    } = formQuery;
    const { timestampToZeroTimeStamp, getLocalMomentByZeroTimeStamp } = utils.formator;

    const headers = useLockAppIdHeaders();
        const PAGINATION_HEIGHT = 64;
        const { showSticky } = useHideTableStickyScroll({
                paginAtionHight: PAGINATION_HEIGHT,
                dataSource: list,
        });

    useEffect(() => {
        let decodeFailVehicleSearchValue = ''
        try {
            decodeFailVehicleSearchValue = FailVehicleSearchValue ? decodeURIComponent(FailVehicleSearchValue) : ''
        } catch (error) {
            console.log(error);
            decodeFailVehicleSearchValue = FailVehicleSearchValue
        }
        form.setFieldsValue({
            ...value,
            vehicle: {
                type: FailVehicleSearchType || 'vehicle',
                value: decodeFailVehicleSearchValue,
            },
            time:
                FailStartTime && FailEndTime
                    ? [
                          getLocalMomentByZeroTimeStamp(FailStartTime) || null,
                          getLocalMomentByZeroTimeStamp(FailEndTime) || null,
                      ]
                    : initTimeRange,
            company: { fleetIds: FailFleetIds, includeSubFleet: FailIncludeSubFleet ?? 1 },
        });
    }, []);
    useEffect(() => {
        tableRef.current?.loadDataSource();
    }, [order]);

    const reTryEvidenceConfirm = async (evidenceId?: number | undefined, evidence?: any) => {
        if (!evidenceId) {
            if (!selectedEvidences.length) {
                message.warning(i18n.t('message', '请选择重试项'));
                return;
            }
        }
        let evidenceIds: number[] = [];
        if (!evidenceId) {
            // evidenceIds = selectedEvidences.map((evidence: any) => evidence.evidenceId);
            selectedEvidences.forEach((evidenceItem) => {
                evidenceIds.push(evidenceItem.evidenceId);
            });
        } else {
            evidenceIds = [evidenceId];
        }
        const params = {
            evidenceIds: evidenceIds.join(','),
        };
        await retry(params);
        message.success(i18n.t('message', '重试请求发送成功'));
        tableRef.current?.loadDataSource();
        setSelectedEvidences([]);
    };
    const deleteRecords = async (records: Record<string, any>, batchDelete = false) => {
        await props.deleteRecords(records, batchDelete);
        setSelectedEvidences([]);
        tableRef.current?.loadDataSource();
    };
    const columns = [
        {
            title: i18n.t('name', '视频名称'),
            dataIndex: 'evidenceName',
            key: 'evidenceName',
            ellipsis: { showTitle: false },
            fixed: 'left',
            width: 200,
        },
        {
            title: i18n.t('name', '车牌号码'),
            dataIndex: 'vehicleNumber',
            key: 'vehicleNumber',
            ellipsis: { showTitle: false },
            width: 200,
        },
        pageType === 'video-library'
            ? {
                  title: i18n.t('name', '司机姓名'),
                  dataIndex: 'driverName',
                  key: 'driverName',
                  ellipsis: { showTitle: false },
                  width: 200,
                  render: (_: unknown, row: any) => {
                      // montageDriverList 这种类型未过滤权限，可直接传入不用其他限制入参
                      return (
                          <StarryAbroadOverflowEllipsisContainer>
                              <AuthDriverShow driverList={row?.montageDriverList} />
                          </StarryAbroadOverflowEllipsisContainer>
                      );
                  },
              }
            : null,
        {
            title: i18n.t('name', '设备编码'),
            dataIndex: 'deviceNo',
            key: 'deviceNo',
            ellipsis: { showTitle: false },
            width: 200,
            render: (text: string, record: any) => {
                const { deviceNo, deviceAlias } = record?.vehicleInfo || {};
                const name = deviceAlias || deviceNo || '-';
                const title = deviceAlias ? `${deviceAlias}(${deviceNo})` : deviceNo;
                return <Tooltip title={title}>{name}</Tooltip>;
            },
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetName',
            key: 'fleetName',
            calcWidth: () => 200,
            width: 280,
            ellipsis: { showTitle: false },
            render: (_: unknown, row: any) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow fleetList={row?.vehicleInfo?.fleetList} />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '产生时间'),
            dataIndex: 'createTime',
            key: 'createTime',
            calcWidth: () => 165,
            width: 180,
            sorter: true,
            defaultSortOrder:
                formQuery.FailComplexSort &&
                (formQuery.FailComplexSort == 'create_time asc' ? 'ascend' : 'descend'),
            ellipsis: { showTitle: false },
            render: (text: number) => {
                const time = utils.formator.zeroTimeStampToFormatTime(text);
                return (
                    <Tooltip title={time}>
                        <OverflowEllipsisContainer>{time}</OverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '视频时间'),
            dataIndex: 'startTime',
            key: 'startTime',
            ellipsis: { showTitle: false },
            calcWidth: () => 180,
            width: 220,
            render: (text: number, record: any) => {
                const time =
                    utils.formator.zeroTimeStampToFormatTime(
                        text,
                        undefined,
                        getUserMdFormat(config?.timeFormat),
                    ) +
                    ' - ' +
                    utils.formator.zeroTimeStampToFormatTime(
                        record.endTime,
                        undefined,
                        'HH:mm:ss',
                        undefined,
                        // @ts-ignore
                        record.startTime,
                    );
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {time}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '失败原因'),
            dataIndex: 'failCause',
            key: 'failCause',
            ellipsis: { showTitle: false },
            calcWidth: () => 200,
            width: 250,
            render: (value: any, record: any) => {
                if(!value && isNil(record?.failCode) ) return '-';
                let failDetail = getFailReason(value,record.failCode);
                let failDetailStr = '';
                if (failDetail.length > 1) {
                    failDetailStr = failDetail.join(';');
                    failDetail = failDetail.map((item) => (
                        <>
                            <span>{item};</span>
                            <br />
                        </>
                    ));
                    return (
                        <OverflowEllipsisContainer
                            tooltip={{ overlay: failDetail }}
                        >
                            {failDetailStr || '-'}
                        </OverflowEllipsisContainer>
                    );
                } else {
                    failDetailStr = failDetail[0];
                    return (
                        <StarryAbroadOverflowEllipsisContainer>
                            {failDetailStr || '-'}
                        </StarryAbroadOverflowEllipsisContainer>
                    );
                }
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            key: 'operate',
            ellipsis: true,
            width: 180,
            fixed: 'right',
            calcWidth: () => utils.general.calcTableOperateWidth(),
            render: (text: string, row: any) => {
                return (
                    <Space size={20} key="operateSpace">
                        <Auth
                            key="retryButton"
                            code={
                                pageType === 'device-upload'
                                    ? '@base:@page:device.upload@action:reset.task'
                                    : '@base:@page:video.library@action:batch.reset.task'
                            }
                        >
                            <Tooltip title={i18n.t('name', '重试')}>
                                <a
                                    onClick={() => {
                                        reTryEvidenceConfirm(row.evidenceId, row);
                                    }}
                                >
                                    <SyncOutlined />
                                </a>
                            </Tooltip>
                        </Auth>
                        <Auth
                            key="deleteButton"
                            code={
                                pageType === 'device-upload'
                                    ? '@base:@page:device.upload@action:delete.task'
                                    : '@base:@page:video.library@action:batch.delete.task'
                            }
                        >
                            <Tooltip title={i18n.t('action', '删除')}>
                                <a onClick={() => deleteRecords([row], false)}>
                                    <IconDeleteFill />
                                </a>
                            </Tooltip>
                        </Auth>
                    </Space>
                );
            },
        },
    ].filter((item) => item);
    const items: QueryFormProps['items'] = [
        {
            name: 'vehicle',
            field: SearchInput,
            fieldProps: {
                searchOptions: ['vehicle'],
            },
        },
        {
            label: i18n.t('name', '归属车组'),
            name: 'company',
            field: FleetTreeSelect,
            fieldProps: {
                // showSubFleetContent: true,
                placeholder: i18n.t('message', '请选择归属车组'),
            },
        },
        {
            label: i18n.t('name', '产生时间'),
            name: 'time',
            // field: RangePicker,
            field: DateRange,
            colSize: 2,
            itemProps: {
                initialValue: initTimeRange,
            },
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    allowClear: true,
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment('00:00:00', 'HH:mm:ss'),
                            moment('23:59:59', 'HH:mm:ss'),
                        ],
                    },
                    separator: '~  ',
                    ranges: getPickerRangeTWM(),
                    disabledDate: disabledAfterDate,
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];
    const queryItems = getCustomItems(getQueryForm, items);
    const handleTableChange: TableProps['onChange'] = (pagination, filters, sorter) => {
        //@ts-ignore
        const { order = 'descend' } = sorter;
        const orderEnum = {
            descend: 'desc',
            ascend: 'asc',
            null: 'desc',
        };
        setOrder(`create_time ${orderEnum[order]}`);
    };
    const fetchData = async (values: any) => {
        const { vehicle, company, time, page, pageSize } = values;
        let startTime, endTime;
        if (time) {
            startTime = timestampToZeroTimeStamp(time[0]);
            endTime = timestampToZeroTimeStamp(time[1]);
        } else {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                time: initTimeRange,
            });
            startTime = timestampToZeroTimeStamp(initTimeRange[0]);
            endTime = timestampToZeroTimeStamp(initTimeRange[1]);
        }
        setSearchStore({
            FailVehicleSearchType: vehicle?.type,
            FailVehicleSearchValue: vehicle?.value,
            FailFleetIds: company?.fleetIds,
            FailIncludeSubFleet: company?.includeSubFleet,
            FailPage: page,
            FailPageSize: pageSize,
            FailStartTime: startTime,
            FailEndTime: endTime,
        });
        if (injectSearchList) {
            setSelectedEvidences([]);
            return injectSearchList({ ...values, complexSort: order });
        }
        let ids;
        if (vehicle && vehicle.value && vehicle.value.replace(/\s/g, '')) {
            ids = await fetchBaseData(vehicle, headers);
            if (!ids || !ids.length) {
                return new Promise((resolve) => {
                    resolve({
                        total: 0,
                        list: [],
                    });
                });
            }
        }
        const params = {
            ...values,
            ...company,
            [`${vehicle ? vehicle.type : 'device'}Ids`]: ids,
            fields: 'vehicle,montage_driver',
            states: '4',
            evidenceTypes: pageType === 'device-upload' ? '6' : '2,5,8',
            startTime,
            endTime,
            time: undefined,
            page: Number(page),
            pageSize: Number(pageSize),
        };
        if (order) {
            params.complexSort = order;
        }
        setSearchStore({
            ...searchStore.get(),
            FailComplexSort: params.complexSort,
        });
        // 重置选择项
        setSelectedEvidences([]);
        const result = await getVideoPageProcessList(params);
        setList(result.list);
        return result;
    };
    const handleRowSelectChange = (_: any, selectedRows: React.SetStateAction<any[]>) => {
        setSelectedEvidences(selectedRows);
    };
    runCustomFun(getInstances, {
        form,
        table: {
            ...tableRef.current,
            reload: tableRef.current?.loadDataSource,
        },
    });
    const batchReLoadButton = () => {
        return (
            <Auth
                key="batchReLoadButton"
                code={
                    pageType === 'device-upload'
                        ? '@base:@page:device.upload@action:batch.reset.task'
                        : '@base:@page:video.library@action:batch.reset.task'
                }
            >
                <Button
                    onClick={() => {
                        reTryEvidenceConfirm();
                    }}
                    disabled={selectedEvidences.length === 0}
                >
                    <SyncOutlined />
                    {i18n.t('name', '批量重试')}
                </Button>
            </Auth>
        );
    };
    const batchDeleteButton = () => {
        return (
            <Auth
                key="batchDeleteButton"
                code={
                    pageType === 'device-upload'
                        ? '@base:@page:device.upload@action:batch.delete.task'
                        : '@base:@page:video.library@action:batch.delete.task'
                }
            >
                <Button
                    onClick={() => {
                        deleteRecords(selectedEvidences, true);
                    }}
                    disabled={selectedEvidences.length === 0}
                >
                    <DeleteOutlined />
                    {i18n.t('name', '批量删除')}
                </Button>
            </Auth>
        );
    };
    useUpdateEffect(() => {
        runCustomFun(onSelectRows, selectedEvidences);
    }, [selectedEvidences]);

    const getRowSelection = () => {
        const rowSelection: TableProps['rowSelection'] = {
            selectedRowKeys: selectedEvidences.map((evidence) => evidence.evidenceId),
            onChange: handleRowSelectChange,
        };
        // 定制 getCheckboxProps
        if(typeof getListItemCheckboxProps === 'function') {
            rowSelection.getCheckboxProps = getListItemCheckboxProps;
        }
        if (tableRowSelection) {
            return tableRowSelection() ? rowSelection : undefined;
        }
        return rowSelection;
    };
    // @ts-ignore
    const newColumns = getCustomItems(getColumns, columns)?.map(item => {
        if (item.width) return item;
        else {
            return {
                ...item,
                width: 180,
            };
        }
    });
    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting(newColumns, {
        storageKey: '@base:@page:video.library.fail',
        disabledKeys: [
            'evidenceName',
            'vehicleNumber',
            'deviceNo',
            'createTime',
            'failCause',
            'operate',
        ],
        ...getColumnSetting?.(),
    });
    return (
        <ListDataContainer
            loadDataSourceOnMount={false}
            queryForm={{
                items: queryItems,
                form,
            }}
            ref={tableRef as any}
            toolbar={{
                extraLeft: (
                    <Space>
                        {getCustomJsx(getTableLeftRender, [
                            batchReLoadButton(),
                            batchDeleteButton(),
                        ])}
                    </Space>
                ),
                // @ts-ignore
                extraIconBtns: getTableIconBtns(getIconBtns, [
                    <TableColumnSetting key="setting" {...tableColumnSettingProps} />,
                ]),
            }}
            // @ts-ignore
            getDataSource={fetchData}
            listRender={(data) => {
                return (
                    <Table
                        className={cn(!showSticky ? 'video-list-fail-table-scroll' : '','video-fail-table')}
                        scroll={{ x: '100%' }}
                        columns={tableColumns}
                        dataSource={data}
                        rowKey="evidenceId"
                        onChange={handleTableChange}
                        rowSelection={getRowSelection()}
                        sticky={{
                            offsetScroll: PAGINATION_HEIGHT,
                            offsetHeader: calcHeight(), // 距离container顶部的高度
                            getContainer: () => {
                                return document.querySelector('#root') as HTMLElement;
                            },
                        }}
                    />
                );
            }}
        />
    );
};
export default withSharePropsHOC<DeleteType, VideoLibraryFailShareProps>(VideoLibraryFail);
