/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import { get } from 'lodash';
import moment from 'moment';
import { useSubmitFn } from '@streamax/hooks';
// @ts-ignore
import {
    Form,
    Radio,
    Checkbox,
    NewDatePicker,
    Space,
    Button,
    Tooltip,
    message,
    Affix,
    Badge,
    // @ts-ignore
    Segmented,
    Input,
    Select,
    Switch,
} from '@streamax/poppy';
import { IconInformation } from '@streamax/poppy-icons';
import AllCheckbox from '@/components/AllCheckbox';
import { getAppGlobalData, i18n, utils,   StarryAbroadFormItem as AFormItem, Auth } from '@base-app/runtime-lib';
import VehicleTreeSelect from '@/components/VehicleTreeSelectOld';
import { videoCut } from '@/service/evidence';
import VideoTimeLength from '../VideoTimeLength';
import useStreamTypeAndTime from '../useStreamTypeAndTime';
import { StreamTypeEnum, Pick<PERSON>rameEnum } from '../../../constant';
import type { Moment } from 'moment';
import './index.less';
import { checkFieldSpace } from '@/utils/commonFun';
import { PROTOCOL_TYPE, DEVICE_PRIMARY_TYPE } from '@/utils/constant';
import useBaseFleetVehicleTree from '@/hooks/useFleetTreeData/useBaseFleetVehicleTree';
import { getVehicleDetail } from '@/service/vehicle';
import { CONTROL_RANGE, isNeedControl } from '@/utils/flow';
import { CreateVideo } from '@/types/pageReuse/videoLibrary';
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import { getCustomJsx } from '@/utils/pageReuse';
import DefaultIconInformation from '@/components/DefaultIncoInformation';
import { check28181 } from '@/utils/28181utils';
import { useAuthChannelData, useChannelData } from '@/hooks/useChannelData';
import { PickFrameModelMap } from '@/utils/constant';
import { OptionItem } from '@/types';
import InfoBack from '@/components/InfoBack';

// 自适应下载码流类型
const ANYONE = 3;
// streamType为ANYONE下有效，0 主码流优先 1 子码流优先
const getStreamPriorityMap = {
    [StreamTypeEnum.MAJOR]: 0,
    [StreamTypeEnum.MINOR]: 1
};

interface Option {
    label: string;
    value: string;
    deviceAlias: string;
    deviceNo: string;
    hasAlias: boolean;
}

type UploadSettingProps = {
    type: string;
    havePickFrame: boolean;
    changeDrawerVisible: (visible: boolean) => void;
};
export type UploadSettingShareProps = CreateVideo;
const defaultVideoTimeLength = 1 * 60; // 默认5分钟
const defaultStreamType = StreamTypeEnum.MAJOR; // 默认高清
const { timestampToZeroTimeStamp } = utils.formator;

const VideoUploadSetting = React.forwardRef(
    (props: UploadSettingProps & UploadSettingShareProps, ref: any) => {
        /****定制*****/
        const {
            getVideoFileTypes,
            getVideoDrawerBlock,
            getVideoDrawerSubmitButton,
            injectFormSubmit,
        } = props;
        /****定制*****/
        const { type, changeDrawerVisible, havePickFrame = true } = props;
        const isDeviceUpload = type === 'device-upload';
        const [form] = Form.useForm();
        const [required, setRequired] = useState(true);
        const [vehicle, setVehicle] = useState<any>({});
        const [selectedDeviceInfo, setSelectedDeviceInfo] = useState<any>();
        const [autoDown, setAutoDown] = useState(true);
        const [editNameFlag, setEditNameFlag] = useState<boolean>(false);
        const [beforeEditName, setBeforeEditName] = useState<string>();
        const [deviceOptions, setDeviceOptions] = useState<Option[]>([]);
        const [hiddenBlackbox, setHiddenBlackbox] = useState(false);
        const validateBaseVehicleNumber = utils.validator.validateBaseVehicleNumber;
        const userConfig = getAppGlobalData('APP_USER_CONFIG');
        const uploadSettingRef = useRef();
        const {
            streamTypeOptions,
            timeNodes,
            streamType,
            setStreamType,
            onVideoLengthChange,
            onStreamTypeChange,
            unReport,
            reset: resetStatus,
            deviceAbilityMajor,
            deviceAbilityMinor,
        } = useStreamTypeAndTime({
            defaultVideoTimeLength,
            deviceInfo: selectedDeviceInfo,
            havePickFrame: !isDeviceUpload && havePickFrame,
            vehicle
        });
        const [uploadChannels, setUploadChannels] = useState<OptionItem[]>([]);
        const { fetchAuthDeviceChannel } = useAuthChannelData();
        const {
            treeData,
            loadFleets,
            loadedKeys, // 已经加载得节点
            fleetList, // 全量车组数据
            vehicleList, // 全量车辆数据
        } = useBaseFleetVehicleTree();
        useEffect(() => {
            form.setFieldsValue({ streamType });
        }, [streamType]);
        // console.log(vehicle, getVideoFileTypes)
        const reset = () => {
            onStreamTypeChange(defaultStreamType);
            form.resetFields();
            setVehicle({});
            setDeviceOptions([]);
            setHiddenBlackbox(false);
            setAutoDown(true);
            resetStatus();
            setSelectedDeviceInfo(undefined);
            setStreamType(StreamTypeEnum.MINOR);
            setUploadChannels([]);
        };

        const normalTip = i18n.t(
            'message',
            '高低速仅针对音视频及图片文件有效。',
        );
        const downloadTooltip = Auth.check(
            '@base:@page:video.library@action:high.speed.download',
        )
            ? normalTip +
              i18n.t(
                  'message',
                  '请注意：在高速模式下，视频上传时会暂停本地录像，直至本次任务上传完毕后才会恢复录像，请谨慎选择',
              )
            : normalTip;
        const [save, saveLoading] = useSubmitFn(async () => {
            let values: any = {};
            try {
                values = await form.validateFields();
            } catch (e: any) {
                form.scrollToField(e.errorFields[0].name[0]);
                throw e;
            }
            const {
                startTime,
                networkType,
                fileType,
                streamType,
                channelNoList,
                velocityMode,
                videoLength,
                videoName,
                deviceId,
                pickFrameModel
            } = values;
            // modify夏令时视频库剪辑不做调整
            const startTimeStamp = timestampToZeroTimeStamp(startTime);
            const endTimeStamp = startTimeStamp + videoLength;
            const authId = deviceId;
            const vehicleNumber = get(vehicle, 'vehicleNumber');
            let pickFrameParam; // 当为设备上传页面和不是抽帧时，不传
            let streamTypeParam = streamType;
            if (!authId) {
                message.warn(i18n.t('message', '所选车辆设备授权ID为空'));
                return;
            }

            if (!isDeviceUpload && streamType === StreamTypeEnum.PICK_FRAME) {
                // 抽帧的高清标清，按pickFrameModel字段选择的来
                const haveMajor = streamTypeOptions.some(
                    ({ value }) => value === StreamTypeEnum.MAJOR,
                );
                const haveMinor = streamTypeOptions.some(
                    ({ value }) => value === StreamTypeEnum.MINOR,
                );
                pickFrameParam = {
                    pickFrame: PickFrameEnum.PickFrame,
                };
                // 有主码流选项，或者柱子码流选项都没有下载主码流，只有子码流选项，下载子码流
                const majorOrMinor = haveMajor || !haveMinor ? StreamTypeEnum.MAJOR : StreamTypeEnum.MINOR;
                // 有选择延时录像剪辑主子码流，则按选择的类型进行剪辑，设置的auto，按原逻辑计算优先码流类型
                streamTypeParam = pickFrameModel !== PickFrameModelMap[0].value && pickFrameModel ? pickFrameModel : majorOrMinor;
            }

            if (injectFormSubmit) {
                await injectFormSubmit({
                    vehicleNumber,
                    streamTypeParam,
                    streamTypeOptions,
                    isDeviceUpload,
                    selectedDeviceInfo,
                    uploadChannels,
                    vehicle,
                    form,
                    unReport,
                    autoDown
                });
            } else {
                // 28181设备剪辑时，只能传fileType为1 的文件
                const resultFileType = check28181(selectedDeviceInfo || {}) ? fileType.join().split(',').filter((i: string) => Number(i) === 1) : fileType.join().split(',');
                const cutFormData = {
                    // evidenceName: `${vehicleNumber}-${moment().format('YYYY-MM-DD')}`,
                    // 证据类型，1-报警证据、2-设备视频剪辑、3-人脸图片、4-刷卡图片、5-服务器视频剪辑、6-服务器回放自动上传、7-报警事件图片
                    evidenceType: isDeviceUpload ? '6' : '2',
                    // 证据来源，业务层自定义，1-自动上传，2-手动上传
                    sourceType: 2,
                    authId,
                    fileParam: {
                        startTime: startTimeStamp,
                        endTime: endTimeStamp,
                        // 传最大值 后端兼容规则
                        networkType: Math.max(...networkType),
                        velocityMode,
                        pickFrameParam, // 抽帧参数
                        paramList: resultFileType.map((type: string) => {
                                return {
                                    fileType: Number(type),
                                    // 延时录像选择auto传anyone，
                                    streamType: (unReport && autoDown) || pickFrameModel === PickFrameModelMap[0].value ? ANYONE : streamTypeParam,
                                    streamPriority:
                                        (unReport && autoDown) || pickFrameModel === PickFrameModelMap[0].value
                                            ? getStreamPriorityMap[streamTypeParam]
                                            : undefined,
                                    channelNoList:
                                        channelNoList?.map((i: string) => Number(i)) || [],
                                };
                            }),
                    },
                    evidenceName: videoName.trim(),
                    // operationModelCode: 'video',
                    // operationTypeCode: 'create',
                    // operationDetailTypeCode: 'create',
                    // logParams: [{ data: `${vehicleNumber}-${moment().format('YYYY-MM-DD')}` }],
                    prefixUrl: window.location.origin
                };
                await videoCut(cutFormData);
            }
            const needControl = await isNeedControl(authId, CONTROL_RANGE.VIDEO_CUT);
            if (needControl) {
                message.warn(i18n.t('message', '流量使用超额，请处理流量限制。'));
            }
            reset();
            changeDrawerVisible(false);
            message.success(i18n.t('message', '创建成功'));
        });


        useImperativeHandle(ref, () => ({
            reset,
            handleDrawerVisibleChange,
        }));
        // 隐藏新增视频时调用
        const handleDrawerVisibleChange = () => {
            if (editNameFlag) {
                setEditNameFlag(false);
            }
            if (beforeEditName) {
                setBeforeEditName(undefined);
            }
        };

        const changeRequired = (checkedValues: string[]) => {
            if (checkedValues.includes(FILETYPE.VIDEO)) {
                setRequired(true);
                return;
            }
            if (checkedValues.includes(FILETYPE.BLANK)) setRequired(false);
        };
        const FILETYPE = {
            VIDEO: '1',
            BLANK: '2,3,4',
            list: [
                {
                    value: '1',
                    label: i18n.t('name', '录像'),
                },
                {
                    value: '2,3,4',
                    label: i18n.t('name', '黑匣子'),
                },
            ],
        };
        const INTERNETTYPE = {
            list: [
                {
                    value: 0,
                    label: i18n.t('name', '有线网络'),
                },
                {
                    value: 1,
                    label: i18n.t('name', 'WIFI'),
                },
                {
                    value: 2,
                    label: i18n.t('name', '3G/4G/5G'),
                },
            ],
        };
        const VELOCITYMODE = {
            LOW: 0,
            HIGH: 1,
            list: [
                {
                    value: 0,
                    label: i18n.t('name', '低速下载'),
                    key: 'LOW',
                },
                {
                    value: 1,
                    label: i18n.t('name', '高速下载'),
                    key: 'HIGH'
                },
            ],
        };
        // 车牌号-年-月-日-视频
        const handleVideoNameBlur = (e: any) => {
            const targetName = e.target.value;
            const videoName = form.getFieldValue('videoName');
            if (!editNameFlag && videoName && targetName.trim() !== beforeEditName) {
                setEditNameFlag(true);
            }
        };
        // 选择车辆自动生成视频名称（未编辑视频名称）
        const autoVideoName = (vehicleNumber: string, time: Moment) => {
            if (!editNameFlag && vehicleNumber && time) {
                const videoName = `${vehicleNumber}-${moment(time).format(
                    userConfig.timeFormat,
                )}-${i18n.t('name', '视频')}`;
                setBeforeEditName(videoName);
                form.setFieldsValue({
                    ...form.getFieldsValue(),
                    videoName,
                });
            }
        };
        const autoSelectDevice = () => {
            const { vehicleId, deviceList } = vehicle || {};
            if (!vehicleId) {
                form.setFieldsValue({
                    ...form.getFieldsValue(),
                    deviceId: null,
                });
                setHiddenBlackbox(false);
                setDeviceOptions([]);
                return;
            }
            const list = deviceList.map((p: any) => ({
                label: p.deviceAlias || p.deviceNo,
                value: p.authId,
                deviceAlias: p.deviceAlias,
                deviceNo: p.deviceNo,
                hasAlias: !!p.deviceAlias,
            }));
            setDeviceOptions(list);
            const primaryDevice =
                deviceList.find((p: any) => p.primaryType === DEVICE_PRIMARY_TYPE.main) ||
                deviceList[0];
            // 默认选中主设备
            if (primaryDevice) {
                form.setFieldsValue({
                    ...form.getFieldsValue(),
                    deviceId: primaryDevice.authId,
                });
                hiddenBlackboxWith905(primaryDevice.authId);
            } else {
                form.setFieldsValue({
                    ...form.getFieldsValue(),
                    deviceId: null,
                });
                setUploadChannels([]);
            }

            if (primaryDevice && check28181(primaryDevice)) {
                setHiddenBlackbox(true);
            }
        };
        useEffect(() => {
            autoVideoName(vehicle?.vehicleNumber, form.getFieldValue('startTime'));
            autoSelectDevice();
        }, [vehicle.vehicleNumber]);

        const fileTypeList = useMemo(() => {
            if (getVideoFileTypes) {
                return getVideoFileTypes(FILETYPE.list, vehicle);
            }
            if (!hiddenBlackbox) return FILETYPE.list;
            return FILETYPE.list.filter((p) => p.value !== FILETYPE.BLANK);
        }, [hiddenBlackbox, vehicle]);

        const handleTimeChange = (time: Moment) => {
            autoVideoName(vehicle?.vehicleNumber, time);
        };
        const handleSelectedDeviceChange = (value: any) => {
            hiddenBlackboxWith905(value);
        };
        const hiddenBlackboxWith905 = (authId: string) => {
            // 判断是否是905协议，如果是905协议则隐藏黑匣子选项
            const deviceList = vehicle.deviceList || [];
            const deviceItem = deviceList.find((item: { authId: string }) => item.authId === authId);
            setSelectedDeviceInfo(deviceItem);
            const deviceChannelList = deviceItem?.deviceChannelList || [];
            if (deviceChannelList?.length) {
                  setUploadChannels(
                      deviceChannelList?.map((item) => ({
                          label: item.channelAlias,
                          value: item.channelNo,
                      })) || [],
                  );
            } else {
                fetchAuthDeviceChannel({ deviceIds: deviceItem?.deviceId }).then(res => {
                    console.log("res", res);
                    setUploadChannels(res || []);
                });
            }
            const device = deviceList.find((p: any) => p.authId === authId);
            if (device && device.protocolType === PROTOCOL_TYPE.JT905) {
                setHiddenBlackbox(true);
                const values = form.getFieldsValue();
                // 如果选中了黑匣子，需要清空
                if (values.fileType && values.fileType.includes(FILETYPE.VIDEO)) {
                    form.setFieldsValue({
                        ...values,
                        fileType: [FILETYPE.VIDEO],
                    });
                } else {
                    form.setFieldsValue({
                        ...values,
                        fileType: [],
                    });
                }
                setRequired(true);
            } else {
                setHiddenBlackbox(false);
            }
        };
        // 超出长度显示...并title提示
        const addTitle = (text: string) => <span title={text}>{text}</span>;
        const checkSpace = (rule: any, value: string) => {
            return checkFieldSpace(value, i18n.t('message', '视频名称不能为空'));
        };
        const vehicelDetailMapRef = useRef<Record<string, any>>({});
        const onVehicleChange = async (vehicleId: string) => {
            if (!vehicleId) {
                setVehicle({});
                return;
            }
            let data = vehicelDetailMapRef.current[vehicleId];
            if (data) {
                setVehicle(data);
                return;
            }
            data = await getVehicleDetail({ fields: 'device,channel', vehicleId: vehicleId });
            vehicelDetailMapRef.current[vehicleId] = data;
            //切换车辆重置码流类型和时间
            setStreamType(StreamTypeEnum.MINOR);
            form.setFieldsValue({
                videoLength: defaultVideoTimeLength
            });
            setVehicle(data);
        };
        /*****提取***/
        const vehicleForm = (
            <AFormItem
                key="vehicleForm"
                label={i18n.t('name', '选择车辆')}
                className="form-item"
                name="vehicle"
                valuePropName="vehicleId"
                rules={[{ required: true }]}
            >
                <VehicleTreeSelect
                    vehicleId={''}
                    onChange={(vehicleId) => onVehicleChange(vehicleId)}
                    onlySelectOnline={false}
                    showState={false}
                    treeData={treeData}
                    loadFleets={loadFleets}
                    loadedKeys={loadedKeys}
                    fleetList={fleetList}
                    vehicleList={vehicleList}
                    onAutoRemoveSelectedVehicle={() =>
                        message.warn(i18n.t('message', '车辆被停用，请重新选择车辆'))
                    }
                />
            </AFormItem>
        );
        const deviceForm = (
            <AFormItem
                key="deviceForm"
                label={i18n.t('name', '设备')}
                className="form-item"
                name="deviceId"
                rules={[{ required: true }]}
                hidden={deviceOptions?.length <= 1}
            >
                <Select
                    placeholder={i18n.t('message', '请选择设备')}
                    onChange={handleSelectedDeviceChange}
                    getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                >
                    {deviceOptions.map((item) => (
                        <Select.Option key={item.deviceNo} value={item.value}>
                            <Tooltip
                                title={
                                    item.deviceAlias
                                        ? `${item.deviceAlias}(${item.deviceNo})`
                                        : item.deviceNo
                                }
                                className="device-alias-option-tooltip-item"
                            >
                                {item.deviceAlias || item.deviceNo}
                            </Tooltip>
                        </Select.Option>
                    ))}
                </Select>
            </AFormItem>
        );
        const videoTimeForm = (
            <AFormItem
                key="videoTimeForm"
                label={i18n.t('name', '视频时间')}
                className="form-item"
                name="startTime"
                rules={[{ required: true }]}
                initialValue={moment().startOf('day')}
            >
                {/**@ts-ignore */}
                {<NewDatePicker showTime onChange={handleTimeChange} getPopupContainer={(triggerNode: HTMLElement) => triggerNode} />}
            </AFormItem>
        );

        const videoLengthForm = (
            <AFormItem
                key="videoLengthForm"
                label={i18n.t('name', '视频时长')}
                className="form-item"
                name="videoLength"
                rules={[{ required: true }]}
                initialValue={defaultVideoTimeLength}
            >
                <VideoTimeLength
                    timeNodes={timeNodes}
                    streamType={streamType}
                    onChange={onVideoLengthChange}
                />
            </AFormItem>
        );
        const streamTypeForm = (
            <AFormItem
                key="streamTypeForm"
                label={
                    <div className="form-item-stream-type-label">
                        <div className="video-type-wrap">
                            <Space>
                                <span>{i18n.t('name', '视频类型')}</span>
                                {vehicle?.vehicleId && unReport && (
                                    <Tooltip
                                        title={i18n.t(
                                            'message',
                                            '所选类型没有视频，自动下载其他类型的视频',
                                        )}
                                    >
                                        <a>
                                            <DefaultIconInformation />
                                        </a>
                                    </Tooltip>
                                )}
                            </Space>
                            {vehicle?.vehicleId && unReport && (
                                <div>
                                    <Space>
                                        <span>{i18n.t('message', '自适应下载')}</span>
                                        <Switch
                                            checked={autoDown}
                                            onChange={(checked: boolean) => setAutoDown(checked)}
                                            size="small"
                                        />
                                    </Space>
                                </div>
                            )}
                        </div>
                    </div>
                }
                name="streamType"
                className="form-item"
                rules={[{ required }]}
                initialValue={StreamTypeEnum.MAJOR}
            >
                <Segmented
                    block
                    // @ts-ignore
                    onChange={(value: number) => onStreamTypeChange(Number(value))}
                    options={streamTypeOptions
                        .map((item) => {
                            return {
                                ...item,
                                icon: <Badge color={item.color} />,
                                label: <Tooltip title={item.label}>{item.label}</Tooltip>,
                            };
                        })
                        .filter((i) => !!i)}
                />
            </AFormItem>
        );

            // 设备同时有高清、标清能力，并且选中了延时录像，才展示延时录像模式字段
        const pickFrameModelForm = (
            streamType === StreamTypeEnum.PICK_FRAME && deviceAbilityMajor && deviceAbilityMinor &&
            <AFormItem
                key="pickFrameModelForm"
                label={i18n.t('name', '延时录像模式')}
                name="pickFrameModel"
                className="form-item"
                rules={[
                    {
                        required: true,
                    }
                ]}
                dependencies={[]}
                initialValue={PickFrameModelMap[0].value}
            >
                <Radio.Group>
                    {PickFrameModelMap.map((item) => (
                        <Radio value={item.value} key={item.value}>
                            {addTitle(item.label)}
                        </Radio>
                    ))}
                </Radio.Group>
            </AFormItem>
        );
        const videoNameForm = (
            <AFormItem
                key="videoNameForm"
                label={i18n.t('name', '视频名称')}
                name="videoName"
                className="form-item"
                rules={[
                    {
                        required: true,
                        validator: checkSpace,
                    },
                    { validator: validateBaseVehicleNumber },
                ]}
            >
                <Input
                    placeholder={i18n.t('message', '请输入视频名称')}
                    maxLength={50}
                    allowClear
                    onBlur={handleVideoNameBlur}
                />
            </AFormItem>
        );
        const videoFilesTypeForm = (
            <Form.Item
                key="videoFilesTypeForm"
                label={i18n.t('name', '文件类型')}
                rules={[{ required: true }]}
                name="fileType"
                initialValue={[FILETYPE.VIDEO]}
            >
                <Checkbox.Group onChange={changeRequired}>
                    {fileTypeList.map((item) => (
                        <Checkbox value={item.value} key={item.value}>
                            {addTitle(item.label)}
                        </Checkbox>
                    ))}
                </Checkbox.Group>
            </Form.Item>
        );
        const videoChannelForm = (
            <Form.Item
                key="videoChannelForm"
                label={i18n.t('name', '上传通道')}
                name="channelNoList"
                rules={[{ required }]}
            >
                <AllCheckbox
                    options={uploadChannels}
                    disabled={uploadChannels?.length === 0}
                    label={
                        <span>
                            <span>{i18n.t('state', '全选')}</span>
                            <span
                                className="form-label-tip"
                                title={i18n.t('message', '（注：非权限通道不会生效）')}
                            >
                                {i18n.t('message', '（注：非权限通道不会生效）')}
                            </span>
                        </span>
                    }
                />
            </Form.Item>
        );
        const networkTypeForm = (
            <Form.Item
                key="networkTypeForm"
                label={i18n.t('name', '网络模式')}
                name="networkType"
                rules={[{ required: true }]}
                initialValue={INTERNETTYPE.list.map((item) => item.value)}
            >
                <Checkbox.Group>
                    {INTERNETTYPE.list.map((item) => (
                        <Checkbox value={item.value} key={item.value}>
                            {addTitle(item.label)}
                        </Checkbox>
                    ))}
                </Checkbox.Group>
            </Form.Item>
        );
        const velocityModeForm = (
            <Form.Item
                key="velocityModeForm"
                label={<div>
                    <span className='download-mode'>{ i18n.t('name', '下载模式') }</span>
                    <div key="highSpeedDownload" style={{ marginTop: '12px' }}>
                        <InfoBack
                            className="download-set-icon-container"
                            title={downloadTooltip}
                        />
                    </div>
                </div>
                }
                name="velocityMode"
                rules={[{ required: true }]}
                initialValue={VELOCITYMODE.LOW}
                className='download-mode-radio'
            >
                    <Radio.Group>
                        {VELOCITYMODE.list.map((item) => {
                            if (item.key !== 'HIGH') {
                                return (
                                    <Radio value={item.value} key={item.value}>
                                        {addTitle(item.label)}
                                    </Radio>
                                );
                            }
                            return (
                                <Auth
                                    key="highSpeedDownload"
                                    code="@base:@page:video.library@action:high.speed.download"
                                >
                                    <Radio value={item.value} key={item.value}>
                                        {addTitle(item.label)}
                                    </Radio>
                                </Auth>
                            );
                        })}
                    </Radio.Group>
            </Form.Item>
        );
        const submitButton = (
            <Button
                key="submitButton"
                onClick={() => {
                    reset();
                    changeDrawerVisible(false);
                }}
            >
                {i18n.t('action', '取消')}
            </Button>
        );
        const cancelButton = (
            <Button key="cancelButton" type="primary" onClick={save} loading={saveLoading}>
                {i18n.t('action', '保存')}
            </Button>
        );
        
        return (
            <>
                <div className="create-upload-setting" ref={uploadSettingRef}>
                    <Form form={form} layout="vertical">
                        {getCustomJsx(getVideoDrawerBlock, [
                            vehicleForm,
                            deviceForm,
                            videoTimeForm,
                            videoLengthForm,
                            streamTypeForm,
                            pickFrameModelForm,
                            videoNameForm,
                            videoFilesTypeForm,
                            videoChannelForm,
                            networkTypeForm,
                            velocityModeForm,
                        ], {
                            streamTypeForm: {
                                vehicleId: vehicle?.vehicleId,
                                unReport,
                                autoDown,
                                setAutoDown,
                                required,
                                streamTypeOptions,
                                StreamTypeEnum,
                                onStreamTypeChange,
                                timeNodes,
                                streamType,
                                onVideoLengthChange
                            }
                        })}
                    </Form>
                </div>
                <Affix offsetBottom={0} style={{ position: 'absolute', width: '100%'}} target={() => uploadSettingRef.current?.parentNode}>
                    <div className="create-upload-bottom">
                        <Space>
                            {getCustomJsx(getVideoDrawerSubmitButton, [submitButton, cancelButton])}
                        </Space>
                    </div>
                </Affix>
            </>
        );
    },
);
export default withSharePropsHOC<UploadSettingProps, UploadSettingShareProps>(VideoUploadSetting);
