import { useAsyncEffect, useUpdate } from '@streamax/hooks';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import {Space, Form, Input, Button, message, Steps, Radio, Row, Col, DatePicker, NewDatePicker} from '@streamax/poppy';
import type { Rule } from '@streamax/poppy/lib/form';
import type { SelectValue } from '@streamax/poppy/lib/select';
import {
    i18n,
    utils,
    RouterPrompt,
    getAppGlobalData,
    StarryAbroadFormItem,
    StarryAbroadLRLayout,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import { usePasswordConfig } from '@base-app/runtime-lib';
import {
    StarryCard,
    StarryBreadcrumb,
    Action,
    AreaSelection,
    // @ts-ignore
} from '@base-app/runtime-lib';
// @ts-ignore
import type { FleetItem } from '@base-app/runtime-lib/@types/components/FleetTreeSelect';
import { useHistory, useLocation } from '@base-app/runtime-lib/core';
import { InfoPanel, PasswordInput } from '@streamax/starry-components';
import { cloneDeep, uniq } from 'lodash';
import React, { useState, useEffect, useRef } from 'react';
import { steps } from './constants';
import type { AuthFleetItem, StepSavaInfoItem } from './type';
import UserDataAuthorize from './user-data-authorize';
import type { UserRoleRefProps } from './user-role-authorize';
import UserRoleAuthorize from './user-role-authorize';
import { isDataAuth } from './utils';
import { fetchParameterDetail } from '../../../service/parameter';
import { getUserDetail, createUser, updateUser, userAuthorityRole } from '../../../service/user';
import FleetTreeSelect from '@/components/AuthFleetSelectShow';
import AuthSelect from '@/components/AuthSelectShow';
import AuthTip from '@/components/AuthTip';
import { useAppList } from '@/hooks';
import type { AppList } from '@/hooks/useAppList';
import CompanyRequest from '@/service/company';
import { fetchFleetPage } from '@/service/fleet';
import { getUserInfoConfig } from '@/service/tenant';
import type { SourceFleetType } from '@/types/company';
// import FleetTreeSelect from './FleetTreeSelect';
import type { Instances } from '@/types/pageReuse/pageReuseBase';
import { NO_AUTH_NUMBER, getOnlyOneSpaceBetweenWords, validateMinSixLen } from '@/utils/commonFun';
import {expireTypeMap, SAAS_APPID} from '@/utils/constant';
import { runCustomFun } from '@/utils/pageReuse';
import api from '@/service/evidence';
import './index.less';
import {RspFormLayout, RspHorizontal, useResponsiveShow} from '@streamax/responsive-layout';
import {EXPIRE_TYPE} from "@/runtime-pages/user-manage/components/ActivateUserModal";
import {DatePickerProps} from "antd/lib/date-picker";
import moment from "moment";

const { encryptPassword } = utils.general;
const { generalValidateUserEmail } = utils.pattern;

const FormItem = Form.Item;
const { TextArea } = Input;
const { Step } = Steps;
const FIRST_FLEET_NAME = 0;
const DEFAULT_INCLUDESUBFLEET = 0;
interface InitAppList extends AppList {
    disabled?: boolean;
    name?: number | string | null;
}
type fleetItem = Record<number | string, any>;
interface UserFleetConfig {
    belongToIsOpen: number; // 用户归属车组 开关（这个开的前提下才会有下面的俩）
    autoAuthIsOpen: number; // 自动授权
    mustRelateIsOpen: number; // 是否必须关联车组
}

const TENANT_ROLE = 1;

export type UserAddEditShareProps = Instances;

const AddEditUser = (props: UserAddEditShareProps) => {
    const userRoleAuthorizeRef = useRef<UserRoleRefProps>(null);

    const { inSaaS, appList: allAppList } = useAppList({
        userId: getAppGlobalData('APP_USER_INFO').userId,
    });

    const [appList, setAppList] = useState<any>([]); // 用户关联了角色的应用
    const [loading, setLoading] = useState(false);
    const [initAppList, setInitAppList] = useState<InitAppList[]>([]); // copy一份applist，具有setDisabled能力
    const [appFleetData, setAppFleetData] = useState<Record<string, FleetItem[]>>(); // 用于请求车组树的appid
    const { pwdRules } = usePasswordConfig();
    const [singleColumn, setSingleColumn] = useState<boolean>(false);
    const [form] = Form.useForm();
    const [when, setWhen] = useState(true);
    // 存储步骤保存需要的用户id和是否数据授权
    const [stepSaveInfo, setStepSaveInfo] = useState<StepSavaInfoItem>();
    const [isRequire, setIsRequire] = useState(false); // 归属车组非必填时 除了第一行外的校验
    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');
    const [tenateConfig, setTenateConfig] = useState<any>();
    const [userAutoExpireEnable, setUserAutoExpireEnable] = useState<boolean>();
    const [uname, setUname] = useState<string>();
    const showAuthTip = useRef(false);
    const [current, setCurrent] = useState<number>(0);
    const [isTenantManageRole, setIsTenantManageRole] = useState<boolean>(false);
    const [userFleetConfig, setUserFleetConfig] = useState<UserFleetConfig>({} as UserFleetConfig); // 用户归属配置状态 true: 打开， false： 关闭
    const firstRef = useRef(true);
    const appFleetDataRef = useRef<Record<string, FleetItem[]>>({}); //处理多次设置时，导致只有最后一次生效
    const fleetTreeEleRef = useRef<fleetItem>({});
    const isTenantRole = useRef<boolean>(false); // 登陆的是否为租户管理员
    const [selectedDate, setSelectedDate] = useState<any>();

    const {
        // @ts-ignore
        query: { userId },
    } = useLocation();
    const history = useHistory();
    const isEdit = !!userId;
    //海外风格
    const { isAbroadStyle } = useSystemComponentStyle();
    const update = useUpdate();
    const forceUpdate = () => {
        update();
    };

    const { getInstances } = props;

    runCustomFun(getInstances, {
        setSelectRoles: (role: any[]) => {
            userRoleAuthorizeRef.current?.setSelectRoles(role);
            form.setFieldsValue({ authorizeRole: role });
        },
    });

    useEffect(() => {
        getRoleAppList();
    }, [allAppList]);
    const getShowAuthTip = () => {
        if (fleetTreeEleRef.current) {
            showAuthTip.current = false;
            for (const key in fleetTreeEleRef.current) {
                const item = fleetTreeEleRef.current[key];
                if (item?.getAuth) {
                    showAuthTip.current = true;
                }
            }
        }
        return showAuthTip.current;
    };
    const getConfig = async () => {
        const params = {
            tenantId: APP_USER_INFO.tenantId,
            keys: 'tenant.user.info.config,tenant.user.fleet.config,tenant.user.state.config',
        };
        try {
            const userConfigInfo = await getUserInfoConfig(params);
            const info = userConfigInfo['tenant.user.info.config']
                ? JSON.parse(userConfigInfo['tenant.user.info.config'])
                : { emailIsOpen: 1, contactInformationIsOpen: 0 };
            const userFleetConfig = userConfigInfo['tenant.user.fleet.config']
                ? JSON.parse(userConfigInfo['tenant.user.fleet.config'])
                : false;
            const userStateConfig = userConfigInfo['tenant.user.state.config']
                ? JSON.parse(userConfigInfo['tenant.user.state.config'])
                : {};
            setUserFleetConfig(userFleetConfig);
            setTenateConfig(info);
            setUserAutoExpireEnable(Boolean(userStateConfig.userAutoExpireSwitch));

            // eslint-disable-next-line no-empty
        } catch (error) {}
    };
    const getRoleAppList = async () => {
        const { list } = await userAuthorityRole({
            page: 1,
            pageSize: 1e8,
            userId,
        });
        const isTenantUser = await api.queryNotOnlyFunctionAdmin({
            userId: getAppGlobalData('APP_USER_INFO').userId,
            appId: getAppGlobalData('APP_ID'),
        }); // 当前登陆的是否为租户/应用管理员
        isTenantRole.current = isTenantUser;
        const authRoleApp = uniq(
            (list || []).map((item: { appId: number; roleTypeId: number }) => {
                return item.appId;
            }),
        );
        const newAppList = allAppList.filter((item: { value: number }) =>
            authRoleApp.includes(item.value),
        );
        setAppList(isTenantUser ? allAppList : newAppList);
    };

    useAsyncEffect(async () => {
        let appListParams: InitAppList[] = [];

        if (appList && appList.length) {
            appListParams = appList.map((i) => ({ ...i, disabled: false, name: null }));
            setInitAppList(appListParams);
        }
        firstRef.current &&
            form.setFieldsValue({
                fleets: [undefined],
            });

        // 编辑用户的时候要请求用户信息回填表单明文展示, desensitize, 1脱敏，0明文
        if (userId && ((appList && (appList.length || appList.length == 0)) || !inSaaS)) {
             // 新增的时候请求默认区号并设置
            const areaCodeData = await fetchParameterDetail({ parameterKey: 'AREACODE' });
            getUserDetail({ userId, desensitize: 0 }).then(async (data: any) => {
                const { areaCode, phoneNumber, password, fleets = [], expireInfo, roleType } = data;
                setIsTenantManageRole(Boolean(roleType == TENANT_ROLE));
                const _password = password || '';
                /**********车组权限获取开始*********/
                // 车组权限展示特殊 先通过userId查询应用，再通过用户详情的appId和应用id过滤，剩余的fleetId再查询fleet/page过滤权限，得到有权限的fleetList
                let allFleet: any[] = [];
                let authFleets: any[] = [];
                // [65926][5288]行业层引入，不会请求appList，过滤时，从globalData中取appId过滤
                if (!inSaaS) {
                    authFleets = (fleets || []).filter((item: any) => {
                        return item.appId === getAppGlobalData('APP_ID');
                    });
                } else {
                    authFleets = isTenantRole.current
                        ? fleets
                        : (fleets || []).filter((item: any) => {
                              const application = appList.find(
                                  (applicationItem: any) => applicationItem.value === item.appId,
                              );
                              return application ? true : false;
                          });
                }

                for (let i = 0; i < authFleets.length; i++) {
                    const item = authFleets[i];
                    const { list: fleet } = await fetchFleetPage({
                        appId: item.appId,
                        fleetIds: item.fleetId,
                        page: 1,
                        pageSize: 1e8,
                        domainType: 1,
                    });
                    if (fleet && fleet.length) {
                        allFleet = [...allFleet, ...fleet];
                    }
                }
                fleets.forEach((item: any) => {
                    const hasFleet = allFleet.find(
                        (authFleet) => authFleet.fleetId === item.fleetId,
                    );
                    // 在fleet/page中没有找到该fleetId，说明无权限
                    if (!hasFleet) {
                        item.fleetId = NO_AUTH_NUMBER; //-2统一表示无权限
                    }
                });
                const expireType = expireInfo?.expireTime === -1 ? EXPIRE_TYPE.FOREVER : EXPIRE_TYPE.CUSTOM;
                const expireTime = expireType === EXPIRE_TYPE.FOREVER ? undefined : expireInfo?.expireTime;
                setSelectedDate(expireTime);
                /*************权限获取完*******/
                form.setFieldsValue({
                    ...data,
                    password: _password,
                    confirmPassword: _password,
                    codeTel: {
                        areaCode: areaCode || Number(areaCodeData.parameterValue),
                        phone: phoneNumber,
                    },
                    fleets:
                        fleets && fleets.length
                            ? fleets.map((i: { appId: any; fleetId: any }) => ({
                                  appId: i?.appId,
                                  fleetId: {
                                      fleetIds: i?.fleetId,
                                      includeSubFleet: DEFAULT_INCLUDESUBFLEET,
                                  },
                              }))
                            : [undefined],
                    expireType,
                    expireTime : expireTime ? moment(expireTime * 1000) : undefined
                });
                // 回填后需要请求对应的车组，并且增加禁用项
                if (fleets && fleets.length) {
                    if ((appList && appList.length) || !inSaaS) {
                        fleets.forEach((item: { appId: SelectValue }, index: number) => {
                            onAppChange(item?.appId, index, 'back', appListParams);
                        });
                    }
                }
            });
        } else {
            if (!inSaaS) {
                onAppChange(getAppGlobalData('APP_ID'), 0, 'back', appListParams);
            }
            // 新增的时候请求默认区号并设置
            firstRef.current &&
                fetchParameterDetail({ parameterKey: 'AREACODE' }).then((data: any) => {
                    const { parameterValue } = data;
                    form.setFieldsValue({
                        codeTel: {
                            areaCode: Number(parameterValue),
                        },
                        fleets: [undefined],
                    });
                });
        }
        firstRef.current && getConfig();
        firstRef.current = false;
    }, [appList]);

    const validatorConfirmPwd = (rule: any, value: any) => {
        if (!value && form.getFieldValue('password')) {
            return Promise.reject(i18n.t('message', '请确认密码'));
        }
        if (!value || form.getFieldValue('password') === value) {
            return Promise.resolve();
        }
        return Promise.reject(i18n.t('message', '两次输入的密码不一致'));
    };
    /**处理空格校验 bug 64542，若升级了ant5.9.0可以直接用validateDebounce属性**/
    const passwordOnInput = (e) => {
        form.setFieldsValue({
            password: e.target.value,
        });
        form.validateFields(['password']);
        form.validateFields(['confirmPassword']);
    };
    const confirmPasswordOnInput = (e) => {
        form.setFieldsValue({
            confirmPassword: e.target.value,
        });
        form.validateFields(['confirmPassword']);
    };
    /**处理空格校验**/
    const validatorCodeTel = (rule: any, value: any = {}) => {
        const { areaCode, phone } = value;
        if (tenateConfig?.contactInformationIsOpen) {
            if (!phone) {
                return Promise.reject(i18n.t('message', '联系方式必填'));
            }
        }
        if (!areaCode) {
            return Promise.reject(i18n.t('message', '请选择国家或地区'));
        }
        if (phone) {
            if (!/^[0-9-]+$/.test(phone)) {
                return Promise.reject(i18n.t('message', '请输入正确的联系方式'));
            }
            if (/^[-]+$/.test(phone)) {
                return Promise.reject(i18n.t('message', '请输入正确的联系方式'));
            }
            if (phone && (phone.length < 6 || phone.length > 15)) {
                return Promise.reject(i18n.t('message', '请输入6~15个字符'));
            }
        }
        return Promise.resolve();
    };

    const confirmPwdRules: Rule[] = [{ validator: validatorConfirmPwd }];

    if (!userId) {
        pwdRules.push({
            required: true,
        });
        confirmPwdRules.push({
            required: true,
        });
    }

    const getCompleteFleetInfo = (fleet: { appId: string; fleetId: string }[]) => {
        const result: AuthFleetItem[] = [];
        fleet.forEach((item, index) => {
            const currentFleet = appFleetDataRef.current?.[index]?.find(
                (fleetItem) => fleetItem.key === item.fleetId,
            );
            result.push({
                ...item,
                fleetName: currentFleet?.title || '',
                isIncludeSub: 1,
            });
        });
        return result;
    };

    const onFinish = (oldValues: any) => {
        // 归属车组开起 不是必须关联车组 才有以下逻辑
        userFleetConfig?.belongToIsOpen &&
            !userFleetConfig?.mustRelateIsOpen &&
            setIsRequire(!!oldValues.fleets.length);
        // 非必填需要二次校验
        form.validateFields().then((values: any) => {
            setLoading(true);
            const { codeTel, account, userDesc, userName, fleets, email, expireTime, expireType } = values;
            const tempFleets = (fleets || [])
                .map((i: { appId: number; fleetId: { fleetIds: any } }) => {
                    if (inSaaS) {
                        if (i && (i.appId || i.appId === SAAS_APPID) && i.fleetId) {
                            // 必选要两个都填才传给后端
                            return {
                                appId: i.appId,
                                fleetId: i.fleetId?.fleetIds,
                            };
                        }
                    } else {
                        if (i && i.fleetId) {
                            // 非bp应用也需要传appid
                            return {
                                appId: getAppGlobalData('APP_ID') || '',
                                fleetId: i.fleetId?.fleetIds,
                            };
                        }
                    }
                    return undefined;
                })
                .filter((i: any) => i);
            const params = {
                ...values,
                password: values.password ? encryptPassword(values.password) : '', // 空表示不修改密码，传空串
                account: (account || '').trim(),
                userDesc: (userDesc || '').trim(),
                userName: (userName || '').trim(),
                phoneNumber: codeTel.phone,
                areaCode: codeTel.areaCode,
                fleets: tempFleets,
                email: email || '',
                ...(userAutoExpireEnable ? { expireTime : expireType === EXPIRE_TYPE.FOREVER ? -1 : selectedDate} : {})

            };
            delete params.confirmPassword;
            delete params.codeTel;
            delete params.expireType;
            if (userId) {
                params.userId = userId;
            }
            (userId ? updateUser(params) : createUser(params))
                .then((result) => {
                    setLoading(false);
                    setWhen(false);
                    if (result) {
                        message.success(i18n.t('message', '操作成功'));
                        // 新增跳转下一步
                        if (!isEdit) {
                            // @ts-ignore
                            setStepSaveInfo((data) => ({
                                ...data,
                                userId: result,
                                authFleet: getCompleteFleetInfo(tempFleets),
                            }));
                            nextStep();
                            return;
                        }
                        // 进入用户详情页面
                        Action.openActionUrl({
                            code: '@base:@page:user.manage:add@action:detail',
                            history,
                            url: '/user-manage/user-detail',
                            params: {
                                userId: userId || result,
                            },
                        });
                    } else {
                        message.error(i18n.t('message', '操作失败'));
                    }
                })
                .catch(() => {
                    setLoading(false);
                })
                .finally(() => setLoading(false));
        });
    };
    const fetchGroupDataSource = async (name: number, tempAppId: number) => {
        const list = await CompanyRequest.getList({ appId: tempAppId });
        const flatData: FleetItem[] = list.map((x: SourceFleetType) => {
            return {
                key: x['key'],
                title: x['title'],
                value: x['value'],
                parentId: x['parentId'],
            };
        });
        // 处理多次setAppFleetData时只有最后一次生效，导致传入fleetTree数据有问题
        appFleetDataRef.current = {
            ...appFleetDataRef.current,
            [name]: flatData,
        };
        setAppFleetData({
            ...appFleetDataRef.current,
        });
    };
    const handleParams = async (
        appId: number,
        name: number,
        type: 'back' | 'onChange' | 'remove' = 'onChange',
    ) => {
        if (!appId && type !== 'remove') {
            setAppFleetData({
                [name]: [],
            });
            return;
        }
        if (type === 'remove') {
            // 删除第二行后，当第三行有数据时 需要再次触发请求车组树数据
            const fleets = form.getFieldValue('fleets');
            const tempAppIds = [];
            for (let i = name; i < fleets.length; i++) {
                if (fleets[i] && fleets[i].appId) {
                    tempAppIds.push({
                        name: i,
                        appId: fleets[i].appId,
                    });
                }
            }
            if (tempAppIds.length) {
                tempAppIds.forEach((item) => {
                    fetchGroupDataSource(item.name, item.appId);
                });
            } else {
                // 没有appId，代表后面几行无数据，无需触发请求
                return;
            }
        } else {
            fetchGroupDataSource(name, appId);
        }
    };
    const onAppClear = (name: number) => {
        // 需要清空归属应用时 需要清空之前选的归属车组
        const fleetsData = form.getFieldValue('fleets');
        const tempData = cloneDeep(fleetsData);
        for (let i = 0; i < tempData.length; i++) {
            if (i === name) {
                tempData[i] = undefined;
                break;
            }
        }
        form.setFieldsValue({
            fleets: tempData,
        });
        appFleetDataRef.current = {
            ...appFleetDataRef.current,
            [name]: [],
        };
        setAppFleetData({
            ...appFleetDataRef.current,
        });
    };
    const onAppChange = (
        value: SelectValue,
        name: number,
        type: 'back' | 'onChange' | 'remove' = 'onChange',
        appListParams?: InitAppList[],
    ) => {
        const fleets = form.getFieldsValue()['fleets'] || [];
        if (fleets?.[name]?.fleetId && type === 'onChange') {
            const tempFleets = cloneDeep(fleets);
            // 上一次操作 选了车组，后直接切换应用，需要清除之前选的车组
            tempFleets[name].fleetId = undefined;
            form.setFieldsValue({
                fleets: tempFleets,
            });
        }
        const tempAppData = cloneDeep(appListParams ?? initAppList);
        tempAppData.forEach((tmp) => {
            const target = fleets.find(
                (fleet: { appId: number }) => fleet && fleet.appId && fleet.appId === tmp.value,
            );
            const targetIndex = fleets.map((i: { appId: any }) => i && i.appId).indexOf(tmp.value);
            if (target) {
                tmp['disabled'] = true;
                tmp['name'] = targetIndex;
            } else {
                tmp['disabled'] = false;
                tmp['name'] = null;
            }
        });
        if (type === 'back') {
            setInitAppList((preData) => {
                const returnData: InitAppList[] = [];
                preData.forEach((pre) => {
                    const target = tempAppData.find((tmp) => tmp.value === pre.value);
                    if (target?.disabled) {
                        returnData.push(target);
                    } else {
                        returnData.push(pre);
                    }
                });
                return returnData;
            });
        } else {
            setInitAppList(tempAppData);
        }
        // 切换应用 请求车组
        handleParams(value as number, name, type);
    };
    const validateSpace = () => {
        if (uname !== uname?.trim()) {
            return Promise.reject(i18n.t('message', '只能输入英文字母、数字、下划线、邮箱字符'));
        }
        return Promise.resolve();
    };

    // 用户名的校验，编辑时，不校验用户名长度，只校验必填；
    // 新建时，校验长度和必填，命名规则
    const nameRules: Rule[] = userId
        ? [{ required: true }]
        : [
              { required: true },
              { validator: utils.validator.validateBaseUser },
              { min: 6, type: 'string' },
              { max: 50, type: 'string' },
              { validator: validateMinSixLen }
          ];

    const prevStep = () => {
        setCurrent(current - 1);
    };

    const nextStep = () => {
        setCurrent(current + 1);
    };


    const handleDateChange = (date: any) => {
        if (date) {
            // 设置为该天的最后一秒
            const lastSecondOfDay = moment(date).endOf('day').unix();
            setSelectedDate(lastSecondOfDay);
        }
    };

    const fleetsFormItem = () => {
        return userFleetConfig?.belongToIsOpen ? (
            <div className="user-fleet-item">
                <div className="poppy-form-item-label">
                    <label
                        className={`${
                            userFleetConfig?.mustRelateIsOpen ? 'poppy-form-item-required' : ''
                        }`}
                    >
                        {i18n.t('name', '归属车组')}
                        <AuthTip show={getShowAuthTip()} />
                    </label>
                </div>
                <Form.List name="fleets">
                    {(fields, { add, remove }) => (
                        <Space direction="vertical" style={{ width: singleColumn ? '100%' : 390 }}>
                            {fields.map(({ key, name, ...restField }, index: number) => {
                                const fleetItem = (
                                    <FleetTreeSelect
                                        showSearch
                                        allowClear
                                        treeNodeFilterProp="title"
                                        ref={(node: any) => (fleetTreeEleRef.current[key] = node)}
                                        onInitDone={forceUpdate}
                                        placeholder={i18n.t('message', '请选择归属车组')}
                                        // 非bp需要请求当前租户下的车组数据
                                        isLoadTreeData={!inSaaS}
                                        validator={true}
                                        flatTreeData={appFleetData ? appFleetData[name] : null}
                                        notDefaultBp={inSaaS}
                                    />
                                );
                                const applicationItem = (
                                    <AuthSelect
                                        allowClear
                                        options={initAppList}
                                        onChange={(value) => onAppChange(value, name)}
                                        disabled={fleetTreeEleRef.current[key]?.getAuth}
                                        onClear={() => onAppClear(name)}
                                        // options={appList.filter((p: any) => p.value != 0)}
                                        placeholder={i18n.t('message', '请选择应用')}
                                    />
                                );
                                const inSaaSWidth = inSaaS ? '193px' : '340px';
                                return (
                                    <div style={{ display: 'flex' }} key={key}>
                                        {inSaaS && (
                                            <StarryAbroadFormItem
                                                {...restField}
                                                name={[name, 'appId']}
                                                key={`appId${key}`}
                                                required={false}
                                                // @ts-ignore
                                                rules={[
                                                    (isRequire && name > FIRST_FLEET_NAME) ||
                                                    userFleetConfig?.mustRelateIsOpen
                                                        ? {
                                                              required: true,
                                                              message: i18n.t(
                                                                  'name',
                                                                  '请选择归属应用',
                                                              ),
                                                          }
                                                        : undefined,
                                                ].filter((i) => i)}
                                                style={{width:singleColumn?'100%':'140px',marginRight: '8px'}}
                                            >
                                                {applicationItem}
                                            </StarryAbroadFormItem>
                                        )}
                                        <StarryAbroadFormItem
                                            {...restField}
                                            name={[name, 'fleetId']}
                                            key={`fleetId${key}`}
                                            required={false}
                                            // @ts-ignore
                                            rules={[
                                                (isRequire && name > FIRST_FLEET_NAME) ||
                                                userFleetConfig?.mustRelateIsOpen
                                                    ? {
                                                          required: true,
                                                          message: i18n.t('name', '请选择归属车组'),
                                                      }
                                                    : undefined,
                                            ].filter((i) => i)}
                                            style={{
                                                width: singleColumn?'100%':inSaaSWidth,
                                                marginRight: '8px',
                                            }}
                                        >
                                            {fleetItem}
                                        </StarryAbroadFormItem>
                                        {inSaaS && (
                                            <div className='fleets-add-btn-box'>
                                                {index === 0 ? (
                                                    <Button
                                                        type="primary"
                                                        disabled={fields.length >= appList.length}
                                                        onClick={() => add()}
                                                    >
                                                        +
                                                    </Button>
                                                ) : (
                                                    <Button
                                                        disabled={
                                                            fleetTreeEleRef.current[key]?.getAuth
                                                        }
                                                        onClick={() => {
                                                            remove(name);
                                                            onAppChange(undefined, name, 'remove');
                                                        }}
                                                    >
                                                        -
                                                    </Button>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                );
                            })}
                        </Space>
                    )}
                </Form.List>
            </div>
        ) : null;
    };

    // /** @description 如果不开启邮箱校验，值为空时则不校验 */
    // const generalValidateEmail = (rules: any, value: string) => {
    //     if (/^[a-zA-Z0-9+@._-]+$/.test(value) || (!tenateConfig?.emailIsOpen && !value)) {
    //         return Promise.resolve();
    //     } else {
    //         return Promise.reject(i18n.t('message', "可使用字母、数字和 '@' '.' '-' '_'符号"));
    //     }
    // };
       const handleSingleColumn = (single) => {
           setSingleColumn(single);
       };

    const disabledDate: DatePickerProps['disabledDate'] = (current) => {
        return current && current < moment().endOf('day');
    };

    const baseInfoForm = (
        <Form form={form} layout="vertical" onFinish={onFinish}
              initialValues={{
                  expireType: EXPIRE_TYPE.FOREVER
              }}>
            <div className="add-edit-user">
                 <RspFormLayout layoutType="auto" onSingleColumn={handleSingleColumn}>
                    <StarryAbroadFormItem
                        validateFirst
                        name="account"
                        label={i18n.t('name', '用户名')}
                        rules={nameRules}
                        getValueFromEvent={(e) => {
                            const value = e.target.value.toLowerCase();
                            setUname(value);
                            return getOnlyOneSpaceBetweenWords(value);
                        }}
                    >
                        <Input
                            allowClear
                            autoComplete={'off'}
                            placeholder={i18n.t('message', '请输入')}
                            disabled={!!userId}
                            maxLength={50}
                        />
                    </StarryAbroadFormItem>
                    <StarryAbroadFormItem
                        name="email"
                        label={i18n.t('name', '邮箱')}
                        validateFirst
                        rules={[
                            {
                                required: tenateConfig?.emailIsOpen ? true : false,
                            },
                            {
                                validator: (rules, value) => generalValidateUserEmail(rules, value, !tenateConfig?.emailIsOpen),
                            },
                            {
                                type: 'email',
                                message: i18n.t('message', '邮箱格式错误'),
                            },
                            {
                                min: 1,
                                max: 50,
                            },
                        ]}
                    >
                        <Input
                            allowClear
                            autoComplete={'off'}
                            placeholder={i18n.t('message', '请输入用户邮箱')}
                            maxLength={50}
                        />
                    </StarryAbroadFormItem>
                    <StarryAbroadFormItem
                        name="password"
                        validateFirst
                        label={i18n.t('name', '密码')}
                        rules={pwdRules}
                    >
                        <PasswordInput
                            onInput={passwordOnInput}
                            allowClear
                            placeholder={i18n.t('message', '请输入密码')}
                            autoComplete="new-password"
                            maxLength={20}
                        />
                    </StarryAbroadFormItem>
                    <StarryAbroadFormItem
                        validateFirst
                        name="confirmPassword"
                        dependencies={['password']}
                        label={i18n.t('name', '确认密码')}
                        rules={confirmPwdRules}
                    >
                        <PasswordInput
                            onInput={confirmPasswordOnInput}
                            placeholder={i18n.t('message', '请再次输入密码')}
                            autoComplete="new-password"
                        />
                    </StarryAbroadFormItem>
                    <StarryAbroadFormItem
                        validateFirst
                        // validateTrigger = 'onblur'
                        name="codeTel"
                        label={i18n.t('name', '联系方式')}
                        className="code-tel"
                        rules={[
                            {
                                required: tenateConfig?.contactInformationIsOpen ? true : false,
                            },
                            { validator: validatorCodeTel },
                        ]}
                    >
                        <AreaSelection />
                    </StarryAbroadFormItem>
                     {userAutoExpireEnable ?
                         <>
                             <StarryAbroadFormItem
                                 name="expireType"
                                 label={i18n.t('name', '用户有效期')}
                                 required
                             >
                                 <Radio.Group disabled={isTenantManageRole && !!userId}>
                                     <RspHorizontal align='middle' gutter={[32, 16]}>
                                         {expireTypeMap.map((item) => {
                                             return (
                                                 <Radio value={item.value}>{item.label}</Radio>
                                             );
                                         })}
                                     </RspHorizontal>
                                 </Radio.Group>
                             </StarryAbroadFormItem>
                             <Form.Item
                                 shouldUpdate={(prevValues, curValues) =>
                                     prevValues.expireType !== curValues.expireType
                                 }
                                 noStyle
                             >
                                 {({ getFieldValue }) => {
                                     return getFieldValue('expireType') === EXPIRE_TYPE.CUSTOM ? (
                                         <StarryAbroadFormItem
                                             label={i18n.t('name', '到期日期')}
                                             required
                                             rules={[
                                                 {
                                                     required: true,
                                                     message: i18n.t(
                                                         'message',
                                                         '有效期不能为空',
                                                     ),
                                                 },
                                             ]}
                                             name="expireTime"
                                         >
                                             <NewDatePicker
                                                 disabled={isTenantManageRole && !!userId}
                                                            style={{width: '100%'}}
                                                 disabledDate={disabledDate}
                                                 showToday={false}
                                                 onChange={handleDateChange}
                                             />
                                         </StarryAbroadFormItem>
                                     ) : null;
                                 }}
                             </Form.Item>
                         </>
                         : null
                     }
                    {fleetsFormItem()}
                    <RspFormLayout.SingleRow>
                    <StarryAbroadFormItem
                            name="userDesc"
                            label={i18n.t('name', '描述')}
                        >
                            <TextArea
                                allowClear
                                showCount
                                maxLength={500}
                                placeholder={i18n.t('message', '请输入用户描述')}
                            />
                        </StarryAbroadFormItem>
                    </RspFormLayout.SingleRow>
                </RspFormLayout>
            </div>

            <div className='base-info-form-add-btn-box'>
                <StarryAbroadLRLayout>
                    <Button
                        onClick={() => {
                            history.goBack();
                        }}
                    >
                        {i18n.t('action', '取消')}
                    </Button>
                    <Button loading={loading} type="primary" htmlType="submit">
                        {isEdit ? i18n.t('action', '保存') : i18n.t('action', '保存并下一步')}
                    </Button>
                </StarryAbroadLRLayout>
            </div>
        </Form>
    );

    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <StarryCard>
                <main className="user-manage-add-container">
                    {!isEdit ? (
                        <div className="user-manage-steps-wrapper">
                            <Steps current={current}>
                                {steps.map((item: any) => (
                                    <Step title={item.title} key={item.key} />
                                ))}
                            </Steps>
                        </div>
                    ) : null}
                    {current === 0 && (
                        <InfoPanel style={{paddingTop:12}} title={i18n.t('name', '基本信息')}>{baseInfoForm}</InfoPanel>
                    )}
                    {!isEdit ? (
                        <>
                            {current === 1 && (
                                <UserRoleAuthorize
                                    stepSaveInfo={stepSaveInfo}
                                    setStepSaveInfo={setStepSaveInfo}
                                    onFinish={nextStep}
                                    ref={userRoleAuthorizeRef}
                                />
                            )}
                            {current === 2 && isDataAuth() ? (
                                <UserDataAuthorize stepSaveInfo={stepSaveInfo} />
                            ) : null}
                        </>
                    ) : null}
                </main>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default withShareRootHOC(AddEditUser);
