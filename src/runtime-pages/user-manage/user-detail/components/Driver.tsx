import { StarryAbroadOverflowEllipsisContainer } from '@base-app/runtime-lib';
import { useEffect, useState, useRef } from 'react';
import {
    Space,
    message,
    Input,
    Modal,
    Button,
    Table,
    Tooltip,
} from '@streamax/poppy';
import { Auth, i18n } from '@base-app/runtime-lib';
import Empty from './Empty';
import DriverModal from './DriverModal';
import { IconRequest, IconDelete, IconSearch02 } from '@streamax/poppy-icons';
import {
    userAuthorityDriverPage,
    deleteUserAuthorityDriver,
    postUserAuthorityDriver,
} from '@/service/user';
import type { DriverPageItem } from '@/service/user';
import { StarryModal } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer, Total } from '@streamax/starry-components';
import type { TablePaginationConfig } from '@streamax/poppy/lib/table';
import AuthFleetShow from '@/components/AuthFleetShow';
import './Driver.less';
import { dataAuthCodes } from '../const';
import ResponsiveHorizontal from '@streamax/responsive-layout/lib/rsp-horizontal';
import { RspBasicLayout, useResponsiveShow } from '@streamax/responsive-layout';

const { confirm } = StarryModal;
const DEFAULT_PAGE = 1;
const DEFAULT_PAGESIZE = 10;
type DriverDataItem = DriverPageItem;
interface DriverProps {
    userId: string;
    appId: string;
}
interface DriverTableProps extends DriverProps {
    isInclude: boolean;
    style?: React.CSSProperties;
}
const DriverTable: React.FC<DriverTableProps> = (props) => {
    const search = useRef<string>('');

    const [currentPageData, setCurrentPageData] = useState<DriverDataItem[]>(
        [],
    );
    const [pageLoading, setPageLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGESIZE,
        total: 0,
        size: 'middle',
        showTotal: Total.showTotal,
        showSizeChanger: true,
        showQuickJumper: true,
    });
    // 弹框是否显示
    const [visible, setVisible] = useState(false);

    const { isInclude, userId, appId, style } = props;
    const addAuthCode = dataAuthCodes['addDataAuth'];
    const delAuthCode = dataAuthCodes['delDataAuth'];

    const addAuth = Auth.check(addAuthCode);


    useEffect(() => {
        // 切换应用的时候要看此应用下面有没有数据权限，没有显示点击添加，有直接显示表格
        getList();
    }, [appId]);

    // 获取司机列表
    const getList = async (queryPage?: number, queryPageSize?: number) => {
        setPageLoading(true);
        try {
            const { list, total, page, pageSize } = await userAuthorityDriverPage({
                userId,
                appId,
                isInclude: isInclude ? 1 : 0,
                page: queryPage || DEFAULT_PAGE,
                pageSize: queryPageSize || pagination.pageSize || DEFAULT_PAGESIZE,
                driverNameOrJobNumber: search.current,
            });
            setPageLoading(false);
            setCurrentPageData(list);
            setPagination((prev) => {
                return {
                    ...prev,
                    current: page,
                    pageSize: pageSize,
                    total: total,
                };
            });
        } catch (error) {
            setPageLoading(false);
        }
    };

    const handleTableChange = (pagination: TablePaginationConfig) => {
        getList(pagination.current, pagination.pageSize);
    };

    const deleteDriver = (record: DriverDataItem) => {
        const { driverId, driverName } = record;
        confirm({
            size:'small',
            centered: true,
            title: i18n.t('name', '删除确认'),
            icon: <IconRequest />,
            content: i18n.t('message', '确认要删除“{name}”司机吗？', {
                name: driverName,
            }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () =>
                new Promise<void>((resolve, reject) => {
                    deleteUserAuthorityDriver({
                        userId,
                        driverId,
                        isInclude: isInclude ? 1 : 0,
                    })
                        .then((data: unknown) => {
                            if (data) {
                                message.success(i18n.t('message', '操作成功'));
                                resolve();
                                getList();
                            } else {
                                message.error(i18n.t('message', '操作失败'));
                                reject();
                            }
                        })
                        .catch(() => {
                            reject();
                        });
                }),
        });
    };

    // 司机相关
    const driverColumns = [
        {
            title: i18n.t('name', '司机姓名'),
            dataIndex: 'driverName',
            ellipsis: { showTitle: false },
            render: (text: string) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {text}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '司机工号'),
            dataIndex: 'jobNumber',
            ellipsis: { showTitle: false },
            render: (text: string) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {text}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetList',
            ellipsis: { showTitle: false },
            render: (text: DriverDataItem['fleetList']) => {
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        <AuthFleetShow fleetList={text} />
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            // @ts-ignore
            render: (_, record: DriverDataItem) => {
                return (
                    <Auth code={delAuthCode}>
                        <Tooltip title={i18n.t('action', '删除')}>
                            <IconDelete
                                className="operate-btn"
                                // @ts-ignore
                                onClick={() => deleteDriver(record)}
                            />
                        </Tooltip>
                    </Auth>
                );
            },
        },
    ];

    // 打开选择司机弹框
    const openModal = () => {
        setVisible(true);
    };

    const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        search.current = e.target.value.trim();
    };

    const handleOk = (type: string, data: { ids: string[] }) => {
        if (!data.ids.length) {
            setVisible(false);
            return;
        }

        postUserAuthorityDriver({
            driverIds: data.ids.join(','),
            userId,
            appId,
            isInclude: isInclude ? 1 : 0,
        }).then((data: boolean) => {
            if (data) {
                message.success(i18n.t('message', '操作成功'));
                setVisible(false);
                getList();
            } else {
                message.error(i18n.t('message', '操作失败'));
            }
        });
    };

    const generateContent = () => {
        /* if (pagination.total == 0) {
            // 列表为空
            return (
                <Empty
                    descText={isInclude ? i18n.t('message', '暂无包含司机') :
                        i18n.t('message', '暂无剔除司机')}
                    clickText={i18n.t('action', '请添加')}
                    onClick={openModal}
                    className="table-no-data"
                />
            );
        } */
        // 不为空的情况下，展示表格
        return (
            <Table
                bordered="around"
                rowKey="vehicleId"
                columns={driverColumns}
                dataSource={currentPageData}
                loading={pageLoading}
                // @ts-ignore
                pagination={{
                    ...pagination,
                }}
                onChange={handleTableChange}
                locale={{
                    emptyText: () => {
                        return addAuth ? (
                            <Empty
                                descText={`${i18n.t('message', '暂无数据')}`}
                                height={56}
                                clickText={i18n.t('action', '请添加')}
                                onClick={async ()=>{
                                    const showSafeVerify = await Auth.safeCheck(addAuthCode)
                                    if (showSafeVerify){
                                        openModal()
                                    }
                                }}
                                className={'emptyAddButtom'}
                            />
                        ) : (
                            <Empty
                                descText={`${i18n.t('message', '暂无数据')}`}
                                height={56}
                                clickText={''}
                                className={'emptyAddButtom'}
                            />
                        );
                    },
                }}
            />
        );
    };
    const isResponsive = useResponsiveShow({
        xs: false,
        sm: false,
        md: false,
        lg: false,
        xl: true,
        xxl: true,
    });
    return (
        <div className="driver-container-table-wrapper" style={style}>
            <div className="header-wrapper">
                <div className="header-row">
                    <Space size={20}>
                        <div className="title-text">
                            {isInclude
                                ? i18n.t('name', '包含司机')
                                : i18n.t('name', '剔除司机')}
                        </div>
                    </Space>
                </div>
                <div className="header-row">
                    <ResponsiveHorizontal
                        gutter={[0, 24]}
                        justify="space-between"
                        align="middle"
                    >
                        <Auth code={addAuthCode}>
                            <div className="operate-btn-wrapper">
                                <Button type="primary" onClick={openModal}>
                                    {i18n.t('name', '添加司机')}
                                </Button>
                            </div>
                        </Auth>

                        <div className="search-wrapper">
                            <Input
                                placeholder={i18n.t(
                                    'action',
                                    '请输入司机姓名或工号',
                                )}
                                prefix={<IconSearch02 />}
                                maxLength={50}
                                allowClear
                                onChange={onSearchChange}
                                // @ts-ignore
                                onPressEnter={() => {
                                    getList();
                                }}
                            />
                        </div>
                    </ResponsiveHorizontal>
                </div>
            </div>
            <div
                className="table-wrapper"
                style={{ minHeight: isResponsive ? '559px' : '0px' }}
            >
                {generateContent()}
            </div>
            <DriverModal
                visible={visible}
                type={isInclude ? 'include' : 'exclude'}
                authorizeAppId={appId}
                onCancel={() => setVisible(false)}
                onOk={handleOk}
                userId={userId}
            />
        </div>
    );
};

const Driver: React.FC<DriverProps> = (props) => {
    const { userId, appId } = props;
    return (
        <div className="driver-container">
            <RspBasicLayout
               gutter={[40,24]}
                preset='ListMap'
            >
                <RspBasicLayout.Item>
                    <DriverTable isInclude userId={userId} appId={appId}/>
                </RspBasicLayout.Item>
                <RspBasicLayout.Item>
                    <DriverTable
                        isInclude={false}
                        userId={userId}
                        appId={appId}
                    />
                </RspBasicLayout.Item>
            </RspBasicLayout>
        </div>
    );
};
export default Driver;
