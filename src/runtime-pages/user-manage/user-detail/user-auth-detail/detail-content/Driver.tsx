import { useState, useRef } from 'react';
import { i18n } from '@base-app/runtime-lib';
import classNames from 'classnames';
import { Input } from '@streamax/poppy';
import { StarryTable } from '@base-app/runtime-lib';
import { userAuthorityDriverPage } from '../../../../../service/user';
import {
    IconSearch02
} from '@streamax/poppy-icons';
const DriverTable = (props: any) => {
    const [search, setsSearch] = useState<any>();

    const tableRef = useRef(null);

    // 包含司机还是剔除司机
    const { isInclude, userId, powerId } = props;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setsSearch(e.target.value?.trim());
    };

    const driverColumns = [
        {
            title: i18n.t('name', '司机ID'),
            dataIndex: 'driverId',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '司机姓名'),
            dataIndex: 'driverName',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '司机工号'),
            dataIndex: 'jobNumber',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'companyName',
        },
    ];

    // 获取车辆列表
    const getDriverList = async (params: any) => {
        try {
            const { list } = await userAuthorityDriverPage({
                ...params,
                userId,
                powerId,
                driverNameOrJobNumber: search,
                isInclude: isInclude ? 1 : 0,
            });
            return Promise.resolve({
                list,
                total: 100,
            });
        } catch (error) {
            return Promise.resolve({
                list: [],
                total: 0,
            });
        }
    };

    return (
        <div
            className={classNames({
                'included-driver': isInclude,
                'unincluded-driver': !isInclude,
            })}
        >
            <div className="opr-tool">
                <span className="title">
                    {isInclude ? i18n.t('name', '包含司机') : i18n.t('name', '剔除司机')}
                </span>
                <Input
                    allowClear
                    className="query-input"
                    prefix={<IconSearch02 />}
                    placeholder={i18n.t('message', '请输入司机姓名或工号')}
                    maxLength={50}
                    value={search}
                    onChange={handleChange}
                    // @ts-ignore
                    onPressEnter={()=>{tableRef.current && tableRef.current.reload();}}
                />
            </div>
            <StarryTable
                columns={driverColumns}
                fetchDataAfterMount
                ref={tableRef as any}
                fetchDataFunc={getDriverList}
                rowKey="driverId"
            />
        </div>
    );
};

export default (props: any) => {
    const { userId, powerId } = props;

    return (
        <>
            <DriverTable isInclude userId={userId} powerId={powerId} />
            <DriverTable isInclude={false} userId={userId} powerId={powerId} />
        </>
    );
};
