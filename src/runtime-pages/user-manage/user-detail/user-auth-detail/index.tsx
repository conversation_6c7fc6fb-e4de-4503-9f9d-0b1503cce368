/*
 * @LastEditTime: 2025-05-16 13:25:12
 */
import { useState, useEffect } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { StarryBreadcrumb, StarryCard } from '@base-app/runtime-lib';
import { Tabs, message } from '@streamax/poppy';
import DetailContent from './detail-content';
import { getUserDetail, userAuthorityRolePage } from '../../../../service/user';
import './index.less';

const { TabPane } = Tabs;

const UserAuthDetail = (props: any) => {
    const [userInfo, setUserInfo] = useState<any>({});

    const [activeKey, setActiveKey] = useState<any>();

    const [combinationList, setcombinationList] = useState<any[]>([]);

    const { userId, powerId } = props.location.query;

    useEffect(() => {
        // 请求用户信息，渲染title， 因为权限中心也要跳转过来，所以不能从model读取数据，要重新请求
        getUserDetail({
            userId,
            desensitize: 0,
        }).then((data: any) => {
            setUserInfo(data);
        });

        // 请求用户权限组合列表，渲染tabpane
        userAuthorityRolePage({
            userId,
            page: 1,
            pageSize: 999999,
        }).then((data: any) => {
            const comList: any[] = [];
            data.list.forEach((item: any) => {
                if (item.combination) {
                    comList.push({
                        powerId: item.powerId,
                        roleName: item.roleName,
                    });
                }
            });
            setcombinationList(comList);
            // 用户中心携带powerid跳转至权限详情要选中相应的tab页， 权限中心跳转默认选中第一个tab页
            setActiveKey(powerId || comList[0].powerId);
        });
    }, []);

    return (
        <StarryBreadcrumb>
            <StarryCard
                className="user-auth-detail"
                title={`${i18n.t('message', '查看用户')}-${userInfo.userName}`}
            >
                <Tabs activeKey={activeKey} onChange={(key) => setActiveKey(key)}>
                    {combinationList.map((item) => (
                        <TabPane key={item.powerId} tab={item.roleName}>
                            <DetailContent
                                // 当前选中的tab 的权限组合id
                                powerId={activeKey}
                                userId={userId}
                            />
                        </TabPane>
                    ))}
                </Tabs>
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default UserAuthDetail;
