import React, { useEffect, useState, useRef, useContext, useImperativeHandle } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { IconSearch02 } from '@streamax/poppy-icons';
import { Tree, Input, Badge, Empty } from '@streamax/poppy';
import { getVehiclerListByPage as fetchVehiclePage } from '@/service/vehicle';
import { topVehicle } from '../../../../../../service/flow-center';
import VehicleRequest from '@/service/vehicle';
import { useDebounceFn } from '@streamax/hooks';
import { DataContext } from '../../../Context';
import { timeZone, getBgColor, getTextColor, compare, dst } from '../../../../Common';
import ComponentLoading from '@/components/component-loading';
import { getOffsetTimeByTimeStamp } from '@/utils/commonFun';
import './index.less';
import moment from 'moment';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { useResponsiveShow } from '@streamax/responsive-layout';

const ClassNamePrefix = 'vehicle-tree-pro';
const unit = 1024 * 1024 * 1024; // Byte -> GB

const VehicleTree = (props: any, ref: any) => {
    const { queryTime, onTreeSelect, onReset } = useContext(DataContext);
    const [treeData, setTreeData] = useState<any[]>([]);
    const [selectedKey, setSelectedKey] = useState<string>('');
    const [searchValue, setSearchValue] = useState<string>('');
    const allTreeDataRef = useRef<any[]>([]);
    const [loading, setLoading] = useState(false);

    useImperativeHandle(ref, () => {
        return {
            reset,
        };
    });

    useEffect(() => {
        reset();
        // 获取车辆列表
        (async () => {
            setLoading(true);
            try {
                const list = await fetchVehicleData();
                // const rankData = await getTopVehicle();
                const arr: any[] = await getSortList(list);
                const rankTree = arr?.sort(compare('total', 'vehicleNumber'));
                const newRankTree = rankTree.map((item: any, index: any) => {
                    return Object.assign(item, { count: index + 1 });
                });
                // setListData(list);
                allTreeDataRef.current = newRankTree;
                setTreeData(newRankTree);
                setLoading(false);
            } catch (error) {
                setLoading(false);
                console.error(error);
            }
        })();
    }, [queryTime]);
    const getSortList = async (data: any) => {
        const arr: any = [];
        const rankData = await getTopVehicle();
        data?.map((value: any) => {
            if (!value.deviceList.length) {
                return;
            }
            if (value.deviceList.length === 1) {
                arr.push({
                    ...value,
                    deviceId: value?.deviceList?.[0]?.deviceId,
                    total: (
                        rankData.find(
                            (flowItem: any) =>
                                flowItem.vehicleId == value.vehicleId &&
                                flowItem.deviceId == value?.deviceList?.[0]?.deviceId,
                        )?.total / unit || 0
                    ).toFixed(2),
                });
            } else {
                value.deviceList.map(
                    (deviceItem: { deviceId: string; deviceAlias: string; deviceNo: string }) => {
                        arr.push({
                            ...value,
                            total: (
                                rankData.find(
                                    (flowItem: { vehicleId: string; deviceId: string }) =>
                                        flowItem.vehicleId == value.vehicleId &&
                                        flowItem.deviceId == deviceItem.deviceId,
                                )?.total / unit || 0
                            ).toFixed(2),
                            deviceId: deviceItem.deviceId,
                            deviceAlias: deviceItem.deviceAlias,
                            deviceNo: deviceItem.deviceNo,
                            mutilDevice: true,
                        });
                    },
                );
            }
        });
        return arr;
    };

    // 查询车辆数据
    async function fetchVehicleData() {
        return fetchVehiclePage({
            page: 1,
            pageSize: 999999,
            fields: 'device,fleet',
        }, true)
            .then((res: any) => {
                return res?.list || [];
            })
            .catch(() => {
                return [];
            });
    }

    // 查询车辆耗流
    async function getTopVehicle() {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return await topVehicle({
            startDate: queryTime[0],
            endDate: queryTime[1],
            timeZoneOffset: getOffsetTimeByTimeStamp(moment(queryTime[0])),
        });
    }

    // 根据车牌/设备号查询
    async function fetchVehivleDevice(value: any) {
        const requestParams = {
            fields: 'device, fleet,channel',
            vehicleNumberOrDeviceNo:value?value:'',
        };
        const data = await VehicleRequest.getPageList(requestParams);
        const filteredVehicle: any = [];
        data?.list?.map((item) => {
            if (item.vehicleNumber?.toLowerCase()?.includes(value?.toLowerCase())) {
                filteredVehicle.push(item);
            } else {
                item.deviceList = item?.deviceList?.filter((deviceItem) =>
                    deviceItem.deviceNo?.toLowerCase()?.includes(value?.toLowerCase()),
                );
                filteredVehicle.push(item);
            }
        });
        return filteredVehicle || [];
    }

    const handleSearch = async (value: string, data?: any) => {
        let newTreeData: any[] = [];
        if (value) {
            const arr: any = await getSortList(data);
            // 搜索结果加上total count字段再排序
            const rankTree = arr.sort(compare('total', 'vehicleNumber'));
            newTreeData = rankTree.map((item: any, index: any) => {
                return Object.assign(item, { count: index + 1 });
            });
        } else {
            newTreeData = allTreeDataRef.current;
        }
        setTreeData(newTreeData);
    };

    const { run: debounceHandleSearch } = useDebounceFn(handleSearch, { wait: 300 });

    const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        // 字符大小限制
        setSearchValue(value);
        // 需要根据车牌/设备号实时搜索
        const data = await fetchVehivleDevice(value);
        debounceHandleSearch(value, data);
    };

    const handleSelect = (selectedKey: string, e: any) => {
        const { vehicleNumber, deviceId } = e.node;
        searchValue !== '' && setSearchValue(vehicleNumber);
        setSelectedKey(selectedKey);
        onTreeSelect &&
            onTreeSelect(selectedKey.length == 0 ? '' : `vehicle-${deviceId}`, {
                type: 'vehicle',
                title: vehicleNumber,
            });
    };

    const reset = () => {
        setSearchValue('');
        setSelectedKey('');
        setTreeData(allTreeDataRef.current);
        onTreeSelect('');
        onReset('');
    };
    const renderContentItem = (data: any) => {
        const { vehicleNumber, deviceAlias, deviceNo, mutilDevice } = data;
        return (
            <span>
                <OverflowEllipsisContainer>
                    {`${vehicleNumber}${mutilDevice ? ` (${deviceAlias ?? deviceNo})` : ''}`}
                </OverflowEllipsisContainer>
            </span>
        );
    };

    // 添加唯一key
    const newData = treeData?.map((item) => ({
        ...item,
        key: item.deviceId,
    }));

    const renderNode = (nodeData: any) => {
        const { count, total } = nodeData;
        return (
            <div title={''} className="tree-node-content">
                <div>
                    <Badge
                        count={count}
                        style={{ backgroundColor: getBgColor(count), color: getTextColor(count) }}
                    />
                </div>

                <div className="tree-node-title">
                    {renderContentItem(nodeData)}
                </div>
                <div className="tree-node-info" title={total.toString()}>
                    {total?.toLocaleString()}
                </div>
            </div>
        );
    };
    const isResponsive = useResponsiveShow({
        xs: true,
        sm: true,
        md: true,
        lg: true,
        xl: false,
        xxl: false,
    });  
    return (
        <div className="vehicle-rank-container">
            <div className="search-vehicle">
                <Input
                    prefix={<IconSearch02 />}
                    value={searchValue}
                    maxLength={50}
                    placeholder={i18n.t('message', '请输入车牌号/设备号')}
                    onChange={handleChange}
                    //@ts-ignore
                    onSearch={debounceHandleSearch}
                    allowClear
                />
            </div>
            <div className={ClassNamePrefix}>
                <div className={`${ClassNamePrefix}-index`}>
                    <div className={`${ClassNamePrefix}-body`}>
                        <Tree
                            treeData={newData}
                            //@ts-ignore
                            selectedKeys={selectedKey}
                            //@ts-ignore
                            onSelect={handleSelect}
                            titleRender={renderNode}
                            style={{ width: '100%' }}
                            height={isResponsive?222:616}
                        />
                        {newData.length == 0 && <Empty />}
                        <ComponentLoading loading={loading} />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default React.forwardRef(VehicleTree);
