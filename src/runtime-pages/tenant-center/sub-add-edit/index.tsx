import { useState, useEffect } from 'react';
import { i18n, utils, RouterPrompt, getAppGlobalData, useSystemComponentStyle, StarryAbroadFormItem as AFormItem, StarryAbroadLRLayout } from '@base-app/runtime-lib';
import { useHistory } from '@base-app/runtime-lib/core';
import moment from 'moment';
import {
    StarryCard,
    StarryBreadcrumb,
    Action,
    AreaSelection,
} from '@base-app/runtime-lib';
import CodeTel from '../../../components/CodeTel';
import {
    Form,
    Input,
    Radio,
    NewDatePicker as DatePickerComponent,
    Button,
    message,
    Space,
    Spin,
    Select,
    Divider,
    Tooltip,
    InputNumber,
    Popover,
    Row,
    Container,
} from '@streamax/poppy';
const DatePicker: any = DatePickerComponent;
import { getTenantDetail, updateTenant, createTenant } from '../../../service/tenant';
import { fetchParameterDetail } from '../../../service/parameter';
import { usePasswordConfig } from '@base-app/runtime-lib';
import { OverflowEllipsisContainer, PasswordInput } from '@streamax/starry-components';
import './index.less';
import { IconInformation, IconInformationFill } from '@streamax/poppy-icons';
import useFilterStorageSetMeal from '../hooks/useFilterStorageSetMeal';
import { useRequest } from '@streamax/hooks';
import { getTenantMealOpenConfig } from '@/service/meal';
import { getOnlyOneSpaceBetweenWords, checkFieldSpace, validateMinSixLen } from '@/utils/commonFun';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import { PageBase, FormEdit } from '@/types/pageReuse/pageReuseBase';
import isAdvanceTenant from '@/utils/license-common';
import { constant, stubTrue } from 'lodash';
import { RspFormLayout } from '@streamax/responsive-layout';
export type SubTenantDetailShareProps = PageBase & FormEdit;
type TenantInfo = {
    deviceLimit: number; //租户最大设备数，当且仅当deviceLimitType=1时有值
    deviceLimitType: 0 | 1; //租户最大设备数类型，0-无限制，1-限制
    usedDeviceCount: number; // 租户已使用的设备数量
};
const { encryptPassword } = utils.general;
const validatorIllegalCharacter = utils.validator.illegalCharacter;
const validatorUserName = utils.validator.validateBaseUser;

const FormItem = Form.Item;
const { TextArea } = Input;

const SubAddEdit = (props: SubTenantDetailShareProps) => {
    // 定制
    const { children, onFormSubmit } = props;

    const expireTypes = [
        {
            label: i18n.t('state', '永久有效'),
            value: 1,
        },
        {
            label: i18n.t('state', '自定义'),
            value: 2,
        },
    ];
    const maxDevice = [
        {
            label: i18n.t('name', '无限制'),
            value: 0,
        },
        {
            label: i18n.t('name', '限制'),
            value: 1,
        },
    ];
    const { isAbroadStyle } = useSystemComponentStyle();
    const history = useHistory();
    const { pwdRules } = usePasswordConfig();
    const [loading, setLoading] = useState(false);

    const [confirmLoading, setConfirmLoading] = useState(false);
    const [when, setWhen] = useState(true);
    const [fatherTenantInfo, setFatherTenantInfo] = useState<TenantInfo>({} as TenantInfo);
    const [uname, setUname] = useState<string>();
    const [usedDeviceCount, setUsedDeviceCount] = useState<number>(0); //租户已使用的设备数量
    const [deviceLimitType, setDeviceLimitType] = useState<number>(0); //是否限制
    const [deviceLimit, setDeviceLimit] = useState<number>(0); //子租户最大设备数

    const [form] = Form.useForm();
    const { tenantId } = history.location.query;
    const { option } = useFilterStorageSetMeal();
    const userTenantId = getAppGlobalData('APP_USER_INFO')?.tenantId as string;
    const { data: storageSetMealOpen = false } = useRequest(() =>
        getTenantMealOpenConfig(userTenantId),
    );
    const FormItemWidth = 340;
    // 请求租户详情数据回填表单
    useEffect(() => {
        // 如果是编辑情况，请求数据回填表单
        if (tenantId) {
            setLoading(true);
            queryTenantDetail();
        } else {
            form.setFieldsValue({ expireType: 1 });
            // 新增的时候请求默认区号并设置
            fetchParameterDetail({
                parameterKey: 'AREACODE',
            }).then((data: any) => {
                const { parameterValue } = data;
                form.setFieldsValue({
                    codeTel: {
                        areaCode: Number(parameterValue),
                    },
                });
            });
        }
        // 需知道父租户的设备限制情况
        queryFatherDetail(true);
    }, []);
    const queryFatherDetail = (isInit?: boolean) => {
        getTenantDetail({
            // @ts-ignore
            fields: 'admin',
        }).then((data: any) => {
            setFatherTenantInfo(data);
            if (!tenantId && isInit) {
                // 有可分配设备或父租户未限制设备时，默认1，其余为0
                form.setFieldsValue({
                    deviceLimit:
                        data.deviceLimit - data.usedDeviceCount > 0 || !data.deviceLimitType
                            ? 1
                            : 0, //默认最大设备数为1,被分配完了为0
                });
            }
        });
    };
    const queryTenantDetail = (updateLimit = false) => {
        getTenantDetail({
            // @ts-ignore
            tenantId,
        })
            .then((data: any) => {
                const initialValues = {
                    tenantName: data.tenantName,
                    tenantCode: data.tenantCode,
                    description: data.description,
                    expireType: data.expireTime ? 2 : 1,
                    expireTime: data.expireTime && moment(data.expireTime * 1000),
                    setsId: data.setsId,
                    deviceLimitType: data.deviceLimitType,
                    deviceLimit: data.deviceLimit,
                };
                setUsedDeviceCount(data.usedDeviceCount);
                setDeviceLimitType(data.deviceLimitType);
                setDeviceLimit(data.deviceLimit);
                setLoading(false);
                if (updateLimit) return;
                form.setFieldsValue(initialValues);
            })
            .catch(() => {
                setLoading(false);
            });
    };

    const handleSubmit = () => {
        // 新增为post，编辑为put方法
        // const method = tenantId ? 'put' : 'post';
        form.validateFields()
            .then((values) => {
                const {
                    tenantName,
                    tenantCode,
                    description,
                    expireType,
                    expireTime,
                    account,
                    name,
                    password,
                    email,
                    codeTel,
                    remark,
                    setsId,
                    deviceLimitType,
                    deviceLimit,
                } = values;
                setConfirmLoading(true);
                const params: any = {
                    tenantId,
                    tenantCode,
                    tenantName: (tenantName || '').trim(),
                    description: (description || '').trim(),
                    expireTime: expireType === 1 ? -1 : expireTime?.unix(),
                    storageSwitch: storageSetMealOpen ? 1 : 0,
                    setsId,
                    deviceLimitType: fatherTenantInfo.deviceLimitType || deviceLimitType ? 1 : 0,
                    deviceLimit:
                        fatherTenantInfo.deviceLimitType || deviceLimitType ? deviceLimit : null,
                };
                // 新增的情况下添加租户管理员相关信息
                if (!tenantId) {
                    params.tenantAdmin = {
                        account: (account || '').trim(),
                        name: (name || '').trim(),
                        password: encryptPassword(password),
                        email,
                        remark: (remark || '').trim(),
                        phone: codeTel.phone,
                        areaCode: codeTel.areaCode,
                    };
                }

                (tenantId ? updateTenant : createTenant)(params, [
                    {
                        errorCode: *********,
                        errorMessage: i18n.t('message', '可分配设备数发生变动，请重新填写'),
                    },
                ])
                    .then((data: any) => {
                        setConfirmLoading(false);
                        if (data) {
                            setWhen(false);
                            message.success(i18n.t('message', '操作成功'));
                            if (onFormSubmit) {
                                onFormSubmit({ params, data });
                                return;
                            }
                            Action.openActionUrl({
                                code: '@base:@page:sub.tanant.manage:edit@action:save',
                                url: '/sub-tenant-manage/detail',
                                history,
                                params: {
                                    tenantId: tenantId || data.tenantId,
                                }
                            });
                        } else {
                            message.error(i18n.t('message', '操作失败'));
                        }
                    })
                    .catch((error) => {
                        queryTenantDetail(true);
                        queryFatherDetail();
                        setConfirmLoading(false);
                    });
            })
            .catch(() => {
                // console.log('err', err)
            });
    };

    const validatorConfimPwd = (rule: any, value: any) => {
        if (!value || form.getFieldValue('password') === value) {
            return Promise.resolve();
        }
        return Promise.reject(i18n.t('message', '两次输入的密码不一致'));
    };

    const disabledDate = (currentDate: any) => {
        return moment().isAfter(currentDate.format('YYYY-MM-DD'));
    };

    const validatorCodeTel = (rule: any, value: any = {}) => {
        const { areaCode, phone } = value;
        if (!areaCode) {
            return Promise.reject(i18n.t('message', '请选择国家或地区'));
        }
        if (!phone) {
            return Promise.reject(i18n.t('message', '请输入联系方式'));
        }
        if (!/^[0-9-]+$/.test(phone)) {
            return Promise.reject(i18n.t('message', '请输入正确的联系方式'));
        }
        if (/^[-]+$/.test(phone)) {
            return Promise.reject(i18n.t('message', '请输入正确的联系方式'));
        }
        if (phone && (phone.length < 6 || phone.length > 15)) {
            return Promise.reject(i18n.t('message', '请输入6~15个字符'));
        }
        return Promise.resolve();
    };

    const onValuesChange = (changedValues: any) => {
        if (changedValues.tenantCode) {
            const values = form.getFieldsValue();
            form.setFieldsValue({
                ...values,
                tenantCode: changedValues.tenantCode.toUpperCase(),
            });
        }
    };
    const validateSpace = () => {
        if (uname !== uname?.trim()) {
            return Promise.reject(i18n.t('message', '只能输入英文字母、数字、下划线、邮箱字符'));
        }
        return Promise.resolve();
    };
    const checkSpaceName = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '姓名不能为空'));
    };
    const getMaxDeviceNumber = () => {
        // 编辑时最大设备数要加上子租户的最大设备数
        if (tenantId) {
            return fatherTenantInfo.deviceLimit - fatherTenantInfo.usedDeviceCount + deviceLimit;
        }
        // 新增时最大设备数只跟父租户有关
        return fatherTenantInfo.deviceLimit - fatherTenantInfo.usedDeviceCount;
    };
    const renderMaxLimitDevice = () => {
        return fatherTenantInfo?.deviceLimitType ? (
            <AFormItem
                name="deviceLimit"
                label={
                    <>
                        {i18n.t('name', '最大设备数')}
                        {tenantId ? (
                            <Popover
                                content={() => (
                                    <div className="default-icon-information-deviceLimit-type">
                                        {i18n.t('name', '限制的设备数量不能小于租户已使用的数量')}
                                    </div>
                                )}
                                className="question-icon"
                                placement="right"
                                color="rgba(0,0,0,0.75)"
                            >
                                <IconInformation
                                    className="default-icon-information"
                                    style={{ marginLeft: '8px' }}
                                />
                            </Popover>
                        ) : null}
                    </>
                }
                rules={[
                    {
                        required: true,
                        message: i18n.t('name', '最大设备数不能为空'),
                    },
                ]}
            >
                <InputNumber
                    defaultValue={1}
                    min={(() => {
                        if (tenantId) {
                            return usedDeviceCount || 1;
                        }
                        return getMaxDeviceNumber() == 0 ? 0 : 1;
                    })()}
                    max={getMaxDeviceNumber()}
                    precision={0}
                    placeholder={i18n.t('name', '请输入设备数量')}
                />
            </AFormItem>
        ) : (
            <AFormItem
                className="usefulForm radio-type-form-item"
                name="deviceLimitType"
                label={
                    <>
                        {i18n.t('name', '最大设备数')}
                        {deviceLimitType || !tenantId ? (
                            <Popover
                                content={() => (
                                    <div className="default-icon-information-deviceLimit-type">
                                        {tenantId
                                            ? i18n.t(
                                                  'name',
                                                  '限制的设备数量不能小于租户已使用的数量',
                                              )
                                            : i18n.t('name', '保存后不能修改选项，请谨慎选择')}
                                    </div>
                                )}
                                className="question-icon"
                                placement="right"
                                color="rgba(0,0,0,0.75)"
                            >
                                <IconInformation
                                    className="default-icon-information"
                                    style={{ marginLeft: '8px' }}
                                />
                            </Popover>
                        ) : null}
                    </>
                }
                rules={[
                    {
                        required: true,
                        message: i18n.t('name', '最大设备数不能为空'),
                    },
                ]}
            >
                <Radio.Group disabled={tenantId}>
                    {maxDevice.map(({ value, label }) => (
                        <Radio key={value} value={value}>
                            <OverflowEllipsisContainer>{label}</OverflowEllipsisContainer>
                        </Radio>
                    ))}
                </Radio.Group>
            </AFormItem>
        );
    };

    const TipText = () => {
        let tipText = '';
        // 父租户限制
        if (fatherTenantInfo.deviceLimitType) {
            // 编辑时展示
            if (tenantId) {
                tipText = i18n.t(
                    'name',
                    '子租户已使用{used}个设备，可给子租户最多分配{max}个设备',
                    {
                        used: usedDeviceCount,
                        max:
                            fatherTenantInfo.deviceLimit -
                            fatherTenantInfo.usedDeviceCount +
                            deviceLimit,
                    },
                );
            } else {
                tipText = i18n.t('name', '当前可分配{max}个设备', {
                    max: fatherTenantInfo.deviceLimit - fatherTenantInfo.usedDeviceCount,
                });
            }
        } else if (tenantId && deviceLimitType) {
            tipText = i18n.t('name', '子租户已使用{num}个设备', {
                num: usedDeviceCount,
            });
        }
        if (isAbroadStyle) {
            return (
                <div className="deviceLimit-used-tip">({tipText})</div>
            );
        }
        return (
            tipText && (
                <span
                    className={`deviceLimit-used-tip ${
                        fatherTenantInfo.deviceLimitType ? 'form-column-one' : ''
                    }`}
                >
                    <IconInformation
                        style={{ marginLeft: '8px' }}
                        className="default-icon-information"
                    />
                    <OverflowEllipsisContainer>{tipText}</OverflowEllipsisContainer>
                </span>
            )
        );
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能不会保存您所做的更改。',
                )}
            />
            <StarryCard className="add-edit-tenant">
                <Spin spinning={loading}>
                    <Form
                        layout="vertical"
                        form={form}
                        onValuesChange={onValuesChange}
                    >
                        <Container>
                            <div
                                className={`form-title ${
                                    tenantId ? 'form-title-edit' : ''
                                }`}
                            >
                                {i18n.t('name', '基本信息')}
                            </div>
                            <div className="form-content-row">
                                <RspFormLayout layoutType="auto">
                                    <AFormItem
                                        validateFirst
                                        name="tenantName"
                                        label={i18n.t('name', '子租户名称')}
                                        rules={[
                                            {
                                                required: true,
                                            },
                                            {
                                                min: 1,
                                                type: 'string',
                                            },
                                            {
                                                max: 50,
                                                type: 'string',
                                            },
                                            {
                                                whitespace: true,
                                            },
                                            {
                                                validator:
                                                    validatorIllegalCharacter,
                                            },
                                        ]}
                                    >
                                        <Input
                                            allowClear
                                            placeholder={i18n.t(
                                                'name',
                                                '请输入租户名称',
                                            )}
                                            maxLength={50}
                                        />
                                    </AFormItem>
                                    <AFormItem
                                        validateFirst
                                        name="tenantCode"
                                        label={i18n.t('name', '子租户ID')}
                                        rules={[
                                            {
                                                required: true,
                                            },
                                            {
                                                min: 2,
                                                type: 'string',
                                            },
                                            {
                                                max: 50,
                                                type: 'string',
                                            },
                                            {
                                                whitespace: true,
                                            },
                                            {
                                                pattern: /^[A-Z0-9]+$/,
                                                message: i18n.t(
                                                    'message',
                                                    '只能输入大写英文和数字',
                                                ),
                                            },
                                        ]}
                                    >
                                        <Input
                                            allowClear
                                            placeholder={i18n.t(
                                                'message',
                                                '请输入子租户ID',
                                            )}
                                            disabled={!!tenantId}
                                            maxLength={50}
                                        />
                                    </AFormItem>

                                    {isAdvanceTenant() ? null : (
                                        <>
                                            <AFormItem
                                                className="radio-type-form-item"
                                                name="expireType"
                                                label={i18n.t(
                                                    'name',
                                                    '租户有效期',
                                                )}
                                                rules={[
                                                    {
                                                        required: true,
                                                    },
                                                ]}
                                            >
                                                <Radio.Group>
                                                    {expireTypes.map(
                                                        ({ value, label }) => (
                                                            <Radio
                                                                key={value}
                                                                value={value}
                                                            >
                                                                <OverflowEllipsisContainer>
                                                                    {label}
                                                                </OverflowEllipsisContainer>
                                                            </Radio>
                                                        ),
                                                    )}
                                                </Radio.Group>
                                            </AFormItem>
                                            <FormItem
                                                noStyle
                                                shouldUpdate={(
                                                    prevValues,
                                                    curValues,
                                                ) =>
                                                    curValues.expireType !==
                                                    prevValues.expireType
                                                }
                                            >
                                                {({ getFieldValue }) =>
                                                    getFieldValue(
                                                        'expireType',
                                                    ) === 2 ? (
                                                        <AFormItem
                                                            name="expireTime"
                                                            label={i18n.t(
                                                                'name',
                                                                '有效期至',
                                                            )}
                                                            rules={[
                                                                {
                                                                    required:
                                                                        true,
                                                                },
                                                            ]}
                                                        >
                                                            <DatePicker
                                                                showNow={false}
                                                                showTime
                                                                disabledDate={
                                                                    disabledDate
                                                                }
                                                                placeholder={i18n.t(
                                                                    'message',
                                                                    '请选择有效期',
                                                                )}
                                                                style={
                                                                {width:'100%'}
                                                                }
                                                            />
                                                        </AFormItem>
                                                    ) : null
                                                }
                                            </FormItem>
                                            {renderMaxLimitDevice()}
                                            {!fatherTenantInfo?.deviceLimitType ? (
                                                <FormItem
                                                    noStyle
                                                    shouldUpdate={(
                                                        prevValues,
                                                        curValues,
                                                    ) =>
                                                        curValues.deviceLimitType !==
                                                        prevValues.deviceLimitType
                                                    }
                                                >
                                                    {({ getFieldValue }) =>
                                                        getFieldValue(
                                                            'deviceLimitType',
                                                        ) === 1 ? (
                                                            <AFormItem
                                                                name="deviceLimit"
                                                                // label={i18n.t('name', '设备数量')}
                                                                label={
                                                                    <>
                                                                        <div>
                                                                            {i18n.t(
                                                                                'name',
                                                                                '设备数量',
                                                                            )}
                                                                        </div>
                                                                        {isAbroadStyle && (
                                                                            <TipText />
                                                                        )}
                                                                    </>
                                                                }
                                                                rules={[
                                                                    {
                                                                        required:
                                                                            true,
                                                                    },
                                                                ]}
                                                            >
                                                                {/* @ts-ignore */}
                                                                <InputNumber
                                                                    min={
                                                                        tenantId
                                                                            ? usedDeviceCount ||
                                                                              1
                                                                            : 1
                                                                    }
                                                                    max={999999}
                                                                    precision={
                                                                        0
                                                                    }
                                                                    placeholder={i18n.t(
                                                                        'name',
                                                                        '请输入设备数量',
                                                                    )}
                                                                />
                                                            </AFormItem>
                                                        ) : null
                                                    }
                                                </FormItem>
                                            ) : null}
                                            {!isAbroadStyle && <TipText />}
                                            <RspFormLayout.SingleRow>
                                                <AFormItem
                                                    name="description"
                                                    label={i18n.t(
                                                        'name',
                                                        '描述',
                                                    )}
                                                >
                                                    <TextArea
                                                        allowClear
                                                        showCount
                                                        maxLength={500}
                                                        placeholder={i18n.t(
                                                            'message',
                                                            '请输入描述',
                                                        )}
                                                    />
                                                </AFormItem>
                                            </RspFormLayout.SingleRow>
                                        </>
                                    )}
                                </RspFormLayout>
                            </div>
                        </Container>
                        {!tenantId && (
                            <Container>
                                {!isAbroadStyle && (
                                    <Divider style={{ margin: '40px 0' }} />
                                )}
                                <div className="form-title form-title-tenant-manager">
                                    {i18n.t('name', '租户管理员')}
                                </div>
                                <div className="form-content-row">
                                    <RspFormLayout layoutType="auto">
                                        <AFormItem
                                            validateFirst
                                            name="account"
                                            label={i18n.t('name', '用户名')}
                                            rules={[
                                                {
                                                    required: true,
                                                },
                                                {
                                                    validator:
                                                        validatorUserName,
                                                },
                                                {
                                                    min: 6,
                                                    type: 'string',
                                                },
                                                {
                                                    max: 50,
                                                    type: 'string',
                                                },
                                                {
                                                    validator:
                                                        validateMinSixLen,
                                                },
                                            ]}
                                            getValueFromEvent={(e) => {
                                                const value = e.target.value;
                                                setUname(value);
                                                return getOnlyOneSpaceBetweenWords(
                                                    value,
                                                );
                                            }}
                                        >
                                            <Input
                                                allowClear
                                                placeholder={i18n.t(
                                                    'message',
                                                    '请输入用户名',
                                                )}
                                                maxLength={50}
                                            />
                                        </AFormItem>
                                        <AFormItem
                                            validateFirst
                                            name="email"
                                            label={i18n.t('name', '邮箱')}
                                            rules={[
                                                {
                                                    required: true,
                                                },
                                                {
                                                    type: 'email',
                                                    message: i18n.t(
                                                        'message',
                                                        '邮箱格式错误',
                                                    ),
                                                },
                                                {
                                                    min: 1,
                                                    type: 'string',
                                                },
                                                {
                                                    max: 50,
                                                    type: 'string',
                                                },
                                            ]}
                                        >
                                            <Input
                                                allowClear
                                                placeholder={i18n.t(
                                                    'message',
                                                    '请输入邮箱',
                                                )}
                                                maxLength={50}
                                            />
                                        </AFormItem>
                                        <AFormItem
                                            validateFirst
                                            name="password"
                                            label={i18n.t('name', '密码')}
                                            rules={[
                                                {
                                                    required: true,
                                                },
                                                {
                                                    whitespace: true,
                                                },
                                                ...pwdRules,
                                            ]}
                                        >
                                            <PasswordInput
                                                allowClear
                                                placeholder={i18n.t(
                                                    'message',
                                                    '请输入密码',
                                                )}
                                                autoComplete="new-password"
                                                maxLength={20}
                                            />
                                        </AFormItem>
                                        <AFormItem
                                            validateFirst
                                            name="confirmPassword"
                                            dependencies={['password']}
                                            label={i18n.t('name', '确认密码')}
                                            rules={[
                                                {
                                                    required: true,
                                                },
                                                {
                                                    validator:
                                                        validatorConfimPwd,
                                                },
                                            ]}
                                        >
                                            <PasswordInput
                                                autoComplete="new-password"
                                                allowClear
                                                placeholder={i18n.t(
                                                    'message',
                                                    '请再次输入密码',
                                                )}
                                            />
                                        </AFormItem>
                                        <AFormItem
                                            validateFirst
                                            name="name"
                                            label={i18n.t('name', '姓名')}
                                            rules={[
                                                {
                                                    required: true,
                                                    validator: checkSpaceName,
                                                },
                                                {
                                                    min: 1,
                                                    type: 'string',
                                                },
                                                {
                                                    max: 50,
                                                    type: 'string',
                                                },
                                                {
                                                    validator:
                                                        utils.validator
                                                            .illegalCharacter,
                                                },
                                            ]}
                                        >
                                            <Input
                                                allowClear
                                                placeholder={i18n.t(
                                                    'message',
                                                    '请输入姓名',
                                                )}
                                                maxLength={50}
                                            />
                                        </AFormItem>
                                        <AFormItem
                                            validateFirst
                                            name="codeTel"
                                            label={i18n.t('name', '联系方式')}
                                            rules={[
                                                {
                                                    required: true,
                                                },
                                                { validator: validatorCodeTel },
                                            ]}
                                        >
                                            {/* @ts-ignore */}
                                            <AreaSelection />
                                        </AFormItem>
                                        <RspFormLayout.SingleRow>
                                            <AFormItem
                                                name="remark"
                                                label={i18n.t('name', '描述')}
                                            >
                                                <TextArea
                                                    allowClear
                                                    showCount
                                                    maxLength={500}
                                                    placeholder={i18n.t(
                                                        'message',
                                                        '请输入用户描述',
                                                    )}
                                                />
                                            </AFormItem>
                                        </RspFormLayout.SingleRow>
                                    </RspFormLayout>
                                </div>
                            </Container>
                        )}
                        {!tenantId && storageSetMealOpen ? (
                            <Container>
                                {!isAbroadStyle && (
                                    <Divider style={{ margin: '40px 0' }} />
                                )}
                                <div className="form-title form-title-tenant-manager">
                                    <Space size={16}>
                                        {i18n.t('name', '存储套餐')}
                                        <Tooltip
                                            title={i18n.t(
                                                'message',
                                                '仅可根据当前租户的剩余容量和设置的空间预警阈值选择相应套餐',
                                            )}
                                            placement="right"
                                        >
                                            <IconInformationFill className="icon-tip" />
                                        </Tooltip>
                                    </Space>
                                </div>
                                <RspFormLayout layoutType="fixed">
                                    <RspFormLayout.Col>
                                        <AFormItem
                                            validateFirst
                                            name="setsId"
                                            label={i18n.t('name', '开通套餐')}
                                            rules={[
                                                {
                                                    required: true,
                                                },
                                            ]}
                                        >
                                            <Select
                                                allowClear
                                                showSearch
                                                filterOption={(input, option) =>
                                                    (
                                                        (option?.label as string) ??
                                                        ''
                                                    )
                                                        .toLowerCase()
                                                        .includes(
                                                            input.toLowerCase(),
                                                        )
                                                }
                                                placeholder={i18n.t(
                                                    'message',
                                                    '请选择套餐',
                                                )}
                                                options={option}
                                            />
                                        </AFormItem>
                                    </RspFormLayout.Col>
                                </RspFormLayout>
                            </Container>
                        ) : null}
                    </Form>
                    <StarryAbroadLRLayout className="form-title-tenant-manager">
                        <Button
                            onClick={() => {
                                history.goBack();
                            }}
                        >
                            {i18n.t('action', '取消')}
                        </Button>
                        <Button
                            loading={confirmLoading}
                            type="primary"
                            onClick={handleSubmit}
                        >
                            {i18n.t('action', '保存')}
                        </Button>
                    </StarryAbroadLRLayout>
                    {children}
                </Spin>
            </StarryCard>
        </StarryBreadcrumb>
    );
};
export default withShareRootHOC(SubAddEdit);
