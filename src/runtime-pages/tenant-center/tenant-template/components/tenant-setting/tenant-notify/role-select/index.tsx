import { Form, Radio, Select, message } from '@streamax/poppy';
import type { RadioChangeEvent } from '@streamax/poppy/lib/radio';
import { i18n, StarryAbroadFormItem } from '@base-app/runtime-lib';
import React, { useEffect, useState } from 'react';
import { MAX_SELECT_ROLE, NotifyRoleRange, RoleOptionProps } from '..';
import AuthSelect from '@/components/AuthSelectShow';
import './index.less';
import { marginBottom, marginTop } from 'html2canvas/dist/types/css/property-descriptors/margin';
import { MAX_TEMPLATE_USER_ROL_SELECT_NUMBER } from '@/runtime-pages/tenant-center/tenant-template/const';
interface RoleSelectProps {
    value?: number;
    edit: boolean;
    notifyType: string;
    form: Form.FormInstance;
    roleOptions: RoleOptionProps[];
    partRoles: string;
}

const RoleSelect = (props: RoleSelectProps) => {
    const { value = 1, edit, notifyType, form, roleOptions = [], partRoles } = props;

    const [roleRange, setRoleRange] = useState<number>(value);

    useEffect(() => {
        value && setRoleRange(value);
    }, [value]);

    // 角色范围改变
    const handleTypeChange = (e: RadioChangeEvent) => {
        const value = e.target.value;
        form.setFieldsValue({
            ...form.getFieldsValue(),
            [notifyType]: value,
        });
        setRoleRange(value);
    };
    return (
        <>
            <Radio.Group
                defaultValue={1}
                onChange={handleTypeChange}
                className="select-role-item-range-container"
                disabled={!edit}
                value={roleRange}
            >
                <Radio value={NotifyRoleRange.ALL} className="left-radio">
                    {i18n.t('name', '所有角色')}
                </Radio>
                <Radio value={NotifyRoleRange.PART}>
                    <div>{i18n.t('name', '部分角色')}</div>
                </Radio>
            </Radio.Group>
            {roleRange === NotifyRoleRange.PART && (
                <StarryAbroadFormItem
                    className="choose-part-roles-item"
                    name={partRoles}
                    style={{
                        width: 340,
                        marginTop: 24,
                        marginBottom:0
                    }}
                    rules={[
                        {
                            validator: (_, value) => {
                                if (value?.length > MAX_TEMPLATE_USER_ROL_SELECT_NUMBER) {
                                    return Promise.reject(
                                        new Error(
                                            i18n.t('message', '最多选择{number}个通知角色', {
                                                number: MAX_TEMPLATE_USER_ROL_SELECT_NUMBER,
                                            }),
                                        ),
                                    );
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                >
                    <AuthSelect
                        mode="multiple"
                        options={roleOptions}
                        disabled={!edit}
                        showArrow
                        getPopupContainer={() => document.getElementById('root') as HTMLElement}
                        className="select-part-role"
                        placeholder={i18n.t('message', '请选择要通知的角色')}
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                        }}
                        filterOption={(inputValue: any, option: any) => {
                            return option?.label
                                ?.toUpperCase()
                                ?.includes?.(inputValue?.toUpperCase());
                        }}
                        virtual={false}
                        keepSort={true}
                    />
                </StarryAbroadFormItem>
            )}
        </>
    );
};
export default RoleSelect;
