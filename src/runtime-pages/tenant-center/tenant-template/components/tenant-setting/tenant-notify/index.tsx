import {
    Button,
    Checkbox,
    Col,
    Container,
    Divider,
    Form,
    Row,
    Select,
    Space,
    Switch,
    message,
} from '@streamax/poppy';
import {
    getAppGlobalData,
    i18n,
    StarryAbroadFormItem,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import RoleSelect from './role-select';
import AuthSelect from '@/components/AuthSelectShow';
import { getRoleListByPage } from '@/service/role';
import {
    getTenantTemplateConfig,
    editTenantTemplateConfig,
    getTenantTemplateWithAuthConfig
} from '@/service/tenant-template';
import './index.less';
import isAdvanceTenant, { expireDays } from '@/utils/license-common';
import { useUpdate } from 'ahooks';
import AuthTip from '@/components/AuthTip';
import { languageLabelList } from '@/utils/commonFun';
import { marginBottom } from 'html2canvas/dist/types/css/property-descriptors/margin';
import {filterLockAndNoExistList} from "@/utils/filterLockAndNoExistList";
import { RspFormLayout } from '@streamax/responsive-layout';
import { MAX_TEMPLATE_USER_ROL_SELECT_NUMBER } from '../../../const';
const CheckboxGroup = Checkbox.Group;

export enum NotifyRoleRange {
    ALL = 1,
    PART = 2,
}
export const MAX_SELECT_ROLE = 20;
export interface RoleOptionProps {
    label: string;
    value: string;
    disabled?: boolean;
}
export enum PushMessageType {
    message = '1',
    email = '2',
}
export const validatorMaxRole = (_, value) => {
    if (value?.length > MAX_TEMPLATE_USER_ROL_SELECT_NUMBER) {
        return Promise.reject(
            new Error(
                i18n.t('message', '最多选择{number}个推送角色', { number: MAX_TEMPLATE_USER_ROL_SELECT_NUMBER }),
            ),
        );
    }
    return Promise.resolve();
};
const configKey = isAdvanceTenant() ? 'vehicleLicenseExpireNotice' : 'expireNotice';
const initalConfig = {
    expireNoticeType: 1,
    renewalNoticeType: 1,
    childExpireNoticeType: 1,
    childRenewalNoticeType: 1,

    expireNoticeRoles: [],
    renewalNoticeRoles: [],
    childExpireNoticeRoles: [],
    childRenewalNoticeRoles: [],

    childTenantNotice: 1,
    noticeType: [PushMessageType.message],
};
const rolesField = [
    `expireNoticeRoles`,
    `renewalNoticeRoles`,
    `childExpireNoticeRoles`,
    `childRenewalNoticeRoles`,
];
const configField = [
    ...rolesField,
    `expireNoticeType`,
    `renewalNoticeType`,
    `childExpireNoticeType`,
    `childRenewalNoticeType`,
    `messagePushSwitch`,
    `emailPushSwitch`,
    `childTenantNotice`,
];

const needFilterParamName = [
    'childExpireNoticeRoles',
    'childRenewalNoticeRoles',
    'expireNoticeRoles',
    'renewalNoticeRoles'
];

function TenantNotifySetting(props: any, ref: any) {
    const { openIsWhen, inherit, templateId } = props;

    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');

    const [tenantForm] = Form.useForm();
    const [subTenantForm] = Form.useForm();
    const [noticeForm] = Form.useForm();
    const [advanceTenantForm] = Form.useForm();

    const [roleOptions, setRoleOptions] = useState<RoleOptionProps[]>([]);
    const [operateStatus, setOperateStatus] = useState({
        tenant: false,
        subTenant: false,
        license: false,
        noticeType: false,
    });
    const [tenantRule, setTenantRule] = useState<string>(''); // 租户管理员ID
    const [subTenantControl, setSubTenantControl] = useState<boolean>(true);
    const [config, setConfig] = useState(initalConfig);
    const [authLanguages, setAuthLanguages] = useState([]);
    const authSelectRoleRef = useRef(null);
    const [noticeType, setNoticeType] = useState(['1']);
    const update = useUpdate();

       const { isAbroadStyle } = useSystemComponentStyle();

    const onNoticeTypeChange = (value) => {
        setNoticeType(value);
    };
    // 初始化
    useEffect(() => {
        getRuleList();
        getConfig();
        getAuthorityLanguageList();
    }, []);
    // 语言类型
    const getAuthorityLanguageList = async () => {
        const { data } = await getTenantTemplateConfig({
            templateId,
            configType: 'language',
        });
        const formatList = (data.list || []).map((item: any) => ({
            value: item?.language,
            label: languageLabelList[item?.language] || item?.language,
        }));
        setAuthLanguages(formatList);
    };

    const handleOuterLoad = () => {
        getConfig();
        setOperateStatus({
            tenant: false,
            subTenant: false,
            license: false,
            noticeType: false,
        });
        openIsWhen && openIsWhen(false);
    };

    useImperativeHandle(ref, () => ({
        resume: handleOuterLoad,
        reload: handleOuterLoad,
    }));
    // 查询配置
    const getConfig = async (key?: string) => {
        const { data: info = {},authRoleInfo = {} } = await getTenantTemplateWithAuthConfig({
            templateId,
            configType: configKey,
        });
        needFilterParamName.forEach(i=>{
            info[i] = filterLockAndNoExistList(info[i],authRoleInfo);
        });
        const data = getFormatData(info);
        setConfig(data);
        if (!key) {
            setNoticeType(data.noticeType || []);
            // 预付租户
            if (isAdvanceTenant()) {
                advanceTenantForm.setFieldsValue(data);
            } else {
                tenantForm.setFieldsValue(data);
                subTenantForm.setFieldsValue(data);
                noticeForm.setFieldsValue(data);
                setSubTenantControl(data.childTenantNotice === 1 ? true : false);
            }
        } else {
            if (key === 'tenant') {
                tenantForm.setFieldsValue(data);
            } else if (key === 'subTenant') {
                subTenantForm.setFieldsValue(data);
                setSubTenantControl(data.childTenantNotice === 1 ? true : false);
            } else if (key === 'noticeType') {
                setNoticeType(data.noticeType || []);
                noticeForm.setFieldsValue(data);
            } else if (key === 'license') {
                advanceTenantForm.setFieldsValue(data);
                setNoticeType(data.noticeType || []);
            }
        }
    };
    const getFormatData = (info: any) => {
        const defaultData = {
            ...initalConfig,
            expireNoticeRoles: [tenantRule],
            renewalNoticeRoles: [tenantRule],
            childExpireNoticeRoles: [tenantRule],
            childRenewalNoticeRoles: [tenantRule],
        };
        if (!Object.keys(info).length) {
            // 初次无数据
            return defaultData;
        } else {
            configField.forEach((item) => {
                if (rolesField.includes(item)) {
                    const oldItems = info[item] && info[item].length ? info[item].split(',') : [];
                    defaultData[item] = oldItems;
                } else {
                    defaultData[item] = info[item] ?? 1;
                }
            });
            // 通知类型
            const noticeType = [];
            if (info.messagePushSwitch === 1) {
                noticeType.push(PushMessageType.message);
            }
            if (info.emailPushSwitch === 1) {
                noticeType.push(PushMessageType.email);
            }
            if (noticeType.length) {
                defaultData.noticeType = noticeType;
            } else {
                defaultData.noticeType = [PushMessageType.message];
            }
            // @ts-ignore
            defaultData.emailLangType = info.emailLangType;
            // @ts-ignore
            defaultData.noticeRemainDays = info.noticeRemainDays || 30;
            return defaultData;
        }
    };

    // 获取角色列表
    const getRuleList = async () => {
        const params = {
            page: 1,
            pageSize: 1e8,
            isOwn: false,
            userId: APP_USER_INFO.userId,
        };
        const { list } = await getRoleListByPage(params);
        const authRoleIds = (list || []).map((item) => item.roleId);

        const { data } = await getTenantTemplateConfig({
            templateId,
            configType: 'role',
            page: 1,
            pageSize: 999999999,
            isOwn: false,
            state: 1,
            roleType: 3,
        });
        const options = (data.list || [])
            .map((item) => {
                if (item?.roleType === 1) {
                    setTenantRule(item.roleId);
                }
                return {
                    label: i18n.t(`@i18n:@role__${item.parentId}`, item.roleName),
                    value: item.parentId,
                    disabled: item.roleType === 1 ? true : false,
                    roleType: item.roleType,
                };
            })
            .filter((item) => authRoleIds.includes(item.value));
        setRoleOptions(options);
    };
    // 编辑
    const handleEdit = (key: string) => {
        setOperateStatus({ ...operateStatus, [key]: true });
        openIsWhen && openIsWhen(true);
    };
    // 取消
    const handleCancel = (key: string) => {
        const newStatus = { ...operateStatus, [key]: false };
        if (
            !newStatus.tenant &&
            !newStatus.subTenant &&
            !newStatus.license &&
            !newStatus.noticeType
        ) {
            openIsWhen && openIsWhen(false);
        }
        setOperateStatus(newStatus);
        if (key === 'tenant') {
            tenantForm.setFieldsValue(config);
        } else if (key === 'subTenant') {
            subTenantForm.setFieldsValue(config);
            setSubTenantControl(config.childTenantNotice === 1 ? true : false);
        } else if (key === 'noticeType') {
            setNoticeType(config.noticeType || ['1']);
            noticeForm.setFieldsValue(config);
        } else {
            setNoticeType(config.noticeType);
            advanceTenantForm.setFieldsValue(config);
        }
    };
    // 保存
    const handleSave = async (type: string) => {
        let formData = {};
        if (type === 'tenant') {
            formData = await tenantForm.validateFields();
        } else if (type === 'subTenant') {
            formData = await subTenantForm.validateFields();
        } else if (type === 'noticeType') {
            formData = await noticeForm.validateFields();
        } else if (type === 'license') {
            formData = await advanceTenantForm.validateFields();
        }
        let reqParams: any = {};
        // 处理接口获取的数据
        for (const key in config) {
            if (config[key] instanceof Array) {
                reqParams[key] = config[key].join(',');
            } else {
                reqParams[key] = config[key];
            }
        }
        // 处理从表单拿到的数据
        for (const key in formData) {
            if (formData[key] instanceof Array) {
                reqParams[key] = formData[key].join(',');
            } else {
                reqParams[key] = formData[key];
            }
        }
        if (type === 'subTenant') {
            reqParams.childTenantNotice = subTenantControl ? 1 : 0;
        }
        if (type === 'license' || type === 'noticeType') {
            // 消息推送
            reqParams.messagePushSwitch = reqParams.noticeType?.includes(PushMessageType.message)
                ? 1
                : 0;
            // 邮件推送
            reqParams.emailPushSwitch = reqParams.noticeType?.includes(PushMessageType.email)
                ? 1
                : 0;
            if(reqParams.noticeType?.includes(PushMessageType.email) && !authLanguages.find(item => item?.value === reqParams.emailLangType)) {
                return message.error(i18n.t('message','语言“{name}“使用权限已被停用，保存失败',{
                    name: languageLabelList[reqParams.emailLangType] || reqParams.emailLangType
                }));
            }
        }
        if (authLanguages) delete reqParams.noticeType;
        if (isAdvanceTenant()) {
            const {
                messagePushSwitch,
                emailPushSwitch,
                noticeRemainDays,
                expireNoticeRoles,
                emailLangType,
            } = reqParams;
            reqParams = {
                messagePushSwitch,
                emailPushSwitch,
                noticeRemainDays,
                expireNoticeRoles,
                emailLangType,
            };
        }
        const params = {
            templateId,
            configType: configKey,
            configData: reqParams,
        };
        const res = await editTenantTemplateConfig(params);
        if (res) {
            const newStatus = { ...operateStatus, [type]: false };
            if (
                !newStatus.tenant &&
                !newStatus.subTenant &&
                !newStatus.license &&
                !newStatus.noticeType
            ) {
                openIsWhen && openIsWhen(false);
            }
            const data = getFormatData(params.configData);
            setConfig(data);
            setOperateStatus(newStatus);
            message.success(i18n.t('message', '保存成功'));
        }
    };

    // 操作按钮
    const getOperateBtns = (key: string, status: boolean) => {
        return status ? (
            <Space size={12}>
                <Button className="button" onClick={() => handleCancel(key)} type="link">
                    {i18n.t('action', '取消')}
                </Button>
                <Button className="button button-save" type="link" onClick={() => handleSave(key)}>
                    {i18n.t('action', '保存')}
                </Button>
            </Space>
        ) : (
            <Button
                onClick={() => handleEdit(key)}
                className="button"
                type="link"
                disabled={inherit}
            >
                {i18n.t('action', '编辑')}
            </Button>
        );
    };
    // 子租户开关状态变化
    const handleSwitchChange = (checked: boolean) => {
        setSubTenantControl(checked);
    };

    return (
        <div className="tenant-message-notify-container-scope">
            {isAdvanceTenant() ? (
                <Container>
                    <Form
                        className="advance-tenant-form"
                        form={advanceTenantForm}
                        layout="vertical"
                    >
                        <div>
                            <div className="card-header">
                                <p className="left-title">
                                    {i18n.t('name', '车辆License到期通知设置')}
                                </p>

                                <p>{getOperateBtns('license', operateStatus?.license)}</p>
                            </div>
                            <div className="left-title-tip">
                                {i18n.t(
                                    'name',
                                    '在车辆License到期之前，系统将每天发送到期提醒通知',
                                )}
                            </div>
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                gap: 8,
                                alignItems: 'center',
                            }}
                        >
                            <StarryAbroadFormItem
                                label={i18n.t('name', '到期时间')}
                                name="noticeRemainDays"
                                className="form-item"
                                required
                                rules={[
                                    {
                                        required: true,
                                        message: i18n.t('message', '到期时间不能为空'),
                                    },
                                ]}
                            >
                                <Select
                                    disabled={!operateStatus.license}
                                    placeholder={i18n.t('message', '请选择到期时间')}
                                    options={expireDays}
                                    getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                                />
                            </StarryAbroadFormItem>
                            <span className="notice-remain-day-unit">{i18n.t('name', '天')}</span>
                        </div>
                        <Form.Item
                            label={i18n.t('name', '通知方式')}
                            name="noticeType"
                            className="form-item"
                            required
                            rules={[
                                {
                                    required: true,
                                    message: i18n.t('message', '通知方式不能为空'),
                                },
                            ]}
                        >
                            <CheckboxGroup
                                disabled={!operateStatus.license}
                                onChange={onNoticeTypeChange}
                            >
                                <div className="notice-type-check-box">
                                    <div className="notice-type-check-box">
                                        <Checkbox value={PushMessageType.message}>
                                            {i18n.t('name', '消息推送')}
                                        </Checkbox>
                                        <Checkbox value={PushMessageType.email}>
                                            {i18n.t('name', '邮件推送')}
                                        </Checkbox>
                                    </div>
                                </div>
                            </CheckboxGroup>
                        </Form.Item>
                        {noticeType?.includes(PushMessageType.email) ? (
                            <StarryAbroadFormItem
                                label={i18n.t('name', '邮件语言')}
                                name="emailLangType"
                                className="form-item"
                                required
                                rules={[
                                    {
                                        required: true,
                                    },
                                ]}
                            >
                                <Select
                                    disabled={!operateStatus.license}
                                    placeholder={i18n.t('message', '请选择邮件语言')}
                                    options={authLanguages}
                                    getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                                />
                            </StarryAbroadFormItem>
                        ) : null}

                        <StarryAbroadFormItem
                            label={
                                <span>
                                    {i18n.t('name', '推送角色')}
                                    <AuthTip show={authSelectRoleRef.current?.getModeAuth} />
                                </span>
                            }
                            name="expireNoticeRoles"
                            className="form-item push-select-form-item"
                            rules={[
                                {
                                    validator: validatorMaxRole,
                                },
                            ]}
                        >
                            <AuthSelect
                                ref={authSelectRoleRef}
                                mode="multiple"
                                placeholder={i18n.t('message', '请选择推送角色')}
                                options={roleOptions}
                                disabled={!operateStatus.license}
                                onInitDone={update}
                                getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                                // @ts-ignore
                                filterOption={(inputValue: string, option: { label: string }) => {
                                    return option?.label
                                        ?.toUpperCase()
                                        ?.includes?.(inputValue?.toUpperCase());
                                }}
                                virtual={false}
                                keepSort={true}
                            />
                        </StarryAbroadFormItem>
                    </Form>
                </Container>
            ) : (
                <>
                    <Container>
                        <Form form={tenantForm} layout="vertical" className="tenant-message-notify">
                            <div className="card-header">
                                <p className="left-title">{i18n.t('name', '通知角色')}</p>
                                <p>{getOperateBtns('tenant', operateStatus?.tenant)}</p>
                            </div>
                            <Form.Item
                                label={i18n.t('name', '到期通知角色')}
                                name="expireNoticeType"
                            >
                                <RoleSelect
                                    notifyType="expireNoticeType"
                                    partRoles="expireNoticeRoles"
                                    edit={operateStatus?.tenant}
                                    form={tenantForm}
                                    roleOptions={roleOptions}
                                />
                            </Form.Item>
                            <Form.Item
                                label={i18n.t('name', '续期通知角色')}
                                name="renewalNoticeType"
                                style={{
                                    marginBottom: 0,
                                }}
                            >
                                <RoleSelect
                                    notifyType="renewalNoticeType"
                                    partRoles="renewalNoticeRoles"
                                    edit={operateStatus?.tenant}
                                    form={tenantForm}
                                    roleOptions={roleOptions}
                                />
                            </Form.Item>
                        </Form>
                    </Container>

                    <Divider className="divider" />
                    <Container>
                        <Form form={subTenantForm} layout="vertical">
                            <div className="card-header sub-tenant-card-header">
                                <p className="left-title">
                                    {i18n.t('name', '开启子租户通知')}
                                    <Switch
                                        className="subtenant-notify-switch"
                                        disabled={!operateStatus.subTenant}
                                        checked={subTenantControl}
                                        onChange={handleSwitchChange}
                                    />
                                </p>
                                <p>{getOperateBtns('subTenant', operateStatus.subTenant)}</p>
                            </div>
                            {subTenantControl && (
                                <div className="card-content">
                                    <RspFormLayout layoutType="auto">
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '到期通知角色')}
                                        name="childExpireNoticeRoles"
                                        style={{
                                            // width: 340,
                                            marginBottom: isAbroadStyle?  '' : 0,
                                            marginTop: 24,
                                        }}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value?.length > MAX_TEMPLATE_USER_ROL_SELECT_NUMBER) {
                                                        return Promise.reject(
                                                            new Error(
                                                                i18n.t(
                                                                    'message',
                                                                    '最多选择{number}个通知角色',
                                                                    { number: MAX_TEMPLATE_USER_ROL_SELECT_NUMBER },
                                                                ),
                                                            ),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <AuthSelect
                                            options={roleOptions}
                                            className="sub-select"
                                            placeholder={i18n.t('message', '请选择角色')}
                                            mode="multiple"
                                            disabled={!operateStatus.subTenant}
                                            showArrow
                                            getPopupContainer={() =>
                                                document.getElementById('root') as HTMLElement
                                            }
                                            filterOption={(inputValue: any, option: any) => {
                                                return option?.label
                                                    ?.toUpperCase()
                                                    ?.includes?.(inputValue?.toUpperCase());
                                            }}
                                            virtual={false}
                                            keepSort={true}
                                        />
                                    </StarryAbroadFormItem>
                                    <StarryAbroadFormItem
                                        label={i18n.t('name', '续期通知角色')}
                                        name="childRenewalNoticeRoles"
                                        style={{
                                            marginBottom: 0,
                                            marginTop: 24,
                                            // width: 340,
                                        }}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value?.length > MAX_TEMPLATE_USER_ROL_SELECT_NUMBER) {
                                                        return Promise.reject(
                                                            new Error(
                                                                i18n.t(
                                                                    'message',
                                                                    '最多选择{number}个通知角色',
                                                                    { number: MAX_TEMPLATE_USER_ROL_SELECT_NUMBER },
                                                                ),
                                                            ),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <AuthSelect
                                            options={roleOptions}
                                            className="sub-select"
                                            placeholder={i18n.t('message', '请选择角色')}
                                            mode="multiple"
                                            disabled={!operateStatus.subTenant}
                                            showArrow
                                            getPopupContainer={() =>
                                                document.getElementById('root') as HTMLElement
                                            }
                                            filterOption={(inputValue: any, option: any) => {
                                                return option?.label
                                                    ?.toUpperCase()
                                                    ?.includes?.(inputValue?.toUpperCase());
                                            }}
                                            virtual={false}
                                            keepSort={true}
                                        />
                                    </StarryAbroadFormItem>
                                    </RspFormLayout>
                                </div>
                            )}
                        </Form>
                    </Container>
                    <Container>
                        <Form form={noticeForm} layout="vertical">
                            <Divider className="divider" />
                            <div className="card-header">
                                <p className="left-title">{i18n.t('name', '通知方式')}</p>
                                <p>{getOperateBtns('noticeType', operateStatus?.noticeType)}</p>
                            </div>
                            <Form.Item
                                style={{
                                    marginBottom: noticeType?.includes(PushMessageType.email)
                                        ? 24
                                        : 0,
                                }}
                                label={i18n.t('name', '通知方式')}
                                name="noticeType"
                                className="form-item"
                                required
                                rules={[
                                    {
                                        required: true,
                                        message: i18n.t('message', '通知方式不能为空'),
                                    },
                                ]}
                            >
                                <CheckboxGroup
                                    disabled={!operateStatus.noticeType}
                                    onChange={onNoticeTypeChange}
                                >
                                    <div className="notice-type-check-box">
                                        <div className="notice-type-check-box">
                                            <Checkbox value={PushMessageType.message}>
                                                {i18n.t('name', '消息推送')}
                                            </Checkbox>
                                            <Checkbox value={PushMessageType.email}>
                                                {i18n.t('name', '邮件推送')}
                                            </Checkbox>
                                        </div>
                                    </div>
                                </CheckboxGroup>
                            </Form.Item>

                            {noticeType?.includes(PushMessageType.email) ? (
                                <RspFormLayout layoutType="auto">
                                <RspFormLayout.SingleRow>
                                <StarryAbroadFormItem
                                    label={i18n.t('name', '邮件语言')}
                                    name="emailLangType"
                                    className="form-item"
                                    style={{ marginBottom: 0 }}
                                    required
                                    rules={[
                                        {
                                            required: true,
                                            message: i18n.t('message', '邮件语言不能为空'),
                                        },
                                    ]}
                                >
                                    <Select
                                        disabled={!operateStatus.noticeType}
                                        placeholder={i18n.t('message', '请选择邮件语言')}
                                        options={authLanguages}
                                        getPopupContainer={(triggerNode: HTMLElement) =>
                                            triggerNode
                                        }
                                    />
                                </StarryAbroadFormItem>
                                </RspFormLayout.SingleRow>
                                </RspFormLayout>
                            ) : null}
                        </Form>
                    </Container>
                </>
            )}
        </div>
    );
}
export default forwardRef(TenantNotifySetting);
