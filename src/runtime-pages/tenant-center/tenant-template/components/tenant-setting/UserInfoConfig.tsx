/*
 * @LastEditTime: 2025-06-30 14:51:15
 */
import { useState, useEffect, useImperativeHandle, useRef, forwardRef } from 'react';
import { i18n } from '@base-app/runtime-lib';
import { filterLockAndNoExistList } from '@/utils/filterLockAndNoExistList';
import { message } from '@streamax/poppy';
import {
    editTenantTemplateConfig,
    getTenantTemplateWithAuthConfig,
} from '@/service/tenant-template';
import BaseUserInfo from '@/runtime-pages/tenant-center/components/user-info-config/BaseUserInfo';
import UserCountSafeInfo from '@/runtime-pages/tenant-center/components/user-info-config/UserCountSafeInfo';
import TemplateAuthRoleSelect from '../TemplateAuthRoleSelect';
import { MAX_PRIVACY_USER_ROLE_COUNT } from '@/runtime-pages/tenant-center/const';
import { MAX_TEMPLATE_USER_ROL_SELECT_NUMBER } from '../../const';

function UserInfoConfig(props: any, ref: any) {
    const { openIsWhen, templateId, inherit } = props;
    const [userInfo, setUserInfo] = useState({});
    const [infoState, setInfoState] = useState({
        base: false,
        safe: false,
    });
    const baseUserInfoRef = useRef();
    const userCountSafeInfoRef = useRef();

    const getConfig = async () => {
        try {
            const { data: info = {}, authRoleInfo = {}, authUserInfo = {} } =
                await getTenantTemplateWithAuthConfig({
                    templateId,
                    configType: 'securityConf',
                });
            info.roleIds = filterLockAndNoExistList(
                info.roleIds,
                authRoleInfo,
            );
            info.userIds = filterLockAndNoExistList(
                info.userIds,
                authUserInfo,
            );
            setUserInfo(info);
        } catch (error) {
        }
    };
    const onStateChange = (type: 'base' | 'safe', state: boolean) => {
        setInfoState((pre) => {
            return { ...pre, [type]: state };
        });
    };
    useEffect(() => {
        openIsWhen?.(infoState.base || infoState.safe);
    }, [infoState]);
    useEffect(() => {
        getConfig();
    }, []);
    const cancelClick = () => {
        baseUserInfoRef.current?.resume?.();
        userCountSafeInfoRef.current?.resume?.();
    };
    useImperativeHandle(ref, () => ({
        resume: cancelClick,
        reload: cancelClick
    }));
    
    const updateUserInfoConfig = async (values) => {
        const params = {
            templateId,
            configType: 'securityConf',
            configData: values
        };
        await editTenantTemplateConfig(params);
    };
    return (
        <div className="user-safe-tab">
            <BaseUserInfo
                ref={baseUserInfoRef}
                userInfo={userInfo}
                reloadData={getConfig}
                onStateChange={onStateChange}
                disabled={inherit}
                updateUserInfoConfig={updateUserInfoConfig}
                {...props}
            />
            <UserCountSafeInfo
                ref={userCountSafeInfoRef}
                userInfo={userInfo}
                disabled={inherit}
                onStateChange={onStateChange}
                reloadData={getConfig}
                updateUserInfoConfig={updateUserInfoConfig}
                maxCount={MAX_TEMPLATE_USER_ROL_SELECT_NUMBER}
                roleSelect={
                    <TemplateAuthRoleSelect
                        templateId={templateId}
                        placeholder={i18n.t('message', '请选择指定角色')}
                        maxCount={MAX_TEMPLATE_USER_ROL_SELECT_NUMBER}
                    />
                }
                {...props}
            />
        </div>
    );
}
export default forwardRef(UserInfoConfig);
