/*
 * @LastEditTime: 2025-06-30 14:46:07
 */
import { i18n } from '@base-app/runtime-lib';

export const DEFAULT_TABLE_DATA = {
    list: [],
};
// 租户模板通知角色最大支持修改为99个
export const MAX_TEMPLATE_USER_ROL_SELECT_NUMBER = 99;
export const formatTranslation = (data: any[]) => {
    return (data || []).map((item) => {
        return {
            langType: item?.langType,
            translation: item?.translationValue,
        };
    });
};
// 重复提示语
export const REPEAT_TIPS = {
    120050036: i18n.t('message', '模板名称重复'),
    120050037: i18n.t('message', '模板编码重复'),
};
export const CHANNEL_LIST = [
    {
        value: '1',
        label: 'CH1',
    },
    {
        value: '2',
        label: 'CH2',
    },
    {
        value: '3',
        label: 'CH3',
    },
    {
        value: '4',
        label: 'CH4',
    },
];
