/*
 * @LastEditTime: 2025-07-01 22:31:57
 */
import { Auth, getAppGlobalData, i18n, mosaicManager, StarryStorage } from '@base-app/runtime-lib';
import DefaultBasicInfo from '@/runtime-pages/tenant-center/tenant-template/base/components/DefaultBasicInfo';
import { SegmentData } from '@/runtime-pages/tenant-center/tenant-template/base/types';
import createStrategyEditAllFactory from '@/runtime-pages/tenant-center/tenant-template/base/createStrategyEditAllFactory';
import { CONFIGURE_TYPE_CHANNEL, INTERNATIONAL_TYPE } from './base';
import { paramListTransfer } from './util';
import DynamicChannelTableForm, { EffectiveChannelModeEnum, PRIVATE_SETTING_CODE } from '@/components/DynamicChannelTableForm';
import { useLocation } from '@base-app/runtime-lib/core';
import ChannelSetting from './components/ChannelSetting';
import { CONFIGURE_TYPE_DEVICE, CONFIGURE_TYPE_FLOW } from '@/runtime-pages/strategy-center/channel-setting/constants';
import { useAsyncEffect } from '@streamax/hooks';
import { useEffect, useRef, useState } from 'react';
const authCode = '@base:@page:tenant.templat:detail:edit.channel';
export default () => {
    const { query } = useLocation();
    const { configureId, detail, templateId, configType } = query || {};
    const [strategyDetail, setStrategyDetail] = useState({});
    const paramListRef = useRef<any[]>([]);
    let channelDetail = {} as any;
    let channelList = [];
    const isDeviceMode = configType === 'channelDeviceSetting';
    const storage = StarryStorage();
    useAsyncEffect(async () => {
        try {
            channelDetail = await storage.getItem(configureId); //JSON.parse(detail);
            if(isDeviceMode) {
                channelDetail.paramList = channelDetail?.configureValueList;
                setStrategyDetail(channelDetail);
                paramListRef.current = channelDetail?.configureValueList;
            } else{
                channelList = channelDetail?.configureValueList?.map((item) => {
                    let channelInfo = {};
                    try {
                        channelInfo = JSON.parse(item.paramValue);
                    } catch (error) {
                        console.error(error);
                    }
                    return { ...channelInfo };
                });
            }
        } catch (error) {
            console.error(error);
        }
    }, [configureId]);
    
    const segmentData: SegmentData[] = [
        {
            key: 'basicInfo',
            segmentName: i18n.t('name', '基本信息'),
            segmentContent: (
                <DefaultBasicInfo
                    type="add"
                    internationalType={INTERNATIONAL_TYPE}
                />
            ),
            bottomDivider: true,
        },
        {
            key: 'channelSetting',
            segmentName: i18n.t('name', '通道设置'),
            segmentContent: isDeviceMode ? (
                <ChannelSetting inDetailPage={false} strategyDetail={strategyDetail} />
            ) : (
                <DynamicChannelTableForm
                    appId={getAppGlobalData('APP_ID')}
                    showOperate={false}
                    operateType="templateEdit"
                    buttonOperateType="link"
                    configureId={configureId}
                    channelList={channelList}
                    configureType={CONFIGURE_TYPE_FLOW}
                    authCodes={{
                        channelTypeCode: authCode,
                    }}
                />
            ),
        },
    ];
    const formatParamData = (params: any) => {
        const data = Array.isArray(params) ? params[1] : params;
        const { channelSettingList = [] } = data || {};
        if(isDeviceMode)  {
            return data;
        };
        return paramListTransfer.toSubmit(channelSettingList?.filter(Boolean));
    };

    const resToFormData = (res: any) => {
        const { description, configureName, configureValueList = [], configureType } = res;
        const basicInfo = {
            configureName,
            description,
        };
        const newParamList = paramListTransfer.toRender(configureValueList, configureType);
        // 故意设置通道的form表单设置不进去，内部组件获取有设置
        if(isDeviceMode) return [basicInfo];
        return [basicInfo, newParamList];
    };

    return createStrategyEditAllFactory(segmentData, CONFIGURE_TYPE_CHANNEL, {
        formatParamData,
        resToFormData,
        internationalType: INTERNATIONAL_TYPE,
    });
};
