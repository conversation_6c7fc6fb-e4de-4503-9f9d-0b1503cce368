import React, { useEffect, useState, useImperativeHandle, useContext, useRef } from 'react';
import { Form, Switch, message, Input, Spin } from '@streamax/poppy';
import { mosaicManager, StarryModal, useSystemComponentStyle } from '@base-app/runtime-lib';
import { i18n, Auth, getAppGlobalData } from '@base-app/runtime-lib';
import { IconRequest } from '@streamax/poppy-icons';
import type { ParamListItem } from '../type';
import { editStrategy } from '@/service/strategy';
import { getAlarmTypeAuthorityPage } from '@/service/alarm';
import CopyLinkSetting from '@/runtime-pages/strategy-center/alarm-linkage/components/step1/CopyLinkSetting';
import { isEqual, isNil, pickBy } from 'lodash';
import './ChannelSetting.scoped.less';
import { paramListTransfer } from './../util';
import { Context } from '../../../../base/context';
import { localeCompare } from '@/utils/alarmTypeSort';
import useUrlState from "@ahooksjs/use-url-state";
import DynamicChannelTableForm, { EffectiveChannelModeEnum, PRIVATE_SETTING_CODE } from '@/components/DynamicChannelTableForm';
import { defaultSetting } from '@/modules/strategy/ChannelStrategy';
import { getDeviceModelList } from '@/service/device';
import { HandleSetting } from '../../add-edit-alarm-handle/type';
import { useDebounceFn } from '@streamax/hooks';
import { Actions } from '@/runtime-pages/strategy-center/channel-setting/constants';
import { fetchParameterDetail } from '@/service/parameter';
const { addPage, copyPage } = Actions;

interface HandleSettingProps {
    activeKey: string;
    openIsWhen?: (flag: boolean) => void;
    inDetailPage: boolean;
    selectAppId?: number;
    strategyDetail: any;
    getDetailData: () => void;
}
export interface AlarmType {
    value: string;
    label: string;
}

type AlarmGroupType = {
    alarmTypeList: AlarmType[];
    alarmTypes: number[];
    config: string;
};

const ChannelSettingComponent = (props: HandleSettingProps, ref: any) => {
    const {
        activeKey,
        openIsWhen,
        inDetailPage,
        selectAppId,
        strategyDetail,
        getDetailData,
    } = props;
    const [form] = Form.useForm();
    const [alarmTypes, setAlarmTypes] = useState<AlarmType[]>([]);
    const [alarmTypeGroup, setAlarmTypeGroup] = useState<AlarmGroupType[]>([]);
    const [filterAlarmTypeGroup, setFilterAlarmTypeGroup] = useState<
        AlarmGroupType[]
    >([]);
    const [currentType, setCurrentType] = useState<string>('');
    const [handleSetting, setHandleSetting] = useState<Record<string, any>>({});
    const [backUpTypeSetting, setBackUpTypeSetting] = useState<
        Record<string, HandleSetting>
    >({});
    const [editing, setEditing] = useState(false);
    const [searchValue, setSearchValue] = useState<string>('');
    const [copySettingVisible, setCopySettingVisible] =
        useState<boolean>(false);
    const [currentTypeInGroup, setCurrentTypeInGroup] =
        useState<boolean>(false); // 当前激活的类型是否在组中
    const [isSave, setIsSave] = useState<boolean>(false);
    const [copyInit, setCopyInit] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const alarmSearchRef = useRef(null);
    const editingRef = useRef(false);
    const channelRef = useRef(null);
    const isShowChanelTypeColumns = Auth.check(addPage.channelType.authCode);
    const { platformOrMix } = mosaicManager.useTenantMosaicTypeParams();
    const showMosaicSetting = Auth.check(PRIVATE_SETTING_CODE) && platformOrMix;
    // 详情策略获取url的appId
    const [searchParams] = useUrlState({ appId: undefined });
    const appId = searchParams?.appId;
    const [channelEnable, setChannelEnable] = useState<boolean>(false);
    useEffect(() => {
        setEditing(false);
        openIsWhen && openIsWhen(false);
    }, [activeKey]);

    useEffect(() => {
        setEditing(false);
        openIsWhen && openIsWhen(false);
    }, [activeKey]);

    useEffect(() => {
        if (!inDetailPage) {
            const result = formatAlarmTypeGroup(alarmTypes, handleSetting);
            setAlarmTypeGroup(result);
            setParsedGroup(searchValue);
        }
    }, [handleSetting]);

    useEffect(() => {
        const data: any = paramListTransfer.toRender(
            strategyDetail?.paramList || [],
            strategyDetail.configureType,
            { showMosaicSetting, channelEnable, isShowChanelTypeColumns },
        );
        setHandleSetting(data);
    }, [strategyDetail?.paramList, channelEnable, showMosaicSetting]);

    useEffect(() => {
        searchAlarmType('');
        const result = formatAlarmTypeGroup(
            alarmTypes,
            paramListTransfer.toRender(
                strategyDetail?.paramList || [],
                strategyDetail.configureType,
                { showMosaicSetting, channelEnable, isShowChanelTypeColumns },
            ),
        );
        setAlarmTypeGroup(result);
    }, [alarmTypes, strategyDetail?.paramList, channelEnable, showMosaicSetting]);
    /**查询优先级配置，平台优先级或设备优先级，平台优先级时需要展示通道使能字段***/
    const fetchTenantMosaicTypeParams = async () => {
        const parameter = await fetchParameterDetail(
            { parameterKey: 'DEVICE.EFFECTIVE.CHANNEL.MODE' },
            false,
        );
        setChannelEnable(
            EffectiveChannelModeEnum.PLAT === Number(parameter?.parameterValue),
        );
    };
    useEffect(() => {
        fetchTenantMosaicTypeParams();
    }, []);
    useEffect(() => {
        setLoading(true);
        // 获取型号类型
        getDeviceModelList({
            filterPermission: true,
            appId: inDetailPage ? Number(appId) : Number(selectAppId || appId),
        })
            .then((data: any) => {
                if (data) {
                    const _data = data;
                    setAlarmTypes(
                        _data.sort((a, b) => localeCompare(a.label, b.label)),
                    );
                    if (_data.length > 0) {
                        const type = _data[0].value;
                        setCurrentType(type);
                    }
                } else {
                    return;
                }
            })
            .finally(() => {
                setLoading(false);
            });
    }, [selectAppId]);

    useEffect(() => {
        const current = {
            ...defaultSetting,
            ...(handleSetting[currentType] || {}),
        };
        form && form.setFieldsValue(current);
        const channelSettingList = current.channelSettingList.filter(
            (item) => item,
        );
        channelRef.current &&
            channelRef.current?.setFieldsValue(channelSettingList || []);
    }, [currentType, handleSetting]);
    // 处理分组
    function formatAlarmTypeGroup(
        list: AlarmType[] = [],
        settings: Record<string, any>,
    ) {
        const settingMap: any = pickBy(settings, (item) => {
            const filterChannelSettingList = (
                item?.channelSettingList || []
            ).filter((item) => item);
            const parseItem = filterChannelSettingList?.map((item) => {
                if (isNil(item.channelTypeId) || !(item.channelTypeId?.toString())) {
                    delete item.channelTypeId;
                }
                if (
                    isNil(item.algorithmModel) || !(item.algorithmModel?.toString()))
                {
                    delete item.algorithmModel;
                }
                return item;
            });
            if (
                Object.keys(item || {}).length &&
                !(
                    isEqual(
                        defaultSetting.channelSettingList.sort(),
                        parseItem.sort(),
                    ) ||
                    isEqual(
                        defaultSetting.vehicleChannelSettingList.sort(),
                        parseItem.sort(),
                    )
                )
            ) {
                return true;
            }
            return false;
        });
        const settingList = [];
        for (const key in settingMap) {
            const value = settingMap[key];
            const alarmType = list.find((item) => item.value == key);
            if (alarmType) {
                settingList.push({
                    ...alarmType,
                    alarmType: key,
                    config: objOrderToString(value),
                });
            }
        }
        // 将相同配置的分类归为同一分组
        const alarmTypesGroup: any[] = [];
        settingList.forEach((item) => {
            const { config, label, value } = item;
            let index = -1;
            const info = alarmTypesGroup.find((atg) => {
                index++;
                return atg.config === item.config;
            });
            // 如果已有此分组，则加入到此分组下
            if (info) {
                alarmTypesGroup[index].alarmTypeList.push({
                    label,
                    value,
                });
                alarmTypesGroup[index].alarmTypes.push(value);
            } else {
                alarmTypesGroup.push({
                    config: config,
                    alarmTypes: [value],
                    alarmTypeList: [
                        {
                            label,
                            value,
                        },
                    ],
                });
            }
        });
        // 默认设置
        const defaultSettingTypes = list.filter((at) => !settingMap[at.value]);
        const result = [...alarmTypesGroup, ...defaultSettingTypes];
        // 需要先对含有alarmTypeList的项对其内部进行排序;以免影响后续的按alarmTypeList首项进行排序
        result.forEach((item: any) => {
            if (item?.alarmTypeList) {
                item.alarmTypeList.sort((aItem, bItem) => {
                    return localeCompare(aItem.label, bItem.label);
                });
            }
        });
        result.sort((a, b) => {
            if (a?.alarmTypeList && b?.alarmTypeList) {
                return localeCompare(
                    a.alarmTypeList[0].label,
                    b.alarmTypeList[0].label,
                );
            } else if (a?.label && b?.label) {
                return localeCompare(a.label, b.label);
            }
            return 0;
        });
        // 默认选择第一个分组的第一项
        const firstType = result[0]?.alarmTypeList?.[0]?.value;
        let tempCurrentTypeInGroup = false;
        if (inDetailPage && firstType !== undefined) {
            if (isSave) {
                const notEdit =
                    result.find((item) => item.config === undefined) ||
                    result[0].alarmTypeList[0];
                setCurrentType(notEdit.value);
                alarmTypesGroup.map((item) => {
                    if (
                        item.config &&
                        item.alarmTypes.includes(notEdit.value)
                    ) {
                        tempCurrentTypeInGroup = true;
                        return;
                    }
                });
                setCurrentTypeInGroup(tempCurrentTypeInGroup);
                setIsSave(false);
            } else {
                setCurrentType(firstType);
                setCurrentTypeInGroup(true);
            }
        }
        return result;
    }
    const sortObjectKeys = (obj, compareFunction) => {
        return Object.keys(obj)
            .sort(compareFunction) // 使用传入的比较函数
            .reduce((acc, key) => {
                acc[key] = obj[key];
                return acc;
            }, {});
    };
    // 将一个对象的所有属性名和值进行排序，然后转为字符串
    function objOrderToString(obj: Record<string, any>) {
        const keySort = Object.keys(obj).sort((a: any, b: any) =>
            a.localeCompare(b),
        );
        const result = {};
        keySort.forEach((key) => {
            let value = obj[key];
            if (Array.isArray(value)) {
                value = value.map((obj) => {
                    return sortObjectKeys(obj, (a, b) => b.localeCompare(a));
                });
            }
            result[key] = value;
        });

        return JSON.stringify(result);
    }
    // 向外暴露的api
    useImperativeHandle(ref, () => ({
        validateFields: validateFields,
        setFieldsValue: setFormValue,
    }));
    //验证抛出
    const validateFields = async () => {
        return await submit();
    };
    // 复制回填数据
    const setFormValue = (data: any) => {
        setHandleSetting(
            paramListTransfer.toRender(data, strategyDetail.configureType),
        );
        setCopyInit(true);
    };
    useEffect(() => {
        copyInit && searchAlarmType('');
    }, [copyInit]);

    const submit = async (modifySameGroup = false) => {
        // 校验当前表单，通过就保存到 handleSetting 并返回
        let values: any = {};
        try {
            values = await form?.validateFields();
            values = filterEmptyData(values);
        } catch (e: any) {
            form?.scrollToField(e.errorFields[0].name[0]);
            throw e;
        }
        const setting = {
            ...handleSetting,
            ...{
                [currentType]: { ...values },
            },
        };
        // 同步修改同配置型号
        if (modifySameGroup) {
            // @ts-ignore
            const { alarmTypes } = findAlarmTypeGroup(currentType);
            if (alarmTypes) {
                alarmTypes.forEach((type: any) => {
                    setting[type] = { ...values };
                });
            }
        }
        setHandleSetting(setting);
        const paramList: ParamListItem[] = paramListTransfer.toSubmit(
            setting,
            strategyDetail.configureType,
            handleSetting,
            { showMosaicSetting, channelEnable, isShowChanelTypeColumns },
        );
        return paramList;
    };

    const detailPageReset = () => {
        setHandleSetting({ ...backUpTypeSetting });
        setEditing(false);
    };

    const selectType = async (item: AlarmType, inGroup: boolean) => {
        if (item.value === currentType) {
            return;
        }
        if (inDetailPage) {
            if (editing) {
                saveModal(() => {
                    detailPageReset();
                    setCurrentType(item.value);
                    setCurrentTypeInGroup(inGroup);
                });
            } else {
                setCurrentTypeInGroup(inGroup);
                setCurrentType(item.value);
            }
        } else {
            const formValue = await form?.validateFields();
            setHandleSetting({
                ...handleSetting,
                ...{
                    [currentType]: filterEmptyData(formValue),
                },
            });
            setCurrentType(item.value);
        }
    };

    // 详情页面的几个操作
    const detailPageEdit = () => {
        const data = JSON.parse(JSON.stringify(handleSetting));
        setBackUpTypeSetting(data);
        setEditing(true);
        editingRef.current = true;
    };

    // 寻找型号类型所在的分组
    function findAlarmTypeGroup(key: number | string) {
        // @ts-ignore
        return alarmTypeGroup.find((item) =>
            (item.alarmTypes || []).includes(key),
        );
    }

    // 复制配置
    async function handleCopyConfig(types: number[]) {
        if (inDetailPage) {
            // 找到分组的配置currentType
            // @ts-ignore
            const { config } = findAlarmTypeGroup(currentType);
            const setting = { ...handleSetting };
            types.forEach((type) => {
                setting[type] = JSON.parse(config);
            });
            const paramList: ParamListItem[] = paramListTransfer.toSubmit(
                setting,
                strategyDetail.configureType,
            );
            submitConfig(paramList);
        } else {
            const formValue = await form?.validateFields();
            const setting = { ...handleSetting };
            types.forEach((type) => {
                setting[type] = filterEmptyData(formValue);
            });
            setting[currentType] = filterEmptyData(formValue);
            setHandleSetting(setting);
        }
        setCopySettingVisible(false);
    }

    const _detailPageSave = async (modifySameGroup?: boolean) => {
        setIsSave(true);
        const paramList = await submit(modifySameGroup);
        submitConfig(paramList);
    };
    const { run: detailPageSave } = useDebounceFn(_detailPageSave, {
        wait: 300,
    });

    // 提交配置
    async function submitConfig(paramList: any[]) {
        try {
            await editStrategy({
                configureId: strategyDetail.configureId || '',
                configureName: strategyDetail.configureName || '',
                configureType: strategyDetail.configureType || '',
                paramList,
            });
            getDetailData();
            message.success(i18n.t('message', '操作成功'));
            setEditing(false);
        } catch (e: any) {
            message.error(e);
        }
    }

    const setParsedGroup = (value: string) => {
        const list = alarmTypes.filter((item: AlarmType) =>
            item.label?.toLowerCase().includes(value.toLowerCase()),
        );
        const result = formatAlarmTypeGroup(
            list,
            inDetailPage
                ? paramListTransfer.toRender(
                      strategyDetail.paramList || [],
                      strategyDetail.configureType,
                  )
                : handleSetting,
        );
        setFilterAlarmTypeGroup(result);
        setSearchValue(value);
        return result;
    };
    // 模糊搜索型号类型
    function searchAlarmType(value: string) {
        const result = setParsedGroup(value);
        const currentItem =
            result[0]?.value || result[0]?.value === 0
                ? result[0]
                : result[0]?.alarmTypeList[0];
        if (currentItem) {
            selectType(currentItem, result[0]?.value ? false : true);
        } else {
            setCurrentType('');
        }
    }

    // 复制禁选项
    function getCopyDisabledKeys() {
        const keys = (findAlarmTypeGroup(currentType) || {}).alarmTypes || [];
        // @ts-ignore
        return keys.concat([currentType]);
    }
    const onFocus = () => {
        if (editing && editingRef.current) {
            alarmSearchRef.current?.blur();
            saveModal(() => {
                editingRef.current = false;
                detailPageReset();
            });
        }
    };
    // 保存确认框
    const saveModal = (onkFun: () => void) => {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '提示'),
            content: i18n.t('message', '您确认不保存当前设置？'),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                onkFun?.();
            },
        });
    };

    // 搜索框
    const renderSearchBar = (
        <div className="search-wrapper">
            <Input
                value={searchValue}
                maxLength={50}
                onFocus={onFocus}
                ref={alarmSearchRef}
                onChange={(e) => searchAlarmType(e.target.value)}
                placeholder={i18n.t('message', '请输入设备型号或设备机型名称')}
            />
        </div>
    );

    const renderAlarmTypeItem = (item: AlarmType, inGroup: boolean) => {
        const { value, label } = item;
        return (
            <li
                key={value}
                className={
                    currentType === value
                        ? 'select-item select-item-active'
                        : 'select-item'
                }
                onClick={() => {
                    selectType(item, inGroup);
                }}
                title={label}
            >
                {label}
            </li>
        );
    };

    const renderAlarmTypeGroup = (
        <div className="left">
            {renderSearchBar}
            <ul className="alarm-select-list">
                {filterAlarmTypeGroup.map((item: any) => {
                    const { alarmTypeList } = item;
                    if (alarmTypeList) {
                        return (
                            <div
                                className="alarm-type-group"
                                key={alarmTypeList
                                    .sort((a, b) =>
                                        localeCompare(a.label, b.label),
                                    )
                                    .map((p: any) => p.value)
                                    .join('-')}
                            >
                                {alarmTypeList.map((item: AlarmType) =>
                                    renderAlarmTypeItem(item, true),
                                )}
                            </div>
                        );
                    }
                    return renderAlarmTypeItem(item, false);
                })}
            </ul>
        </div>
    );
    // 点击复制按钮
    async function handleClickCopy() {
        if (inDetailPage) {
            setCopySettingVisible(true);
        } else {
            try {
                await form?.validateFields();
                setCopySettingVisible(true);
            } catch (err) {
                message.error(i18n.t('message', '表单校验未通过'));
            }
        }
    }

    const handleElement = (
        <div className="channel-setting" id="channel-setting-id">
            <DynamicChannelTableForm
                appId={searchParams.appId}
                showOperate={false}
                ref={channelRef}
                operateType={inDetailPage ? 'customCopy' : 'customAdd'}
                buttonOperateType="link"
                authCodes={{
                    channelTypeCode: inDetailPage
                        ? copyPage.channelType.authCode
                        : addPage.channelType.authCode,
                }}
                channelForm={form}
                channelTypeSelectProps={{
                    getPopupContainer: () =>
                        document.getElementById('channel-setting-id'),
                }}
                mosaicTypeSelectProps={{
                    getPopupContainer: () =>
                        document.getElementById('channel-setting-id'),
                }}
            />
        </div>
    );
    const add = (
        <div className="edit-layout">
            <div className="setting-header">
                <div className="setting-title">
                    {i18n.t('name', '设备型号')}
                </div>
                <div>
                    {currentType != '' ? (
                        <a
                            onClick={handleClickCopy}
                            style={{ marginRight: '24px' }}
                        >
                            {i18n.t('action', '复制')}
                        </a>
                    ) : null}
                </div>
            </div>
            <div className="flex">
                {renderAlarmTypeGroup}
                {currentType !== '' ? (
                    handleElement
                ) : (
                    <div className="push-setting null-data">
                        <img src={require('@/assets/img_empty.png')} />
                        <p>{i18n.t('message', '暂无数据')}</p>
                    </div>
                )}
            </div>
        </div>
    );
    const filterEmptyData = (data) => {
        return {
            channelSettingList: (data.channelSettingList || []).filter(
                (item) => item,
            ),
        };
    };
    const renderOperate = () => {
        return currentType == '' ? null : (
            <div>
                {currentTypeInGroup && (
                    <Auth code="@base:@page:channel.setting@action:tab.channel.setting:edit">
                        <a
                            onClick={handleClickCopy}
                            style={{ marginRight: '24px' }}
                        >
                            {i18n.t('action', '复制')}
                        </a>
                    </Auth>
                )}
                <Auth code="@base:@page:channel.setting@action:tab.channel.setting:edit">
                    <a
                        onClick={() => {
                            openIsWhen && openIsWhen(true);
                            detailPageEdit();
                        }}
                        // @ts-ignore
                        disabled={currentType !== '' ? false : true}
                    >
                        {i18n.t('action', '编辑')}
                    </a>
                </Auth>
            </div>
        );
    };

    const edit = (
        <div className="edit-layout">
            <div className="setting-header">
                <span className="setting-title">
                    {i18n.t('name', '设备型号')}
                </span>
                {editing ? (
                    <div>
                        <a
                            onClick={() => {
                                openIsWhen && openIsWhen(false);
                                detailPageReset();
                            }}
                            style={{ marginRight: '24px' }}
                        >
                            {i18n.t('action', '取消')}
                        </a>
                        <a
                            onClick={() => {
                                openIsWhen && openIsWhen(false);
                                detailPageSave();
                            }}
                        >
                            {i18n.t('action', '保存')}
                        </a>
                        {currentTypeInGroup && (
                            <a
                                onClick={() => {
                                    openIsWhen && openIsWhen(false);
                                    detailPageSave(true);
                                }}
                                style={{ marginLeft: '24px' }}
                            >
                                {i18n.t('action', '同步修改同配置型号')}
                            </a>
                        )}
                    </div>
                ) : (
                    renderOperate()
                )}
            </div>
            <div className="flex">
                {renderAlarmTypeGroup}
                {currentType !== '' ? (
                    handleElement
                ) : (
                    <div className="null-data">
                        <img src={require('@/assets/img_empty.png')} />
                        <p>{i18n.t('message', '暂无数据')}</p>
                    </div>
                )}
            </div>
        </div>
    );
    return (
        <Spin spinning={loading}>
            <div className="channel-mode-setting">
                {inDetailPage ? edit : add}
                <CopyLinkSetting
                    alarmTypeList={alarmTypes}
                    visible={copySettingVisible}
                    placeholder={i18n.t('message', '请选择设备型号')}
                    checkText={i18n.t('name', '已选型号')}
                    label={i18n.t('name', '设备型号')}
                    disabledKeys={getCopyDisabledKeys()}
                    onOk={handleCopyConfig}
                    onCancel={() => setCopySettingVisible(false)}
                />
            </div>
        </Spin>
    );
};
export default React.forwardRef(ChannelSettingComponent);
