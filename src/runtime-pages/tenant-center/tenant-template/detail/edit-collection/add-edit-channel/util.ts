/*
 * @LastEditTime: 2025-07-01 22:31:41
 */
import { CONFIGURE_TYPE } from '@/hooks/useChannelData/constants';
import type { ParamListItem, channelItem } from './type';
import { CONFIGURE_TYPE_DEVICE } from '@/runtime-pages/strategy-center/channel-setting/constants';
import { ParamItem } from '@/modules/strategy/types';
import { isNil } from 'lodash';

/**通道前缀 */
const CHANNEL_PREFIX = 'channel_';

// 转换paramList参数
export const paramListTransfer = {
    toSubmit: (setting: Record<string, any>, configureType: number, oldData: Record<string, any> = {}, otherConfig?: Record<string, any>) => {
        const newParamList: ParamListItem[] = [];
        if (configureType == CONFIGURE_TYPE_DEVICE) {
             /**组装老数据，需要还原老数据的起停用**/ 
            const { showMosaicSetting, isShowChanelTypeColumns } = otherConfig || {};
            Object.keys(setting).forEach((alarmType) => {
                const itemVal: any = setting[alarmType];
                const oldDataItem = oldData?.[alarmType]?.channelSettingList || [];
                Object.keys(itemVal).forEach((key) => {
                    const paramValue = itemVal[key] || [];
                    const formatParamValue = paramValue?.map((item, index) => {
                        const oldItem = oldDataItem?.[index];
                        const enable = item.enable ?? oldItem?.enable ?? 1;
                        let channelTypeId = item.channelTypeId;
                        let algorithmModel = item.algorithmModel;
                        // 没有展示马赛克类型时去要取老值
                        if(!isNil(showMosaicSetting) && !showMosaicSetting) {
                            algorithmModel = oldItem?.algorithmModel;
                        }
                        // 没有展示马赛克类型时去要取老值
                        if(!isNil(showMosaicSetting) && !isShowChanelTypeColumns) {
                            channelTypeId = oldItem?.channelTypeId;
                        }
                        return {
                            ...item,
                            channelTypeId,
                            algorithmModel,
                            enable,
                        };
                    });
                    // 转为字符串
                    if (alarmType) {
                        newParamList.push({
                            paramName: alarmType, //设备机型名称
                            paramValue: JSON.stringify(formatParamValue),
                        });
                    }
                });
            });
            return newParamList;
        } else {
            setting.forEach((item: channelItem) => {
                newParamList.push({
                    paramName: `${CHANNEL_PREFIX}${item?.channelNumber}`,
                    paramValue: JSON.stringify({
                        ...item,
                    }),
                });
            });
        }

        return newParamList;
    },
    toRender: (
        params: { paramName: string; paramValue: string }[],
        configureType: number,
        otherConfig: Record<string, any>,
    ) => {
        try {
            const { showMosaicSetting, isShowChanelTypeColumns, channelEnable } = otherConfig || {};
            const setting: any = {};
            if (configureType == CONFIGURE_TYPE_DEVICE) {
                params.forEach((item: ParamItem) => {
                    const { paramName } = item;
                    let paramValue = [];
                    try {
                        paramValue = JSON.parse(item.paramValue);
                    } catch (error) {
                        console.error(error);
                    }
                    
                    const value = (paramValue || []).map((item: any) => {
                        item.enable = item.enable ?? 1;
                        if(!showMosaicSetting) {
                            delete item.algorithmModel;
                        }
                        if(!isShowChanelTypeColumns) {
                            delete item.channelTypeId;
                        }
                        return item;
                    });
                    if (!setting.hasOwnProperty(paramName)) {
                        setting[paramName as string] = {
                            channelSettingList: value,
                        };
                    }
                });
                return setting;
            }
            return (params || []).map((item) => {
                const data = JSON.parse(item.paramValue || '{}');
                return data;
            });
        } catch (err) {
            console.error('params format error', err);
        }
    },
};
