import {
    Form,
    NewDatePicker as DatePickerComponent,
    InputNumber,
    Input,
    Radio,
    Tooltip,
    Divider,
    Container,
    message,
} from '@streamax/poppy';
import type { FormInstance } from '@streamax/poppy/lib/form';
import { IconInformation } from '@streamax/poppy-icons';
import {
    getAppGlobalData,
    i18n,
    usePasswordConfig,
    utils,
    useSystemComponentStyle,
    StarryAbroadFormItem as AFormItem,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import {
    OverflowEllipsisContainer,
    PasswordInput,
    InfoPanel,
    Descriptions,
} from '@streamax/starry-components';
// @ts-ignore
import { AreaSelection } from '@base-app/runtime-lib';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { checkFieldSpace, formatToNearestHalf, getOnlyOneSpaceBetweenWords, validateMinSixLen } from '@/utils/commonFun';
import { DetailContentItem } from '@/runtime-pages/tenant-center/components/TenantInfo';
import './index.scoped.less';
import isAdvanceTenant from '@/utils/license-common';
import { getTenantTotalVehicleSpace } from '@/service/meal';
import { VEHICLE_SPACE_SIZE } from '@/utils/constant';
import { RspFormLayout } from '@streamax/responsive-layout';

interface OptionItemProps {
    label: string;
    value: any;
}

const FormItem = Form.Item;
const { TextArea } = Input;
const validatorIllegalCharacter = utils.validator.illegalCharacter;
const validatorUserName = utils.validator.validateBaseUser;
const DatePicker: any = DatePickerComponent;

interface BaseInfoProps {
    tenantId?: string;
    form: FormInstance;
    fatherTenantInfo: any;
    deviceLimit: number;
    usedDeviceCount: number;
    deviceLimitType: number;
    storageSetMealOpen: boolean;
    adminList: any;
    detail: any;
}

export default function BaseInfo(props: BaseInfoProps) {
    const {
        tenantId,
        form,
        fatherTenantInfo,
        deviceLimit,
        usedDeviceCount,
        deviceLimitType,
        storageSetMealOpen,
        adminList,
        detail,
    } = props;
    useEffect(() => {
        getTenantVehicleStore();
    }, [detail]);
    const { pwdRules } = usePasswordConfig();
    const [totalVehicleStore, setTotalVehicleStore] = useState<number>(0);
    const { isAbroadStyle } = useSystemComponentStyle();
    const maxDevice = [
        {
            label: i18n.t('name', '无限制'),
            value: 0,
        },
        {
            label: i18n.t('name', '限制'),
            value: 1,
        },
    ];

    const expireTypes = [
        {
            label: i18n.t('state', '永久有效'),
            value: 1,
        },
        {
            label: i18n.t('state', '自定义'),
            value: 2,
        },
    ];
    const disabledDate = (currentDate: any) => {
        return moment().isAfter(currentDate.format('YYYY-MM-DD'));
    };
    const validatorConfimPwd = (rule: any, value: any) => {
        if (!value || form.getFieldValue('password') === value) {
            return Promise.resolve();
        }
        return Promise.reject(i18n.t('message', '两次输入的密码不一致'));
    };

    const validatorCodeTel = (rule: any, value: any = {}) => {
        const { areaCode, phone } = value;
        if (!areaCode) {
            return Promise.reject(i18n.t('message', '请选择国家或地区'));
        }
        if (!phone) {
            return Promise.reject(i18n.t('message', '请输入联系方式'));
        }
        if (!/^[0-9-]+$/.test(phone)) {
            return Promise.reject(i18n.t('message', '请输入正确的联系方式'));
        }
        if (/^[-]+$/.test(phone)) {
            return Promise.reject(i18n.t('message', '请输入正确的联系方式'));
        }
        if (phone && (phone.length < 6 || phone.length > 15)) {
            return Promise.reject(i18n.t('message', '请输入6~15个字符'));
        }
        return Promise.resolve();
    };

    const checkSpaceName = (rule: any, value: any) => {
        return checkFieldSpace(value, i18n.t('message', '姓名不能为空'));
    };
    const getMaxDeviceNumber = () => {
        // 编辑时最大设备数要加上子租户的最大设备数
        if (tenantId) {
            return fatherTenantInfo.deviceLimit - fatherTenantInfo.usedDeviceCount;
        }
        // 新增时最大设备数只跟父租户有关
        return fatherTenantInfo.deviceLimit - fatherTenantInfo.usedDeviceCount;
    };

    const TipText = () => {
        let tipText = '';
        // 父租户限制
        if (fatherTenantInfo.deviceLimitType) {
            // 编辑时展示
            if (tenantId) {
                tipText = i18n.t(
                    'name',
                    '子租户已使用{used}个设备，可给子租户最多分配{max}个设备',
                    {
                        used: usedDeviceCount,
                        max: fatherTenantInfo.deviceLimit - fatherTenantInfo.usedDeviceCount,
                    },
                );
            } else {
                tipText = i18n.t('name', '当前可分配{max}个设备', {
                    max: fatherTenantInfo.deviceLimit - fatherTenantInfo.usedDeviceCount,
                });
            }
        } else if (tenantId && deviceLimitType) {
            tipText = i18n.t('name', '子租户已使用{num}个设备', {
                num: usedDeviceCount,
            });
        }
        if (isAbroadStyle) {
            return (
                <div className="deviceLimit-used-tip">{tipText}</div>
            );
        }
        return (
            tipText && (
                <span
                    className={`deviceLimit-used-tip ${
                        fatherTenantInfo.deviceLimitType ? 'form-column-one' : ''
                    }`}
                >
                    <IconInformation
                        style={{ marginLeft: '8px' }}
                        className="sub-add-default-icon-information"
                    />
                    <OverflowEllipsisContainer>{tipText}</OverflowEllipsisContainer>
                </span>
            )
        );
    };
    const renderMaxLimitDevice = () => {
        return fatherTenantInfo?.deviceLimitType ? (
            <AFormItem
                name="deviceLimit"
                label={
                    <>
                        {i18n.t('name', '最大设备数')}
                        {tenantId ? (
                            <StarryAbroadIcon>
                                <Tooltip
                                    title={i18n.t('name', '限制的设备数量不能小于租户已使用的数量')}
                                    className="question-icon"
                                >
                                    <IconInformation
                                        className="sub-add-default-icon-information"
                                        style={{ marginLeft: '8px' }}
                                    />
                                </Tooltip>
                            </StarryAbroadIcon>
                        ) : null}
                    </>
                }
                rules={[
                    {
                        required: true,
                        message: i18n.t('name', '最大设备数不能为空'),
                    },
                ]}
                style={{
                    marginBottom:0
                }}
            >
                <InputNumber
                    defaultValue={1}
                    min={(() => {
                        if (tenantId) {
                            return usedDeviceCount;
                        }
                        return getMaxDeviceNumber() == 0 ? 0 : 1;
                    })()}
                    max={getMaxDeviceNumber()}
                    precision={0}
                    placeholder={i18n.t('name', '请输入设备数量')}
                />
            </AFormItem>
        ) : (
            <AFormItem
                className="usefulForm radio-type-form-item"
                name="deviceLimitType"
                label={
                    <>
                        {i18n.t('name', '最大设备数')}
                        {deviceLimitType || !tenantId ? (
                            <Tooltip
                                title={
                                    tenantId
                                        ? i18n.t('name', '限制的设备数量不能小于租户已使用的数量')
                                        : i18n.t('name', '保存后不能修改选项，请谨慎选择')
                                }
                                className="question-icon"
                            >
                                <IconInformation
                                    className="sub-add-default-icon-information"
                                    style={{ marginLeft: '8px' }}
                                />
                            </Tooltip>
                        ) : null}
                    </>
                }
                rules={[
                    {
                        required: true,
                        message: i18n.t('name', '最大设备数不能为空'),
                    },
                ]}
            >
                <Radio.Group disabled={!!tenantId}>
                    {maxDevice.map(({ value, label }) => (
                        <Radio key={value} value={value}>
                            <OverflowEllipsisContainer>{label}</OverflowEllipsisContainer>
                        </Radio>
                    ))}
                </Radio.Group>
            </AFormItem>
        );
    };
    const tenantManageItems: DetailContentItem[] = [
        {
            label: i18n.t('name', '用户名'),
            content: adminList ? adminList[0]?.account || '-' : '-',
        },
        {
            label: i18n.t('name', '邮箱'),
            content: (adminList && adminList[0]?.email) ?? '-',
        },
        {
            label: i18n.t('name', '联系方式'),
            content: (adminList && adminList[0]?.phoneNumber) ?? '-',
        },
        {
            label: i18n.t('name', '姓名'),
            content: adminList ? adminList[0]?.userName || '-' : '-',
        },
    ];
    const generateDetail = (items: DetailContentItem[]) => (
        <Descriptions className="tenant-detail-content">
            {items.map(({ label, content, ellipsis, column = 1 }, index) => (
                <Descriptions.Item column={column} ellipsis={ellipsis} key={index} label={label}>
                    {content}
                </Descriptions.Item>
            ))}
        </Descriptions>
    );
    
    const getTenantVehicleStore = async () => {
        const totalStore = await getTenantTotalVehicleSpace({
            tenantId: getAppGlobalData('APP_USER_INFO').tenantId
        });
        setTotalVehicleStore(Number(totalStore));
    };
    const onStoreBlur = (e) => {
        const value = e.target.value;
        if (value) {
            let roundedValue = formatToNearestHalf(value);
            roundedValue = roundedValue < VEHICLE_SPACE_SIZE.MIN ? VEHICLE_SPACE_SIZE.MIN : roundedValue;
            roundedValue = roundedValue > totalVehicleStore ? totalVehicleStore : roundedValue;
            if(value < VEHICLE_SPACE_SIZE.MIN || value > totalVehicleStore) {
                form.setFieldsValue({
                    vehicleStorageSpace: roundedValue,
                });
            } else {
                // 延迟设置，保证设置时在组件自身格式化之后，然后去覆盖他
                setTimeout(() => {
                    form.setFieldsValue({
                        vehicleStorageSpace: roundedValue,
                    });
                }, 0);
            }
        }
    };

    const onTenantCodeBlur = (e) => {
        const value = e.target.value;
        form.setFieldsValue({
            ...form.getFieldsValue(),
            tenantCode: value.toUpperCase(),
        });
    }

    return (
        <div className="tenant-base-info">
            <Container>
                <div className={`form-title ${tenantId ? 'form-title-edit' : ''}`}>
                    {i18n.t('name', '基本信息')}
                </div>
                <div className="form-content-row">
                    <RspFormLayout layoutType='auto'>
                    <AFormItem
                        validateFirst
                        name="tenantName"
                        label={i18n.t('name', '子租户名称')}
                        rules={[
                            {
                                required: true,
                            },
                            {
                                min: 1,
                                type: 'string',
                            },
                            {
                                max: 50,
                                type: 'string',
                            },
                            {
                                whitespace: true,
                            },
                            {
                                validator: validatorIllegalCharacter,
                            },
                        ]}
                    >
                        <Input
                            allowClear
                            placeholder={i18n.t('name', '请输入租户名称')}
                            maxLength={50}
                        />
                    </AFormItem>
                    <AFormItem
                        validateFirst
                        name="tenantCode"
                        label={i18n.t('name', '子租户ID')}
                        rules={[
                            {
                                required: true,
                            },
                            {
                                min: 2,
                                type: 'string',
                            },
                            {
                                max: 50,
                                type: 'string',
                            },
                            {
                                whitespace: true,
                            },
                            {
                                pattern: /^[a-zA-Z0-9]+$/,
                                message: i18n.t('message', '只能输入大、小写英文和数字'),
                            },
                        ]}
                    >
                        <Input
                            allowClear
                            placeholder={i18n.t('message', '请输入子租户ID')}
                            disabled={!!tenantId}
                            maxLength={50}
                            onBlur={onTenantCodeBlur}
                        />
                    </AFormItem>
                    <RspFormLayout.SingleRow>
                    <AFormItem name="description" label={i18n.t('name', '描述')}>
                        <TextArea
                            allowClear
                            showCount
                            maxLength={500}
                            placeholder={i18n.t('message', '请输入描述')}
                        />
                    </AFormItem>
                    </RspFormLayout.SingleRow>
                    </RspFormLayout>
                </div>
            </Container>
            {
            !tenantId
             && (
                <Container>
                    {!isAbroadStyle && <Divider style={{ margin: '42px 0 36px' }} />}
                    <div className="form-title form-title-tenant-manager">
                        {i18n.t('name', '租户管理员')}
                    </div>
                    <div className="form-content">
                        <div className="form-content-row">
                            <RspFormLayout layoutType='auto'>
                            <AFormItem
                                validateFirst
                                name="account"
                                label={i18n.t('name', '用户名')}
                                rules={[
                                    {
                                        required: true,
                                    },
                                    {
                                        validator: validatorUserName,
                                    },
                                    {
                                        min: 6,
                                        type: 'string',
                                    },
                                    {
                                        max: 50,
                                        type: 'string',
                                    },
                                    { validator: validateMinSixLen },
                                ]}
                                getValueFromEvent={(e) => {
                                    const value = e.target.value;
                                    return getOnlyOneSpaceBetweenWords(value);
                                }}
                            >
                                <Input
                                    allowClear
                                    placeholder={i18n.t('message', '请输入用户名')}
                                    maxLength={50}
                                />
                            </AFormItem>
                            <AFormItem
                                validateFirst
                                name="email"
                                label={i18n.t('name', '邮箱')}
                                rules={[
                                    {
                                        required: true,
                                    },
                                    {
                                        validator: utils.pattern.generalValidateUserEmail,
                                    },
                                    {
                                        type: 'email',
                                        message: i18n.t('message', '邮箱格式错误'),
                                    },
                                    {
                                        min: 1,
                                        type: 'string',
                                    },
                                    {
                                        max: 50,
                                        type: 'string',
                                    },
                                ]}
                            >
                                <Input
                                    allowClear
                                    placeholder={i18n.t('message', '请输入邮箱')}
                                    maxLength={50}
                                />
                            </AFormItem>
                            <AFormItem
                                validateFirst
                                name="name"
                                label={i18n.t('name', '姓名')}
                                rules={[
                                    {
                                        required: true,
                                        validator: checkSpaceName,
                                    },
                                    {
                                        min: 1,
                                        type: 'string',
                                    },
                                    {
                                        max: 50,
                                        type: 'string',
                                    },
                                    {
                                        validator: utils.validator.illegalCharacter,
                                    },
                                ]}
                            >
                                <Input
                                    allowClear
                                    placeholder={i18n.t('message', '请输入姓名')}
                                    maxLength={50}
                                />
                            </AFormItem>
                            <AFormItem
                                validateFirst
                                name="password"
                                label={i18n.t('name', '密码')}
                                rules={[
                                    {
                                        required: true,
                                    },
                                    {
                                        whitespace: true,
                                    },
                                    ...pwdRules,
                                ]}
                            >
                                <PasswordInput
                                    allowClear
                                    placeholder={i18n.t('message', '请输入密码')}
                                    autoComplete="new-password"
                                    maxLength={20}
                                />
                            </AFormItem>
                            <AFormItem
                                validateFirst
                                name="confirmPassword"
                                dependencies={['password']}
                                label={i18n.t('name', '确认密码')}
                                rules={[
                                    {
                                        required: true,
                                    },
                                    {
                                        validator: validatorConfimPwd,
                                    },
                                ]}
                            >
                                <PasswordInput
                                    allowClear
                                    autoComplete="new-password"
                                    placeholder={i18n.t('message', '请再次输入密码')}
                                />
                            </AFormItem>
                            <AFormItem
                                validateFirst
                                name="codeTel"
                                label={i18n.t('name', '联系方式')}
                                rules={[
                                    {
                                        required: true,
                                    },
                                    { validator: validatorCodeTel },
                                ]}
                            >
                                {/* @ts-ignore */}
                                <AreaSelection />
                            </AFormItem>
                            </RspFormLayout>
                        </div>
                    </div>
                </Container>
            )}
            {tenantId && (
                <InfoPanel
                    title={i18n.t('name', '租户管理员')}
                    className="tenant-manage-info-container"
                >
                    {generateDetail(tenantManageItems)}
                </InfoPanel>
            )}

            {isAdvanceTenant() ? null : (
                <Container>
                    {!isAbroadStyle && <Divider style={{ margin: '16px 0 36px' }} />}
                    <div className="form-title form-title-base-config">
                        {i18n.t('name', '基础配置')}
                    </div>
                    <div className="form-content-row">
                        <RspFormLayout layoutType='fixed'>
                            <RspFormLayout.Col>
                            <AFormItem
                            className="radio-type-form-item"
                            name="expireType"
                            label={i18n.t('name', '租户有效期')}
                            rules={[
                                {
                                    required: true,
                                },
                            ]}
                        >
                            <Radio.Group>
                                {expireTypes.map(({ value, label }) => (
                                    <Radio key={value} value={value}>
                                        <OverflowEllipsisContainer>
                                            {label}
                                        </OverflowEllipsisContainer>
                                    </Radio>
                                ))}
                            </Radio.Group>
                        </AFormItem>
                        <FormItem
                            noStyle
                            shouldUpdate={(prevValues, curValues) =>
                                curValues.expireType !== prevValues.expireType
                            }
                        >
                            {({ getFieldValue }) =>
                                getFieldValue('expireType') === 2 ? (
                                    <AFormItem
                                        name="expireTime"
                                        label={i18n.t('name', '有效期至')}
                                        rules={[
                                            {
                                                required: true,
                                            },
                                        ]}
                                    >
                                        <DatePicker
                                            showNow={false}
                                            showTime
                                            disabledDate={disabledDate}
                                            style={{width:'100%'}}
                                            placeholder={i18n.t('message', '请选择有效期')}
                                        />
                                    </AFormItem>
                                ) : null
                            }
                        </FormItem>
                        {renderMaxLimitDevice()}
                        {!fatherTenantInfo?.deviceLimitType ? (
                            <FormItem
                                noStyle
                                shouldUpdate={(prevValues, curValues) =>
                                    curValues.deviceLimitType !== prevValues.deviceLimitType
                                }
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('deviceLimitType') === 1 ? (
                                        <AFormItem
                                            name="deviceLimit"
                                            label={
                                                <>
                                                    <div>{i18n.t('name', '设备数量')}</div>
                                                    {isAbroadStyle && <TipText />}
                                                </>
                                            }
                                            rules={[
                                                {
                                                    required: true,
                                                    message: i18n.t('message', '设备数量不能为空'),
                                                },
                                            ]}
                                        >
                                            {/* @ts-ignore */}
                                            <InputNumber
                                                min={tenantId ? usedDeviceCount : 1}
                                                max={999999}
                                                precision={0}
                                                placeholder={i18n.t('name', '请输入设备数量')}
                                            />
                                        </AFormItem>
                                    ) : null
                                }
                            </FormItem>
                        ) : null}
                        {!isAbroadStyle && <TipText />}
                            </RspFormLayout.Col>
                        </RspFormLayout>
                    </div>
                </Container>
            )}
            {
            storageSetMealOpen && !isAdvanceTenant() ? (
                <Container>
                    {!isAbroadStyle && <Divider style={{ margin: '16px 0 36px' }} />}
                    <div className="form-title form-title-tenant-manager">
                        {i18n.t('name', '存储空间')}
                    </div>
                    <div className="form-content">
                        <div className="form-content-row">
                            <RspFormLayout layoutType='fixed'>
                            <RspFormLayout.Col>
                            <AFormItem
                                validateTrigger="onBlur"
                                name="vehicleStorageSpace"
                                label={i18n.t('name', '单车存储空间')}
                                rules={[
                                    {
                                        required: true,
                                    }
                                ]}
                            >
                                <InputNumber
                                    style={{width: '100%'}}
                                    step={0.5}
                                    min={VEHICLE_SPACE_SIZE.MIN}
                                    precision={1}
                                    max={totalVehicleStore}
                                    onBlur={onStoreBlur}
                                    placeholder={i18n.t('message', '请输入单车存储空间')}
                                    addonAfter={i18n.t('name', 'GB')}
                                />
                            </AFormItem>
                                </RspFormLayout.Col>
                            </RspFormLayout>
                        </div>
                    </div>
                </Container>
            ) : null}
        </div>
    );
}
