import {
    Button,
    Checkbox,
    Form,
    message,
    Select,
    Space,
    Switch,
    Tooltip,
} from '@streamax/poppy';
import {
    Auth,
    getAppGlobalData,
    i18n,
    StarryAbroadFormItem as AFormItem,
    StarryAbroadFormItem,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import { InfoPanel } from '@streamax/starry-components';
import React, {
    useState,
    useEffect,
    useImperativeHandle,
    useRef,
    forwardRef,
} from 'react';
import type { SafetyWay } from '../CustomWays';
import CustomWays from '../CustomWays';
import { getRoleListByPage } from '@/service/role';
import { setUserInfoConfig } from '@/service/tenant';
import { useAsyncEffect, useDebounceFn, useUpdate } from '@streamax/hooks';
import { getTenantParamsSetting } from '@/service/parameter';
import { MailTypeEnum } from '@/const/mail';
import { RspFormLayout } from '@streamax/responsive-layout';
import './index.scoped.less';
import { IconInformationFill } from '@streamax/poppy-icons';
import { MAX_USER_ROL_SELECT_NUMBER_V2 } from '@/utils/constant';
import SwitchWithTips from '../SwitchWithTips/SwitchWithTips';
type AuthSelectItem = { value: string };
interface SafetyConfirmProps {
    emailConfig?: 0 | 1;
}

const ValidateWaysMap: Record<string, number> = {
    phone: 0,
    email: 1,
};
// 免验证默认30分钟
const DEFAULT_SECURITY_MODE_VALIDITY_MINUTES = 30;
const expireTimeList = [
    {
        value: 30,
        label: 30,
    },
    {
        value: 60,
        label: 60,
    },
    {
        value: 90,
        label: 90,
    },
    {
        value: 120,
        label: 120,
    },
];
export const enum FormField {
    securityVerification = 'securityVerification',
    userRoleConfig = 'userRoleConfig',
    /**
     * 指定用户：0-关闭 1-开启
     * **/
    specifyUserConfig = 'specifyUserConfig',
    roleIds = 'roleIds',
    /**
     * 指定用户列表，多个用英文逗号隔开
     * **/
    userIds = 'userIds',
    /**
     * 是否允许信任设备：0-不允许 1-允许
     * **/
    allowTrustDevice = 'allowTrustDevice',
    /**
     * 操作保护开关：0-关闭 1-开启
     * **/
    operationProtection = 'operationProtection',
    securityModeValidityMinutes = 'securityModeValidityMinutes',
}
/**角色的类型枚举值***/
export enum RoleTypeEnum {
    /**租户管理员*/
    TENANT = 1,
    /**应用管理员*/
    APP, //
    /**功能管理员*/
    FEATURE,
}
interface UserSafeInfoConfigProps {
    userInfo: Record<string, any>;
    onStateChange?: (type: 'base' | 'safe', state: boolean) => void;
    reloadData: () => void;
    updateUserInfoConfig: (params: Record<string, any>) => void;
    roleSelect?: React.Component;
    userSelect?: React.Component;
    disabled?: boolean;
    requestAdminRole?: boolean;
    maxCount?: number;
}
function UserSafeInfoConfig(props: UserSafeInfoConfigProps, ref: any) {
    const {
        onStateChange,
        disabled,
        roleSelect,
        userSelect,
        userInfo,
        reloadData,
        requestAdminRole,
        updateUserInfoConfig,
        maxCount
    } = props;
    const [editing, setEditing] = useState(false);
    const [tenantRule, setTenantRule] = useState<string>('');
    const [safetyForm] = Form.useForm();
    const [subForm] = Form.useForm();

    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');
    const [safetyConfig, setSafetyConfig] = useState<SafetyConfirmProps>();
    const [confirmWay, setConfirmWay] = useState<SafetyWay>();

    const [rulesOptions, setRulesOptions] = useState<
        { label: string; value: string; disabled?: boolean }[]
    >([]);
    const maxUserRoleCount = maxCount ?? MAX_USER_ROL_SELECT_NUMBER_V2;
    const hasPhoneAuth = Auth.check(
        '@base:@page:tenant.detail@action:tab.tenant.config:mobile.security',
    );
    const getTenantRuleValueToForm = () => {
        return tenantRule ? [tenantRule] : [];
    };
    useEffect(() => {
        requestAdminRole && getRuleList();
    }, [requestAdminRole]);

    const validatorMaxRole = (_, value: AuthSelectItem[]) => {
        const LIMIT_NUM = maxUserRoleCount;
        if (value?.length > LIMIT_NUM) {
            return Promise.reject(
                new Error(
                    i18n.t('message', '最多选择{number}个角色', {
                        number: LIMIT_NUM,
                    }),
                ),
            );
        }
        return Promise.resolve();
    };

    const validatorMaxUser = (_, value: AuthSelectItem[]) => {
        const LIMIT_NUM = maxUserRoleCount;
        if (value?.length > LIMIT_NUM) {
            return Promise.reject(
                new Error(
                    i18n.t('message', '最多选择{number}个用户', {
                        number: LIMIT_NUM,
                    }),
                ),
            );
        }
        return Promise.resolve();
    };

    useEffect(() => {
        initConfig();
    }, [userInfo]);

    useEffect(() => {
        const roleIds = parseIdsToArray(
            userInfo?.roleIds,
            getTenantRuleValueToForm(),
        );
        safetyForm.setFieldsValue({
            roleIds: roleIds,
        });
    }, [rulesOptions, tenantRule]);

    // 获取角色列表
    const getRuleList = async () => {
        const params = {
            page: 1,
            pageSize: 1e8,
            isOwn: false,
            userId: APP_USER_INFO.userId,
            filterByVisitor: true,
            state: 1,
            roleType: RoleTypeEnum.TENANT, // 租户管理员
        };
        const { list } = await getRoleListByPage(params);
        const options = (list || []).map((item) => {
            if (item.roleType === RoleTypeEnum.TENANT) {
                setTenantRule(item.roleId);
            }
            return {
                label: i18n.t(`@i18n:@role__${item.roleId}`, item.roleName),
                value: item.roleId,
                disabled: item.roleType === RoleTypeEnum.TENANT ? true : false,
            };
        });
        setRulesOptions(options);
    };
    const parseIdsToArray = (ids: string, defaultIds: string[] = []) => {
        return ids
            ? ([...new Set([...ids?.split(','), ...defaultIds])]).map((item) => {
                  return {
                      value: item,
                  };
              })
            : (defaultIds || []).map((item) => {
                  return {
                      value: item,
                  };
              });
    };
    const initConfig = async () => {
        try {
            const ways: string[] = [];
            let defaultWay = '';
            for (const key in ValidateWaysMap) {
                if (
                    String(userInfo?.verificationType)?.includes(
                        ValidateWaysMap[key],
                    )
                ) {
                    ways.push(key);
                }
                if (ValidateWaysMap[key] === userInfo?.defaultType) {
                    defaultWay = key;
                }
            }
            !ways.length && ways.push('email');
            !defaultWay.length && (defaultWay = 'email');
            const roleIds = parseIdsToArray(
                userInfo?.roleIds,
                getTenantRuleValueToForm(),
            );
            const userIds = parseIdsToArray(userInfo?.userIds);
            safetyForm.setFieldsValue({
                safeConfirmWays: hasPhoneAuth
                    ? {
                          ways: ways || ['email'],
                          default: defaultWay || 'email',
                      }
                    : {
                          ways: ['email'],
                          default: 'email',
                      },
                roleIds: roleIds,
                securityVerification: userInfo?.securityVerification || 0,
                userRoleConfig: userInfo?.userRoleConfig || 0,
                emailConfig: userInfo?.emailConfig || 0,
                allowTrustDevice: userInfo?.allowTrustDevice ?? 1,
                operationProtection: userInfo?.operationProtection || 0,
                specifyUserConfig: userInfo?.specifyUserConfig || 0,
                userIds: userIds,
                securityModeValidityMinutes:
                    userInfo.securityModeValidityMinutes ||
                    DEFAULT_SECURITY_MODE_VALIDITY_MINUTES,
            });
            subForm.setFieldsValue({
                emailType: userInfo.emailType || MailTypeEnum.SMTP,
                senderEmail: userInfo.senderEmail,
                password: userInfo.password,
                sender: userInfo.sender,
                smtpAddress: userInfo.smtpServer,
                port: userInfo.port,
                encryptType:
                    userInfo.encryptType === 0 || userInfo.encryptType
                        ? userInfo.encryptType
                        : '',
                userName: userInfo.userName,
            });
            setSafetyConfig({
                emailConfig: userInfo?.emailConfig || 0,
            });
        } catch (error) {
        }
    };
    // 取消编辑
    function handleEditCancel() {
        initConfig();
        onStateChange?.('safe', false);
        setEditing(false);
    }
    const { run: handleSave } = useDebounceFn(_handleSave, {wait: 300});
    // 保存编辑
    async function _handleSave() {
        const safety = await safetyForm.validateFields();
        let email;
        if (safetyConfig?.emailConfig && safety.securityVerification) {
            email = await subForm.validateFields();
            email = { ...email, smtpServer: email.smtpAddress };
            delete email.smtpAddress;
        }
        if (!email) {
            email = {
                emailType: userInfo.emailType || MailTypeEnum.SMTP,
                senderEmail: userInfo.senderEmail,
                password: userInfo.password,
                sender: userInfo.sender,
                smtpServer: userInfo.smtpServer,
                port: userInfo.port,
                encryptType:
                    userInfo.encryptType === 0 || userInfo.encryptType
                        ? userInfo.encryptType
                        : '',
                userName: userInfo.userName,
            };
        }
        let roleIds =
            typeof safety.roleIds === 'string'
                ? safety.roleIds
                : safety.roleIds?.map((item) => item.value ?? item)?.join();
        if (!roleIds) {
            roleIds = userInfo.roleIds;
        }
        let ways = safety.safeConfirmWays?.ways
            .map((way: string | number) => {
                return ValidateWaysMap[way];
            })
            ?.join();
        if (!ways) {
            ways = userInfo.verificationType;
        }
        const params = {
            ...userInfo,
            securityVerification: safety?.securityVerification,
            userRoleConfig: safety?.userRoleConfig ?? userInfo.userRoleConfig,
            roleIds,
            verificationType: hasPhoneAuth ? ways : '1',
            defaultType:
                hasPhoneAuth && confirmWay?.default === 'phone' ? 0 : 1,
            ...email,
            emailConfig: safetyConfig?.emailConfig,
            allowTrustDevice:
                safety.allowTrustDevice ?? userInfo.allowTrustDevice,
            operationProtection: safety.operationProtection,
            specifyUserConfig:
                safety.specifyUserConfig ?? userInfo.specifyUserConfig,
            securityModeValidityMinutes:
                safety.securityModeValidityMinutes ??
                userInfo.securityModeValidityMinutes,
            userIds:
                (safety.userIds || [])
                    .map((item) => item.value ?? item)
                    .join(',') || userInfo.userIds,
        };
        try {
            await updateUserInfoConfig(params);
            reloadData();
            message.success(i18n.t('message', '设置成功'));
            setEditing(false);
            onStateChange?.('safe', false);
        } catch (error) {
            onStateChange?.('safe', false);
            setEditing(false);
        }
    }

    useImperativeHandle(ref, () => ({
        resume: handleEditCancel,
    }));

    const actionNodes = !editing ? (
        <Button
            type="link"
            disabled={disabled}
            onClick={() => {
                onStateChange?.('safe', true);
                setEditing(true);
            }}
            style={{
                padding: 0,
                height: 'unset',
                fontWeight: 'normal',
            }}
        >
            {i18n.t('action', '编辑')}
        </Button>
    ) : (
        <>
            <a onClick={handleEditCancel}>{i18n.t('action', '取消')}</a>
            <a onClick={handleSave}>{i18n.t('action', '保存')}</a>
        </>
    );
    // 验证方式改变
    const handleConfirmWayChange = (confirmWays: SafetyWay) => {
        setConfirmWay(confirmWays);
    };

    const forceUpdate = useUpdate();

    useAsyncEffect(async () => {
        const emailType =
            (await getTenantParamsSetting({ parameterKey: 'emailType' })) ||
            MailTypeEnum.SMTP;
        subForm.setFieldsValue({ emailType });
        forceUpdate();
    }, []);
    function handleSwitchValue(checked: boolean) {
        return checked ? 1 : 0;
    }
    return (
        <InfoPanel
            title={i18n.t('name', '账号保护配置')}
            extraRight={actionNodes}
            className="tenant-center-user-safety-config"
            operationWrap
        >
            <div className="login-safety-confirm">
                <div className="login-safety-confirm-title-tip">
                    {i18n.t(
                        'message',
                        '*用户需要通过手机或邮箱进行安全验证，请确保用户信息已关联正确的邮箱和手机号',
                    )}
                </div>
                {
                    <div className="safety-config-container">
                        <Form
                            form={safetyForm}
                            layout="vertical"
                            className="safety-confirm-form"
                            colon={false}
                        >
                            <Space
                                className="config-switch-row"
                                size={16}
                                align={'center'}
                            >
                                <span className="config-switch-row-title">
                                    {i18n.t('name', '登录保护')}
                                </span>
                                <Form.Item
                                    name={FormField.securityVerification}
                                    valuePropName="checked"
                                    noStyle
                                    getValueFromEvent={handleSwitchValue}
                                >
                                    <SwitchWithTips
                                        disabled={
                                            !editing
                                        }
                                        tooltip={i18n.t(
                                            'message',
                                            '开启后，用户登录时需要安全验证',
                                        )}
                                    />
                                </Form.Item>
                            </Space>

                            <Form.Item
                                dependencies={['securityVerification']}
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    if (!getFieldValue('securityVerification'))
                                        return null;
                                    return (
                                        <Form.Item
                                            dependencies={[
                                                FormField.securityVerification,
                                            ]}
                                            noStyle
                                        >
                                            {({ getFieldValue }) => {
                                                if (
                                                    !getFieldValue(
                                                        FormField.securityVerification,
                                                    )
                                                )
                                                    return null;
                                                return (
                                                    <>
                                                        <div className="login-check-box-tip">
                                                            <Form.Item
                                                                name={
                                                                    FormField.allowTrustDevice
                                                                }
                                                                noStyle
                                                                valuePropName="checked"
                                                                getValueFromEvent={(
                                                                    e,
                                                                ) =>
                                                                    e.target
                                                                        .checked
                                                                        ? 1
                                                                        : 0
                                                                }
                                                            >
                                                                <Checkbox
                                                                    disabled={
                                                                        !editing
                                                                    }
                                                                >
                                                                    {i18n.t(
                                                                        'message',
                                                                        '允许用户选择信任登录设备，身份验证通过后同设备7天内免二次安全验证登录',
                                                                    )}
                                                                </Checkbox>
                                                            </Form.Item>
                                                        </div>
                                                        <div className="form-item-user-wrap">
                                                            <div>
                                                                <div className="form-item-user-wrap-title">
                                                                    {i18n.t(
                                                                        'name',
                                                                        '用户范围',
                                                                    )}
                                                                </div>
                                                                <div className="login-safety-confirm-title-tip">
                                                                    {i18n.t(
                                                                        'message',
                                                                        '*默认所有用户都要进行安全验证，支持单独指定需要安全验证的角色和用户',
                                                                    )}
                                                                </div>
                                                            </div>
                                                            <div className="form-item-user">
                                                                <Space
                                                                    size={16}
                                                                    align={
                                                                        'center'
                                                                    }
                                                                    className="config-switch-select-row"
                                                                >
                                                                    <span className="config-switch-row-title">
                                                                        {i18n.t(
                                                                            'name',
                                                                            '指定角色',
                                                                        )}
                                                                    </span>
                                                                    <Form.Item
                                                                        name={
                                                                            FormField.userRoleConfig
                                                                        }
                                                                        valuePropName="checked"
                                                                        noStyle
                                                                        getValueFromEvent={
                                                                            handleSwitchValue
                                                                        }
                                                                    >
                                                                        <SwitchWithTips
                                                                            disabled={
                                                                                !editing
                                                                            }
                                                                            tooltip={i18n.t(
                                                                                'message',
                                                                                '指定角色关联的用户需要进行安全验证，租户管理员必须安全验证',
                                                                            )}
                                                                        />
                                                                    </Form.Item>
                                                                </Space>
                                                                <Form.Item
                                                                    dependencies={[
                                                                        FormField.userRoleConfig,
                                                                    ]}
                                                                    noStyle
                                                                >
                                                                    {({
                                                                        getFieldValue,
                                                                    }) => {
                                                                        if (
                                                                            !getFieldValue(
                                                                                FormField.userRoleConfig,
                                                                            )
                                                                        ) {
                                                                            return null;
                                                                        }
                                                                        return (
                                                                            <RspFormLayout layoutType="fixed">
                                                                                <RspFormLayout.Col>
                                                                                    <StarryAbroadFormItem
                                                                                        name={
                                                                                            FormField.roleIds
                                                                                        }
                                                                                        className="custom-form-item auth-select-form-item-wrap"
                                                                                        rules={[
                                                                                            {
                                                                                                required:
                                                                                                    true,
                                                                                                message:
                                                                                                    i18n.t(
                                                                                                        'name',
                                                                                                        '指定角色不能为空',
                                                                                                    ),
                                                                                            },
                                                                                            {
                                                                                                validator:
                                                                                                    validatorMaxRole,
                                                                                            },
                                                                                        ]}
                                                                                        style={{
                                                                                            marginBottom: 0,
                                                                                        }}
                                                                                    >
                                                                                       {roleSelect &&
                                                                                        React.cloneElement(
                                                                                            roleSelect,
                                                                                            {
                                                                                                disabled:
                                                                                                    !editing,
                                                                                            },
                                                                                        )}
                                                                                    </StarryAbroadFormItem>
                                                                                </RspFormLayout.Col>
                                                                            </RspFormLayout>
                                                                        );
                                                                    }}
                                                                </Form.Item>
                                                            </div>
                                                            {!userSelect ? null : (
                                                                <div className="form-item-user">
                                                                    <Space
                                                                        size={
                                                                            16
                                                                        }
                                                                        align={
                                                                            'center'
                                                                        }
                                                                        className="config-switch-select-row"
                                                                    >
                                                                        <span className="config-switch-row-title">
                                                                            {i18n.t(
                                                                                'name',
                                                                                '指定用户',
                                                                            )}
                                                                        </span>
                                                                        <Form.Item
                                                                            name={
                                                                                FormField.specifyUserConfig
                                                                            }
                                                                            valuePropName="checked"
                                                                            noStyle
                                                                            getValueFromEvent={
                                                                                handleSwitchValue
                                                                            }
                                                                        >
                                                                            <SwitchWithTips
                                                                                disabled={
                                                                                    !editing
                                                                                }
                                                                                tooltip={i18n.t(
                                                                                    'message',
                                                                                    '指定用户需要进行安全验证',
                                                                                )}
                                                                            />
                                                                        </Form.Item>
                                                                    </Space>
                                                                    <Form.Item
                                                                        dependencies={[
                                                                            FormField.specifyUserConfig,
                                                                        ]}
                                                                        noStyle
                                                                    >
                                                                        {({
                                                                            getFieldValue,
                                                                        }) => {
                                                                            if (
                                                                                !getFieldValue(
                                                                                    FormField.specifyUserConfig,
                                                                                )
                                                                            )
                                                                                return null;
                                                                            return (
                                                                                <RspFormLayout layoutType="fixed">
                                                                                    <RspFormLayout.Col>
                                                                                        <StarryAbroadFormItem
                                                                                            name={
                                                                                                FormField.userIds
                                                                                            }
                                                                                            className='auth-select-form-item-wrap'
                                                                                            style={{
                                                                                                marginBottom: 0,
                                                                                            }}
                                                                                            rules={[
                                                                                                {
                                                                                                    required:
                                                                                                        true,
                                                                                                    message:
                                                                                                        i18n.t(
                                                                                                            'message',
                                                                                                            '指定用户不能为空',
                                                                                                        ),
                                                                                                },
                                                                                                {
                                                                                                    validator:
                                                                                                        validatorMaxUser,
                                                                                                },
                                                                                            ]}
                                                                                        >
                                                                                            {userSelect &&
                                                                                                React.cloneElement(
                                                                                                    userSelect,
                                                                                                    {
                                                                                                        disabled:
                                                                                                            !editing,
                                                                                                    },
                                                                                                )}
                                                                                        </StarryAbroadFormItem>
                                                                                    </RspFormLayout.Col>
                                                                                </RspFormLayout>
                                                                            );
                                                                        }}
                                                                    </Form.Item>
                                                                </div>
                                                            )}
                                                        </div>
                                                    </>
                                                );
                                            }}
                                        </Form.Item>
                                    );
                                }}
                            </Form.Item>

                            <Space
                                size={16}
                                className="config-switch-row"
                                align={'center'}
                            >
                                <span className="config-switch-row-title">
                                    {i18n.t('name', '操作保护')}
                                </span>
                                <Form.Item
                                    name={FormField.operationProtection}
                                    valuePropName="checked"
                                    noStyle
                                    getValueFromEvent={handleSwitchValue}
                                >
                                    <SwitchWithTips
                                        disabled={
                                            !editing
                                        }
                                        tooltip={i18n.t(
                                            'message',
                                            '开启后，用户在平台进行敏感操作时需要安全验证',
                                        )}
                                    />
                                </Form.Item>
                            </Space>
                            <Form.Item
                                dependencies={[FormField.operationProtection]}
                                noStyle
                            >
                                {({ getFieldValue }) => {
                                    if (
                                        !getFieldValue(
                                            FormField.operationProtection,
                                        )
                                    )
                                        return null;
                                    return (
                                        <>
                                            <RspFormLayout layoutType="fixed">
                                                <RspFormLayout.Col>
                                                    <div className="expire-time">
                                                        <AFormItem
                                                            label={
                                                                <Space>
                                                                    {i18n.t(
                                                                        'name',
                                                                        '免验证有效期',
                                                                    )}
                                                                    <Tooltip
                                                                        title={i18n.t(
                                                                            'message',
                                                                            '从用户最近一次安全验证的时间开始算起',
                                                                        )}
                                                                        placement="right"
                                                                    >
                                                                        <span>
                                                                            <StarryAbroadIcon>
                                                                                <IconInformationFill className="tips-icon-information" />
                                                                            </StarryAbroadIcon>
                                                                        </span>
                                                                    </Tooltip>
                                                                </Space>
                                                            }
                                                            required
                                                            className="expire-day-item"
                                                            name={
                                                                FormField.securityModeValidityMinutes
                                                            }
                                                        >
                                                            <Select
                                                                disabled={
                                                                    !editing
                                                                }
                                                                options={
                                                                    expireTimeList
                                                                }
                                                                placeholder={i18n.t(
                                                                    'message',
                                                                    '请选择免验证有效期',
                                                                )}
                                                            />
                                                        </AFormItem>
                                                        <span className="limit-time-unit">
                                                            {i18n.t(
                                                                'name',
                                                                '分',
                                                            )}
                                                        </span>
                                                    </div>
                                                </RspFormLayout.Col>
                                            </RspFormLayout>
                                           
                                        </>
                                    );
                                }}
                                 
                                 
                            </Form.Item>
                             <Form.Item
                                dependencies={[FormField.operationProtection, FormField.securityVerification]}
                                noStyle
                            >
                            {({ getFieldValue }) => {
                                    if (
                                        !getFieldValue(
                                            FormField.operationProtection,
                                        ) && !getFieldValue(
                                            FormField.securityVerification,
                                        )
                                    )
                                        return null;
                                    return (
                                        <>
                                           <AFormItem
                                                label={i18n.t(
                                                    'name',
                                                    '安全验证方式',
                                                )}
                                                name="safeConfirmWays"
                                                className="safe-confirm-ways"
                                                valuePropName="value"
                                                rules={[
                                                    { required: true },
                                                    {
                                                        validator: (
                                                            _,
                                                            value,
                                                        ) => {
                                                            if (
                                                                !value?.ways
                                                                    ?.length
                                                            ) {
                                                                return Promise.reject(
                                                                    new Error(
                                                                        i18n.t(
                                                                            'message',
                                                                            '请选择安全验证方式',
                                                                        ),
                                                                    ),
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    },
                                                ]}
                                                initialValue={{
                                                    ways: ['email', 'phone'],
                                                    default: 'email',
                                                }}
                                            >
                                                <CustomWays
                                                    onChange={
                                                        handleConfirmWayChange
                                                    }
                                                    disabled={!editing}
                                                />
                                            </AFormItem>
                                        </>
                                    );
                                }}
                            </Form.Item>
                        </Form>
                    </div>
                }
            </div>
        </InfoPanel>
    );
}
export default forwardRef(UserSafeInfoConfig);
