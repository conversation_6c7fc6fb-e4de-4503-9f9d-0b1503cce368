import React, { useRef, useEffect, useState } from 'react';
import './index.less';
import {
    i18n,
    StarryAbroadIcon,
    useSystemComponentStyle,
    utils,
} from '@base-app/runtime-lib';
import {
    IconDownloadFill,
    IconDetailsFill,
    IconEyeOpen,
} from '@streamax/poppy-icons';
import { Col, Row, Tooltip, message, Space } from '@streamax/poppy';
import { numFormat } from '@/utils/commonFun';
import { useSize, useDebounceEffect, useLockFn } from '@streamax/hooks';
import white_block from '@/assets/white_block.png';
import html2Canvas from 'html2canvas';
import JsPDF from 'jspdf';
// @ts-ignore
import { saveAs } from 'file-saver';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { Action } from '@base-app/runtime-lib'; // 使用公共组件
import { getTargetCurrency } from '@/utils/bill';
import { zipSync } from 'fflate';
import { fetchFileDownloadUrl } from '@/service/gss';
import moment from 'moment';
import { deviceBillExport } from '@/service/tenant';
import type { ExcelHeader } from '@/service/types';
import {withSharePropsHOC} from "@streamax/page-sharing-core";
import { getCustomJsx } from '@/utils/pageReuse';

/**定制复用 */
export interface CardShareProps {
    getCardHeader: (header: React.ReactNode[])=> React.ReactNode[];
};
/**定制复用 */

type AmountItemType =
    | boolean
    | React.ReactChild
    | React.ReactFragment
    | React.ReactPortal
    | null
    | undefined;

const Card = (props: { info: any; tenantType?: number } & CardShareProps)=> {
    /**定制复用 */
    const {getCardHeader} = props;
    /**定制复用 */

    const { info, tenantType } = props;

    const ref = useRef<any>();

    const {
        companyInfo,
        tenantAddress,
        billSettle,
        billTable,
        bankInfo,
        tenantServiceInfo,
        invoice,
        customerInfo,
        billId,
    } = info;
    const {
        list = [],
        subTotal,
        taxTotal,
        discount,
        taxSwitch,
    } = billTable || {};
    const {
        logo,
        simpleName,
        address,
        addressDistrict,
        addressDetail,
        website,
    } = companyInfo || {};
    const { shippingAddress, billingAddress } = tenantAddress || {};
    const { settleAmount, dueData, billPeriod, currency } = billSettle || {};

    const { beneficiaryBank, beneficiary, bankAC, routingNumber, bankAddress } =
        bankInfo || {};
    const { serviceTelNum, serviceMail } = tenantServiceInfo || {};
    const { billNumber, billMonth } = invoice || {};
    const { taxId, taxSystem, customerCompanyName } = customerInfo || {};
    const size = useSize(ref);

    const { isAbroadStyle } = useSystemComponentStyle();

    const currencyChar = getTargetCurrency(currency)?.char;

    const pdfGenerate = async () => {
        const title = `${i18n.t('name', 'invoice编号')}-${billNumber}`;
        const dom = document.getElementById(
            `pdfDom-${billNumber}`,
        ) as HTMLElement;
        // hidden_dom.appendChild(dom);
        const canvas = await html2Canvas(dom, {
            allowTaint: true,
            scale: 2,
        });
        // hidden_dom.removeChild(dom);
        const margin = 40;
        const contentWidth = canvas.width;
        const contentHeight = canvas.height;
        const pdfWidth = 595.28;
        const pdfHight = isAbroadStyle ? 851.89 : 841.89;
        const pageHeight = (contentWidth / pdfWidth) * pdfHight;
        const BLOCK_40 = (contentWidth / pdfWidth) * margin;
        let lastHeight = contentHeight;
        let position = margin;
        const imgWidth = pdfWidth - margin * 2;
        const imgHeight =
            (pdfWidth / contentWidth) * contentHeight - margin * 2;
        const pageData = canvas.toDataURL('image/jpeg', 1.0);
        const PDF = new JsPDF(undefined, 'pt', 'a4');
        if (lastHeight < pageHeight) {
            PDF.addImage(pageData, 'JPEG', margin, margin, imgWidth, imgHeight);
        } else {
            while (lastHeight > 0) {
                PDF.addImage(
                    pageData,
                    'JPEG',
                    margin,
                    position,
                    imgWidth,
                    imgHeight,
                );

                PDF.addImage(white_block, 'PNG', 0, 0, pdfWidth, 20);

                PDF.addImage(
                    white_block,
                    'PNG',
                    0,
                    pdfHight - margin,
                    pdfWidth,
                    margin,
                );

                lastHeight -= pageHeight - BLOCK_40 * 2;

                position -= pdfHight - margin * 2;

                if (lastHeight > 0) {
                    PDF.addPage();
                }
            }
        }
        PDF.setProperties({
            title: title,
        });
        return {
            title,
            PDF,
        };
    };

    const mergeZipDownLoad = async () => {
        const excelHeader: ExcelHeader[] = [
            {
                columnName: 'no',
                title: i18n.t('name', '序号'),
                index: 0,
                isOption: false,
            },
            {
                columnName: 'deviceNo',
                title: i18n.t('name', '设备编号'),
                index: 1,
            },
            {
                columnName: 'vehicleNumber',
                title: i18n.t('name', '关联车牌号'),
                index: 2,
            },
            {
                columnName: 'fleetNames',
                title: i18n.t('name', '归属车组'),
                index: 3,
            },
            {
                columnName: 'vehicleState',
                title: i18n.t('name', '车辆状态'),
                index: 4,
            },
            {
                columnName: 'tenantCode',
                title: i18n.t('name', '归属租户'),
                index: 5,
            },
            {
                columnName: 'activatedDate',
                title: i18n.t('name', '激活日期'),
                index: 6,
            },
            {
                columnName: 'firstOnlineTime',
                title: i18n.t('name', '首次上线时间'),
                index: 7,
            },
        ];
        const fileName = `${i18n.t('name', '设备清单')}-${billNumber}.xlsx`;
        const pdfDataPromise = pdfGenerate();
        const excelExportPromise = deviceBillExport({
            billId,
            fileName: `${fileName}`,
            excelType: 'XLSX',
            sheetNames: [
                i18n.t('name', '计费设备'),
                i18n.t('name', '未计费设备'),
            ],
            excelHeader,
        });
        const [pdfData, fileId] = await Promise.all([
            pdfDataPromise,
            excelExportPromise,
        ]);
        const data = fileId
            ? await fetchFileDownloadUrl({
                  fileIdList: fileId,
              })
            : undefined;
        const fileUrl = data?.[0]?.fileUrl;
        if (fileUrl) {
            const response = await fetch(fileUrl, { method: 'GET' });
            const excelBlob = await response.blob();
            const excelArrayBuffer = await excelBlob.arrayBuffer();
            const excelUint8Array = new Uint8Array(excelArrayBuffer);
            const { PDF, title } = pdfData || {};
            const pdfBlob = PDF.output('blob');
            const pdfArrayBuffer = await pdfBlob.arrayBuffer();
            const pdfUint8Array = new Uint8Array(pdfArrayBuffer);
            const files = {
                [`${title}.pdf`]: pdfUint8Array,
                [fileName]: excelUint8Array,
            };
            const zipArrayBuffer = zipSync(files);
            const currentTimestamp = moment().unix();
            const zipBlob = new Blob([zipArrayBuffer], {
                type: 'application/zip',
            });
            saveAs(zipBlob, `${billNumber}_${currentTimestamp}`);
        } else {
            message.error(i18n.t('message', '文件获取失败'));
        }
    };

    const download = useLockFn(async (type: 'download' | 'look') => {
        if (!Object.keys(info).length) {
            message.error(i18n.t('message', '暂无数据'));
            return;
        }
        //前端导出pdf
        try {
            if (type === 'look') {
                await openPdfInNewWindow();
            } else {
                //预付租户没有账单直接下载pdf
                if (tenantType) {
                    await mergeZipDownLoad();
                    return;
                }
                const { PDF, title } = await pdfGenerate();
                PDF.save(title + '.pdf');
            }
        } catch (error) {
            //下载出错目前不处理除了后端报错
        }
    });

    const openPdfInNewWindow = async () => {
        try {
            const { PDF } = await pdfGenerate();
            const newWindow = window.open(PDF.output('bloburl'), '_blank');

            const csp = (window as any).APP_CONFIG['content.security.policy'];
            if (newWindow && csp == '1') {
                newWindow.onload = () => {
                    try {
                        if (newWindow.document.body) {
                            newWindow.document.body.style.height = '100vh';
                            newWindow.document.body.style.margin = '0';
                        }
                    } catch (error) {
                        console.error(
                            'Failed to modify new window content:',
                            error,
                        );
                    }
                };
            } else {
                console.error(
                    'Failed to open new window or CSP is not enabled.',
                );
            }
        } catch (error) {
            console.error('Error generating PDF or opening new window:', error);
        }
    };

    const cardHeader = (
        <div className="header">
                <span className="left">
                    <OverflowEllipsisContainer tooltip={{title:billNumber}}>
                        {i18n.t('name', 'invoice编号')}:{billNumber}
                    </OverflowEllipsisContainer>
                </span>
            <Space size={utils.constant.BASE_TABLE_OPERATE_COLUMN_SIZE}>
                {tenantType ? (
                    <Action
                        code="@base:@page:tanant.settlement.bill@action:device.detail"
                        url="/tenant-settlement-bill/device-detail"
                        fellback={''}
                        params={{
                            billId,
                        }}
                    >
                        <a className="download-icon">
                            <Tooltip title={i18n.t('action', '设备明细')}>
                                    <span>
                                        <StarryAbroadIcon>
                                            <IconDetailsFill />
                                        </StarryAbroadIcon>
                                    </span>
                            </Tooltip>
                        </a>
                    </Action>
                ) : null}
                <a
                    className="download-icon"
                    onClick={() => download('download')}
                >
                    <Tooltip title={i18n.t('action', '下载')}>
                            <span>
                                <StarryAbroadIcon>
                                    <IconDownloadFill />
                                </StarryAbroadIcon>
                            </span>
                    </Tooltip>
                </a>
            </Space>
        </div>
    );

    return (
        <div className="tenant-manage-bill-card-container">
            {getCustomJsx(getCardHeader, [cardHeader], {
                info,tenantType,
            })}
            <div className="pdf-container">
                <div className="pdf" ref={ref} style={{ height: size?.height }}>
                    <div className="block_height_48"></div>
                    <div className="pdf-wraper">
                        {/* <div id="hidden_pdf_dom" className="pdf-wraper-dom"></div> */}
                        <div className="pdfDom" id={`pdfDom-${billNumber}`}>
                            <div className="account-wrapper">
                                <div className="logo">
                                    <div>
                                        <img src={logo} />
                                    </div>
                                    <div className="invoice">
                                        <div>{'invoice'}</div>
                                        <div>{billNumber}</div>
                                        <div>{billMonth}</div>
                                    </div>
                                </div>
                                <div className="enterprise-info">
                                    <div>
                                        {'Corporate Name'}:{customerCompanyName}
                                    </div>
                                    <div>
                                        {'Tax ID'}:{taxId || '-'}
                                    </div>
                                    <div>
                                        {'Tax System'}:{taxSystem}
                                    </div>
                                </div>
                                <div className="address-bill-ship">
                                    <div className="item">
                                        <div className="title">
                                            {'Bill to'}:
                                        </div>
                                        <div>
                                            {billingAddress
                                                ?.split('|')
                                                .map((string: string) => (
                                                    <div>{string.trim()}</div>
                                                ))}
                                        </div>
                                    </div>
                                    <div className="item">
                                        <div className="title">
                                            {'Ship to'}:
                                        </div>
                                        <div>
                                            <div
                                                style={{
                                                    wordBreak: 'normal',
                                                    whiteSpace: 'normal',
                                                }}
                                            >
                                                {shippingAddress
                                                    ?.split(',')
                                                    .join(', ')}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="account-amount">
                                <div>{'AMOUNT DUE'}</div>
                                <div className="amount-total">
                                    {currencyChar}
                                    {numFormat(settleAmount || 0)}
                                </div>
                                <div>
                                    {'Currency'}: {currency}
                                </div>
                                <div>
                                    {'DUE DATA'}: {dueData}
                                </div>
                            </div>
                            <div className="account-table">
                                <Row className="table-header">
                                    <Col span={3}>{'Customer'}</Col>
                                    <Col span={5}>{'ITEM'}</Col>
                                    <Col span={4}>{'Subscription'}</Col>
                                    <Col span={4}>{'Quantity'}</Col>
                                    <Col span={4}>{'Unit Prices'}</Col>
                                    <Col span={4}>{'Amount'}</Col>
                                </Row>
                                {list.map(
                                    (item: {
                                        item: AmountItemType;
                                        subscription: AmountItemType;
                                        quantity: AmountItemType;
                                        unitPrice: any;
                                        amount: any;
                                    }) => {
                                        return (
                                            <Row className="account-item">
                                                <Col span={3} />
                                                <Col span={5}>
                                                    <div>
                                                        {item.item}
                                                    </div>
                                                </Col>
                                                <Col span={4}>
                                                    <div className="subscription">
                                                        {item.subscription}
                                                    </div>
                                                </Col>
                                                <Col span={4}>
                                                    {item.quantity}
                                                </Col>
                                                <Col span={4}>
                                                    {item.unitPrice || '-'}
                                                </Col>
                                                <Col span={4}>
                                                    {currencyChar}
                                                    {numFormat(
                                                        item.amount || 0,
                                                    )}
                                                </Col>
                                            </Row>
                                        );
                                    },
                                )}
                                <Row className="table-subtotal">
                                    <Col span={2} />
                                    <Col span={5} />
                                    <Col span={3} />
                                    <Col span={2} />
                                    <Col span={6}>
                                        <div className="subtotal">
                                            <div>{'Subtotal'}</div>
                                            {taxSwitch === 'OFF' ? (
                                                ''
                                            ) : (
                                                <div>{'Tax total'}</div>
                                            )}
                                            <div>{'Discount'}</div>
                                        </div>
                                    </Col>
                                    <Col span={6}>
                                        <div className="subtotal">
                                            <div>
                                                {currencyChar}
                                                {numFormat(subTotal || 0)}
                                            </div>
                                            {taxSwitch === 'OFF' ? (
                                                ''
                                            ) : (
                                                <div>
                                                    {currencyChar}
                                                    {numFormat(taxTotal || 0)}
                                                </div>
                                            )}
                                            <div>
                                                {currencyChar}
                                                {numFormat(discount || 0)}
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                                <Row className="amount-due">
                                    <Col span={2} />
                                    <Col span={5} />
                                    <Col span={3} />
                                    <Col span={2} />
                                    <Col span={6}>
                                        <div className="subtotal">
                                            {'Amount Due'}
                                        </div>
                                    </Col>
                                    <Col span={6}>
                                        <div className="subtotal">
                                            {currencyChar}
                                            {numFormat(settleAmount || 0)}
                                        </div>
                                    </Col>
                                </Row>
                            </div>

                            <div className="Information">
                                <Row>
                                    <Col span={12}>
                                        <div className="remit-Information">
                                            <div className="title">
                                                {'Remit Payment To'}：
                                            </div>
                                            <div className="word-wrap-item">
                                                {simpleName}
                                            </div>
                                            <div className="word-wrap-item">
                                                {addressDetail}
                                            </div>
                                            <div className="word-wrap-item">
                                                {addressDistrict}
                                            </div>
                                            <div className="word-wrap-item">
                                                {address}
                                            </div>
                                            <div className="word-wrap-item">
                                                {website}
                                            </div>
                                        </div>
                                    </Col>
                                    <Col span={12}>
                                        <div className="bank-Information">
                                            <div className="title">
                                                {'Bank Information'}：
                                            </div>
                                            <div>
                                                <span>
                                                    {'Beneficiary Bank'}: &nbsp;
                                                </span>
                                                <span className="word-wrap-item">
                                                    {beneficiaryBank}
                                                </span>
                                            </div>
                                            <div>
                                                <span>
                                                    {'Beneficiary'}: &nbsp;
                                                </span>
                                                <span className="word-wrap-item">
                                                    {beneficiary}
                                                </span>
                                            </div>
                                            <div>
                                                <span>
                                                    {'Bank Address'}: &nbsp;
                                                </span>
                                                <span className="word-wrap-item">
                                                    {bankAddress}
                                                </span>
                                            </div>
                                            <div>
                                                <span>
                                                    {
                                                        'Beneficiary USD BANK A/C#'
                                                    }
                                                    : &nbsp;
                                                </span>
                                                <span className="word-wrap-item">
                                                    {bankAC}
                                                </span>
                                            </div>
                                            <div>
                                                <span>
                                                    {'Routing Number'}: &nbsp;
                                                </span>
                                                <span className="word-wrap-item">
                                                    {routingNumber}
                                                </span>
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            </div>
                            <div className="footer-contact">
                                <Row>
                                    <Col span={12}>
                                        {
                                            'For any question，please contact Streamax at'
                                        }
                                        ：
                                    </Col>
                                    <Col span={12}>
                                        <span className="footer-label">
                                            {'Email'}：
                                        </span>
                                        <span className="footer-content">
                                            {serviceMail}
                                        </span>
                                    </Col>
                                </Row>
                            </div>
                        </div>
                    </div>
                    <div className="block_height_48"></div>
                </div>
            </div>
            <div className="mask">
                <p onClick={() => download('look')}>
                    <IconEyeOpen />
                    <span style={{ marginLeft: '5px' }}>
                        {i18n.t('action', '查看账单')}
                    </span>
                </p>
            </div>
        </div>
    );
}
export default withSharePropsHOC(Card);
