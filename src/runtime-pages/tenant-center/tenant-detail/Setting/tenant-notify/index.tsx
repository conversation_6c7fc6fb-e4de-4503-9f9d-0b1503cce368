import {
    But<PERSON>,
    Checkbox,
    Col,
    Container,
    Divider,
    Form,
    Row,
    Select,
    Space,
    Switch,
    message,
    Input
} from '@streamax/poppy';
import {
    getAppGlobalData,
    i18n,
    StarryAbroadFormItem as AFormItem,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import RoleSelect from './role-select';
import AuthSelect from '@/components/AuthSelectShow';
import { getRoleListByPage } from '@/service/role';
import {getAuthUserInfoConfig, postTenantManagerConfig} from '@/service/tenant';
import './index.less';
import isAdvanceTenant, { expireDays } from '@/utils/license-common';
import AuthTip from '@/components/AuthTip';
import { getUserPage } from '@/service/user';
import { useUpdate } from 'ahooks';
import { fetchAuthorityLanguageList } from '@/service/language';
import { languageLabelList } from '@/utils/commonFun';
import { useDynamicList } from 'ahooks';
import { IconAdd03 as IconAdd, IconRemove } from '@streamax/poppy-icons';
import _ from 'lodash';
import './index.less';
import {filterLockAndNoExistList} from "@/utils/filterLockAndNoExistList";
import { RspFormLayout, RspHorizontal, useResponsiveShow } from '@streamax/responsive-layout';
import { MAX_USER_ROL_SELECT_NUMBER_V2 } from '@/utils/constant';
import AuthRoleSelect from '@/runtime-lib/components/AuthRoleSelect';
import AuthUserSelect from '@/runtime-lib/components/AuthUserSelect';
const CheckboxGroup = Checkbox.Group;
export enum NotifyRoleRange {
    ALL = 1,
    PART = 2,
}
export enum PushMessageType {
    message = '1',
    email = '2',
}
export const MAX_SELECT_ROLE = 20;
export interface RoleOptionProps {
    label: string;
    value: string;
    disabled?: boolean;
}
const configKey = isAdvanceTenant()
    ? 'vehicle.license.expire.notice.config'
    : 'tenant.expire.notice.config';
const initalConfig = {
    expireNoticeType: 1,
    renewalNoticeType: 1,
    childExpireNoticeType: 1,
    childRenewalNoticeType: 1,

    expireNoticeRoles: [],
    renewalNoticeRoles: [],
    childExpireNoticeRoles: [],
    childRenewalNoticeRoles: [],
    expireNoticeUsers: [],
    emailAddrs: [],

    childTenantNotice: 1,
    noticeType: [PushMessageType.message],
};
const configField = [
    `expireNoticeRoles`,
    `renewalNoticeRoles`,
    `childExpireNoticeRoles`,
    `childRenewalNoticeRoles`,
    `expireNoticeType`,
    `renewalNoticeType`,
    `childExpireNoticeType`,
    `childRenewalNoticeType`,
    `messagePushSwitch`,
    `emailPushSwitch`,
    `childTenantNotice`,
    `expireNoticeUsers`,
    `emailAddrs`
];
const configRoles = [
    `expireNoticeRoles`,
    `renewalNoticeRoles`,
    `childExpireNoticeRoles`,
    `childRenewalNoticeRoles`,
    `expireNoticeUsers`,
    `emailAddrs`
];

const needFilterParamName = [
    'childExpireNoticeRoles',
    'childRenewalNoticeRoles',
    'expireNoticeRoles',
    'renewalNoticeRoles'
];

type TenantNotifySettingProps = {
    openIsWhen: (value: boolean) => void;
};

const TenantNotifySetting = (props: TenantNotifySettingProps, ref: any) => {
    const { openIsWhen } = props;

    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');

    const [tenantForm] = Form.useForm();
    const [subTenantForm] = Form.useForm();
    const [noticeForm] = Form.useForm();
    const [advanceTenantForm] = Form.useForm();
    const [roleOptions, setRoleOptions] = useState<RoleOptionProps[]>([]);
    const [operateStatus, setOperateStatus] = useState({
        tenant: false,
        subTenant: false,
        license: false,
        noticeType: false,
    });
    const [tenantRule, setTenantRule] = useState<string>(''); // 租户管理员ID
    const [subTenantControl, setSubTenantControl] = useState<boolean>(true);
    const [config, setConfig] = useState(initalConfig);
    const [authRoleInfo, setAuthRoleInfo] = useState({});
    const [userList, setUserList] = useState([]);
    const [authLanguages, setAuthLanguages] = useState([]);
    const authSelectRoleRef = useRef(null);
    const authSelectUserMsgRef = useRef(null);
    const [noticeType, setNoticeType] = useState(['1']);
    const { list, remove, getKey, insert, resetList } = useDynamicList(['']);
    const update = useUpdate();
    const inSaaS = !getAppGlobalData('APP_ID');
    const onNoticeTypeChange = (value) => {
        setNoticeType(value);
    };
    const validatorMaxRole = (_, value) => {
        if (value?.length > MAX_USER_ROL_SELECT_NUMBER_V2) {
            return Promise.reject(
                new Error(
                    i18n.t('message', '最多选择{number}个推送角色', { number: MAX_USER_ROL_SELECT_NUMBER_V2 }),
                ),
            );
        }
        return Promise.resolve();
    };
    const formatRequestParams = useCallback((params) => {
            return {
                ...params,
                userAuthorityNewRole: {
                    ...params.userAuthorityNewRole,
                    roleType: undefined, // 不限制角色类型，允许查所有类型的角色
                },
            };
        }, []);
    // 初始化
    useEffect(() => {
        getRuleList();
        getAllUserList();
        getAuthorityLanguageList();
    }, []);
    useEffect(() => {
        getConfig();
    }, [tenantRule]);
    const getAllUserList = () => {
        // 当前租户启用的用户
        getUserPage({
            page: 1,
            pageSize: 1e8,
            state: 1,
        }).then((rs: any) =>
            setUserList(
                rs.list.map((item: any) => ({
                    label: item.account,
                    value: item.userId + '',
                })),
            ),
        );
    };
    // 语言类型
    const getAuthorityLanguageList = () => {
        fetchAuthorityLanguageList({
            tenantId: getAppGlobalData('APP_USER_INFO')['tenantId'],
        }).then((data: any) => {
            const formatList = (data || []).map((item: any) => ({
                value: item,
                label: languageLabelList[item] || item,
            }));
            setAuthLanguages(formatList);
        });
    };

    useImperativeHandle(ref, () => ({
        resume: () => {
            getConfig();
            setOperateStatus({
                tenant: false,
                subTenant: false,
                license: false,
                noticeType: false,
            });
            openIsWhen && openIsWhen(false);
        },
    }));
    // 查询配置
    const getConfig = async (key?: string) => {
        const res = await getAuthUserInfoConfig({
            key: configKey,
        }); // 初次为 {}
        const content = res[configKey];
        let info = {};
        if (content) {
            info = JSON.parse(content);
        }
        needFilterParamName.forEach(i=>{
            info[i] = filterLockAndNoExistList(info[i],info.authRoleInfo);
        });
        const data = getFormatData(info);
        setAuthRoleInfo(info?.authRoleInfo || {});
        setConfig(data);
        resetList(data.emailAddrs.length !== 0 ? data.emailAddrs : ['']);
        // 邮箱地址通过resetList设置过后,不需要再设置,避免设置失效
        const omitData = _.omit(data, ['emailAddrs']);
        if (!key) {
            // 预付租户
            if (isAdvanceTenant()) {
                advanceTenantForm.setFieldsValue(omitData);
            } else {
                // 后付租户
                tenantForm.setFieldsValue(data);
                subTenantForm.setFieldsValue(data);
                noticeForm.setFieldsValue(omitData);
                setSubTenantControl(data.childTenantNotice === 1 ? true : false);
            }
            setNoticeType(data.noticeType || []);
        } else {
            if (key === 'tenant') {
                tenantForm.setFieldsValue(data);
            } else if (key === 'subTenant') {
                subTenantForm.setFieldsValue(data);
                setSubTenantControl(data.childTenantNotice === 1 ? true : false);
            } else if (key === 'noticeType') {
                setNoticeType(data.noticeType || []);
                noticeForm.setFieldsValue(omitData);
            } else if (key === 'license') {
                setNoticeType(data.noticeType || []);
                advanceTenantForm.setFieldsValue(omitData);
            }
        }
    };
    const getFormatData = (info: any) => {
        const defaultData = {
            ...initalConfig,
            expireNoticeRoles: [tenantRule],
            renewalNoticeRoles: [tenantRule],
            childExpireNoticeRoles: [tenantRule],
            childRenewalNoticeRoles: [tenantRule],
        };
        if (!Object.keys(info).length) {
            // 初次无数据
            configField.forEach((item) => {
               if (needFilterParamName.includes(item)) {
                    defaultData[item] = (item || [])?.map((item) => {
                        return { value: item };
                    });
                }
            });
            return defaultData;
        } else {
            configField.forEach((item) => {
                if (typeof info[item] === 'string') {
                    const oldItems = info[item].split(',');
                    if (tenantRule) {
                        oldItems.forEach((item: string, index: number) => {
                            if (item === tenantRule) {
                                const tenant = oldItems.splice(index, 1);
                                oldItems.splice(0, 0, tenant[0]);
                            }
                        });
                    }
                    defaultData[item] = oldItems?.filter((item) => item);
                    if (needFilterParamName.includes(item)) {
                        defaultData[item] = (defaultData[item] || [])?.map((item) => {
                            return { value: item };
                        });
                    }
                } else if (configRoles.includes(item)) {
                    if (!defaultData[item]) {
                        defaultData[item] = [];
                    }
                } else {
                    defaultData[item] = info[item] ?? 1;
                }
            });
            // 通知类型
            const noticeType = [];
            if (info.messagePushSwitch === 1) {
                noticeType.push(PushMessageType.message);
            }
            if (info.emailPushSwitch === 1) {
                noticeType.push(PushMessageType.email);
            }
            if (noticeType.length) {
                defaultData.noticeType = noticeType;
            } else {
                defaultData.noticeType = [PushMessageType.message];
            }
            // @ts-ignore
            defaultData.emailLangType = info.emailLangType;
            // @ts-ignore
            defaultData.noticeRemainDays = info.noticeRemainDays || 30;
            //
            defaultData.expireNoticeUsers =
                info.expireNoticeUsers?.split(',')?.filter((item) => item) || [];

            return defaultData;
        }
    };

    // 获取角色列表
    const getRuleList = async () => {
        const params = {
            page: 1,
            pageSize: 1e8,
            isOwn: false,
            userId: APP_USER_INFO.userId,
            filterByVisitor: true,
            state: 1,
            appId: !inSaaS ? getAppGlobalData('APP_ID') : undefined, //fix 124573,若是行业层需要传appId
        };
        const { list } = await getRoleListByPage(params);

        const options = (list || []).map((item) => {
            if (item?.roleType === 1) {
                setTenantRule(item.roleId);
            }
            return {
                label: i18n.t(`@i18n:@role__${item?.roleId}`, item?.roleName),
                value: item?.roleId,
                disabled: item?.roleType === 1 ? true : false,
                key: item?.roleId,
            };
        });
        setRoleOptions(options);
    };
    // 编辑
    const handleEdit = (key: string) => {
        setOperateStatus({ ...operateStatus, [key]: true });
        openIsWhen && openIsWhen(true);
    };
    // 取消
    const handleCancel = (key: string) => {
        const newStatus = { ...operateStatus, [key]: false };
        if (
            !newStatus.tenant &&
            !newStatus.subTenant &&
            !newStatus.license &&
            !newStatus.noticeType
        ) {
            openIsWhen && openIsWhen(false);
        }
        setOperateStatus(newStatus);
        // 邮箱地址单独通过resetList设置过后,不需要再设置,避免重复设置导致失效
        if(!Array.isArray(config?.emailAddrs)){
            resetList(['']);
        }else{
            resetList(config.emailAddrs.length !==0 ? config.emailAddrs : ['']);
        }
        const omitData = _.omit(config, ['emailAddrs']);
        if (key === 'tenant') {
            tenantForm.setFieldsValue(config);
        } else if (key === 'subTenant') {
            subTenantForm.setFieldsValue(config);
            setSubTenantControl(config.childTenantNotice === 1 ? true : false);
        } else if (key === 'noticeType') {
            setNoticeType(config.noticeType);
            noticeForm.resetFields();
            noticeForm.setFieldsValue(omitData);
        } else {
            setNoticeType(config.noticeType);
            advanceTenantForm.setFieldsValue(omitData);
        }
    };
    // 保存
    const handleSave = async (type: string) => {
        let formData = {};
        if (type === 'tenant') {
            formData = await tenantForm.validateFields();
        } else if (type === 'subTenant') {
            formData = await subTenantForm.validateFields();
        } else if (type === 'noticeType') {
            formData = await noticeForm.validateFields();
        } else if (type === 'license') {
            formData = await advanceTenantForm.validateFields();
        }
        let reqParams: any = {};
        // 处理接口获取的数据
        for (const key in config) {
            if (config[key] instanceof Array) {
                if (needFilterParamName.includes(key)) {
                    reqParams[key] = (config[key] || [])?.map((item) => {
                        return item.value;
                    }).join(',');
                } else {
                    reqParams[key] = config[key].join(',');
                }
            } else {
                reqParams[key] = config[key];
            }
        }
        // 处理从表单拿到的数据
        for (const key in formData) {
            let formItem = formData[key];
            if (formItem instanceof Array) {
                if (needFilterParamName.includes(key)) {
                    formItem = (formItem || [])?.map((item) => {
                        return item.value;
                    });
                }
                formItem = formItem.filter((item) => item);
                reqParams[key] = formItem.join(',');
            } else {
                reqParams[key] = formItem;
            }
        }
        if (type === 'subTenant') {
            reqParams.childTenantNotice = subTenantControl ? 1 : 0;
        }
        if (type === 'license' || type === 'noticeType') {
            // 消息推送
            reqParams.messagePushSwitch = reqParams.noticeType?.includes(PushMessageType.message)
                ? 1
                : 0;
            // 邮件推送
            reqParams.emailPushSwitch = reqParams.noticeType?.includes(PushMessageType.email)
                ? 1
                : 0;
            if (
                reqParams.noticeType?.includes(PushMessageType.email) &&
                !authLanguages.find((item) => item?.value === reqParams.emailLangType)
            ) {
                return message.error(
                    i18n.t('message', '语言“{name}“使用权限已被停用，保存失败', {
                        name: languageLabelList[reqParams.emailLangType] || reqParams.emailLangType,
                    }),
                );
            }
        }
        delete reqParams.noticeType;
        if (isAdvanceTenant()) {
            const {
                messagePushSwitch,
                emailPushSwitch,
                noticeRemainDays,
                expireNoticeRoles,
                expireNoticeUsers,
                emailLangType,
                emailAddrs
            } = reqParams;
            reqParams = {
                messagePushSwitch,
                emailPushSwitch,
                noticeRemainDays,
                expireNoticeRoles,
                expireNoticeUsers,
                emailLangType,
                emailAddrs
            };
        }
        const params = {
            configList: [
                {
                    key: configKey,
                    value: JSON.stringify(reqParams),
                },
            ],
        };
        const res = await postTenantManagerConfig(params);
        if (res) {
            getConfig(type);
            const newStatus = { ...operateStatus, [type]: false };
            if (
                !newStatus.tenant &&
                !newStatus.subTenant &&
                !newStatus.license &&
                !newStatus.noticeType
            ) {
                openIsWhen && openIsWhen(false);
            }
            setOperateStatus(newStatus);
            message.success(i18n.t('message', '保存成功'));
        }
    };

    // 操作按钮
    const getOperateBtns = (key: string, status: boolean) => {
        return status ? (
            <Space size={12}>
                <Button className="button" onClick={() => handleCancel(key)} type="link">
                    {i18n.t('action', '取消')}
                </Button>
                <Button className="button button-save" type="link" onClick={() => handleSave(key)}>
                    {i18n.t('action', '保存')}
                </Button>
            </Space>
        ) : (
            <Button onClick={() => handleEdit(key)} className="button" type="link">
                {i18n.t('action', '编辑')}
            </Button>
        );
    };
    // 子租户开关状态变化
    const handleSwitchChange = (checked: boolean) => {
        setSubTenantControl(checked);
    };
    const isResponsive = useResponsiveShow({
        xs: false,
        sm: false,
        md: true,
        lg: true,
        xl: true,
        xxl: true,
    });
    const MailInput = (index: number, item: any, disabled: boolean, form: any) => (
        <div key={getKey(index)} className='mail'>
            <div className='mail-input'>
                <AFormItem label={index==0?i18n.t('name', '邮箱地址'):''} className='form-item' preserve={false}
                    validateFirst={true}
                    rules={[
                        {
                            type: 'email',
                            message: i18n.t('message', '邮箱格式不正确'),
                        },
                        {
                            validator: (rule, value) => {
                                const mailList = form.getFieldValue('emailAddrs');
                                if(mailList?.filter((item)=>item)?.filter((_,i)=>i!==index)?.includes(value)){
                                    return Promise.reject(i18n.t('message', '邮箱地址不能重复'));
                                }else{
                                    return Promise.resolve();
                                }
                            }
                        }
                    ]}
                    name={['emailAddrs', getKey(index)]}
                    initialValue={item}
                    style={{width:isResponsive?'340px':'100%'}}
                >
                    <Input maxLength={50} placeholder={i18n.t('message',"请输入邮箱地址")} disabled={disabled} />
                </AFormItem>
            </div>
            <div className='btn-box'>
                {index === 0 && (
                    <Button
                        className="mail-btn btn-add"
                        type="primary"
                        onClick={() => {
                            !disabled && insert( list.length, '');
                        }}
                    > +</Button>
                )}

                {list.length > 1 && index !== 0 && (
                    <Button
                        className="mail-btn"
                        onClick={() => {
                            !disabled && remove(index);
                        }}
                    >-</Button>
                )}
            </div>
        </div>
    );
    const { isAbroadStyle } = useSystemComponentStyle();
    return (
        <div className="tenant-message-notify-container">
            {isAdvanceTenant() ? (
                <Container>
                    <Form
                        className="advance-tenant-form"
                        form={advanceTenantForm}
                        layout="vertical"
                    >
                        <div>
                            <div className="card-header">
                            <RspHorizontal justify='space-between' align='middle' gutter={[0,24]}>
                            <span className="left-title">
                                    {i18n.t('name', '车辆License到期通知设置')}
                                </span>
                                {getOperateBtns('license', operateStatus?.license)}
                            </RspHorizontal>
                            </div>
                            <div className="left-title-tip">
                                {i18n.t(
                                    'name',
                                    '在车辆License到期之前，系统将每天发送到期提醒通知',
                                )}
                            </div>
                        </div>

                        <div className="form-content-row">
                                <AFormItem
                                label={i18n.t('name', '到期时间')}
                                name="noticeRemainDays"
                                className="form-item"
                                required
                                rules={[
                                    {
                                        required: true,
                                        message: i18n.t('message', '到期时间不能为空'),
                                    },
                                ]}
                                style={{width:isResponsive?'340px':'100%'}}
                            >
                                <Select
                                    disabled={!operateStatus.license}
                                    placeholder={i18n.t('message', '请选择到期时间')}
                                    options={expireDays}
                                    getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                                />
                            </AFormItem>
                            <span className="notice-remain-day-unit">{i18n.t('name', '天')}</span>
                        </div>
                        <AFormItem
                            label={i18n.t('name', '通知方式')}
                            name="noticeType"
                            className="form-item"
                            required
                            rules={[
                                {
                                    required: true,
                                    message: i18n.t('message', '通知方式不能为空'),
                                },
                            ]}
                        >
                            <CheckboxGroup
                                disabled={!operateStatus.license}
                                onChange={onNoticeTypeChange}
                            >
                                <div className="notice-type-check-box">
                                    <Checkbox value={PushMessageType.message}>
                                        {i18n.t('name', '消息推送')}
                                    </Checkbox>
                                    <Checkbox value={PushMessageType.email}>
                                        {i18n.t('name', '邮件推送')}
                                    </Checkbox>
                                </div>
                            </CheckboxGroup>
                        </AFormItem>
                        <RspFormLayout layoutType='fixed'>
                            <RspFormLayout.Col>
                            {noticeType?.includes(PushMessageType.email) ? (
                        <>
                            <AFormItem
                                label={i18n.t('name', '邮件语言')}
                                name="emailLangType"
                                className="form-item"
                                required
                                rules={[
                                    {
                                        required: true,
                                        message: i18n.t('message', '邮件语言不能为空'),
                                    },
                                ]}
                            >
                                <Select
                                    disabled={!operateStatus.license}
                                    placeholder={i18n.t('message', '请选择邮件语言')}
                                    options={authLanguages}
                                    getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                                />
                            </AFormItem>
                            {list.map((ele, index) => MailInput(index, ele,!operateStatus.license,advanceTenantForm))}
                        </>
                        ) : null}

                        <AFormItem
                            label={
                                <span>
                                    {i18n.t('name', '推送角色')}
                                    <AuthTip show={authSelectRoleRef.current?.getModeAuth} />
                                </span>
                            }
                            name="expireNoticeRoles"
                            className="form-item push-select-form-item"
                            rules={[
                                {
                                    validator: validatorMaxRole,
                                },
                            ]}
                        >
                            {/* <AuthSelect
                                ref={authSelectRoleRef}
                                mode="multiple"
                                placeholder={i18n.t('message', '请选择推送角色')}
                                options={roleOptions}
                                disabled={!operateStatus.license}
                                onInitDone={update}
                                maxTagCount="responsive"
                                getPopupContainer={(triggerNode: HTMLElement) => triggerNode}
                                // @ts-ignore
                                filterOption={(inputValue: string, option: { label: string }) => {
                                    return option?.label
                                        ?.toUpperCase()
                                        ?.includes?.(inputValue?.toUpperCase());
                                }}
                                virtual={false}
                                keepSort={true}
                            /> */}
                            <AuthRoleSelect
                                formatRequestParams={formatRequestParams}
                                adminInfo={{
                                    adminVisible: false,
                                    adminIdList: authRoleInfo?.tenantAdminRoleIdList
                                }}
                                placeholder={i18n.t('message', '请选择推送角色')}
                                maxCount={MAX_USER_ROL_SELECT_NUMBER_V2}
                                showPlaceholder={true}
                                onInitDone={update}
                                disabled={!operateStatus.license}
                            />
                        </AFormItem>
                        <AFormItem
                            label={
                                <span>
                                    {i18n.t('name', '推送用户')}
                                    <AuthTip show={authSelectUserMsgRef.current?.getModeAuth} />
                                </span>
                            }
                            name="expireNoticeUsers"
                            className="form-item push-select-form-item"
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (value?.length > MAX_USER_ROL_SELECT_NUMBER_V2) {
                                            return Promise.reject(
                                                new Error(
                                                    i18n.t(
                                                        'message',
                                                        '最多选择{number}个推送用户',
                                                        {
                                                            number: MAX_USER_ROL_SELECT_NUMBER_V2,
                                                        },
                                                    ),
                                                ),
                                            );
                                        }
                                        return Promise.resolve();
                                    },
                                },
                            ]}
                        >
                            {/* <AuthSelect
                                ref={authSelectUserMsgRef}
                                placeholder={i18n.t('message', '请选择推送用户')}
                                mode="multiple"
                                onInitDone={update}
                                options={userList}
                                maxTagCount="responsive"
                                disabled={!operateStatus.license}
                                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                                // @ts-ignore
                                filterOption={(inputValue: string, option: { label: string }) => {
                                    return option?.label
                                        ?.toUpperCase()
                                        ?.includes?.(inputValue?.toUpperCase());
                                }}
                                virtual={false}
                                keepSort={true}
                            /> */}
                            <AuthUserSelect
                                placeholder={i18n.t('message', '请选择推送用户')}
                                maxCount={MAX_USER_ROL_SELECT_NUMBER_V2}
                            />
                        </AFormItem>
                            </RspFormLayout.Col>
                        </RspFormLayout>
                    </Form>
                </Container>
            ) : (
                <>
                    <div className="role-select-card-header-title">{i18n.t('name', '租户到期通知设置')}</div>
                    <Container>
                        <Form form={tenantForm} layout="vertical">
                            <div className="card-header">
                            <RspHorizontal justify='space-between' align='middle' gutter={[0,24]}>
                            <span className="left-title">{i18n.t('name', '通知角色')}</span>
                            {getOperateBtns('tenant', operateStatus?.tenant)}
                            </RspHorizontal>
                            </div>
                            <div className="card-body role-select-first-form-content">
                                <AFormItem
                                    label={i18n.t('name', '到期通知角色')}
                                    rules={[{ required: true }]}
                                    name="expireNoticeType"
                                >
                                    <RoleSelect
                                        notifyType="expireNoticeType"
                                        partRoles="expireNoticeRoles"
                                        edit={operateStatus?.tenant}
                                        form={tenantForm}
                                        roleOptions={roleOptions}
                                        authRoleInfo={authRoleInfo}
                                    />
                                </AFormItem>
                                <AFormItem
                                    label={i18n.t('name', '续期通知角色')}
                                    rules={[{ required: true }]}
                                    name="renewalNoticeType"
                                >
                                    <RoleSelect
                                        notifyType="renewalNoticeType"
                                        partRoles="renewalNoticeRoles"
                                        edit={operateStatus?.tenant}
                                        form={tenantForm}
                                        roleOptions={roleOptions}
                                        authRoleInfo={authRoleInfo}
                                    />
                                </AFormItem>
                            </div>
                        </Form>
                    </Container>
                    <Container>
                        <Form form={subTenantForm} layout="vertical">
                            <div className="card-header">
                            <RspHorizontal justify='space-between' align='middle' gutter={[0,24]}>
                                <span className="left-title">
                                    {i18n.t('name', '开启子租户通知')}
                                    <Switch
                                        className="subtenant-notify-switch"
                                        disabled={!operateStatus.subTenant}
                                        checked={subTenantControl}
                                        onChange={handleSwitchChange}
                                    />
                                </span>
                                {getOperateBtns('subTenant', operateStatus.subTenant)}
                                </RspHorizontal>
                            </div>
                            {subTenantControl && (
                                <div className="card-body form-content-row role-select-second-form-content">
                                    <RspFormLayout layoutType='auto'>
                                    <AFormItem
                                        label={i18n.t('name', '到期通知角色')}
                                        name="childExpireNoticeRoles"
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value?.length > MAX_USER_ROL_SELECT_NUMBER_V2) {
                                                        return Promise.reject(
                                                            new Error(
                                                                i18n.t(
                                                                    'message',
                                                                    '最多选择{number}个通知角色',
                                                                    { number: MAX_USER_ROL_SELECT_NUMBER_V2 },
                                                                ),
                                                            ),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        {/* <AuthSelect
                                            options={roleOptions}
                                            className="sub-select"
                                            placeholder={i18n.t('message', '请选择角色')}
                                            mode="multiple"
                                            disabled={!operateStatus.subTenant}
                                            showArrow
                                            maxTagCount="responsive"
                                            getPopupContainer={() =>
                                                document.getElementById('root') as HTMLElement
                                            }
                                            filterOption={(inputValue: any, option: any) => {
                                                return option?.label
                                                    ?.toUpperCase()
                                                    ?.includes?.(inputValue?.toUpperCase());
                                            }}
                                            virtual={false}
                                            keepSort={true}
                                        /> */}
                                        <AuthRoleSelect
                                            formatRequestParams={formatRequestParams}
                                            adminInfo={{
                                                adminVisible: false,
                                                adminIdList: authRoleInfo?.tenantAdminRoleIdList
                                            }}
                                            placeholder={i18n.t('message', '请选择角色')}
                                            maxCount={MAX_USER_ROL_SELECT_NUMBER_V2}
                                            showPlaceholder={true}
                                            disabled={!operateStatus.subTenant}
                                        />
                                    </AFormItem>
                                    <AFormItem
                                        label={i18n.t('name', '续期通知角色')}
                                        name="childRenewalNoticeRoles"
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value?.length > MAX_USER_ROL_SELECT_NUMBER_V2) {
                                                        return Promise.reject(
                                                            new Error(
                                                                i18n.t(
                                                                    'message',
                                                                    '最多选择{number}个通知角色',
                                                                    { number: MAX_USER_ROL_SELECT_NUMBER_V2 },
                                                                ),
                                                            ),
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        {/* <AuthSelect
                                            options={roleOptions}
                                            className="sub-select"
                                            placeholder={i18n.t('message', '请选择角色')}
                                            mode="multiple"
                                            disabled={!operateStatus.subTenant}
                                            showArrow
                                            maxTagCount="responsive"
                                            getPopupContainer={() =>
                                                document.getElementById('root') as HTMLElement
                                            }
                                            filterOption={(inputValue: any, option: any) => {
                                                return option?.label
                                                    ?.toUpperCase()
                                                    ?.includes?.(inputValue?.toUpperCase());
                                            }}
                                            virtual={false}
                                            keepSort={true}
                                        /> */}
                                        <AuthRoleSelect
                                            formatRequestParams={formatRequestParams}
                                            adminInfo={{
                                                adminVisible: false,
                                                adminIdList: authRoleInfo?.tenantAdminRoleIdList
                                            }}
                                            placeholder={i18n.t('message', '请选择角色')}
                                            maxCount={MAX_USER_ROL_SELECT_NUMBER_V2}
                                            showPlaceholder={true}
                                            disabled={!operateStatus.subTenant}
                                        />
                                    </AFormItem>
                                    </RspFormLayout>
                                </div>
                            )}
                        </Form>
                    </Container>
                    <Container>
                        <Form form={noticeForm} layout="vertical">
                            <div className="card-header">
                            <RspHorizontal justify='space-between' align='middle' gutter={[0,24]}>
                            <span className="left-title">{i18n.t('name', '通知方式')}</span>
                            {getOperateBtns('noticeType', operateStatus?.noticeType)}
                            </RspHorizontal>
                            </div>
                            <div className="card-body">
                                <RspFormLayout layoutType='fixed'>
                                    <RspFormLayout.Col>
                                    <AFormItem
                                    label={i18n.t('name', '通知方式')}
                                    name="noticeType"
                                    className="form-item"
                                    required
                                    rules={[
                                        {
                                            required: true,
                                            message: i18n.t('message', '通知方式不能为空'),
                                        },
                                    ]}
                                >
                                    <CheckboxGroup
                                        disabled={!operateStatus.noticeType}
                                        onChange={onNoticeTypeChange}
                                    >
                                        <div className="notice-type-check-box">
                                            <Checkbox value={PushMessageType.message}>
                                                {i18n.t('name', '消息推送')}
                                            </Checkbox>
                                            <Checkbox value={PushMessageType.email}>
                                                {i18n.t('name', '邮件推送')}
                                            </Checkbox>
                                        </div>
                                    </CheckboxGroup>
                                </AFormItem>

                                {noticeType?.includes(PushMessageType.email) ? (
                                    <>
                                    <AFormItem
                                        label={i18n.t('name', '邮件语言')}
                                        name="emailLangType"
                                        className="form-item"
                                        required
                                        rules={[
                                            {
                                                required: true,
                                                message: i18n.t('message', '邮件语言不能为空'),
                                            },
                                        ]}
                                    >
                                        <Select
                                            disabled={!operateStatus.noticeType}
                                            placeholder={i18n.t('message', '请选择邮件语言')}
                                            options={authLanguages}
                                            getPopupContainer={(triggerNode: HTMLElement) =>
                                                triggerNode
                                            }
                                        />
                                    </AFormItem>
                                    {list.map((ele, index) => MailInput(index, ele, !operateStatus.noticeType,noticeForm))}
                                    </>
                                ) : null}
                                    </RspFormLayout.Col>
                                </RspFormLayout>
                            </div>
                        </Form>
                    </Container>
                </>
            )}
        </div>
    );
};
export default forwardRef(TenantNotifySetting);
