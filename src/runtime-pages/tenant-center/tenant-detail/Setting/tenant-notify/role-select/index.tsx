import { Form, Radio, Select, message } from '@streamax/poppy';
import type { RadioChangeEvent } from '@streamax/poppy/lib/radio';
import { i18n, StarryAbroadFormItem as AFormItem, useSystemComponentStyle } from '@base-app/runtime-lib';
import React, { useCallback, useEffect, useState } from 'react';
import { MAX_SELECT_ROLE, NotifyRoleRange, RoleOptionProps } from '..';
import AuthSelect from '@/components/AuthSelectShow';
import './index.less';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { RspFormLayout } from '@streamax/responsive-layout';
import AuthRoleSelect from '@/runtime-lib/components/AuthRoleSelect';
import { MAX_USER_ROL_SELECT_NUMBER_V2 } from '@/utils/constant';
interface RoleSelectProps {
    value?: number;
    edit: boolean;
    notifyType: string;
    form: Form.FormInstance;
    roleOptions: RoleOptionProps[];
    partRoles: string;
    authRoleInfo?: Record<string, any>
}

const RoleSelect = (props: RoleSelectProps) => {
    const { value = 1, edit, notifyType, form, roleOptions = [], authRoleInfo, partRoles } = props;

    const [roleRange, setRoleRange] = useState<number>(value);
    const [ruleInfo, setRuleInfo] = useState<any>({});


    useEffect(() => {
        if (roleRange === 2) {
            const length = form.getFieldValue(partRoles)?.length;
            if (length > MAX_USER_ROL_SELECT_NUMBER_V2) {
                const infoObj = {};
                infoObj[`${partRoles}Status`] = 'error';
                infoObj[`${partRoles}Help`] = i18n.t('message', '最多选择{number}个通知角色', {
                    number: MAX_USER_ROL_SELECT_NUMBER_V2,
                });
                setRuleInfo(infoObj);
            } else {
                setRuleInfo({});
            }
        }
    }, [roleRange]);

    useEffect(() => {
        value && setRoleRange(value);
    }, [value]);

    // 角色范围改变
    const handleTypeChange = (e: RadioChangeEvent) => {
        const value = e.target.value;
        form.setFieldsValue({
            ...form.getFieldsValue(),
            [notifyType]: value,
        });
        setRoleRange(value);
    };
    const handleRoleChange = () => {
        setRuleInfo({});
    };
    const formatRequestParams = useCallback((params) => {
                return {
                    ...params,
                    userAuthorityNewRole: {
                        ...params.userAuthorityNewRole,
                        roleType: undefined, // 不限制角色类型，允许查所有类型的角色
                    },
                };
            }, []);
    const { isAbroadStyle } = useSystemComponentStyle();
    const roleRangeSelector = () => {
        return roleRange === NotifyRoleRange.PART && (
                <AFormItem
                    className="tenant-notify-tab-item choose-part-roles-item"
                    name={partRoles}
                    rules={[
                        {
                            validator: (_, value) => {
                                if (value?.length > MAX_USER_ROL_SELECT_NUMBER_V2) {
                                    return Promise.reject(
                                        new Error(
                                            i18n.t(
                                                'message',
                                                '最多选择{number}个通知角色',
                                                { number: MAX_USER_ROL_SELECT_NUMBER_V2 },
                                            ),
                                        ),
                                    );
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                    style={{ marginBottom: 0 }}
                    validateStatus={ruleInfo?.[`${partRoles}Status`]}
                    help={ruleInfo?.[`${partRoles}Help`]}
                >
                    {/* <AuthSelect
                        mode="multiple"
                        options={roleOptions}
                        disabled={!edit}
                        showArrow
                        maxTagCount={'responsive'}
                        getPopupContainer={() =>
                            document.getElementById('root') as HTMLElement
                        }
                        className="select-part-role"
                        placeholder={i18n.t('message', '请选择要通知的角色')}
                        onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                        }}
                        filterOption={(inputValue: any, option: any) => {
                            return option?.label
                                ?.toUpperCase()
                                ?.includes?.(inputValue?.toUpperCase());
                        }}
                        onChange={handleRoleChange}
                        virtual={false}
                        keepSort={true}
                    /> */}
                    <AuthRoleSelect
                        formatRequestParams={formatRequestParams}
                        adminInfo={{
                            adminVisible: false,
                            adminIdList: authRoleInfo?.tenantAdminRoleIdList
                        }}
                        placeholder={i18n.t('message', '请选择要通知的角色')}
                        maxCount={MAX_USER_ROL_SELECT_NUMBER_V2}
                        showPlaceholder={true}
                        disabled={!edit}
                    />
                </AFormItem>
        );

    };
    return (
        <>
            <Radio.Group
                defaultValue={1}
                onChange={handleTypeChange}
                className="select-role-item-range-container"
                disabled={!edit}
                value={roleRange}
            >
                <Radio value={NotifyRoleRange.ALL}>
                    <OverflowEllipsisContainer maxWidth={120}>
                        <span>{i18n.t('name', '所有角色')}</span>
                    </OverflowEllipsisContainer>
                </Radio>
                <Radio value={NotifyRoleRange.PART}>
                    <div className="part-role-container">
                        {i18n.t('name', '部分角色')}
                    </div>
                </Radio>

            </Radio.Group>
            {
            <RspFormLayout layoutType='fixed'>
                <RspFormLayout.Col>
                    {roleRangeSelector()}
                </RspFormLayout.Col>
            </RspFormLayout> 
            }
        </>
    );
};
export default RoleSelect;
