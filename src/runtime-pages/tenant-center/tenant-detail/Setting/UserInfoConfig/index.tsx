/*
 * @LastEditTime: 2025-06-30 20:09:51
 */
import React, {
    useState,
    useEffect,
    useImperativeHandle,
    useRef,
    forwardRef,
    useCallback,
} from 'react';
import {} from '@base-app/runtime-lib';
import { getAppGlobalData, AuthRoleSelect, AuthUserSelect, i18n } from '@base-app/runtime-lib';
import { filterLockAndNoExistList } from '@/utils/filterLockAndNoExistList';
import { getAuthUserInfoConfig, setUserInfoConfig } from '@/service/tenant';
import { message } from '@streamax/poppy';
import { MAX_USER_ROL_SELECT_NUMBER_V2 } from '@/utils/constant';
import BaseUserInfo from '@/runtime-pages/tenant-center/components/user-info-config/BaseUserInfo';
import UserCountSafeInfo from '@/runtime-pages/tenant-center/components/user-info-config/UserCountSafeInfo';

function UserInfoConfig(props: any, ref: any) {
    const { openIsWhen } = props;
    const [userInfo, setUserInfo] = useState({});
    const [infoState, setInfoState] = useState({
        base: false,
        safe: false,
    });
    const baseUserInfoRef = useRef();
    const userCountSafeInfoRef = useRef();
    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');

    const getConfig = async () => {
        const params = {
            tenantId: APP_USER_INFO.tenantId,
            key: 'tenant.user.info.config',
        };
        try {
            const userConfigInfo = await getAuthUserInfoConfig(params); // 无值时返回 ：{}
            const info = userConfigInfo['tenant.user.info.config']
                ? JSON.parse(userConfigInfo['tenant.user.info.config'])
                : {};
            info.roleIds = filterLockAndNoExistList(
                info.roleIds,
                info.authRoleInfo,
            );
            info.userIds = filterLockAndNoExistList(
                info.userIds,
                info.authUserInfo,
            );
            setUserInfo(info);
        } catch (error) {
        }
    };
    const formatRequestParams = useCallback((params) => {
        return {
            ...params,
            userAuthorityNewRole: {
                ...params.userAuthorityNewRole,
                roleType: undefined, // 不限制角色类型，允许查所有类型的角色
            },
        };
    }, []);
    const onStateChange = (type: 'base' | 'safe', state: boolean) => {
        setInfoState((pre) => {
            return { ...pre, [type]: state };
        });
    };
    useEffect(() => {
        openIsWhen?.(infoState.base || infoState.safe);
    }, [infoState]);
    useEffect(() => {
        getConfig();
    }, []);
    const updateUserInfoConfig = async (values) => {
        const params = {
            tenantId: APP_USER_INFO.tenantId,
            configList: [
                {
                    key: 'tenant.user.info.config',
                    value: values,
                },
            ]
        };
        await setUserInfoConfig(params);
    };
    useImperativeHandle(ref, () => ({
        resume: () => {
            baseUserInfoRef.current?.resume?.();
            userCountSafeInfoRef.current?.resume?.();
        },
    }));
    return (
        <div className="user-safe-tab">
            <BaseUserInfo
                ref={baseUserInfoRef}
                userInfo={userInfo}
                reloadData={getConfig}
                onStateChange={onStateChange}
                updateUserInfoConfig={updateUserInfoConfig}
                {...props}
            />
            <UserCountSafeInfo
                ref={userCountSafeInfoRef}
                userInfo={userInfo}
                onStateChange={onStateChange}
                reloadData={getConfig}
                updateUserInfoConfig={updateUserInfoConfig}
                requestAdminRole={true}
                roleSelect={
                    <AuthRoleSelect
                        formatRequestParams={formatRequestParams}
                        adminInfo={{
                            adminVisible: false,
                            adminIdList: userInfo?.authRoleInfo?.tenantAdminRoleIdList
                        }}
                        placeholder={i18n.t('message', '请选择指定角色')}
                        maxCount={MAX_USER_ROL_SELECT_NUMBER_V2}
                        showPlaceholder={true}
                    />
                }
                userSelect={
                    <AuthUserSelect
                        placeholder={i18n.t('message', '请选择指定用户')}
                        maxCount={MAX_USER_ROL_SELECT_NUMBER_V2}
                    />
                }
                {...props}
            />
        </div>
    );
}
export default forwardRef(UserInfoConfig);
