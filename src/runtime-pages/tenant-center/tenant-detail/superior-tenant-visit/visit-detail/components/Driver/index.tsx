import { useEffect, useState, useRef } from 'react';
import { Space, message, Input, Button, Table } from '@streamax/poppy';
import { i18n } from '@base-app/runtime-lib';
import { StarryModal } from '@base-app/runtime-lib';
import { IconRequest, IconDelete, IconSearch02 } from '@streamax/poppy-icons';
import Empty from '../../../components/Empty';
import CustomModal from '../../../components/CustomModal';
import {
    userAuthorityDriverPage,
    deleteUserAuthorityDriver,
    postUserAuthorityDriver,
} from '@/service/user';
import { Total } from '@streamax/starry-components';
import './index.less';
import { RspBasicLayout, RspHorizontal, useResponsiveShow } from '@streamax/responsive-layout';

type RecordType = Record<string, any>;

interface DriverProps {
    userId: string;
    appId: number;
}

interface DriverTableProps {
    appId: number;
    userId: string;
    style?: React.CSSProperties;
    isInclude?: boolean;
}

enum IsIncludeParamEnum {
    IsExclude,
    IsInclude,
}

const DEFAULT_PAGE = 1;
const DEFAULT_PAGESIZE = 10;
const LIST_PAGESIZE = 1e8;

const DriverTable = (props: DriverTableProps) => {
    const search = useRef<string>('');
    const [targetList, setTargetList] = useState<RecordType[]>([]);
    const [currentPageData, setCurrentPageData] = useState(Array<any>());
    const [pageLoading, setPageLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGESIZE,
        total: 0,
        size: 'middle',
        showTotal: Total.showTotal,
        showSizeChanger: true,
        showQuickJumper: true,
    });
    // 弹框是否显示
    const [visible, setVisible] = useState(false);

    const { isInclude, userId, appId, style } = props;

    useEffect(() => {
        // 切换应用的时候要看此应用下面有没有数据权限，没有显示点击添加，有直接显示表格
        fetchTargetList();
        fetchTargetList(undefined, undefined, 'list');
    }, [appId]);

    // 获取司机列表
    const fetchTargetList = async (
        queryPage?: number,
        queryPageSize?: number,
        type: 'list' | 'page' = 'page',
    ) => {
        setPageLoading(true);
        const searchStr = search.current
            ? search.current
            : undefined;
        const params = {
            userId,
            appId,
            isInclude: isInclude ? IsIncludeParamEnum.IsInclude : IsIncludeParamEnum.IsExclude,
            page: type === 'list' ? DEFAULT_PAGE : queryPage || DEFAULT_PAGE,
            pageSize:
                type === 'list'
                    ? LIST_PAGESIZE
                    : queryPageSize || pagination.pageSize || DEFAULT_PAGESIZE,
            driverNameOrJobNumber: type === 'list' ? '' : searchStr,
        };
        try {
            const { list, total, page, pageSize } =
                (await userAuthorityDriverPage({ ...params })) || {};
            if (type === 'list') {
                setTargetList(list || []);
            } else {
                setCurrentPageData(list);
                setPagination((prev) => {
                    return {
                        ...prev,
                        current: page,
                        pageSize: pageSize,
                        total: total,
                    };
                });
            }
            setPageLoading(false);
        } catch (error) {
            setPageLoading(false);
        }
    };

    const handleTableChange = (page: any) => {
        fetchTargetList(page.current, page.pageSize);
    };

    const deleteDriver = (record: any) => {
        const { driverId, driverName } = record;
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            icon: <IconRequest />,
            content: i18n.t('message', '确定要删除“{name}”吗？', { name: driverName }),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () =>
                new Promise<void>((resolve, reject) => {
                    deleteUserAuthorityDriver({
                        userId,
                        driverId,
                        isInclude: isInclude
                            ? IsIncludeParamEnum.IsInclude
                            : IsIncludeParamEnum.IsExclude,
                    })
                        .then((data: any) => {
                            if (data) {
                                message.success(i18n.t('message', '操作成功'));
                                resolve();
                                fetchTargetList();
                                fetchTargetList(undefined, undefined, 'list');
                            } else {
                                message.error(i18n.t('message', '操作失败'));
                                reject();
                            }
                        })
                        .catch(() => {
                            reject();
                        });
                }),
        });
    };

    // 司机相关
    const driverColumns = [
        {
            title: i18n.t('name', '司机姓名'),
            dataIndex: 'driverName',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '司机工号'),
            dataIndex: 'jobNumber',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetList',
            ellipsis: true,
            render: (text: any[]) => {
                if (!text) return '-';
                return (text || []).map((p: any) => p.fleetName as string).join('、');
            },
        },
        {
            title: i18n.t('name', '操作'),
            dataIndex: 'operate',
            render: (_: unknown, record: any) => {
                return (
                    <IconDelete
                        title={i18n.t('action', '删除')}
                        className="operate-btn"
                        onClick={() => deleteDriver(record)}
                    />
                );
            },
        },
    ];

    // 打开选择司机弹框
    const openModal = () => {
        setVisible(true);
    };

    const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        search.current = (e.target.value || '').trim();
    };

    const handleOk = (type: unknown, driverIds: string[]) => {
        if (!driverIds.length) {
            setVisible(false);
            return;
        }

        postUserAuthorityDriver({
            driverIds: driverIds.join(','),
            userId,
            appId,
            isInclude: isInclude ? IsIncludeParamEnum.IsInclude : IsIncludeParamEnum.IsExclude,
        }).then((data: boolean) => {
            if (data) {
                message.success(i18n.t('message', '操作成功'));
                setVisible(false);
                fetchTargetList();
                fetchTargetList(undefined, undefined, 'list');
            } else {
                message.error(i18n.t('message', '操作失败'));
            }
        });
    };

    const generateContent = () => {
        // 不为空的情况下，展示表格
        return (
            <Table
                bordered="around"
                rowKey="vehicleId"
                columns={driverColumns}
                dataSource={currentPageData}
                loading={pageLoading}
                // @ts-ignore
                pagination={{
                    ...pagination,
                }}
                onChange={handleTableChange}
                locale={{
                    emptyText: (
                        <Empty
                            descText={`${i18n.t('message', '暂无数据')}`}
                            clickText={i18n.t('action', '请添加')}
                            onClick={openModal}
                        />
                    ),
                }}
            />
        );
    };
    const isResponsive = useResponsiveShow({
        xs: false,
        sm: false,
        md: false,
        lg: false,
        xl: true,
        xxl: true,
    });
    return (
        <div className="driver-container-table-wrapper" style={style}>
            <div className="header-wrapper">
                <div className="header-row">
                    <Space size={20}>
                        <div className="driver-title-text">
                            {isInclude ? i18n.t('name', '包含司机') : i18n.t('name', '剔除司机')}
                        </div>
                    </Space>
                </div>
                <div className="header-row">
                <RspHorizontal align='middle' justify='space-between' gutter={[0,24]}>
                <div className="operate-btn-wrapper">
                        <Button type="primary" onClick={openModal}>
                            {i18n.t('name', '添加司机')}
                        </Button>
                    </div>
                    <div className="search-wrapper">
                        <Input
                            placeholder={i18n.t('action', '请输入司机姓名或工号')}
                            prefix={<IconSearch02 />}
                            maxLength={50}
                            allowClear
                            onChange={onSearchChange}
                            onPressEnter={() => fetchTargetList()}
                        />
                    </div>
                </RspHorizontal>
                </div>
            </div>
            <div className="table-wrapper"
            style={{ minHeight: isResponsive ? '559px' : '0px' }}
            >{generateContent()}</div>
            <CustomModal
                visible={visible}
                type={isInclude ? 'include' : 'exclude'}
                object="driver"
                valueType="value"
                authorizeAppId={appId}
                onCancel={() => setVisible(false)}
                onOk={handleOk}
                targetList={targetList}
            />
        </div>
    );
};

export default (props: DriverProps) => {
    const { userId, appId } = props;
    return (
        <div className="driver-container">
        <RspBasicLayout gutter={[40,24]} preset='ListMap'>
                <RspBasicLayout.Item>
                <DriverTable isInclude userId={userId} appId={appId}/>
                </RspBasicLayout.Item>
                <RspBasicLayout.Item>
                <DriverTable isInclude={false} userId={userId} appId={appId} />
                </RspBasicLayout.Item>
        </RspBasicLayout>
        </div>
    );
};
