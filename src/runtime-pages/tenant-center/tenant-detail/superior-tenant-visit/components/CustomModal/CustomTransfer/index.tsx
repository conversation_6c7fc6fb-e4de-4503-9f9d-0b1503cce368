import React, {
    useState,
    useEffect,
    useImperativeHandle,
    useMemo,
    useRef,
    Ref,
} from 'react';
import {Button, Table, Input, Form, Row, Col, ProForm} from '@streamax/poppy';
import { compact } from 'lodash';
import { StarryMuliteTreeSelect } from '@base-app/runtime-lib';
//@ts-ignore
import { Total } from '@streamax/starry-components';
import { useDispatch, getDvaApp } from '@base-app/runtime-lib/core';
import { i18n, useConstructor, getAppGlobalData } from '@base-app/runtime-lib';
import { getUserLine, getUserStation, getUserPage } from '@/service/user';
import TransferRight from '../../TransferRight';
import VehicleInput from '../../VehicleInput';
import PermissionCenterModel from '../../../model';
import {
    generateColumns,
    getLeftSearchInputTitle,
    getSearchLabel,
} from './constant';
import type { Dispatch } from '@base-app/runtime-lib/core';
import type { TransferRightRef } from '../../TransferRight';
import { useSystemComponentStyle } from '@base-app/runtime-lib';

import './index.less';
import { RspBasicLayout, useResponsiveShow } from '@streamax/responsive-layout';

export type Type =
    | 'vehicle'
    | 'driver'
    | 'device'
    | 'line'
    | 'station'
    | 'user'
    | 'channel';

export interface QueryParams {
    fleetIds?: string;
    driverName?: string;
    vehicleNumber?: string;
    page?: number;
    pageSize?: number;
    searchValue?: string | { type: string; value: string };
    deviceNo?: string;
    fields?: string;
    stationName?: string;
    keyword?: string;
}

interface FormFieldValue {
    company?: string[];
    searchValue?: string;
}

interface CustomTransferProps {
    type: Type;
    rowKey?: string; // 若需指定rowKey - 用于标识该项值
    targetList?: any[];
    authorizeAppId?: number | undefined;
    visible?: boolean;
    backShow?: boolean; // 是否回显操作选中外部数据，默认false
}

export interface CustomTransferRef {
    getSelectedData: () => any[]; // 获取选中的数据列表
    getSelectedValues: () => string[]; // 获取选中的值列表
}

const titleKeyMap: Record<Type, string> = {
    driver: 'driverName',
    vehicle: 'vehicleNumber',
    device: 'deviceNo',
    line: 'lineName',
    station: 'stationName',
    user: 'account',
    channel: 'deviceNo-vehicleNumber',
};

const CustomTransfer: React.ForwardRefRenderFunction<
    CustomTransferRef,
    CustomTransferProps
> = (props, ref) => {
    const {
        type,
        targetList,
        authorizeAppId,
        visible,
        rowKey = undefined,
        backShow = false,
    } = props;

    const dispatch: Dispatch = useDispatch();
    const transferRightRef = useRef<TransferRightRef>(null);
    const [leftShowDataList, setLeftShowDataList] = useState<any[]>([]); // 左侧穿梭框展示数据列表
    const [leftSelected, setLeftSelected] = useState<string[]>([]); // 左侧穿梭框选中项
    const [queryParams, setQueryParams] = useState<QueryParams>({
        page: 1,
        pageSize: 10,
    }); // 查询条件
    const [totals, setTotals] = useState<number>(0); // 左侧数据总数（用于表格分页）
    const [companyTreeData, setCompanyTreeData] = useState<any>([]); // 车组树数据
    const [tableLoading, setTableLoading] = useState(false);
    const {
        // 当前模式是否是海外风格
        isAbroadStyle,
    } = useSystemComponentStyle();

    const idKey = rowKey || (type === 'channel' ? 'deviceId' : `${type}Id`);
    const titleKey = titleKeyMap[type];
    const targetValMap = (targetList || []).reduce((obj, item) => {
        // 外部数据
        obj[item[idKey]] = { ...item };
        return obj;
    }, {});

    useConstructor(() => {
        const dvaApp = getDvaApp();
        dvaApp && dvaApp.model(PermissionCenterModel);
    });

    useImperativeHandle(ref, () => ({
        getSelectedData: () => transferRightRef.current?.getDataList() || [],
        getSelectedValues: () => {
            const vals = leftSelected || [];
            if (backShow) return vals;
            return vals.reduce((arr: string[], val) => {
                if (!targetValMap[val]) {
                    arr.push(val);
                }
                return arr;
            }, []);
        },
    }));

    // 处理默认选中状态
    const handleDefaultChecked = (list: any[]): any[] => {
        if (backShow) return list;
        const data = list.map((item) => {
            const val = item[idKey];
            if (targetValMap[val]) {
                return {
                    ...item,
                    defaultChecked: true,
                };
            } else return { ...item };
        });
        return data;
    };

    // 请求列表成功后设置数据
    const listRequestSuccess = (list: any[], total: number) => {
        const dataList = handleDefaultChecked(list || []);
        setLeftShowDataList(dataList);
        setTotals(total || 0);
        setTableLoading(false);
    };

    const getChannelTitle = (item: {
        deviceNo: string;
        vehicleNumber: string;
        [index: string]: any;
    }) => {
        const { deviceNo, vehicleNumber } = item;
        return (
            <div
                className="channel-selected-item"
                key={`${deviceNo}_${vehicleNumber}`}
            >
                <span title={deviceNo || '-'}>{deviceNo || '-'}</span>
                <span className="vehicle-number" title={vehicleNumber || '-'}>
                    {vehicleNumber || '-'}
                </span>
            </div>
        );
    };

    const rightDataSource: {
        key: string;
        title: string | JSX.Element;
        [index: string]: any;
    }[] = useMemo(() => {
        const list = (leftShowDataList || []).concat(targetList || []);
        return list.map((item) => {
            return {
                ...item,
                key: item[idKey] || '-',
                title:
                    type === 'channel'
                        ? getChannelTitle(item)
                        : item[titleKey] || '-',
            };
        });
    }, [leftShowDataList, targetList]);

    const rightValues: string[] = useMemo(() => {
        if (backShow) return leftSelected;
        const vals = (leftSelected || []).map((val) =>
            targetValMap[val] ? null : val,
        );
        return compact(vals);
    }, [backShow, leftSelected]);

    useEffect(() => {
        const selecteds = (targetList || []).map((item) => item[idKey]);
        setLeftSelected(() => selecteds);
    }, [targetList]);

    // 加载车组树的数据
    useEffect(() => {
        if (visible && authorizeAppId && type !== 'user') {
            dispatch({
                type: 'permissionCenter/fetchChooseCompanyList',
                payload: {
                    page: 1,
                    pageSize: 1e8,
                    appId: authorizeAppId,
                    domainType: 1,
                },
            }).then((rs: any) => {
                const tmpData = (rs.list || []).map((i: any) => {
                    return {
                        id: i.fleetId,
                        pId: i.parentId,
                        value: i.fleetId,
                        title: i.fleetName,
                    };
                });
                setCompanyTreeData(tmpData);
            });
        }
    }, [visible, authorizeAppId]);

    useEffect(() => {
        const { fleetIds, searchValue, page, pageSize } = queryParams;
        if (authorizeAppId || authorizeAppId == 0) {
            setTableLoading(true);
            if (type === 'channel') {
                let vehicleIds = undefined,
                    deviceIds = undefined;
                if (searchValue) {
                    // @ts-ignore
                    const { type: searchType, value } = searchValue || {};
                    if (searchType === 'vehicleNumber') {
                        vehicleIds = value;
                    } else {
                        deviceIds = value;
                    }
                }
                const params = {
                    page,
                    pageSize,
                    fleetIds,
                    fields: 'fleet,channel',
                    vehicleIds,
                    deviceIds,
                    appId: authorizeAppId,
                };
                dispatch({
                    type: 'permissionCenter/fetchDeviceData',
                    payload: {
                        ...params,
                        filterChannelInfo: true,
                    },
                })
                    .then((res: any) => {
                        const { total, list } = res || {};
                        listRequestSuccess(list, total);
                    })
                    .finally(() => setTableLoading(false));
            } else {
                const params: QueryParams = {
                    page,
                    pageSize: 10,
                    fleetIds,
                    fields: 'fleet',
                };
                if (type === 'driver' && searchValue) {
                    // @ts-ignore
                    params.driverNameOrJobNumber = `${(searchValue || '').replace(
                        /(^\s*)|(\s*$)/g,
                        '',
                    )}`;
                } else if (type === 'vehicle') {
                    // @ts-ignore
                    params.vehicleNumber = (searchValue || '').replace(
                        /(^\s*)|(\s*$)/g,
                        '',
                    );
                    if (fleetIds && fleetIds !== '') {
                        params.page = 1;
                    }
                } else if (type === 'device') {
                    // @ts-ignore
                    params.deviceNo =
                        (searchValue || '').replace(/(^\s*)|(\s*$)/g, '') ||
                        undefined;
                    if (fleetIds && fleetIds !== '') {
                        params.page = 1;
                    }
                    dispatch({
                        type: 'permissionCenter/fetchDeviceData',
                        payload: {
                            ...params,
                            appId: authorizeAppId,
                        },
                    }).then((rs: any) => {
                        const { total = 0, list = [] } = rs || {};
                        listRequestSuccess(list, total);
                    });
                    return;
                }
                // 此处根据line 和station  查询数据
                if (type === 'line' || type === 'station') {
                    const lineOrStationParams: any = {
                        fleetIds,
                        keyword: searchValue,
                        stationName: searchValue,
                        appId: authorizeAppId,
                        userId: getAppGlobalData('APP_USER_INFO').userId,
                        page,
                        pageSize,
                    };

                    (type == 'line' ? getUserLine : getUserStation)({
                        ...lineOrStationParams,
                    }).then((res: any) => {
                        const { total = 0, list = [] } = res || {};
                        listRequestSuccess(list, total);
                    });
                }

                if (type === 'driver' || type === 'vehicle') {
                    dispatch({
                        type: 'permissionCenter/fetchVehicleOrDriver',
                        payload: {
                            ...params,
                            type,
                            domainType: type === 'driver' ? 3 : 1,
                            appId: authorizeAppId,
                            fields: 'fleet',
                            vehicleNumber: params.vehicleNumber
                                ? encodeURIComponent(params.vehicleNumber)
                                : null,
                            decode: params.vehicleNumber ? 1 : null,
                        },
                    }).then((rs: any) => {
                        const { total = 0, list = [] } = rs || {};
                        listRequestSuccess(list, total);
                    });
                }
                if (type === 'user') {
                    getUserPage({
                        page,
                        pageSize,
                        // @ts-ignore
                        account: (searchValue || '').trim() || undefined,
                    })
                        .then((res) => {
                            const { list, total } = res || {};
                            listRequestSuccess(list, total);
                        })
                        .finally(() => setTableLoading(false));
                }
            }
        }
    }, [queryParams]);

    // 点击查询，提交表单数据
    const handleSubmit = (values: FormFieldValue) => {
        const { company = [], searchValue } = values;
        // 点击查询的时候保存查询条件，用于分页查询数据
        setQueryParams(() => ({
            ...queryParams,
            page: 1, // 点击查询重置页码
            fleetIds: company.map((p: any) => p.value).join(','),
            searchValue,
        }));
    };

    const onSelect = (record: any, selected: boolean) => {
        const currentKey = record[idKey];
        let selectedKeys = leftSelected;
        if (selected) {
            selectedKeys = [...new Set(selectedKeys.concat(currentKey))];
        } else {
            selectedKeys = selectedKeys.filter((key) => key !== currentKey);
        }
        setLeftSelected(selectedKeys);
    };

    const onSelectAll = (selected: boolean) => {
        const currentPageKeys = (leftShowDataList || []).map(
            (item) => item[idKey] as string,
        );
        let selectedKeys = leftSelected || [];
        if (selected) {
            selectedKeys = [...new Set(selectedKeys.concat(currentPageKeys))];
        } else if (backShow) {
            selectedKeys = selectedKeys.filter(
                (key) => !currentPageKeys.includes(key),
            );
        } else {
            const vals = selectedKeys.map((val) => {
                if (targetValMap[val]) return val;
                if (currentPageKeys.includes(val)) return null;
                return val;
            });
            selectedKeys = compact(vals);
        }
        setLeftSelected(selectedKeys);
    };

    const rightChange = (vals: string[]) => {
        if (backShow) {
            setLeftSelected(vals);
        } else {
            const targetKeys = (targetList || []).map((item) => item[idKey]);
            setLeftSelected(targetKeys.concat(vals));
        }
    };

    const rowSelection = {
        selectedRowKeys: leftSelected,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        getCheckboxProps: (record: any) => ({
            disabled: record.defaultChecked,
        }),
    };

    const companyFormItem =  type === 'user' ? null : {
        label: i18n.t('name', '归属车组'),
        name: "company",
        field: StarryMuliteTreeSelect,
        fieldProps: {
            treeCheckable: true,
            treeCheckStrictly: true,
            showArrow: true,
            allowClear: true,
            dropdownMatchSelectWidth: false,
            treeData: companyTreeData,
            placeholder: i18n.t('message', '请选择车组'),
            maxTagCount: 'responsive' as const,
            dropdownStyle: {
                maxWidth: '400px',
            },
            showCheckedStrategy: "TreeSelect.SHOW_ALL"
        },
    }

    const searchFormItem = useMemo(()=>{
        return {
            label: type === 'channel' ? '' : getSearchLabel(type),
            name: "searchValue",
            field: type === 'channel' ? VehicleInput : Input,
            fieldProps: type === 'channel' ? {
                searchType: 'server',
                authorizeAppId: authorizeAppId,
            }:{
                allowClear: true,
                placeholder: getLeftSearchInputTitle(type),
                maxLength: 50
            }
        }
    },[type,authorizeAppId])

    // 获取表单项
    const getFormItems = () => {
        const items = [
            type === 'channel' ||
            type === 'user'
                ? searchFormItem
                : companyFormItem,
            type === 'channel' ||
            type === 'user'
                ? companyFormItem
                : searchFormItem
        ].filter(Boolean);
        return items;
    };

    const isResponsive = useResponsiveShow({ xs: false, sm: false, md: false, lg: false, xl: true, xxl: true });
    return (
        <div className="sup-tenant-visit-authorize-custom-transfer">
            <div className="left-wrapper custom-transfer-list">
                <div className="custom-transfer-list-body">
                    <RspBasicLayout preset="VideoMap" gutter={[0, 0]}>
                        {!isResponsive && (
                            <RspBasicLayout.Item>
                                <div className={`right-wrapper ${!isResponsive ? 'right-wrapper-top' : ''}`}>
                                    <TransferRight
                                        ref={transferRightRef}
                                        dataSource={rightDataSource}
                                        value={rightValues}
                                        onChange={rightChange}
                                    />
                                </div>
                            </RspBasicLayout.Item>
                        )}
                        <RspBasicLayout.Item>
                            <div style={{ paddingTop: isAbroadStyle?'0':12 }}>
                                <div className="search-form-wrapper">
                                    <ProForm.QueryForm
                                        items={getFormItems()}
                                        onSearch={handleSubmit}
                                        layout={'vertical'}
                                        resetText={false}
                                    />
                                </div>
                                <div className="table-content-wrapper">
                                    <Table
                                        loading={tableLoading}
                                        dataSource={leftShowDataList}
                                        columns={generateColumns(type)}
                                        rowKey={idKey}
                                        bordered={false}
                                        pagination={{
                                            total: totals,
                                            current: queryParams.page,
                                            pageSize: 10,
                                            showQuickJumper: false,
                                            showSizeChanger: false,
                                            showTotal: Total.showTotal,
                                            onChange: (page: number) => {
                                                setQueryParams({
                                                    ...queryParams,
                                                    page,
                                                });
                                            },
                                        }}
                                        rowSelection={rowSelection}
                                    />
                                </div>
                            </div>
                        </RspBasicLayout.Item>
                        {isResponsive && (
                            <RspBasicLayout.Item>
                                <div className="right-wrapper">
                                    <TransferRight
                                        ref={transferRightRef}
                                        dataSource={rightDataSource}
                                        value={rightValues}
                                        onChange={rightChange}
                                    />
                                </div>
                            </RspBasicLayout.Item>
                        )}
                    </RspBasicLayout>
                </div>
            </div>
        </div>
    );
};

const CustomTransferWrapper = React.forwardRef<
    CustomTransferRef,
    CustomTransferProps
>(CustomTransfer) as (
    props: React.PropsWithChildren<CustomTransferProps> & {
        ref?: React.Ref<CustomTransferRef>;
    },
) => React.ReactElement;

export default CustomTransferWrapper;
