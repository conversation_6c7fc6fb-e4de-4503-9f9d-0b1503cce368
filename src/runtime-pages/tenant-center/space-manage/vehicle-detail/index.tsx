import { useEffect, useState, useRef } from 'react';
import { Button, Container, message, Space, Tabs, Tag } from '@streamax/poppy';
import {
    PageCardLayout,
    PageBreadcrumbLayout,
    StarryModal,
} from '@base-app/runtime-lib';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { i18n, useSystemComponentStyle, utils } from '@base-app/runtime-lib';
import { Descriptions, InfoPanel } from '@streamax/starry-components';
import { useLocation } from '@base-app/runtime-lib/core';
import { IconRequest } from '@streamax/poppy-icons';
import { getVehicleDetail } from '@/service/vehicle';
import { getCapacityByVehicle } from '@/service/flow-center';
import clearTaskApi from '@/service/clear-task';
import DeviceShow from '@/components/DeviceShow';
import type { DeviceData } from '@/components/DeviceShow';
import Detail from './components/Detail';
import ManualClear, { EVIDENCE_TYPES_PARAM } from './components/ManualClear';
import { SPACE_STATE } from '../const';
import './index.less';
import { useRequest } from '@streamax/hooks';
import { getStorageStatisticsSwitch } from '@/service/meal';
import { getDeviceDetail } from '@/service/device';
import { byteTransform } from '../utils';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import type { SpaceVehicleDetailCustom } from '@/types/pageReuse/pageReuseBase';

export type SpaceVehicleDetailShareProps = SpaceVehicleDetailCustom;

const defaultPage = {
    page: 1,
    pageSize: 20,
};
const {
    formator: { formatByte },
} = utils;
const SpaceVehicleDetail = (props: SpaceVehicleDetailShareProps) => {

    /** 复用 */
    const { getCardDetail, injectSearchList } = props;
    /** 复用 */

    const { isAbroadStyle } = useSystemComponentStyle();
    const {
        // @ts-ignore
        query: { deviceId, vehicleId },
    } = useLocation();
    const [vehicleData, setVehicleData] = useState<Record<string, any>>({});
    const [total, setTotal] = useState<string>('');
    const manualClearRef = useRef<any>();
    // 存储统计开关
    const statisticsSwitch = useRequest(getStorageStatisticsSwitch).data || false;
    useEffect(() => {
        if (deviceId) {
            initData();
        }
    }, [deviceId]);
    const initData = () => {
        getDeviceDetail({
            deviceId,
            fields: 'fleet, device',
        }).then((rs: Record<string, any>) =>
            setVehicleData((prev) => ({
                ...prev,
                ...rs,
            })),
        );
        getCapacityByVehicle(
            Object.assign(defaultPage, {
                deviceIds: deviceId,
                vehicleIds: vehicleId,
            }),
        ).then((rs) => {
            if (!rs) return;
            const { vehicleSize, usedSize, status } = rs.list?.[0] || {
                vehicleSize: null,
                usedSize: null,
                status: null,
            };
            setVehicleData((prev) => ({
                ...prev,
                vehicleSize,
                usedSize,
                status,
            }));
        });
    };
    const onChange = () => {
        initData();
    };

    const handleClear = () => {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '清理确认'),
            content: i18n.t('message', '清理后车辆的报警和视频文件将无法恢复。'),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                const params = {
                    taskName: i18n.t('name', '车辆可清理文件'),
                    taskType: '2', // 任务类型：1-自动生成,2-手动触发
                    condition: {
                        vehicleIds: vehicleId,
                        evidenceTypes: EVIDENCE_TYPES_PARAM,
                    },
                    logParams: [
                        {
                            data: vehicleData.vehicleNumber,
                        },
                    ],
                };
                clearTaskApi.createClearTask(params).then((rs) => {
                    if (rs) {
                        message.success(i18n.t('message', '操作成功'));
                    }
                });
                manualClearRef?.current?.getVehicleFileList();
            },
        });
    };

    const items = [
        {
            label: i18n.t('name', '车牌号'),
            content: vehicleData.vehicleNumber || '-',
            key: 1,
        },
        {
            label: i18n.t('name', '设备号'),
            content: <DeviceShow deviceInfo={vehicleData as DeviceData} />,
            key: 2,
        },
        {
            label: i18n.t('name', '归属车组'),
            content: (vehicleData.fleetList || [])
                .map(({ fleetName }: Record<'fleetName', string>) => fleetName)
                .join('、'),
            key: 3,
        },
        {
            label: i18n.t('name', '容量上限'),
            content: !vehicleData.vehicleSize
                ? '-'
                : byteTransform(Number(vehicleData.vehicleSize), true, 'GB', 1) ?? '-',
            key: 4,
        },
        {
            label: i18n.t('name', '已用容量'),
            // eslint-disable-next-line no-nested-ternary
            content: !statisticsSwitch
                ? '-'
                : vehicleData.usedSize
                    ? formatByte(vehicleData.usedSize, true)
                    : 0,
            key: 5,
        },
        {
            label: i18n.t('name', '空间状态'),
            content: SPACE_STATE()[vehicleData.status]?.label ?? '-',
            key: 6,
        },
    ];

    return (
        <PageBreadcrumbLayout>
            <PageCardLayout>
                <InfoPanel
                    title={
                        <Space wrap>
                            {vehicleData.vehicleNumber}
                            {SPACE_STATE()[vehicleData?.status]?.label ? (
                                <Tag
                                    color={SPACE_STATE()[vehicleData?.status].color}
                                    style={{ display: 'block' }}
                                >
                                    {SPACE_STATE()[vehicleData?.status]?.label}
                                </Tag>
                            ) : null}
                        </Space>
                    }
                    showCollapseBtn
                    outTitle
                    defaultCollapsed={true}
                    extraRight={
                        <Button danger onClick={handleClear}>{i18n.t('action', '清理')}</Button>
                    }
                    operationWrap
                >
                    <Descriptions className='vehicle-space-detail-description'>
                        {items.map((item) => (
                            <Descriptions.Item label={item.label} key={item.key}>
                                {item.content}
                            </Descriptions.Item>
                        ))}
                    </Descriptions>
                </InfoPanel>
                <Tabs defaultActiveKey="1" destroyInactiveTabPane={true} hiddenLine={isAbroadStyle}>
                    <Tabs.TabPane tab={i18n.t('name', '分配明细')} key="1">
                        <Detail
                            deviceId={deviceId}
                            statisticsSwitch={statisticsSwitch}
                            onGetVehicleIdDetailSuccess={(capacity) => setTotal(capacity)}
                            onChange={onChange}
                            usedSize={vehicleData?.usedSize}
                        />
                    </Tabs.TabPane>
                    <Tabs.TabPane tab={i18n.t('name', '可清理文件')} key="2">
                        <ManualClear
                            vehicleNumber={vehicleData.vehicleNumber}
                            vehicleId={vehicleId}
                            ref={manualClearRef}
                            getCardDetail={getCardDetail}
                            injectSearchList={injectSearchList}
                        />
                    </Tabs.TabPane>
                </Tabs>
            </PageCardLayout>
        </PageBreadcrumbLayout>
    );
};

export default withShareRootHOC<SpaceVehicleDetailShareProps, any>(SpaceVehicleDetail);
