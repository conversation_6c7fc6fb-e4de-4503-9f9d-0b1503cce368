import React, { forwardRef, useContext, useEffect, useImperativeHandle, useState } from 'react';
//@ts-ignore
import {
    Action,
    evidenceUtils,
    g_emmiter,
    getAppGlobalData,
    i18n,
    mosaicManager,
    MosaicTypeEnum,
    StarryModal as Modal,
    useUrlSearchStore,
    utils,
} from '@base-app/runtime-lib';
import VideoCard from './VideoCard';
import { IconRefresh, IconRequest as DeleteConfirmIcon } from '@streamax/poppy-icons';
import {
    Button,
    Checkbox,
    ConfigProvider,
    Container,
    Form,
    message,
    ProForm,
    Select,
    Space,
    Spin,
    Tooltip,
} from '@streamax/poppy';
import { useUpdateEffect } from '@streamax/hooks';
import Icon from '@streamax/poppy-icons/lib/Icon';
import './ManualClear.less';
import moment from 'moment';
import alarmApi from '@/service/alarm';
import evidenceApi, { batchDeleteEvidence, DElFlAG } from '@/service/evidence';
import DateRange from '@/components/DateRange';
import classNames from 'classnames';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { FileType, getPickerRanges } from '@/utils/commonFun';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import DisableRightMouseClickImage from '@/components/DisableRightMouseClickImage';
import InfoBack from '@/components/InfoBack';
import Empty from '@/components/AuthorizeRoleCom/Empty';
import { ProtocolTypesEnum } from '@/types';
import { head } from 'lodash';
import { RspCardLayout } from '@streamax/responsive-layout';
import type { SpaceVehicleDetailCustom } from '@/types/pageReuse/pageReuseBase';
import { aspectRatioImage } from '@base-app/runtime-lib';
import { getCustomJsx } from '@/utils/pageReuse';
import { renderEvidenceCardType } from '@/runtime-lib/modules/Evidence/utils';

const {
    formator: {
        formatByte,
        secondsToTime,
        timestampToZeroTimeStamp,
        zeroTimeStampToFormatTime,
        getLocalMomentByZeroTimeStamp,
    },
} = utils;
const { QueryForm } = ProForm;
type Props = {
    vehicleId: string;
    vehicleNumber: string;
} & SpaceVehicleDetailCustom;
interface ReqParams {
    page: number;
    pageSize: number;
    vehicleIds: string;
    timeRange: any[];
    alarmTypes?: string;
    sourceTypes: string;
    cleanStatus: number;
    states?: any;
    evidenceTypes?: string;
}

const CLEAN_STATUS = 0; // 证据清理状态：0-未清理，1-已清理
export const EVIDENCE_TYPES_PARAM = '1,2,5,8,9'; // 查询、清理 - 证据类型的参数
const VIDEO_FILE_TYPES = '2,5,8'; // 同视频库页面的evidenceTypes
const EVIDENCE_FILE_TYPES = '1,9'; // 同证据列表页面的evidenceTypes
const INITIAL_SIZE = '0.00 MB';
const DONE = 3;
const DataClear = (props: Props, ref: any) => {

    /** 复用 */
    const { getCardDetail, injectSearchList } = props;
    /** 复用 */
    const { AspectRatioImage } = aspectRatioImage.AspectRatioImageV1;

    const { vehicleId, vehicleNumber } = props;
    const { renderEmpty } = useContext(ConfigProvider.ConfigContext);
    mosaicManager.useMosaicInit();

    const FILE_TYPES = [
        {
            label: i18n.t('name', '视频文件'),
            value: VIDEO_FILE_TYPES,
        },
        {
            label: i18n.t('name', '证据文件'),
            value: EVIDENCE_FILE_TYPES,
        },
    ];
    const initTimeRange = [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')];
    const initQueryParams = {
        page: 1,
        pageSize: 20,
        vehicleIds: '',
        timeRange: [...initTimeRange],
        sourceTypes: '',
        cleanStatus: CLEAN_STATUS,
    };
    const searchStore = useUrlSearchStore();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [queryParams, setQueryParams] = useState<ReqParams>(initQueryParams);
    const [listData, setListData] = useState<any[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [size, setSize] = useState<string | number>(INITIAL_SIZE);
    const [alarmTypes, setAlarmTypes] = useState<any>([]);
    const [checkedList, setCheckedList] = useState<string[]>([]);
    const [indeterminate, setIndeterminate] = useState(false); // 全选按钮的半选样式
    const [checkAll, setCheckAll] = useState(false); // 全选标志位
    const [hasNextPage, setHasNextPage] = useState<boolean>(false);

    useImperativeHandle(ref, () => ({
        getVehicleFileList: () => fetchList(),
    }));
    useEffect(() => {
        // 报警类型
        alarmApi
            // .getAuthorityCategory({ appId })
            .getAuthAlarmTypes({
                tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
                page: 1,
                pageSize: 1e5,
                state: 1,
            })
            .then((rs) => {
                setAlarmTypes(rs);
            });
        const {
            page,
            pageSize,
            alarmTypes,
            cleanStatus = CLEAN_STATUS,
            startTime,
            endTime,
            states,
            appId,
            fleetIds,
            vehicleIds,
            includeSubFleet,
            searchCarGroupName,
            ...values
        } = searchStore.get();
        const searchList = {
            ...values,
            page: Number(page) || 1,
            pageSize: Number(pageSize) || 20,
            alarmTypes: alarmTypes && Number(alarmTypes),
            cleanStatus: cleanStatus && Number(cleanStatus),
            timeRange:
                startTime && endTime
                    ? [
                        getLocalMomentByZeroTimeStamp(Number(startTime)) || null,
                        getLocalMomentByZeroTimeStamp(Number(endTime)) || null,
                    ]
                    : initTimeRange,
            vehicleIds: vehicleIds,
            fleetIds,
            includeSubFleet: (includeSubFleet && Number(includeSubFleet)) || 1,
        };
        form.setFieldsValue(searchList);
        setQueryParams(searchList);
        // fetchList();
    }, []);

    // 获取证据列表
    const fetchList = async () => {
        setLoading(true);
        let startTime, endTime;
        if (queryParams.timeRange?.[0]) {
            startTime = timestampToZeroTimeStamp(queryParams.timeRange?.[0]);
            endTime = timestampToZeroTimeStamp(queryParams.timeRange?.[1]);
        } else {
            form.setFieldsValue({
                ...form.getFieldsValue(),
                timeRange: initTimeRange,
            });
            startTime = timestampToZeroTimeStamp(initTimeRange[0]);
            endTime = timestampToZeroTimeStamp(initTimeRange[1]);
        }
        const params = {
            ...queryParams,
            vehicleIds: vehicleId,
            startTime,
            endTime,
            fields: 'driver,file,alarm,vehicle,file_url,videoChannel',
            evidenceTypes: queryParams.evidenceTypes ?? EVIDENCE_TYPES_PARAM,
            states: DONE,
        };
        const { page, pageSize, vehicleIds, alarmTypes, sourceTypes, cleanStatus } = params;
        searchStore.set({
            startTime,
            endTime,
            page,
            pageSize,
            vehicleIds,
            cleanStatus,
            alarmTypes,
            sourceTypes,
            evidenceTypes: queryParams.evidenceTypes,
        });
        // @ts-ignore
        delete params.timeRange;
        // @ts-ignore
        delete params.vehicleId;
        if (injectSearchList) {
            setLoading(true);
            const { list, hasNextPage = false, dataSize } = await injectSearchList(params).finally(() => {
                setLoading(false);
            });
            setListData(list.map((i: any) => {
                return i;
            }));
            setHasNextPage(hasNextPage);
            setSize(
                formatByte(dataSize.totalSize || 0),
            );
            setTotal(dataSize.total || 0);
            return;
        }
        const mosaicFlags = mosaicManager.getMosaicFlags(params.evidenceTypes, (type) => {
            const isVideo = VIDEO_FILE_TYPES.includes(type);
            return isVideo ? MosaicTypeEnum.videoStore : MosaicTypeEnum.alarm;
        });
        evidenceApi
            .getPageList({
                ...params,
                fields: `${params.fields},noTotal`,
                mosaicFlags,
            })
            .then((rs: any) => {
                const list = rs.list || [];
                const data = list.map((i: any) => {
                    return i;
                });
                setHasNextPage(rs?.hasNextPage);
                if (data?.length === 0 && page !== 1) {
                    const currentPage = page - 1;
                    setQueryParams({
                        ...queryParams,
                        page: currentPage,
                    });
                    return;
                }
                setListData(data);
                // 获取统计数据
                evidenceApi
                    .getFileTotalSize({
                        ...params,
                    })
                    .then((rs) => {
                        if (!rs.totalSize) {
                            setSize(INITIAL_SIZE);
                        } else {
                            setSize(formatByte(rs.totalSize));
                        }
                        const totalNum = rs.total || 0;
                        setTotal(totalNum);
                    });
            })
            .catch(() => {
                setListData([]);
                setHasNextPage(false);
                setSize(INITIAL_SIZE);
                setTotal(0);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const searchHandler = (params: ReqParams) => {
        setQueryParams({
            ...queryParams,
            ...params,
            page: 1,
            vehicleIds: vehicleId,
        });
    };

    const pageChange = (pageArow: string) => {
        if (pageArow === 'pre') {
            setQueryParams({
                ...queryParams,
                page: queryParams.page - 1,
                vehicleIds: vehicleId,
            });
        } else {
            setQueryParams({
                ...queryParams,
                page: queryParams.page + 1,
                vehicleIds: vehicleId,
            });
        }
    };

    const queryItems = [
        {
            label: i18n.t('name', '文件类型'),
            name: 'evidenceTypes',
            field: Select,
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择文件类型'),
                options: FILE_TYPES,
            },
        },
        {
            label: i18n.t('name', '报警类型'),
            name: 'alarmTypes',
            field: Select,
            itemProps: {},
            fieldProps: {
                allowClear: true,
                placeholder: i18n.t('message', '请选择报警类型'),
                options: alarmTypes,
            },
        },
        {
            label: i18n.t('name', '时间范围'),
            name: 'timeRange',
            colSize: 2,
            field: DateRange,
            itemProps: {
                initialValue: initTimeRange,
            },
            fieldProps: {
                maxInterval: {
                    value: 31,
                    unitOfTime: 'days',
                },
                pickerProps: {
                    showTime: {
                        hideDisabledOptions: true,
                        defaultValue: [
                            moment().subtract(6, 'days').startOf('day'),
                            moment().endOf('day'),
                        ],
                    },
                    separator: '~',
                    ranges: getPickerRanges(),
                    disabledDate: (current: any) => current && current > moment().endOf('day'),
                    style: {
                        width: '100%',
                    },
                },
            },
        },
    ];

    // 创建清除任务
    const batchDelete = () => {
        // 参考使用证据/视频库列表的删除
        const modal = Modal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content: i18n.t('message', '确认删除所选文件吗？'),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <Icon component={DeleteConfirmIcon as any} />,
            onOk: () => {
                batchDeleteEvidence({
                    evidenceIds: checkedList.join(','),
                    delFlag: DElFlAG.File,
                }).then((rs) => {
                    if (!rs) return;
                    modal.destroy();
                    message.success(i18n.t('message', '删除成功'));
                    setCheckedList([]);
                    setCheckAll(false);
                    setIndeterminate(false);
                    fetchList();
                });
            },
            onCancel: () => {
                modal.destroy();
            },
        });
    };

    // 重置
    const handleReset = () => {
        setQueryParams(initQueryParams);
    };

    useUpdateEffect(() => {
        g_emmiter.emit('clear.manage.search');
        fetchList();
    }, [queryParams]);

    // 获取证据名称
    const getEvidenceName = (data: any) => {
        if (!data?.evidenceName) return '';
        const evidenceName = i18n.t(
            `@i18n:@alarmType__${data?.alarmInfo?.alarmType}`,
            data?.evidenceName,
        );
        return evidenceName;
    };

    const renderTime = (item: Record<string, any>) => {
        if (item.evidenceType === 1) {
            return item.happenTime ? zeroTimeStampToFormatTime(item.happenTime) : '-';
        } else {
            return item.startTime ? zeroTimeStampToFormatTime(item.startTime) : '-';
        }
    };

    const listDom =
        listData.length === 0 ? (
            <div style={{ width: '100%' }}> {renderEmpty('List')}</div>
        ) : (
            <Checkbox.Group
                onChange={(list: string[]) => {
                    setCheckedList(list);
                    setIndeterminate(!!list.length && list.length < listData.length);
                    setCheckAll(list.length === listData.length);
                }}
                value={checkedList}
            >
                <RspCardLayout>
                    {listData.map((item) => {
                        const { fileList = [], vehicleInfo, videoChannelList, evidenceType } = item || {};
                        const { videoList, videoPriority } = evidenceUtils.getVideoList({
                            fileList: fileList,
                            channelPriority: videoChannelList,
                            isN9M: vehicleInfo?.protocolType === ProtocolTypesEnum.N9M,
                            isForceH264: false,
                            evidenceType
                        });
                        const imageList = evidenceUtils.getImageList(fileList, videoPriority);
                        const firstVideo = head(videoList) as any;
                        const existH264 = firstVideo?.fileType === FileType.H264;

                        const tooltip = (
                            <Tooltip title={getEvidenceName(item)}>
                                <OverflowEllipsisContainer>
                                    {getEvidenceName(item)}
                                </OverflowEllipsisContainer>
                            </Tooltip>
                        );
                        // type=1 手动证据 证据名称 or type=2 自动证据 报警类型
                        const evidenceAction =
                            item.evidenceType === 1 ? (
                                <Action
                                    code="@base:@page:space.manage:vehicle.detail@action:tab.cleanable.file:view.evidence"
                                    url="/evidence/detail"
                                    target="blank"
                                    params={{
                                        evidenceId: item.evidenceId,
                                        fromList: '1',
                                    }}
                                    needWidthPage
                                    fellback={tooltip}
                                >
                                    {tooltip}
                                </Action>
                            ) : (
                                <Action
                                    code="@base:@page:space.manage:vehicle.detail@action:tab.cleanable.file:view.video"
                                    url="/video/detail"
                                    target="blank"
                                    params={{
                                        evidenceId: item.evidenceId,
                                        fromList: '1',
                                        type: 'video',
                                    }}
                                    needWidthPage
                                    fellback={tooltip}
                                >
                                    {tooltip}
                                </Action>
                            );
                        const detail = (
                            <div className="base-info">
                                <p className="info-title text-overflow-ellipsis">
                                    {evidenceAction}
                                </p>
                                <p className="text-overflow-ellipsis">
                                    <Tooltip title={item.vehicleInfo.vehicleNumber}>
                                        <OverflowEllipsisContainer>
                                            {item.vehicleInfo.vehicleNumber}
                                        </OverflowEllipsisContainer>
                                    </Tooltip>
                                </p>
                                <p className="secondary-text">
                                    <span className='secondary-text-title'>
                                        {item.evidenceType === 1
                                            ? i18n.t('name', '报警时间')
                                            : i18n.t('name', '视频时间')}
                                        ：
                                    </span>
                                    <OverflowEllipsisContainer>
                                        {renderTime(item)}
                                    </OverflowEllipsisContainer>
                                </p>
                                <p className="secondary-text">
                                    <span className='secondary-text-title'>{i18n.t('name', '产生时间')}：</span>
                                    <OverflowEllipsisContainer>
                                        {item.createTime
                                            ? zeroTimeStampToFormatTime(item.createTime)
                                            : '-'}
                                    </OverflowEllipsisContainer>
                                </p>
                            </div>
                        );
                        const isVideo = VIDEO_FILE_TYPES.includes(item.evidenceType);
                        const mosaicSourcecode = isVideo ? MosaicTypeEnum.videoStore : MosaicTypeEnum.alarm;
                        const renderEvidenceCardTypeObject = renderEvidenceCardType(firstVideo, imageList, item.fileChannelAuth, item.displayType);
                        return (
                            <div className="list-item" key={item.evidenceId}>
                                <Checkbox
                                    className="card-checkbox"
                                    value={item.evidenceId}
                                    key={item.evidenceId}
                                />
                                {renderEvidenceCardTypeObject.image ? (
                                    <div className="img-wrapper">
                                        <DisableRightMouseClickImage>
                                            <AspectRatioImage preview={false} src={imageList?.[0]?.url} />
                                        </DisableRightMouseClickImage>
                                    </div>
                                ) : null}
                                {renderEvidenceCardTypeObject.video && (
                                    <div className="video-wrapper">
                                        <VideoCard
                                            existH264={existH264}
                                            firstVideo={firstVideo}
                                            evidenceData={item}
                                            mosaicSourcecode={mosaicSourcecode}
                                        />
                                    </div>
                                )}
                                {renderEvidenceCardTypeObject.noData && (
                                    <div className="default-img-block">
                                        <Empty descText={i18n.t('message', '无数据')} />
                                    </div>
                                )}
                                {getCustomJsx(getCardDetail, [detail], {
                                    item,
                                })
                                }

                            </div >
                        );
                    })}
                </RspCardLayout >
            </Checkbox.Group >
        );
    const onCheckAllChange = (e: any) => {
        setCheckedList(e.target.checked ? listData.map((item) => item.evidenceId) : []);
        setIndeterminate(false);
        setCheckAll(e.target.checked);
    };
    return (
        <div className="space-manage-system-data-clear">
            <div className="right">
                <Container className="container-no-padding-bottom">
                    <QueryForm
                        layout="vertical"
                        items={queryItems}
                        collapseCacheKey={'@base:@page:space.manage.clear'}
                        form={form}
                        onSearch={(reqParams) => searchHandler(reqParams)}
                        onReset={handleReset}
                    />
                </Container>


                <div className="operate">
                    <div className="header">
                        <Button
                            type="primary"
                            disabled={checkedList.length === 0}
                            onClick={() => {
                                batchDelete();
                            }}
                        >
                            {i18n.t('action', '批量删除')}
                        </Button>
                        <Space>
                            <Checkbox
                                className="card-checkbox"
                                indeterminate={indeterminate}
                                checked={checkAll}
                                onChange={onCheckAllChange}
                            />
                            {i18n.t('name', '全选')}
                            <Tooltip title={i18n.t('name', '刷新')}>
                                <IconRefresh className="refresh" onClick={() => fetchList()} />
                            </Tooltip>
                        </Space>
                    </div>

                    <InfoBack
                        style={{ marginTop: 24, marginBottom: 24 }}
                        title={i18n.t('message', '包含证据{total}条{space}共计占用存储空间：{size}', {
                            total: <span className="primary-color">{total}</span>,
                            size: <span className="primary-color">{size}</span>,
                            space: <span style={{ width: '30px' }}></span>,
                        })}
                    />
                </div>
                <Spin spinning={loading}>
                    <div className="image-list">{listDom}</div>
                    {listData.length > 0 ? (
                        <div className="alarm-list-pagination-box">
                            <div
                                className={classNames('alarm-list-pagination-box-item', {
                                    'alarm-list-pagination-box-item-disabled':
                                        queryParams.page == 1,
                                })}
                                onClick={() =>
                                    queryParams.page == 1 ? () => { } : pageChange('pre')
                                }
                                title={i18n.t('message', '上一页')}
                            >
                                <LeftOutlined />
                            </div>
                            <div className={`alarm-list-pagination-box-item current-page`}>
                                {queryParams.page}
                            </div>
                            <div
                                onClick={() =>
                                    hasNextPage
                                        ? pageChange('next')
                                        : () => { }
                                }
                                className={classNames('alarm-list-pagination-box-item', {
                                    'alarm-list-pagination-box-item-disabled':
                                        !hasNextPage,
                                })}
                                title={i18n.t('message', '下一页')}
                            >
                                <RightOutlined />
                            </div>
                        </div>
                    ) : null}
                </Spin>

            </div>
        </div>
    );
};

export default forwardRef(DataClear);
