/* eslint-disable consistent-return */
import React, { useState, useEffect, useRef, useContext } from 'react';
import { groupBy, isNil, pick } from 'lodash';
import {
    ListDataContainer,
    OverflowEllipsisContainer,
    TableColumnSetting,
} from '@streamax/starry-components';
import {
    Auth,
    getAppGlobalData,
    i18n,
    StarryAbroadFormItem,
    StarryAbroadIcon,
    utils,
} from '@base-app/runtime-lib';
import {
    Button,
    Select,
    Space,
    Tooltip,
    Form,
    message,
    Badge,
    InputNumber,
    ProTable,
    Modal,
} from '@streamax/poppy';
import {
    IconDetailsFill,
    IconInformationFill,
    IconDistributionFill,
    IconSwitch02Fill,
    IconExport,
} from '@streamax/poppy-icons';
import type { TableProps } from '@streamax/poppy/lib/table';
import type { GetDataSourceValue } from '@streamax/starry-components/lib/list-data-container';
import { useSubmitFn, useUpdateEffect } from '@streamax/hooks';
import { getCapacityByVehicle } from '@/service/flow-center';
import { useHistory } from '@base-app/runtime-lib/core';
import { Action } from '@base-app/runtime-lib';
import { setDeviceSpaceCapacityV2 } from '@/service/server';
import SearchInput from '@/components/SearchInput';
import DeviceShow from '@/components/DeviceShow';
import type { DeviceData } from '@/components/DeviceShow';
import GroupTreeSpaceAndFlow from '@/components/GroupTreeSpaceAndFlow';
import { SPACE_STATE } from '../const';
import { byteTransform, transferToByte } from '../utils';
import { SwitchContext } from '../index';
import './VehicleUse.less';
import { tableStorageKey } from '@/const/consants';
import InfoBack from '@/components/InfoBack';
import { formatToNearestHalf } from '@/utils/commonFun';
import { VEHICLE_SPACE_SIZE } from '@/utils/constant';
import { shallow } from 'zustand/shallow';
import useRealtimeRTData from '@/modules/realtime-monitor/fleet-vehicle-tree/data/useRealtimeRTData';
import useTreeDataStore, { getTreeDataStore } from '@/modules/vehicle-tree/data/useTreeDataManager/useTreeDataStore';
import { RspDrawerTemplate } from '@streamax/responsive-layout';
import { useDebounceFn } from 'ahooks';
import { exportExcel } from '@/service/import-export';
import moment from 'moment';

import { uuid } from '@/runtime-lib/utils/general';
import { useUnmount } from 'ahooks';
let spaceManageInstanceId = ''
export default (props: { status: string }) => {
    const { status } = props;
    const listDataContainerRef =
        useRef<Record<'loadDataSource', (params?: any) => void>>(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [fleetId, setFleetId] = useState('');
    const [fleetName, setFleetName] = useState<string>();
    const [modalOpen, setModalOpen] = useState(false);
    const [deviceIds, setDeviceIds] = useState<string[]>([]);
    const [usedSize, setUsedSize] = useState(0);
    const [lastQueryParams, setLastQueryParams] = useState({});
    const topFleetIdRef = useRef('');
    const [form] = Form.useForm();
    const [modalForm] = Form.useForm();
    const history = useHistory();
    spaceManageInstanceId = spaceManageInstanceId || 'space-manage' + uuid()
    const instanceId = spaceManageInstanceId
    const treeDataStore = getTreeDataStore(instanceId)
    const { loaded, loadFleets, fleetList } = treeDataStore(
        (state) =>
            pick(state, [
                'loaded',
                'fleetLoaded',
                'loadFleets',
                'refresh',
                'fleetList',
                'filteredVehicleList',
            ]),
        shallow,
    );
    useUnmount(()=>{
        spaceManageInstanceId = ''
    })
    useRealtimeRTData(
        undefined,
        undefined,
        {
            showVehicle: false,
            vehicleDataModule: [],
        },
        instanceId
    );

    useUpdateEffect(() => {
        const params = form.getFieldsValue();
        listDataContainerRef.current?.loadDataSource({ ...params, page: 1 });
    }, [fleetId]);

    useEffect(() => {
        if (status) {
            form.setFieldsValue({ status });
        }
    }, [status]);
    const { statisticsSwitch } = useContext(SwitchContext);
    const queryFormItems = [
        {
            label: '',
            name: 'search',
            field: SearchInput,
            fieldProps: {
                placeholder: i18n.t('name', '请输入车牌号码'),
                maxLength: 50,
                width: 280,
                allowClear: true,
            },
        },
        {
            label: i18n.t('name', '空间状态'),
            name: 'status',
            field: Select,
            fieldProps: {
                placeholder: i18n.t('name', '请选择空间状态'),
                width: 280,
                options: Object.keys(SPACE_STATE()).map((key) => ({
                    label: SPACE_STATE()[key].label,
                    value: key,
                })),
                allowClear: true,
            },
        },
    ];
    const onTreeDataLoad = (treeData) => {
        topFleetIdRef.current = treeData?.[0]?.fId || '';
    };
    const viewDetails = (record: any) => {
        Action.openActionUrl({
            code: '@base:@page:space.manage@action:tab.vehicle:distribute.detail',
            history,
            url: '/space-manage/vehicle-detail',
            params: {
                deviceId: record.deviceId,
                vehicleId: record.vehicleId,
            },
            target: 'blank',
        });
    };

    const handleAllocation = (id: string, row) => {
        row.usedSize &&
            setUsedSize(Number(byteTransform(Number(row.usedSize), false)));
        setModalOpen(true);
        setDeviceIds([id]);
    };

    const columns = [
        {
            title: i18n.t('name', '车牌号'),
            dataIndex: 'vehicleNumber',
            width: 200,
            fixed: 'left',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '归属车组'),
            dataIndex: 'fleetNames',
            width: 200,
            ellipsis: true,
        },
        {
            title: i18n.t('name', '设备号'),
            dataIndex: 'deviceNo',
            ellipsis: true,
            width: 200,
            render: (_: string, record: DeviceData) => (
                <DeviceShow deviceInfo={record} />
            ),
        },
        {
            title: i18n.t('name', '容量上限'),
            dataIndex: 'vehicleSize',
            ellipsis: true,
            width: 180,
            render: (text: string) =>
                // 后端：'0'代表未分配空间 需要展示 -
                statisticsSwitch && text && Number(text)
                    ? byteTransform(Number(text), true, 'GB', 1)
                    : '-',
        },
        {
            title: i18n.t('name', '已用容量'),
            dataIndex: 'usedSize',
            width: 180,
            ellipsis: true,
            // sorter: true,
            // defaultSortOrder: 'descend',
            // sortDirections: ['descend', 'ascend', 'descend'],
            render: (text: number) =>
                statisticsSwitch && text ? byteTransform(text) : '-',
        },
        {
            title: i18n.t('name', '空间状态'),
            dataIndex: 'status',
            ellipsis: true,
            width: 200,
            render: (value: number) =>
                statisticsSwitch && value ? (
                    <Badge
                        color={SPACE_STATE()[value].color}
                        text={SPACE_STATE()[value].label}
                    />
                ) : (
                    '-'
                ),
        },
        {
            title: i18n.t('name', '操作'),
            width: 180,
            fixed: 'right',
            dataIndex: 'action',
            render: (_: unknown, record: any) => {
                return statisticsSwitch ? (
                    <Space size={utils.constant.BASE_TABLE_OPERATE_COLUMN_SIZE}>
                        <Auth code="@base:@page:space.manage@action:tab.vehicle:distribute.detail">
                            <Tooltip title={i18n.t('name', '查看详情')}>
                                <a
                                    className="action-button"
                                    onClick={() => viewDetails(record)}
                                >
                                    <IconDetailsFill />
                                </a>
                            </Tooltip>
                        </Auth>
                        <Auth code="@base:@page:space.manage@action:tab.vehicle:batch.distribute">
                            <Tooltip title={i18n.t('name', '分配')}>
                                <a
                                    className="action-button"
                                    onClick={() =>
                                        handleAllocation(
                                            record.deviceId,
                                            record,
                                        )
                                    }
                                >
                                    <IconDistributionFill />
                                </a>
                            </Tooltip>
                        </Auth>
                    </Space>
                ) : null;
            },
        },
    ];

    const fetchData = async (
        params: any,
    ): Promise<GetDataSourceValue<any[]>> => {
        const { search, page, pageSize, status } = params;
        if (!fleetId) {
            return Promise.resolve({
                list: [],
                page: 1,
                pageSize,
                total: 0,
            });
        }
        const reqParams: any = {
            page,
            pageSize,
            status,
            fleetIds: fleetId,
            vehicleNumber:
                search?.type === 'vehicle' ? search?.value?.trim() : undefined,
            deviceNoOrAlias:
                search?.type === 'device' ? search?.value?.trim() : undefined,
        };
        const data = await getCapacityByVehicle(reqParams);
        setLastQueryParams(reqParams);
        return data;
    };

    const handleRowSelectChange = (
        selectedRowKeys: any[],
        selectedRows: any[],
    ) => {
        setSelectedRowKeys(selectedRowKeys);
        setSelectedRows(selectedRows);
    };

    const handleTableChange: TableProps['onChange'] = (
        pagination,
        filters,
        sorter,
    ) => {
        listDataContainerRef.current?.loadDataSource({
            order: (sorter as Record<'order', string | null>)?.order,
        });
    };

    const handleBatch = () => {
        setModalOpen(true);
        setDeviceIds(selectedRowKeys);
        const usedSize = Math.max(
            ...selectedRows.map((item) => {
                if (item.usedSize) {
                    return Number(item.usedSize);
                }
                return 0;
            }),
        );
        setUsedSize(Number(byteTransform(Number(usedSize), false)));
    };
    const drawerRef = useRef(null);
    const handleTreeSelect = (fleetKey: string,treeNode: Record<string, string>) => {
        if(fleetKey) {
            const fleetId = fleetKey.split("-")[1];
            setFleetId(fleetId);
        } else {
            setFleetId(topFleetIdRef.current);
        }
        setFleetName(treeNode?.name || '');
        drawerRef?.current?.closeDrawer();
    };

    const handleOk = () => {
        modalForm.submit();
    };

    const handleCancel = () => {
        setModalOpen(false);
        setUsedSize(0);
        modalForm.resetFields();
    };
    const onFinish = async (values: Record<'capacity', number>) => {
        const detail = deviceIds.map((item) => {
            return { deviceId: item };
        });
        const rs = await setDeviceSpaceCapacityV2({
            tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
            mode: 2,
            commonCapacity: transferToByte(values.capacity, 'GB'),
            detail: detail,
        });
        if (!rs) return;
        message.success(i18n.t('name', '操作成功'));
        listDataContainerRef.current?.loadDataSource();
        setModalOpen(false);
        setDeviceIds([]);
        setSelectedRowKeys([]);
        modalForm.resetFields();
    };
    const [handleFinish, submitLoading] = useSubmitFn(onFinish);
    const { tableColumns, tableColumnSettingProps } =
        TableColumnSetting.useSetting(columns, {
            storageKey: `${tableStorageKey}space.manage`,
            disabledKeys: ['vehicleNumber', 'deviceNo', 'usedSize', 'action'],
        });
    const onStoreBlur = (e) => {
        const value = e.target.value;
        if (value) {
            let roundedValue = formatToNearestHalf(value);
            roundedValue =
                roundedValue < VEHICLE_SPACE_SIZE.MIN
                    ? VEHICLE_SPACE_SIZE.MIN
                    : roundedValue;
            roundedValue =
                roundedValue > VEHICLE_SPACE_SIZE.MAX
                    ? VEHICLE_SPACE_SIZE.MAX
                    : roundedValue;
            // 延迟设置，保证设置时在组件自身格式化之后，然后去覆盖他
            setTimeout(() => {
                modalForm.setFieldsValue({
                    capacity: roundedValue,
                });
            }, 0);
        }
    };

    // 导出数据
    const _exportData = () => {
         const headersArr = tableColumns
             .filter(i=>i.dataIndex !== 'action')
             .map(({dataIndex,title,},index)=>({
                columnName: dataIndex,
                title,
                index,
            }));

        const sheetArr = [
            {
                sheetName: 'Sheet1',
                excelHeaders: headersArr,
                queryParam: {
                    param: {
                        ...lastQueryParams,
                    },
                },
            },
        ];
        exportExcel({
            executorHandler: 'storageExcelImport',
            serviceName: 'base-flow-service',
            taskType: 1527,
            isAsync: true,
            excelType: 'XLSX',
            fileName: i18n.t('name', '空间管理统计') + `_${moment().unix()}`,
            sheetQueryParams: sheetArr,
        }).then(() => {
            message.success(
                i18n.t('message', '导出成功，请到个人中心中查看导出详情'),
            );
        });
    };
    const { run: exportData } = useDebounceFn(_exportData, {
        wait: 500,
    });
    return (
        <div className="vehicle-use">
            <RspDrawerTemplate breakpoint="lg" gutter={[24, 8]}>
                <RspDrawerTemplate.Left
                    ref={drawerRef}
                    drawerTrigger={
                        <div>
                            <span className="rsp-drawer-title">
                                {isNil(fleetName) || fleetName == ''
                                    ? i18n.t('name', '选择车组')
                                    : fleetName}
                            </span>
                            <StarryAbroadIcon>
                                <IconSwitch02Fill />
                            </StarryAbroadIcon>
                        </div>
                    }
                    drawerProps={{
                        width: '400',
                        className: 'vehicle-use-drawer',
                    }}
                >
                    <div className="left">
                        <GroupTreeSpaceAndFlow
                            loaded={loaded}
                            loadFleets={loadFleets}
                            fleetList={fleetList}
                            onSelect={handleTreeSelect}
                            isExpandFirstGroup={true}
                            status={status}
                            searchModule={['fleet']}
                            onTreeDataLoad={onTreeDataLoad}
                        instanceId={instanceId}/>
                    </div>
                </RspDrawerTemplate.Left>
                <RspDrawerTemplate.Right>
                    <div className="right">
                        <ListDataContainer
                            ref={listDataContainerRef}
                            getDataSource={fetchData}
                            loadDataSourceOnMount={false}
                            queryForm={{ items: queryFormItems, form }}
                            toolbar={{
                                extraLeft: (
                                    <Auth code="@base:@page:space.manage@action:tab.vehicle:batch.distribute">
                                        <Button
                                            type="primary"
                                            onClick={handleBatch}
                                            disabled={
                                                !statisticsSwitch ||
                                                selectedRowKeys.length <= 0
                                            }
                                        >
                                            {i18n.t('name', '批量分配')}
                                        </Button>
                                    </Auth>
                                ),
                                extraIconBtns: [
                                    <Tooltip
                                        title={i18n.t('action', '导出')}
                                        placement="top"
                                        key="action-top"
                                    >
                                        <span>
                                            <StarryAbroadIcon>
                                                <IconExport
                                                    onClick={exportData}
                                                />
                                            </StarryAbroadIcon>
                                        </span>
                                    </Tooltip>,
                                    <TableColumnSetting
                                        {...tableColumnSettingProps}
                                        key="table-column-setting"
                                    />,
                                ],
                            }}
                            listRender={(data) => {
                                return (
                                    <>
                                        <InfoBack
                                            style={{ marginBottom: 24 }}
                                            title={i18n.t(
                                                'message',
                                                '设置单车的存储空间上限，空间不足时根据存储配置向相关用户推送预警通知',
                                            )}
                                        />
                                        <ProTable
                                            scroll={{ x: '100%' }}
                                            aroundBordered
                                            columns={tableColumns}
                                            dataSource={data}
                                            pagination={false}
                                            rowKey={'deviceId'}
                                            rowSelection={{
                                                selectedRowKeys:
                                                    selectedRowKeys,
                                                onChange: handleRowSelectChange,
                                                getCheckboxProps: () => ({
                                                    disabled: !statisticsSwitch,
                                                }),
                                            }}
                                            onChange={handleTableChange}
                                        />
                                    </>
                                );
                            }}
                        />
                    </div>
                </RspDrawerTemplate.Right>
            </RspDrawerTemplate>
            <Modal
                className="space-storage-config"
                size="small"
                title={i18n.t('name', '容量分配')}
                visible={modalOpen}
                onOk={handleOk}
                onCancel={handleCancel}
                confirmLoading={submitLoading}
                getContainer={false}
                destroyOnClose
            >
                <Form
                    layout="vertical"
                    form={modalForm}
                    onFinish={handleFinish}
                >
                    <StarryAbroadFormItem>
                        <InfoBack
                            title={i18n.t(
                                'message',
                                '请注意车辆已用容量{usedStore}GB，上限值小于{usedStore}GB会导致数据被清理，请谨慎操作',
                                {
                                    usedStore: (
                                        <span className="vehicle-used-store-number">
                                            {' '}
                                            {usedSize}{' '}
                                        </span>
                                    ),
                                },
                            )}
                        />
                    </StarryAbroadFormItem>
                    <StarryAbroadFormItem
                        style={{ marginBottom: 0 }}
                        name="capacity"
                        label={i18n.t('name', '容量上限')}
                        rules={[{ required: true }]}
                    >
                        <InputNumber
                            style={{ width: '100%' }}
                            step={0.5}
                            min={VEHICLE_SPACE_SIZE.MIN}
                            onBlur={onStoreBlur}
                            precision={1}
                            max={VEHICLE_SPACE_SIZE.MAX}
                            placeholder={i18n.t('message', '请输入容量上限')}
                            addonAfter={i18n.t('name', 'GB')}
                        />
                    </StarryAbroadFormItem>
                </Form>
            </Modal>
        </div>
    );
};
