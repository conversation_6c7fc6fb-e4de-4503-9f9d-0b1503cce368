import { StarryAbroadOverflowEllipsisContainer, StarryCard, StarryBreadcrumb, StarryModal, i18n, utils, useUrlSearchStore, getAppGlobalData } from '@base-app/runtime-lib';
import { Input, Tooltip, Badge, Space, Switch, message, Button, Table, Tag } from '@streamax/poppy';
import { useRef, useCallback, useState } from 'react';
import { debounce } from 'lodash';
import InfoBack from '@/components/InfoBack';
import { fetchLanguageList, saveLanguageSetting, LanguageState, DefaultLanguageSwitch, QueryTypeEnum} from '@/service/language';
import type { LanguageItem } from '@/service/language';
import type { GetDataSourceParams, RefListDataContainerProps, GetDataSourceValue } from "@streamax/starry-components/lib/list-data-container";
import type { ColumnsType } from '@streamax/poppy/lib/table';
import calcHeight from "@/utils/calcStickyHeight";
import { ListDataContainer } from '@streamax/starry-components';
import type { LanguageCodeType } from '@/runtime-lib/types/type';
const { languageTypeMap } = utils.langue;
// 常量定义
const DEBOUNCE_DELAY = 300; // 防抖延迟时间（毫秒）
const MAX_INPUT_LENGTH = 50; // 输入框最大字符数

const { confirm } = StarryModal;

// Using the imported GetDataSourceValue interface from the ListDataContainer component

const LanguageSetting = () => {
    const listDataContainerRef = useRef<RefListDataContainerProps>(null);
    const searchStore = useUrlSearchStore();
    const formQuery = searchStore.get();
    const [currentData, setCurrentData] = useState<LanguageItem[]>([]);
    
    // 获取语言列表数据
    const fetchData = async (params: GetDataSourceParams): Promise<GetDataSourceValue<LanguageItem[]>> => {
        params.languageDesc = params.languageDesc?.trim();
        const { languageDesc, page, pageSize } = params;
        
        searchStore.set({
            languageDesc: languageDesc || null,
            page,
            pageSize
        });
        const data = await fetchLanguageList({
            queryType: QueryTypeEnum.ALL_ORIGIN,
            languageDesc: languageDesc || undefined
        });
        
        // 保存当前数据以便其他函数使用
        setCurrentData(data || []);
        
        // 返回符合GetDataSourceValue类型的数据
        return {
            list: data || []
        };
    };
    
    // 防抖处理的保存语言设置函数
    const saveLanguageSettingDebounced = useCallback(
        debounce(async (params: { languageList: LanguageItem[] }) => {
            const result = await saveLanguageSetting(params);
            if (result === false) {
                message.error(i18n.t('message', '操作失败'));
                return;
            }
            message.success(i18n.t('message', '操作成功'));
            // 刷新数据
            listDataContainerRef.current?.loadDataSource();
        }, DEBOUNCE_DELAY),
        []
    );
    
    // 封装更新语言配置的通用逻辑
    const updateLanguageConfig = (updateFn: (item: LanguageItem) => LanguageItem) => {
        const languageList = currentData.map(updateFn);
        saveLanguageSettingDebounced({ languageList });
    };
    
    // 处理语言启用/停用
    const handleLanguageStateChange = (record: LanguageItem, enable: boolean) => {
        const title = enable
            ? i18n.t('name', '启用确认')
            : i18n.t('name', '停用确认');
            
        const content = enable
            ? i18n.t('message', '确认启用{language}？', { language: getLanguageText(record.language as LanguageCodeType) })
            : i18n.t('message', '确认要停用{language}吗？停用后系统将不再展示该语言的国际化内容', { language: getLanguageText(record.language as LanguageCodeType) });
            
        confirm({
            title,
            content,
            centered: true,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () => {
                updateLanguageConfig(item => {
                    if (item.language === record.language) {
                        return {
                            ...item,
                            state: enable ? LanguageState.ENABLED : LanguageState.DISABLED
                        };
                    }
                    return item;
                });
            },
        });
    };
    const getLanguageText = (language: LanguageCodeType) => (languageTypeMap[language] || language);
    // 处理设置默认语言
    const handleSetDefaultLanguage = (record: LanguageItem) => {    
        const language = record.language as LanguageCodeType;
        confirm({
            title: i18n.t('name', '设置默认语言'),
            content: i18n.t('message', '确认将{language}设置为默认语言？', { language: getLanguageText(language) }),
            centered: true,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: () => {
                // 需要将原来的默认语言改为非默认，新的设为默认
                updateLanguageConfig(item => {
                    if (item.language === record.language) {
                        return {
                            ...item,
                            defaultSwitch: DefaultLanguageSwitch.DEFAULT
                        };
                    } else if (item.defaultSwitch === DefaultLanguageSwitch.DEFAULT) {
                        return {
                            ...item,
                            defaultSwitch: DefaultLanguageSwitch.NOT_DEFAULT
                        };
                    }
                    return item;
                });
            },
        });
    };
    
    // 表格列定义
    const columns: ColumnsType<LanguageItem> = [
        {
            title: i18n.t('name', '语言类型'),
            dataIndex: 'language',
            ellipsis: true,
            render: (text: LanguageCodeType) => {
                return (
                    <Tooltip title={text}>
                        <StarryAbroadOverflowEllipsisContainer>
                            {getLanguageText(text)}
                        </StarryAbroadOverflowEllipsisContainer>
                    </Tooltip>
                );
            },
        },
        {
            title: i18n.t('name', '状态'),
            dataIndex: 'state',
            render: (state: LanguageState, record: LanguageItem) => {
                const currentLanguage = getAppGlobalData('APP_LANG');
                const isCurrentLanguage = record.language === currentLanguage;
                const isDefaultLanguage = record.defaultSwitch === DefaultLanguageSwitch.DEFAULT;
                const isEnabled = state === LanguageState.ENABLED;
                
                return (
                    <Space>
                        <Badge 
                            status={isEnabled ? 'success' : 'default'} 
                            text={isEnabled ? i18n.t('state', '启用') : i18n.t('state', '停用')} 
                        />
                        <Switch
                            size="small"
                            checked={isEnabled}
                            disabled={isCurrentLanguage || isDefaultLanguage}
                            onChange={() => handleLanguageStateChange(record, !isEnabled)}
                        />
                    </Space>
                );
            },
        },
        {
            title: i18n.t('name', '默认语言'),
            dataIndex: 'defaultSwitch',
            render: (defaultSwitch: DefaultLanguageSwitch, record: LanguageItem) => {
                const isDefault = defaultSwitch === DefaultLanguageSwitch.DEFAULT;
                const isDisable = record.state === LanguageState.DISABLED;
                if (isDisable) {
                    return null;
                }
                if (isDefault) {
                    return (
                        <Button type="text" disabled>
                            <Tag className="default-language-tag">
                                {i18n.t('name', '默认语言')}
                            </Tag>
                        </Button>
                    );
                }
                return (
                    <Button 
                        type="link" 
                        onClick={() => handleSetDefaultLanguage(record)}
                    >
                        {i18n.t('action', '设置为默认语言')}
                    </Button>
                );
            },
        },
    ];
    // 处理搜索输入变化
    const handleSearchChange = debounce((value: string) => {
        searchStore.set({
            ...searchStore.get(),
            languageDesc: value || null,
        });
        
        // 重新加载数据
        listDataContainerRef.current?.loadDataSource({
            languageDesc: value || undefined
        });
    }, DEBOUNCE_DELAY);
    
    // 工具栏配置
    const toolbar = {
        extraRight: (
            <Input
                style={{ width: 280 }}
                prefix
                maxLength={MAX_INPUT_LENGTH}
                defaultValue={formQuery.languageDesc}
                placeholder={i18n.t('message', '请输入语言类型')}
                allowClear
                onChange={(e) => handleSearchChange(e.target.value)}
                onPressEnter={(e) => handleSearchChange((e.target as HTMLInputElement).value)}
            />
        ),
    };
    
    return (
        <StarryBreadcrumb>
            <StarryCard className="language-setting">
                <ListDataContainer<LanguageItem[]>
                    ref={listDataContainerRef}
                    getDataSource={fetchData}
                    toolbar={toolbar}
                    loadDataSourceOnMount={true}
                    listRender={(data) => {                 
                        return (
                            <>
                                <InfoBack
                                    style={{ marginBottom: 24 }}
                                    title={i18n.t('message', '若登录时选择默认语言进入系统，则系统将优先按默认语言展示')}
                                />
                                <Table
                                    rowKey="language"
                                    columns={columns}
                                    dataSource={data}
                                    sticky={{
                                        offsetHeader: calcHeight(),
                                        getContainer: () => {
                                            return document.querySelector('#root') as HTMLElement;
                                        },
                                    }}
                                />
                            </>
                        );
                    }}
                    pagination={false}
                />
            </StarryCard>
        </StarryBreadcrumb>
    );
};

export default LanguageSetting;
