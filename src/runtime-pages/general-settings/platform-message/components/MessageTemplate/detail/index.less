@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';
.message-template-detail {
    .rsp-drawer-title{
        font-size: 20px;
        font-weight: 700;
        line-height: 28px;
        margin-right: 8px;
        color: @starry-text-color-primary;
    }
    .message-template-detail-title {
        font-weight: 700px;
        font-size: 20px;
    }
    .info {
        margin-top: 12px;
    }
    .starry-info-panel-bordered {
        border-bottom: none;
    }
    .starry-info-panel {
        padding-bottom: 0;
    }
    .starry-info-block {
        padding: 0;
    }
    .info-item {
        display: flex;
        margin-bottom: 16px;
        padding-right: 2em;
        .info-item-content {
            flex: 1;
            margin-left: 4px;
            word-break: break-all;
        }
    }
    .info-group {
        .title {
            margin-top: 24px;
        }
    }
    .show-detail-wrapper {
        margin: 12px 0 24px;
    }

    .content-setting-wrapper {
        display: flex;
        .content-setting-left {
            height: 100%;
            box-sizing: content-box;
            max-width: 200px;
            background-color: @starry-bg-color-container;
            .template-tab-title {
                height: 25px;
                margin-bottom: 24px;
                line-height: 25px;
                .title-text {
                    font-weight: 600;
                    font-size: 18px;
                }
            }
            .poppy-tabs-nav {
                width: 100%;
            }
            .poppy-tabs {
                height: 100%;
            }
        }
        .content-setting-right {
            position: relative;
            flex: 1;
            width: 100%;
            .template-tab-right-edit {
                display: flex;
                justify-content: end;
                width: 100%;
                height: 25px;
                margin-bottom: 24px;
                line-height: 25px;
            }
        }
    }
    .message-template-content,
    .message-template-title {
        line-height: 30px;
        min-height: 230px;
        margin-top: 12px;
        padding: 8px;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        border: 1px solid @starry-border-level-1-color;
        border-radius: 6px;
    }
    .message-template-title {
        min-height: 48px;
    }
    .message-template-detail-base {
        background-color: @starry-bg-color-container;
    }
}

.message-template-content-keyword {
    padding: 2px 4px;
    color: @primary-color;
    background: @starry-bg-color-container;
    border-radius: 4px;
}

.message-template-detail::abroad {
    .info::abroad {
        margin-top: 0;
    }
    .poppy-container:has(.message-template-detail-base)::abroad {
        padding: 0 !important;
        background: unset;
        box-shadow: unset;
        .poppy-container::abroad {
            margin-bottom: 24px;
            padding: 24px;
            background-color: @starry-bg-color-container;
            border-radius: 16px;
            box-shadow: 0 1px 2px 0 @starry-card-box-shadow-color;
        }
    }
    .message-template-detail-tabs::abroad {
        .poppy-tabs-nav::before::abroad {
            display: none;
        }
    }
    .content-setting-right-header::abroad {
        display: flex;
        align-items: center;
        justify-content: space-between;

        width: 100%;
        margin-bottom: 24px;
        font-weight: 700;
        .title-text::abroad {
            font-size: 18px;
        }
    }
    .label::abroad {
        font-weight: 600;
    }
    .info-item::abroad {
        color: @starry-text-color-secondary;
        .info-item-content::abroad {
            color: @starry-text-color-primary;
        }
    }
    .message-template-detail-base .starry-info-panel-header::abroad {
        padding-top: 24px;
    }
    .content-setting-wrap::abroad {
        padding: 24px 24px 8px !important;
        background-color: @starry-bg-color-container;
        border-radius: 16px;
        box-shadow: 0 1px 2px 0 @starry-card-box-shadow-color;
    }
    .content-setting-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        width: 100%;
        margin-bottom: 16px;
        font-weight: 700;
        .title-text {
            font-size: 16px;
        }
        .title-text::abroad {
            font-size: 18px;
        }
    }
}
.message-template-detail-drawer{
    .content-setting-left{
        height: 100%;
        .poppy-tabs{
            height: 100%;
            .poppy-tabs-content-holder{
                display: none;
            }
            .poppy-tabs-nav{
                margin-bottom: 0px;
                border-left: 1px solid @starry-border-level-1-color;;
            }
        }
    }
}

