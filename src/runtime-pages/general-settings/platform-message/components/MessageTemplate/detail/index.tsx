import { useState } from 'react';
import {
    i18n,
    getAppGlobalData,
    utils,
    Auth,
    useSystemComponentStyle,
    StarryAbroadIcon,
} from '@base-app/runtime-lib';
import { useParams, useHistory, Link } from '@base-app/runtime-lib/core';
import {
    message,
    Button,
    Col,
    Tabs,
    Row,
    Checkbox,
    Container,
    Spin,
} from '@streamax/poppy';
import { useAsyncEffect } from '@streamax/hooks';
import {
    StarryCard,
    StarryBreadcrumb,
    StarryInfoBlock,
    StarryModal,
} from '@base-app/runtime-lib'; // 使用公共组
import { fetchAuthorityLanguageList } from '@/service/language';
import { Descriptions, InfoPanel } from '@streamax/starry-components';
import {
    fetchMessageLabelList,
    fetchMessageTempDetail,
    deleteMessageTemp,
} from '@/service/message';
import { IconRequest, IconSwitch02Fill } from '@streamax/poppy-icons';
import { languageMap } from '../constant';
import { labelKey2Text } from '../commonFun';
import AssociationSettings from '@/components/TemplateQuote';
import './index.less';
import { withShareRootHOC } from '@streamax/page-sharing-core';
import type {
    ListPageTableBase,
    XSSFilter,
} from '@/types/pageReuse/pageReuseBase';
import { useAppList } from '@/hooks';
import ReactHtmlParser from 'react-html-parser';
import { FilterXSSTypesEnum, useXSSFilter } from '@/hooks/useXSSFilter';
import { RspDrawerTemplate } from '@streamax/responsive-layout';

const { TabPane } = Tabs;

interface LangMap {
    name: string;
    key: string;
}

interface GroupRenderType {
    (text?: any, record?: any): string | React.ReactNode | void;
}

interface GroupType {
    label: string;
    field: string;
    render?: GroupRenderType;
    colspan?: number;
}

export type MessageTemplateShareProps = ListPageTableBase & XSSFilter;

const MessageTemplate = (props: MessageTemplateShareProps) => {
    /**定制项 */
    const { getColumns, injectSearchList, getXssFilter } = props;
    /**end */

    const { messageTmpId }: any = useParams();
    const history = useHistory();
    const [contentMap, setContentMap] = useState<any>({});
    const [langList, setLangList] = useState<any[]>([]);
    const [messageTempDetail, setMailTempDetail] = useState<any>({}); // 关键词列表
    const [quoteModelIds, setQuoteModelIds] = useState<string[]>([]); // 关联策略的ID
    const [activeLangTab, setActiveLangTab] = useState(''); // 当前激活的语言tab
    const { inSaaS } = useAppList({}, [0]);

    const filterXSS = useXSSFilter();
    const renderMessageTemplateContent = (
        type: 'messageTitle' | 'messageContent',
    ) => {
        const defaultConfig = {};
        const currentContent = contentMap?.[activeLangTab]?.[type] || '';
        const { config, data } =
            getXssFilter?.({
                type,
                data: currentContent,
                config: defaultConfig,
            }) || {};
        const filterContent = filterXSS({
            type: FilterXSSTypesEnum.DOM_PURIFY,
            data: data || currentContent,
            config: config || defaultConfig,
        });
        const result = ReactHtmlParser(filterContent);
        return result;
    };

    const {
        // 当前模式是否是海外风格
        isAbroadStyle,
    } = useSystemComponentStyle();
    useAsyncEffect(async () => {
        // 获取语言列表
        fetchAuthorityLanguageList({
            tenantId: getAppGlobalData('APP_USER_INFO').tenantId,
        }).then((res) => {
            setLangList(
                (res || []).map((item: string) => ({
                    name: item,
                    key: item,
                })),
            );
            setActiveLangTab(res?.[0]);
        });
        // 获取关键词列表
        await getKeyWords();
        if (messageTmpId) {
            Promise.all([getKeyWords(), getTempDetails()]).then((res) => {
                const [keyWordsList, data] = res;
                setMailTempDetail(data);
                const keyWords = (keyWordsList || []).map((item: any) => {
                    const obj: any = {};
                    obj.value = item.value;
                    item.languageList.forEach((key: any) => {
                        obj[key.langType] = key.translation;
                    });
                    return obj;
                });
                const templateContentMap: any = {};
                (data?.templateContent || []).forEach((item: any) => {
                    const { langType } = item;
                    templateContentMap[langType] = item;
                });
                // 关联策略ID
                const qmIds = (data?.quoteDetails || []).map(
                    (item: any) => item?.quoteModelId,
                );
                setQuoteModelIds(qmIds);
                replaceLabel(keyWords, templateContentMap);
            });
        }
    }, []);

    // 替换标签
    function replaceLabel(keyWords: any, contentMap: any) {
        const contents = { ...contentMap };
        keyWords.forEach((word: any) => {
            for (const key in contentMap) {
                const { messageTitle, messageContent } = contentMap[key];
                contents[key].messageTitle = labelKey2Text(
                    messageTitle,
                    word.value,
                    word[key],
                    true,
                );
                contents[key].messageContent = labelKey2Text(
                    messageContent,
                    word.value,
                    word[key],
                    true,
                );
            }
        });
        setContentMap(contents);
    }

    // 获取关键词
    async function getKeyWords() {
        return fetchMessageLabelList().then((data: any) => {
            return data;
        });
    }

    // 获取详情
    async function getTempDetails() {
        return fetchMessageTempDetail({
            id: messageTmpId,
            quoteInfo: 1, // 需要返回被引用的策略列表
        }).then((data: any) => {
            return data;
        });
    }

    const items: GroupType[] = [
        {
            label: i18n.t('name', '模板名称'),
            content: messageTempDetail.templateName || '-',
        },
        {
            label: i18n.t('name', '模板类型'),
            content: messageTempDetail.templateType === 1 ? i18n.t('name', '报警弹窗') : '-'
        },
        inSaaS && {
            label: i18n.t('name', '归属应用'),
            content: messageTempDetail.appName ? i18n.t(`@i18n:@app__${messageTempDetail.appId}`, messageTempDetail.appName) : '-'
        },
        {
            label: i18n.t('name', '创建人'),
            content: messageTempDetail.createUserName || '-',
        },
        {
            label: i18n.t('name', '创建时间'),
            content: messageTempDetail.createTime ? utils.formator.zeroTimeStampToFormatTime(messageTempDetail.createTime) : '-'
        },
    ].filter((i) => i);

    // 删除消息模板
    function handelDeleteEmailTemp() {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('message', '删除确认'),
            icon: <IconRequest />,
            content: i18n.t(
                'message',
                '确认删除“{name}”？点击确定后删除消息模板',
                {
                    name: messageTempDetail.templateName,
                },
            ),
            onOk() {
                deleteMessageTemp({
                    id: messageTmpId.toString(),
                }).then((data: any) => {
                    if (data.length === 0) {
                        message.success(i18n.t('message', '操作成功'));
                        history.goBack();
                    } else {
                        const text = data
                            .map(
                                (item: {
                                    configureId: string;
                                    configureName: string;
                                }) =>
                                    i18n.t(
                                        `@i18n:@alarmLinkageStrategy__${item.configureId}`,
                                        item.configureName,
                                    ),
                            )
                            .join('、');
                        message.error(
                            i18n.t(
                                'message',
                                '无法删除，“{name}”正在“{text}”中使用，请取消关联后重试',
                                {
                                    name: messageTempDetail.templateName,
                                    text,
                                },
                            ),
                        );
                    }
                });
            },
        });
    }

    return (
        <StarryBreadcrumb className="message-template-detail">
            {messageTempDetail && (
                <StarryCard>
                    <InfoPanel
                        title={
                            <span className="message-template-detail-title">
                                {messageTempDetail?.templateName}
                            </span>
                        }
                        showCollapseBtn
                        defaultCollapsed
                        extraRight={
                            quoteModelIds.length === 0 && (
                                <Button onClick={handelDeleteEmailTemp} danger>
                                    {i18n.t('action', '删除')}
                                </Button>
                            )
                        }
                        operationWrap
                        className="message-template-detail-base"
                    >
                        <StarryInfoBlock
                            title={i18n.t('name', '基本信息')}
                            operation={
                                <Link
                                    to={`/platform-message/message-template-edit/base-info?messageTempId=${messageTmpId}&pageType=edit`}
                                >
                                    {i18n.t('name', '编辑')}
                                </Link>
                            }
                            operationWrap
                        >
                            <Descriptions>
                                {items.map(item =>  <Descriptions.Item key={item.label} label={item.label}>
                                        {item.content}
                                    </Descriptions.Item> )}
                            </Descriptions>
                        </StarryInfoBlock>
                    </InfoPanel>
                    <Tabs className="message-template-detail-tabs">
                        <TabPane
                            tab={i18n.t('name', '模板内容')}
                            key={'templateContent'}
                        >
                            <div className="content-setting-wrap">
                                <div className="content-setting-header">
                                    <span className="title-text">
                                        {i18n.t('name', '内容设置')}
                                    </span>
                                    <div>
                                        <Link
                                            to={`/platform-message/message-template-edit/template-info?messageTempId=${messageTmpId}&pageType=edit`}
                                        >
                                            {i18n.t('name', '编辑')}
                                        </Link>
                                    </div>
                                </div>
                                <div className="title label">
                                    {i18n.t('name', '操作设置')}
                                </div>
                                <div className="show-detail-wrapper">
                                    <Checkbox
                                        checked={
                                            !!messageTempDetail?.showDetailBtn
                                        }
                                        disabled
                                    >
                                        {i18n.t('name', '展示查看报警详情按钮')}
                                    </Checkbox>
                                </div>
                                <div className="content-setting-wrapper">
                                    <RspDrawerTemplate breakpoint='lg' gutter={[24,24]}>
                                        <RspDrawerTemplate.Left drawerProps={{width:400,className:'message-template-detail-drawer'}}
                                        drawerTrigger={ <div>
                                            <span className='rsp-drawer-title'>{languageMap[activeLangTab]}</span>
                                             <StarryAbroadIcon> <IconSwitch02Fill /></StarryAbroadIcon>
                                            </div>}>
                                        <div className="content-setting-left">
                                        <Tabs
                                            tabPosition="right"
                                            onChange={(key) =>
                                                setActiveLangTab(key)
                                            }
                                            activeKey={activeLangTab}
                                        >
                                            {langList.map((item: LangMap) => {
                                                return (
                                                    <TabPane
                                                        tab={
                                                            languageMap[
                                                                item.name
                                                            ]
                                                        }
                                                        key={item.key}
                                                    />
                                                );
                                            })}
                                        </Tabs>
                                    </div>                                            
                                        </RspDrawerTemplate.Left>
                                        <RspDrawerTemplate.Right>
                                        <div className="content-setting-right">
                                        <div className="label">
                                            {i18n.t('name', '消息标题')}
                                        </div>
                                        <div className="message-template-title">
                                            {renderMessageTemplateContent(
                                                'messageTitle',
                                            )}
                                        </div>
                                        <div className="info-group">
                                            <div className="title label">
                                                {i18n.t('name', '消息内容')}
                                            </div>
                                            <div className="message-template-content">
                                                {renderMessageTemplateContent(
                                                    'messageContent',
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                        </RspDrawerTemplate.Right>
                                    </RspDrawerTemplate>
                                </div>
                            </div>
                        </TabPane>
                        {Auth.check(
                            '@base:@page:platform.message:detail@action:tab.related.setting',
                        ) && (
                            <TabPane
                                tab={i18n.t('name', '关联设置')}
                                key={'associationSettings'}
                            >
                                <AssociationSettings
                                    getColumns={getColumns}
                                    quoteModelIds={quoteModelIds.join(',')}
                                    injectSearchList={injectSearchList}
                                    defaultAlarmLinkageDetailCode="@base:@page:platform.message:detail@action:default.alarm.linkage.detail"
                                    alarmLinkageDetailCode="@base:@page:platform.message:detail@action:alarm.linkage.detail"
                                />
                            </TabPane>
                        )}
                    </Tabs>
                </StarryCard>
            )}
        </StarryBreadcrumb>
    );
};
export default withShareRootHOC<MessageTemplateShareProps, any>(
    MessageTemplate,
);
