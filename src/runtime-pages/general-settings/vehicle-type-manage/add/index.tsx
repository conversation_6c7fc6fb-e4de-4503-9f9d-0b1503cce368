import {
    getAppGlobalData,
    i18n,
    PageCardLayout,
    reLoadLanguage,
    RouterPrompt,
    setAppGlobalData,
    StarryAbroadFormItem,
    StarryAbroadLRLayout,
    StarryBreadcrumb,
    useAppList,
    utils,
} from '@base-app/runtime-lib';
import { useLayoutEffect, useState } from 'react';
import {
    Button,
    Container,
    Form,
    Input,
    message,
    Select,
    Spin,
} from '@streamax/poppy';
import './index.scoped.less';
import { useHistory } from '@base-app/runtime-lib/core';
import { InfoPanel } from '@streamax/starry-components';
import { RspFormLayout, useResponsiveShow } from '@streamax/responsive-layout';
import InternationalInput from '@/components/InternationalInput';
import Uploader, {
    DEFAULT_VEHICLE_TYPE_IMAGE,
    VEHICLE_TYPE_FILE_SIZE,
} from '../components/uploader';
import {
    addEnum,
    DictionaryTypeEnum,
    fetchEnumDetail,
    repeatValidateEnum,
    updateEnum,
} from '@/service/vehicle';
import { useAsyncEffect, useDebounceFn } from '@streamax/hooks';
import { defaultCardImage } from './default-image';
import { fileUploadStream } from '@/service/gss';
import { getImgUrls } from '@/utils/commonFun';
const { encryptPassword: encryptToken } = utils.general;
const validatorIllegalCharacter = utils.validator.illegalCharacter;
const AddVehicleType = () => {
    const history = useHistory();
    const { vehicleTypeCode, vehicleTypeId, appId } = history.location.query;
    const [form] = Form.useForm();
    const [detailData, setDetailData] = useState<any>({});
    const [when, setWhen] = useState<boolean>(true);
    const [pictures, setPictures] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [initLoading, setInitLoading] = useState<boolean>(true);
    const { appList, loaded, inSaaS } = useAppList({}, [0, 66666]);
    const [earthValues, setEarthValues] = useState<any>({});

    const isResponsive = useResponsiveShow({
        xs: true,
        sm: true,
        md: false,
        lg: false,
        xl: false,
        xxl: false,
    });
    useAsyncEffect(async () => {
        if (vehicleTypeId) {
            const vehicleTypeDetail = await fetchEnumDetail({
                id: vehicleTypeId,
            });
            const { enumCode, enumName, appId, id, fileId } =
                vehicleTypeDetail || {};
            const transEnumName = i18n.t(`@i18n:@vehicleTypeEnum__${enumCode}`, enumName);
            setDetailData({
                enumCode,
                enumName: transEnumName,
                appId: Number(appId),
                id,
            });
            form.setFieldsValue({
                enumCode,
                enumName: transEnumName,
                appId: Number(appId),
            });
            const urlData = await getImgUrls(fileId, {
                _appId: 0,
                _tenantId: 0,
            });
            if (urlData.length) {
                const imgObj = {
                    uid: urlData[0].fileId,
                    url: urlData[0].fileUrl,
                    status: 'done',
                };
                form.setFieldsValue({
                    icon: [imgObj],
                });
                setPictures([imgObj]);
            }
            setInitLoading(false);
        } else {
            setInitLoading(false);
            form.setFieldsValue({
                icon: DEFAULT_VEHICLE_TYPE_IMAGE,
            });
            setPictures(DEFAULT_VEHICLE_TYPE_IMAGE);
        }
    }, []);
    const base64ToFormData = (imageBase64: string) => {
        const base64Data = imageBase64.split(',')[1];
        // 转换为 Blob
        const byteCharacters = atob(base64Data);
        const byteArrays = [];
        for (let i = 0; i < byteCharacters.length; i++) {
            byteArrays.push(byteCharacters.charCodeAt(i));
        }
        const byteArray = new Uint8Array(byteArrays);
        const blob = new Blob([byteArray], { type: 'image/png' });
        // 添加到 FormData
        const formData = new FormData();
        formData.append('file', blob);
        return formData;
    };
    
    const uploadDefaultImage = async (base64Data: string) => {
        const formData = base64ToFormData(base64Data);
        const fileId = await fileUploadStream(formData, {
            // @ts-ignore
            _abs: encryptToken(window.localStorage.getItem('AUTH_TOKEN')),
            _tenantId: 0,
            _appId: 0,
        });
        return fileId;
    };
    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            const validateFail = await onVehicleTypeNameBlur();
            if (validateFail) return;
            const {
                icon,
                enumCode,
                enumName,
                appId,
                description = '',
            } = values || {};
            const { translationList, objectName, langId } = earthValues;
            let fileId = icon?.[0].uid;
            if (fileId === '-1') {
                fileId = await uploadDefaultImage(defaultCardImage);
            }
            setLoading(true);
            if (vehicleTypeId) {
                // 通过上一步返回的，直接进入下一步, 走编辑接口
                await updateEnum({
                    id: detailData.id,
                    enumName,
                    fileId,
                    translationList:
                        translationList ||
                        (i18n.exists(`@i18n:@vehicleTypeEnum__${enumCode}`)
                            ? [
                                  {
                                      langType: getAppGlobalData('APP_LANG'),
                                      translationValue: enumName,
                                  },
                              ]
                            : []),
                });
            } else {
                await addEnum({
                    appId: inSaaS ? appId : getAppGlobalData('APP_ID'),
                    enumType: DictionaryTypeEnum.vehicleType,
                    enumCode,
                    enumName,
                    fileId,
                    translationList,
                });
            }
            await reLoadLanguage(true, true);
            setWhen(false);
            setLoading(false);
            message.success(i18n.t('message', '操作成功'));
            history.goBack();
        } catch (e) {
            setLoading(false);
            return Promise.reject(e);
        }
    };
    const handleInternationalInputSave = (values: any) => {
        setEarthValues(values);
    };
    const formValidate = (rule: any, value: string) => {
        return Promise.resolve();
    };
    const onVehicleTypeNameBlur = async () => {
        const values = form.getFieldsValue();
        const validate = (inSaaS && values.appId) || !inSaaS;
        const name = values.enumName?.trim();
        if (validate && name) {
            const data = await repeatValidateEnum({
                id: detailData.id,
                name: name,
                enumType: 'vehicleType',
                appId: inSaaS ? values.appId : getAppGlobalData('APP_ID'),
            });
            if (data) {
                form.setFields([
                    {
                        name: 'enumName',
                        errors: [i18n.t('message', '车辆类型名称重复')],
                    },
                ]);
            }
            return data;
        }
        if (!name) {
            form.setFields([
                {
                    name: 'enumName',
                    errors: [i18n.t('message', '车辆类型名称不能为空')],
                },
            ]);
        }
    };
    return (
        <StarryBreadcrumb>
            <RouterPrompt
                when={when}
                message={i18n.t(
                    'message',
                    '离开当前页？系统可能不会保存您所做的更改。',
                )}
            />
            <PageCardLayout className="vehicle-type-manage-add-edit-page">
                <Spin spinning={initLoading}>
                    <Container>
                        <InfoPanel title={i18n.t('name', '基本信息')}>
                            <Form form={form} layout="vertical">
                                <RspFormLayout layoutType="fixed">
                                    <RspFormLayout.Col>
                                        <StarryAbroadFormItem
                                            label={i18n.t(
                                                'name',
                                                '车辆类型名称',
                                            )}
                                            name={'enumName'}
                                            validateFirst
                                            required
                                            rules={[
                                                {
                                                    required: true,
                                                    whitespace: true,
                                                    message: i18n.t(
                                                        'message',
                                                        '车辆类型名称不能为空',
                                                    ),
                                                },
                                                // {
                                                //     validator: isVehicleTypeRepeat,
                                                // }
                                            ]}
                                            className="form-item international-form-input"
                                            style={{
                                                width: isResponsive
                                                    ? 'calc(100% - 35px)'
                                                    : '100%',
                                            }}
                                        >
                                            <InternationalInput
                                                maxLength={50}
                                                allowClear
                                                modalType={
                                                    vehicleTypeId
                                                        ? 'edit'
                                                        : 'add'
                                                }
                                                internationalType="vehicleTypeEnum"
                                                entryKey={
                                                    (detailData || {}).enumName
                                                }
                                                entryIdOrCode={vehicleTypeCode}
                                                onSave={(values) =>
                                                    handleInternationalInputSave(
                                                        values,
                                                    )
                                                }
                                                onBlur={onVehicleTypeNameBlur}
                                                placeholder={i18n.t(
                                                    'message',
                                                    '请输入车辆类型名称',
                                                )}
                                                formValidate={formValidate}
                                            />
                                        </StarryAbroadFormItem>
                                        {inSaaS && (
                                            <StarryAbroadFormItem
                                                label={i18n.t(
                                                    'name',
                                                    '归属应用',
                                                )}
                                                name="appId"
                                                required
                                                rules={[
                                                    {
                                                        required: true,
                                                        message: i18n.t(
                                                            'message',
                                                            '请选择归属应用',
                                                        ),
                                                    },
                                                ]}
                                            >
                                                <Select
                                                    disabled={
                                                        vehicleTypeId
                                                            ? true
                                                            : false
                                                    }
                                                    options={appList}
                                                    filterOption={(
                                                        inputValue: string,
                                                        option,
                                                    ) => {
                                                        return (
                                                            (
                                                                option?.label ||
                                                                ''
                                                            )
                                                                .toLowerCase()
                                                                .indexOf(
                                                                    (
                                                                        inputValue as string
                                                                    ).toLowerCase(),
                                                                ) !== -1
                                                        );
                                                    }}
                                                    showSearch
                                                    allowClear
                                                    placeholder={i18n.t(
                                                        'message',
                                                        '请选择归属应用',
                                                    )}
                                                />
                                            </StarryAbroadFormItem>
                                        )}
                                        <RspFormLayout.SingleRow>
                                            <Form.Item
                                                validateFirst
                                                label={i18n.t(
                                                    'name',
                                                    '车辆类型图标',
                                                )}
                                                required
                                                name="icon"
                                                className="vehicle-type-picture-form"
                                                rules={[
                                                    {
                                                        required: true,
                                                    },
                                                ]}
                                            >
                                                <StarryAbroadFormItem noStyle>
                                                    <Uploader
                                                        form={form}
                                                        pictures={pictures}
                                                    />
                                                </StarryAbroadFormItem>
                                                <span className="info-text">
                                                    <div className="info-text-line">
                                                        {i18n.t(
                                                            'message',
                                                            '只支持.png 格式,小于{size}k（推荐使用尺寸为128*128的白色图片）',
                                                            {
                                                                size: VEHICLE_TYPE_FILE_SIZE,
                                                            },
                                                        )}
                                                    </div>
                                                    <div>
                                                        {i18n.t(
                                                            'message',
                                                            '注：未上传将使用系统默认的车辆图标',
                                                            {
                                                                size: VEHICLE_TYPE_FILE_SIZE,
                                                            },
                                                        )}
                                                    </div>
                                                </span>
                                            </Form.Item>
                                        </RspFormLayout.SingleRow>
                                    </RspFormLayout.Col>
                                </RspFormLayout>
                            </Form>
                        </InfoPanel>
                    </Container>
                    <StarryAbroadLRLayout>
                        <Button
                            onClick={() => {
                                history.goBack();
                            }}
                        >
                            {i18n.t('action', '取消')}
                        </Button>
                        <Button
                            type="primary"
                            onClick={handleSubmit}
                            loading={loading}
                        >
                            {i18n.t('action', '保存')}
                        </Button>
                    </StarryAbroadLRLayout>
                </Spin>
            </PageCardLayout>
        </StarryBreadcrumb>
    );
};

export default AddVehicleType;
