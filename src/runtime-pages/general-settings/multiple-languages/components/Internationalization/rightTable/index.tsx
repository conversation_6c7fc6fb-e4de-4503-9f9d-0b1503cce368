import { useRef, useState, useEffect, useContext } from 'react';
import {
    Input,
    Modal,
    Form,
    message,
    Space,
    Tooltip,
    Spin,
} from '@streamax/poppy';
import {
    getAppGlobalData,
    i18n,
    utils,
    reLoadLanguage,
} from '@base-app/runtime-lib';
import { StarryTable } from '@base-app/runtime-lib';
import { IcListEditFill, IconImport, IconExport } from '@streamax/poppy-icons';
import {
    fetchEntryKeyLanguage,
    editInternationEntry,
} from '@/service/language';
import { exportExcel } from '@/service/import-export';
import { useHistory } from '@base-app/runtime-lib/core';
import { languageLabelList } from '@/utils/commonFun';
import moment from 'moment';
import { useAppList } from '@/hooks';
import './index.less';
import _, { isNil, omit } from 'lodash';
import useFormUrlSearchStore from '@/hooks/useFormUrlSearchStore';
import { useSubmitFn } from '@streamax/hooks';
import { TabsContext } from '../index';
import {
    StarryAbroadFormItem,
    StarryAbroadIcon,
    StarryAbroadOverflowEllipsisContainer,
} from '@base-app/runtime-lib';
import { getUserAppEntrys } from '@/service/entry';
import { CacheType } from '@/types';
import { localeCompare } from '@/utils/alarmTypeSort';
import { validatorI18n,validateCharacters } from '@/utils/commonFun';

interface ImportAndExportParams {
    serviceName: string;
    executorHandler: string;
    taskType: number;
}
const { zeroTimeStampToFormatTime } = utils.formator;
interface PropsType {
    moduleName: string;
    moduleTitle: string;
    fetchData: (params: any) => any;
    entryField: string; // 词条字段
    langField: string; // 唯一标识字段
    languageType: string[]; // 支持的语言类型
    itemColumns: any[];
    searchParams: any[];
    disabledItems?: string[];
    appList?: any[];
    sheetName?: any;
    fileName?: any;
    EXserviceCode?: any;
    INserviceCode?: any;
    configureType?: any;
    taskTypes?: number;
    defaultParams?: any; // 请求默认参数
    EXColumns?: any;
    formatReqParams?: (params: any) => any; // 格式化请求参数
    // 请求参数为number类型的key数组，用于处理从url获取参数格式化
    searchFormNumberKeys?: string[];
    needSpliceAppId?: boolean; // 导出时是否需要拼接appId
    importParams?: ImportAndExportParams; // 新导入参数  无INserviceCode默认使用此参数
    exportParams?: ImportAndExportParams; // 新导出参数 无EXserviceCode默认使用此参数
    searchButtonDisableProps?: any;
    filterBP?: boolean; //是否过滤bp
    sortKey: string; //是否排序
    formValidate?: (rule: any, value: string) => Promise<any>;
}

interface RecordType {
    entryKey: number;
    langCode: string;
    entryType: string;
    [other: string]: string | number;
}

// 默认设置范围 configureType:模块名 类型是固定的
const StrategyModelNames = {
    '1': 'evidenceReturnStrategy',
    '2': 'alarmLinkageStrategy',
    '3': 'emailSendingStrategy',
    '7': 'dataCleanStrategy',
    '8': 'faceComparisonStrategy',
    '10': 'alarmWebResponseStrategy',
    '11': 'pictureCaptureStrategy',
    '18': 'channelSettingStrategy',
    '20': 'channelDeviceSettingStrategy',
};

const RightTable = (props: PropsType) => {
    const { inSaaS } = useAppList();
    const {
        itemColumns,
        searchParams,
        disabledItems = [],
        moduleName,
        moduleTitle,
        fetchData,
        appList,
        defaultParams = {},
        languageType,
        langField,
        formatReqParams = null,
        configureType,
        EXserviceCode,
        INserviceCode,
        sheetName,
        fileName,
        taskTypes,
        EXColumns,
        searchFormNumberKeys = [],
        needSpliceAppId,
        importParams,
        exportParams,
        filterBP,
        sortKey,
        formValidate
    } = props;
    const [modalVisible, setModalVisible] = useState<boolean>(false);
    const [spinState, setSpinState] = useState(false);
    const [editRow, setEditRow] = useState<any>({});
    const history: any = useHistory();
    const TableRef = useRef<any>();
    const configureTypeRef = useRef<any>();
    const [form] = Form.useForm();
    const [queryP, setQuery] = useState<any>(null);
    // const [params, form: queryForm,, setSearchStore] = useFormUrlSearchStore();
    const {
        params: formQuery,
        form: searchForm,
        setSearchStore,
    } = useFormUrlSearchStore('page', 'pageSize', searchFormNumberKeys);
    const value = useContext(TabsContext);
    let disableAttr = {
        searchProps: {
            disabled: false,
        },
        resetProps: {
            disabled: false,
        },
    };
    if (inSaaS) {
        disableAttr = {
            searchProps: {
                disabled: filterBP
                    ? !Boolean(value[Number(formQuery.tab)])
                    : false,
            },
            resetProps: {
                disabled: filterBP
                    ? !Boolean(value[Number(formQuery.tab)])
                    : false,
            },
        };
    }

    useEffect(() => {
        // tab 属于父级组件的条件，需要去除
        const tableFormQuery = omit(formQuery, 'tab');

        // url没有缓存appId
        if (isNil(tableFormQuery.appId) || tableFormQuery.appId === ''){
            // needSpliceAppId=true，都是带归属应用的tab
            if (needSpliceAppId && history.action === 'REPLACE') {

                tableFormQuery.appId = value[Number(formQuery.tab)];
            }
        }

        TableRef.current?.loadDataSource(tableFormQuery);
    }, []);
    // 获取div的dom元素，判断是否存在滚动条
    const modalContentRef = useRef<HTMLDivElement | null>(null);
    const [isScroll, setIsScroll] = useState(false);
    const hasScrollbar = (ele: HTMLDivElement): boolean => {
        return ele?.scrollHeight > ele?.clientHeight;
    };
    useEffect(() => {
        if (modalVisible) {
            hasScrollbar(modalContentRef.current as HTMLDivElement)
                ? setIsScroll(true)
                : '';
        }
    }, [modalVisible]);

    const edit = (record: RecordType): void => {
        setEditRow(record);
        setModalVisible(true);
        record.APPName = (
            appList?.filter((app: any) => app.value === record.appId)[0] || {}
        ).label;
        form.setFieldsValue({
            ...record,
        });
    };
    const fetchList = async (params: any) => {
        let newParams = JSON.parse(JSON.stringify(params));
        if (
            filterBP &&
            Number(getAppGlobalData('APP_ID')) === 0 &&
            isNil(newParams.appId)
        ) {
            return Promise.resolve({
                list: [],
                total: 0,
            });
        }
        setSearchStore({ ...params });
        if (Number(getAppGlobalData('APP_ID')) !== 0) {
            // 非中台，其他应用引用中台页面
            newParams.appId = getAppGlobalData('APP_ID');
        }
        setQuery(_.cloneDeep(newParams));
        // 格式化请求参数
        if (formatReqParams) {
            newParams = formatReqParams(newParams);
        }
        const reqParam = {
            ...newParams,
            // appId: inSaaS ? newParams.appId : getAppGlobalData('APP_ID'),
            ...defaultParams,
        };
        if (reqParam.configureName) {
            // 查询参数编码
            reqParam.configureName = encodeURIComponent(reqParam.configureName);
            reqParam.decode = 1;
        }
        // 查询词条列表
        const entryList = await fetchData(reqParam);
        const { list, total, configureType } = entryList;
        if(configureType) {
            configureTypeRef.current = configureType;
        }
        if (sortKey) {
            list.sort((a, b) => localeCompare(a[sortKey], b[sortKey])); // 排序
        }
        const entryKeyList = list.map((item: any) => {
            if (item.id && moduleName === 'themeColor') {
                item.themeId = item.id;
                delete item.id;
            } else if (
                moduleName === 'defaultStrategyInternational' &&
                item.configureId
            ) {
                return `@i18n:@${
                    StrategyModelNames[String(item.configureType)]
                }__${item[langField]}`;
            }
            return `@i18n:@${moduleName}__${item[langField]}`;
        });
        if (entryKeyList.length !== 0) {
            // 查询词条翻译
            const data = await fetchEntryKeyLanguage({
                // module: moduleName,
                langKeys: entryKeyList.join(','),
                appId: inSaaS ? searchForm.getFieldValue('appId') : undefined,
            });
            (data || []).forEach((item: any) => {
                list.forEach((entry: any) => {
                    // 默认设置配置在此处用来拼接后进行比较的moduleName要特殊处理(是动态的)，其他tab页的都是直接使用的传进来的值
                    const CompareModuleName =
                        moduleName === 'defaultStrategyInternational'
                            ? StrategyModelNames[String(entry.configureType)]
                            : moduleName;
                    if (
                        `@i18n:@${CompareModuleName}__${entry[langField]}` ===
                        item.langKey
                    ) {
                        const translationList = item.languageList;
                        entry.entryKeyId = item.id;
                        entry.type = item.type;
                        translationList.forEach((ts: any) => {
                            entry[ts.langType] = ts.translation;
                        });
                    }
                });
            });
        }
        console.log(list)
        return Promise.resolve({
            list,
            total: parseInt(total),
        });
    };
    const formItems: any = [...searchParams];

    const columns = (data: string[]) => {
        const list = data.map((item) => ({
            title: languageLabelList[item] || item,
            dataIndex: item,
            width: 200,
            ellipsis: { showTitle: false },
            render: (text: any) => {
                let newText: string;
                if (typeof text === 'string') {
                    newText = text.trim();
                } else {
                    newText = text;
                }
                return (
                    <div
                        style={{
                            width: 100,
                        }}
                    >
                        <Tooltip title={text}>
                            <StarryAbroadOverflowEllipsisContainer>
                                {newText || '-'}
                            </StarryAbroadOverflowEllipsisContainer>
                        </Tooltip>
                    </div>
                );
            },
        }));
        return [
            ...itemColumns.map((item: any) => {
                return {
                    fixed: 'left',
                    width: 200,
                    ...item,
                    ellipsis: { showTitle: false },
                    render: (text: any, record: any) => {
                        if (item.title == i18n.t('name', '归属应用')) {
                            return (
                                <div
                                    style={{
                                        maxWidth: item.width || 200,
                                    }}
                                >
                                    {/* {item.render ? item.render(text, record) : i18n.t(
                                        `@i18n:@app__${record.appId}`,
                                        record.appName,           //appId为0时  applicationName为 -
                                    )} */}
                                    <Tooltip
                                        title={i18n.t(
                                            `@i18n:@app__${record.appId}`,
                                            record.applicationName ||
                                                record.appName,
                                        )}
                                    >
                                        <StarryAbroadOverflowEllipsisContainer>
                                            {i18n.t(
                                                `@i18n:@app__${record.appId}`,
                                                record.applicationName ||
                                                    record.appName,
                                            )}
                                        </StarryAbroadOverflowEllipsisContainer>
                                    </Tooltip>
                                </div>
                            );
                        } else {
                            const content = item.render
                                ? item.render(text, record)
                                : text;
                            // TODO 此处需要重构，先解决title不翻译问题
                            return (
                                <div
                                    style={{
                                        maxWidth: item.width || 200,
                                    }}
                                >
                                    <Tooltip title={content}>
                                        <StarryAbroadOverflowEllipsisContainer>
                                            {content}
                                        </StarryAbroadOverflowEllipsisContainer>
                                    </Tooltip>
                                </div>
                            );
                        }
                    },
                };
            }),
            ...list,
            {
                title: i18n.t('name', '操作'),
                dataIndex: 'operate',
                fixed: 'right',
                width: 180,
                render: (text: string, record: any) => {
                    return (
                        <div style={{ minWidth: '60px' }}>
                            <Tooltip
                                placement="top"
                                title={i18n.t('action', '编辑')}
                            >
                                <span>
                                    <IcListEditFill
                                        className="operation-icon"
                                        onClick={() => edit(record)}
                                    />
                                </span>
                            </Tooltip>
                        </div>
                    );
                },
            },
        ];
    };

    const columnsForEX = (data: string[]) => {
        const list = data.map((item) => ({
            title: languageLabelList[item] || item,
            dataIndex: item,
            ellipsis: true,
            width: 200,
            render: (text: any) => (
                <div
                    title={text}
                    style={{
                        width: 100,
                    }}
                >
                    {text || '-'}
                </div>
            ),
        }));
        return [
            ...EXColumns.map((item: any) => {
                return {
                    ...item,
                    ellipsis: true,
                    fixed: 'left',
                    width: 200,
                };
            }),
            ...list,
            {
                title: i18n.t('name', '操作'),
                dataIndex: 'operate',
                fixed: 'right',
                width: 180,
            },
        ];
    };

    const handleCancel = () => {
        form.resetFields();
        setModalVisible(false);
    };

    // 保存词条
    const [handleOk] = useSubmitFn(async () => {
        const values = await form.validateFields();
        const translationList: any[] = [];

        languageType.forEach((language) => {
            translationList.push({
                langType: language,
                translation: values[language] || '',
            });
        });

        const rs = await editInternationEntry({
            module: moduleName,
            langKey: `@i18n:@${moduleName}__${editRow[langField]}`,
            // languageName:,
            languageList: translationList,
            id: editRow['entryKeyId'],
            type: editRow['type'],
            appId: inSaaS ? searchForm.getFieldValue('appId') : undefined,
        });
        if (rs) {
            handleCancel();
            message.success(i18n.t('message', '操作成功'));
            TableRef.current.reload();
            await reLoadLanguage(true, true);
            // 服务入口需要手动触发一次请求触发service缓存
            moduleName === 'menu' &&
                (await getUserAppEntrys(false, CacheType.NetworkFirst));
        } else {
            message.error(i18n.t('message', '操作失败'));
        }
    });
    //是否对标签去除|\校验
    const isValidationEnabled = moduleName=="label"?true:false;
    const renderItem = (data: any) => {
        data.pop();
        return data.map(
            ({ dataIndex, title }: { dataIndex: string; title: string }) => {
                if (disabledItems && disabledItems.includes(dataIndex)) {
                    return (
                        <StarryAbroadFormItem
                            key={dataIndex}
                            label={title}
                            className="data-index"
                            name={dataIndex == 'appId' ? 'APPName' : dataIndex}
                            rules={[{ validator:isValidationEnabled? validateCharacters:validatorI18n }]}
                        >
                            <Input disabled />
                        </StarryAbroadFormItem>
                    );
                }
                return (
                    <StarryAbroadFormItem
                        key={dataIndex}
                        label={title}
                        name={dataIndex}
                        className="data-index"
                        rules={[
                            {
                                max: 50,
                                type: 'string',
                            },
                            { validator: formValidate || (isValidationEnabled ? validateCharacters : validatorI18n) },
                        ]}
                    >
                        <Input maxLength={50} allowClear />
                    </StarryAbroadFormItem>
                );
            },
        );
    };

    const exportData = () => {
        let columnstab = columnsForEX(languageType);
        if (!inSaaS) {
            columnstab = columnstab.filter((item) => {
                return item.dataIndex !== 'appName';
            });
        }

        const headersArr = columnstab.map((item: any, index: any) => {
            return {
                columnName: item.dataIndex.replace('_', ''),
                title: item.title,
                isOption: false,
                index,
            };
        });
        headersArr.splice(-1);

        let queryParam;
        if (formatReqParams) {
            const cloneQuery = _.cloneDeep(queryP); //深克隆 否则formatReqParams到本源
            queryParam = {
                param: {
                    configureType,
                    ...formatReqParams(cloneQuery),
                    ...defaultParams,
                },
            };
            if (taskTypes == 27) {
                // 报警类型的查询和导出参数名不一样
                queryParam = {
                    param: {
                        configureType,
                        ...defaultParams,
                        typeName: queryParam.param.alarmName,
                        alarmCode: queryParam.param.alarmTypeCode,
                        appId: queryParam.param.appId,
                    },
                };
            }
        } else {
            queryParam = {
                param: {
                    configureType: configureTypeRef.current || configureType,
                    ...queryP,
                    ...defaultParams,
                },
            };
        }
        if (taskTypes !== 27) {
            delete queryParam.param.page;
            delete queryParam.param.pageSize;
        }

        let sheetNameTR;

        if (needSpliceAppId) {
            sheetNameTR = `${sheetName}_${queryP.appId}`;
        } else {
            sheetNameTR = sheetName;
        }

        const sheetArr = [
            {
                sheetName: sheetNameTR,
                excelHeaders: headersArr,
                queryParam,
            },
        ];

        setSpinState(true);
        const formateTime = zeroTimeStampToFormatTime(
            moment.utc(moment().format()).unix(),
            undefined,
            'YYYYMMDDhhmmss',
        );
        // timestampToZeroTimeStamp(moment())
        const exportExcelParams: any = {
            isAsync: true,
            excelType: 'XLSX',
            fileName: `${fileName || sheetName}_${formateTime}`,
            sheetQueryParams: sheetArr,
        };
        if (EXserviceCode) {
            exportExcelParams.serviceCode = EXserviceCode;
        } else {
            exportExcelParams.serviceName = exportParams?.serviceName;
            exportExcelParams.executorHandler = exportParams?.executorHandler;
            exportExcelParams.taskType = exportParams?.taskType;
        }
        exportExcel(exportExcelParams)
            .then(() => {
                message.success(
                    i18n.t('message', '导出成功，请到个人中心中查看导出详情'),
                );
            })
            .finally(() => setSpinState(false));
    };

    const importData = () => {
        history.push({
            query: {
                appId: queryP.appId,
                sheetName,
                taskTypes,
                INserviceCode,
                importParams: JSON.stringify(importParams),
            },
            pathname: '/multiple-languages/internationalization-import',
        });
    };

    return (
        <Spin spinning={spinState}>
            <StarryTable
                fetchDataAfterMount={false}
                aroundBordered
                scroll={{ x: '100%' }}
                fetchDataFunc={fetchList}
                queryProps={{
                    items: formItems,
                    form: searchForm,
                    ...disableAttr,
                }}
                ref={TableRef}
                toolbar={{
                    iconBtns: [
                        <Tooltip title={i18n.t('name', '导入')} placement="top">
                            <span onClick={importData}>
                                <StarryAbroadIcon>
                                    <IconImport />
                                </StarryAbroadIcon>
                            </span>
                        </Tooltip>,
                        <Tooltip title={i18n.t('name', '导出')} placement="top">
                            <span onClick={exportData}>
                                <StarryAbroadIcon>
                                    <IconExport />
                                </StarryAbroadIcon>
                            </span>
                        </Tooltip>,
                        'reload',
                        'column-setting',
                    ],
                    columnSetting: {
                        disabledKeys: [...disabledItems, 'operate'],
                        storageKey:
                            '@base:@page:internationalization.table.' +
                            moduleName,
                    },
                }}
                rowKey="roleId"
                columns={columns(languageType)}
            />
            <Modal
                centered
                title={`${moduleTitle}`}
                visible={modalVisible}
                onOk={handleOk}
                onCancel={handleCancel}
                // getContainer={TableRef.current}
                maskClosable={false}
            >
                <div
                    ref={modalContentRef}
                    className={isScroll ? 'modal-scroll-show-right' : ''}
                >
                    <Form
                        form={form}
                        layout="vertical"
                    >
                        {renderItem([...columns(languageType)])}
                    </Form>
                </div>
            </Modal>
        </Spin>
    );
};

export default RightTable;
