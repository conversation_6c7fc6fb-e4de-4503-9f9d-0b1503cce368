import { ChannelTypeBindConfigure, ChannelTypeEnum, ChannelTypeItem, deleteChannelType, fetchChannelTypeBindConfigurePageList, fetchChannelTypeDetail, updateChannelType } from "@/service/channel";
import useUrlState from "@ahooksjs/use-url-state";
import {
    Auth,
    i18n,
    utils,
    StarryAbroadOverflowEllipsisContainer,
    useSystemComponentStyle,
    getAppGlobalData,
} from '@base-app/runtime-lib';
import { useLockFn, useRequest, useUpdateEffect } from "ahooks";
import {
    PageBreadcrumbLayout,
    PageCardLayout,
    StarryModal,
    Action,
} from '@base-app/runtime-lib';
import { Button, Container, message, Space, Table, Tabs, Tag } from "@streamax/poppy";
import { SwitchStatusEnum } from "@/types";
import { IconRequest } from "@streamax/poppy-icons";
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useMemo, useRef, useState } from "react";
import { Descriptions, InfoPanel, ListDataContainer } from "@streamax/starry-components";
import { channelTypeOptions } from "../../../constants";
import { GetDataSourceParams, RefListDataContainerProps } from "@streamax/starry-components/lib/list-data-container";
import { ColumnsType } from "@streamax/poppy/lib/table";
import ChannelTypeAddEditModal from "../ChannelTypeAddEditModal";
import { getChannelTypeInternationalName } from "@/hooks/useChannelData/utils";
import { useHistory } from '@base-app/runtime-lib/core';

interface InfoItemProps {
    label: string;
    content: string;
    column?: number;
    ellipsis?: boolean;
}

export default () => {
	const [searchParams, setSearchParams] = useUrlState<{
        id: string;
        typeId: string;
        channelTypeName: string;
        state: SwitchStatusEnum;
        type: ChannelTypeEnum;
    }>(undefined, { navigateMode: 'replace' });
    const { id,typeId, channelTypeName = '', state, type } = searchParams || {};
    const { data, refresh } = useRequest(fetchChannelTypeDetail, { defaultParams: [{ id }] });
    const listDataContainerRef = useRef<RefListDataContainerProps>(null);
    const [visible, setVisible] = useState<boolean>(false);
    const [loaded, setLoaded] = useState<boolean>(false);
    const { isAbroadStyle } = useSystemComponentStyle();
    const inSaaS = !getAppGlobalData('APP_ID');
    const history = useHistory();

    const name = loaded ? getChannelTypeInternationalName({
        typeId,
        channelTypeName: decodeURIComponent(channelTypeName ?? ""),
        type,
    }): undefined;

	useUpdateEffect(() => {
        if (data) {
            setSearchParams({
                channelTypeName: encodeURIComponent(data?.channelTypeName ?? ''),
                state: data?.state,
                type: data?.type,
                typeId: data?.typeId
            });
            setLoaded(true);
        }
    }, [data]);

    const infoItems: InfoItemProps[] = [
        {
            label: i18n.t('name', '通道类型'),
            content: name,
        },
        {
            label: i18n.t('name', '类型'),
            content: channelTypeOptions?.find((item) => item.value === data?.type)?.label || '-',
        },
        {
            label: i18n.t('name', '创建人'),
            content: data?.createUserName || '-',
        },
        {
            label: i18n.t('name', '创建时间'),
            content: data?.createTime
                ? utils.formator.zeroTimeStampToFormatTime(data?.createTime)
                : '-',
        },
    ].filter((item) => item);

    const columns: ColumnsType<ChannelTypeBindConfigure> = [
        {
            title: i18n.t('name', '设置名称'),
            dataIndex: 'configureId',
            render: (configureId: number, record) => {
                const name =
                    i18n.t(
                        `@i18n:@channelSettingStrategy__${configureId}`,
                        record?.configureName,
                    ) || '-';
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {record.priority === 0 ? (
                            <Action
                                code={
                                    '@base:@page:channel.setting:type.detail@action:default.channel.setting'
                                }
                                url="/CH-set"
                                fellback={name}
                                params={{
                                    choosedAppId: record.appId,
                                    configureType: record.configureType
                                }}
                            >
                                {name}
                            </Action>
                        ) : (
                            <Action
                                code={
                                    '@base:@page:channel.setting:type.detail@action:custom.channel.setting'
                                }
                                url="/CH-set/custom/detail"
                                fellback={name}
                                params={{
                                    configureId,
                                    choosedAppId: record.appId,
                                    configureType: record.configureType
                                }}
                            >
                                {name}
                            </Action>
                        )}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        inSaaS && {
            title: i18n.t('name', '归属应用'),
            dataIndex: 'appId',
            width: '25%',
            render: (appId: number, record: any) => {
                const text = i18n.t(`@i18n:@app__${appId}`, record?.applicationName) || '-';
                return (
                    <StarryAbroadOverflowEllipsisContainer>
                        {text}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
    ];

    // 停启用
    const handleAble = useLockFn(async (checked: boolean) => {
        const title = checked ? i18n.t('name', '确认启用') : i18n.t('name', '确认停用');
        const content = checked
            ? i18n.t('message', '确认要启用"{name}"通道类型吗？', {
                  name
              })
            : i18n.t('message', '确认要停用"{name}"通道类型吗？', {
                  name
              });
        StarryModal.confirm({
            center: true,
            title,
            content,
            icon: <IconRequest />,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: async () => {
                const reqParams = {
                    id,
                    state: checked ? 1 : 0,
                };
				await updateChannelType(reqParams);
                refresh();
				message.success(checked
					? i18n.t('message', '启用成功')
					: i18n.t('message', '停用成功'));
            },
        });
    });

    const handleEdit = () => {
        setVisible(true);
    };

    const fetchData = async (params: GetDataSourceParams) => {
        const data = await fetchChannelTypeBindConfigurePageList({...params, id});
        return data;
    };

    const handleFinish = () => {
        refresh();
        listDataContainerRef.current?.loadDataSource();
    };

    const handleDelete = () => {
        const content = i18n.t('message', '确认要删除“{name}”通道类型吗？', {
            name,
        });
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '确认删除'),
            content,
            icon: <IconRequest />,
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            onOk: async () => {
                await deleteChannelType({ ids: id });
                message.success(i18n.t('message', '删除成功'));
                history.goBack();
            },
        });
    };
    return (
        <PageBreadcrumbLayout>
            <PageCardLayout>
                <InfoPanel
                    title={
                        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                            <span>{name}</span>
                        </div>
                    }
                    showCollapseBtn
                    outTitle
                    defaultCollapsed={true}
                    extraRight={
                        <Space>
                            {data?.type === ChannelTypeEnum.custom && (
                                <Auth code="@base:@page:channel.setting@action:tab.channel.type.manage:delete">
                                <Button onClick={handleDelete} danger>
                                    {i18n.t('action', '删除')}
                                </Button>
                            </Auth>)}
                        </Space>
                       }
                >
                    <div style={{ marginBottom: 24 }}>
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                marginBottom: 16,
                                height: 24,
                            }}
                        >
                            <div style={{ fontSize: 16, fontWeight: 600 }}>
                                {i18n.t('name', '基本信息')}
                            </div>
                            <div>
                                {
                                    <Button
                                        style={{ padding: 0 }}
                                        onClick={handleEdit}
                                        type="link"
                                    >
                                        {i18n.t('action', '编辑')}
                                    </Button>
                                }
                            </div>
                        </div>
                        <Descriptions>
                            {infoItems.map((item) => (
                                <Descriptions.Item
                                    label={item.label}
                                    key={item.label}
                                    ellipsis={item?.ellipsis ?? true}
                                    column={item.column || 1}
                                >
                                    {item.content}
                                </Descriptions.Item>
                            ))}
                        </Descriptions>
                    </div>
                </InfoPanel>
                <Tabs hiddenLine={isAbroadStyle}>
                    <Tabs.TabPane key="BindConfigure" tab={i18n.t('name', '关联设置')}>
                        <ListDataContainer
                            ref={listDataContainerRef}
                            getDataSource={fetchData}
                            loadDataSourceOnMount
                            toolbar={false}
                            listRender={(data) => {
                                return (
                                    <Table
                                        rowKey="configureId"
                                        columns={columns.filter(item => item)}
                                        dataSource={data}
                                    />
                                );
                            }}
                        />
                    </Tabs.TabPane>
                </Tabs>
                <ChannelTypeAddEditModal
                    id={id}
                    visible={visible}
                    type={type}
                    channelTypeName={name}
                    onClose={() => setVisible(false)}
                    onFinish={handleFinish}
                />
            </PageCardLayout>
        </PageBreadcrumbLayout>
    );
};
