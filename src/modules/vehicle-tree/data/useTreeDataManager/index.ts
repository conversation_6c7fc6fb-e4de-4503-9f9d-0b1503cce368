import type { SOCKET_DATA } from '@/const/socket-topic';
import { SOCKET_TOPIC, VEHICLE_STATE } from '@/const/socket-topic';
import {
    FLEET_PREFIX,
    TOP_FLEET_PARENT_ID,
    UN_GROUP_FLEET_ID,
    VEHICLE_PREFIX,
} from '@/const/vehicle';
import useRecordFilterFnWrap from '@/hooks/useRecordFilterFnWrap';
import {
    splitToMacroTaskByPerformanceAutoTuning,
    getListParentsPath,
} from '@/hooks/useRTData/util';
import useSubscribe from '@/hooks/useSubscribe';
import { useDeviceStore, getDeviceStore } from '@/modules/device/useRTDeviceData';
import useVehicleList from '@/modules/realtime-monitor/fleet-vehicle-tree/data/useVehicleList';
import { DeviceStateType, getOrderVehicleStatePage } from '@/service/vehicle';
import { useAsyncEffect } from '@streamax/hooks';
import { i18n, performanceMeasureUtil } from '@base-app/runtime-lib';
import { get, orderBy, set } from 'lodash';
import { useEffect, useRef } from 'react';
import { useRTDataStore } from '../realtime-data/useRTDataManager';
import useFleetOnlineNumberStore, { getFleetOnlineNumberStore } from '../realtime-data/useRTFleetOnlineNumber';
import useTreeDataStore, { BaseNode, NodeType, TreeData, getTreeDataStore } from './useTreeDataStore';
import { safeTransactionRequest, useSafeStore } from '@/modules/common/withSafeStore';
/**
 * 返回自身及其所有父节点的ID
 * @param fId string
 * @param fleetList Fleet[]
 * @returns result string[]
 */
//  export const getParentIds = <T extends Record<string, any>>(
//     id: string,
//     list: T[],
//     fields = { idKey: 'fId', parentIdKey: 'parentId' },
//     result: string[] = [],
// ) => {
//     // 判断 parentId 是否存在于 list
//     const parentItem = list.find((i) => i[idKey] === id);
//     if(!parentItem) {
//         return result;
//     }
//     result.unshift(id);
//     const { idKey, parentIdKey } = fields;
//     const parentId = parentItem?.[parentIdKey];
//     const parent = parentId ? list.find((i) => i[idKey] === parentId) : null;
//     if (parent) {
//         getParentIds(parent[idKey], list, fields, result);
//     }
//     return result;
// };
export interface UseTreeDataStoreConfig {
    instanceId: string;
    // 是否计算车组车辆树
    countVehicle: boolean;
    // 查询全量车组
    queryFleets: () => Promise<BaseNode[]>;
    // 查询全量车辆
    queryVehicles: () => Promise<BaseNode[]>;
    // 过滤车辆
    filterVehicleCallbacks?: (vehicle: TreeData) => boolean;
    // 将树数据转换为用于渲染的树节点数据
    convertTreeData2Node: (treeData: TreeData, instanceId: string) => Promise<StarryTree.TreeNode>;
    updateTreeDataNodeTitle: (TreeNode: StarryTree.TreeNode, treeData: TreeData, instanceId: string) => StarryTree.TreeNode;
    showVehicle?: boolean; //未分组车组是否展示子车组
    // 自定义车组基础数据
    getCustomFleets?: (data: any[]) => any[];
    // 自定义车辆基础数据
    getCustomVehicles?: (data: any[]) => any[];
    // 自定义未分组标题
    getCustomUnGroupedTitle?: (title: JSX.Element) => JSX.Element;
    // 定义最大的可展示的车辆数
    treeShowVehicleNumber?: number;
}
export const getVidParentKey = (vehicle: BaseNode) =>
    `${vehicle.id}_${vehicle.parentId}`;
const countFleetVehicle = async (
    fleetList: TreeData[],
    vehicleList: TreeData[],
    vIdParents: Record<string, Set<string>>,
) => {
    const result = fleetList.reduce((re, item) => {
        re[item.id] = 0;
        return re;
    }, {} as Record<string, number>);
    const hasCountVids = new Set<string>();
    window.vehicleList = vehicleList;
    for (let index = 0; index < vehicleList.length; index++) {
        const element = vehicleList[index];
        if (hasCountVids.has(element.id)) {
            continue;
        }
        hasCountVids.add(element.id);
        const parentIds = vIdParents[element.id];
        if (parentIds?.size > 0) {
            parentIds.forEach((pId: string) => {
                result[pId] += 1;
            });
        }
    }
    return result;
};

const getVIdParents = splitToMacroTaskByPerformanceAutoTuning(
    async (
        vehicleList: TreeData[],
        result: Record<string, Set<string>> = {},
    ) => {
        vehicleList.forEach((item) => {
            const parentIds = item.parentIds || [];
            if (!result[item.id]) {
                result[item.id] = new Set<string>(parentIds);
            } else {
                result[item.id] = new Set<string>(
                    parentIds.concat(Array.from(result[item.id])),
                );
            }
            return result;
        }, result);
        return [];
    },
);

const fleetPrefix = FLEET_PREFIX;
const vehiclePrefix = VEHICLE_PREFIX;

export const getTreeVehicleKey = (fId: string, vId: string) => {
    return {
        key: `${vehiclePrefix}${fId}-${vId}`,
        // 后面需要转为树数据，id不能重复，所以必须加上车组id
        // id: `${fId}-${vId}`,
        id: `${fId}-${vId}`,
    };
};
/**
 * 返回车辆树key对应的车辆id
 * @param key
 * @returns vId string
 */
export const getVehicleIdByTreeKey = (key: string) => {
    return key.split('-').pop();
};
export const getTreeFleetKey = (fId: string) => {
    return {
        id: fId,
        key: fId !== TOP_FLEET_PARENT_ID ? `${fleetPrefix}${fId}` : '',
    };
};

// 用于拷贝对象，包含属性描述符。主要用于 VehicleList 是有数下描述符的
export const copyWithDescriptors = <T extends {}, U>(
    target: T,
    ...sources: U[]
) => {
    sources.forEach((source: U) => {
        const descriptors = Object.getOwnPropertyDescriptors(source);
        Object.defineProperties(target, descriptors);
    });
    return target;
};
const useTreeDataManager = (useTreeDataStoreConfig: UseTreeDataStoreConfig) => {
    const { instanceId = 'default' } = useTreeDataStoreConfig;
    const { showVehicle = true, treeShowVehicleNumber, getCustomUnGroupedTitle } =
        useTreeDataStoreConfig;
    const unGroupFleet = {
        id: UN_GROUP_FLEET_ID,
        name: getCustomUnGroupedTitle
            ? getCustomUnGroupedTitle(i18n.t('name', '未分组'))
            : i18n.t('name', '未分组'),
        type: NodeType.FLEET,
        parentIds: [],
        parentId: TOP_FLEET_PARENT_ID,
        parentKey: '',
        pos: '',
        key: getTreeFleetKey(UN_GROUP_FLEET_ID).key,
        level: 0,
        isLeaf: showVehicle ? false : true,
        fId: UN_GROUP_FLEET_ID,
        path: [UN_GROUP_FLEET_ID],
        // 特殊标识。排序时需要将未分组放最后面，需要特殊处理
        nameIndex: -1,
    };
    const {
        convertTreeData2Node,
        filterVehicleCallbacks,
        updateTreeDataNodeTitle,
        countVehicle,
        queryFleets,
        queryVehicles,
    } = useTreeDataStoreConfig;
    const treeDataStore = getTreeDataStore(instanceId);
    const fleetOnlineNumberStore = getFleetOnlineNumberStore(instanceId);
    const deviceStore = getDeviceStore(instanceId);
    const loaded = treeDataStore((state) => state.loaded);
    // 更新过滤条件
    useEffect(() => {
        treeDataStore
            .getState()
            .config({
                convertTreeData2Node,
                filterVehicleCallbacks,
                updateTreeDataNodeTitle,
                treeShowVehicleNumber,
                showVehicle
            });
        treeDataStore.getState().refresh();
    }, [convertTreeData2Node, updateTreeDataNodeTitle, filterVehicleCallbacks]);
    const vehicleList = useVehicleList(instanceId);
    // 更新过滤后的车辆
    useEffect(() => {
        if (typeof filterVehicleCallbacks === 'function') {
            const filteredVehicleList = vehicleList.filter((item) =>
                filterVehicleCallbacks?.(item),
            );
            treeDataStore
                .getState()
                .updateFilteredVehicleList(filteredVehicleList);
        } else {
            treeDataStore.getState().updateFilteredVehicleList(vehicleList);
        }
    }, [filterVehicleCallbacks, vehicleList]);

    const proxyVehicleRTData = (
        item: TreeData,
        properties: { key: string; defaultValue: any }[],
    ) => {
        properties.forEach((property) => {
            item[property.key] = item[property.key] || property.defaultValue;
        });
        return;
        const desc = properties.reduce((result, property) => {
            result[property.key] = {
                get() {
                    return (
                        useRTDataStore.getState().rtDataMap[item.id]?.[
                            property.key
                        ] || property.defaultValue
                    );
                },
            } as PropertyDescriptor;
            return result;
        }, {});
        Object.defineProperties(item, desc);
    };
    const proxyFleetRTData = (item: TreeData) => {
        Object.defineProperties(item, {
            vNumber: {
                get(){
                    return treeDataStore.getState().fleetTotalNumberMap[item.id] || 0;
                }
            },
            vOnlineNumber: {
                get(){
                    return fleetOnlineNumberStore.getState().fleetOnlineNumberMap[item.id] || 0;
                }
            },
        });
    };
    const mergeFleetList = useRef(
        splitToMacroTaskByPerformanceAutoTuning(
            (
                fleetListSlice: BaseNode[],
                fIdParentsPathMap: Record<string, string[]>,
            ) => {
                return fleetListSlice.map((item, index) => {
                    if (item.parentId === UN_GROUP_FLEET_ID) {
                        item.parentId = TOP_FLEET_PARENT_ID;
                    }
                    let parentId = item.parentId;
                    // 父级车组id路径
                    const path =
                        parentId !== TOP_FLEET_PARENT_ID
                            ? fIdParentsPathMap[item.id]
                            : [item.id];
                    parentId =
                        path.length <= 1 ? TOP_FLEET_PARENT_ID : parentId;
                    Object.assign(item, {
                        // 未分组Id为0。顶级车组需置为null path 为1也是顶级车组
                        parentId,
                        parentIds: path.slice(0, path.length - 1),
                        type: NodeType.FLEET,
                        key: getTreeFleetKey(item.id).key,
                        parentKey: getTreeFleetKey(parentId).key,
                        pos: '',
                        level: path.length - 1,
                        // 兼容字段
                        fId: item.id,
                        fName: item.name,
                        path,
                        nameIndex: index,
                    });
                    proxyFleetRTData(item);
                    return item;
                });
            },
        ),
    );
    // TODO 车辆的车组不在车组列表时，存在问题
    const mergeVehicleList = useRef(
        splitToMacroTaskByPerformanceAutoTuning(
            (
                vehicleListSlice: BaseNode[],
                newFleetListMap: Record<string, TreeData>,
            ) => {
                return vehicleListSlice.map((item) => {
                    const parentIds = [item.parentId].concat(
                        newFleetListMap[item.parentId]?.path,
                    );
                    Object.assign(item, {
                        // 父级 level + 1
                        level: newFleetListMap[item.parentId]?.level + 1,
                        key: getTreeVehicleKey(item.parentId, item.id).key,
                        parentKey: getTreeFleetKey(item.parentId).key,
                        pos: '',
                        isLeaf: true,
                        parentIds,
                        // 兼容字段
                        vId: item.id,
                        vNo: item.name,
                        fId: item.parentId,
                        path: parentIds,
                        nameIndex: 0,
                    });
                    proxyVehicleRTData(item, [
                        // 车辆状态
                        {
                            key: 'vStates',
                            defaultValue: [],
                        },
                        {
                            key: 'vOnlineNumber',
                            defaultValue: 0,
                        },
                        {
                            key: 'deviceList',
                            defaultValue: [],
                        },
                        // 司机
                        {
                            key: 'driver',
                            defaultValue: '',
                        },
                        {
                            key: 'driverId',
                            defaultValue: '',
                        },
                        {
                            key: 'driverList',
                            defaultValue: [],
                        },
                        // gps
                        {
                            key: 'gps',
                            defaultValue: {},
                        },
                        // 行业数据
                        {
                            key: 'extends',
                            defaultValue: [],
                        },
                    ]);
                    return new Proxy(item, {
                        get(target, property) {
                            return target[property];
                        },
                        set(target, property, value) {
                            if (property === 'type' || property === 'isLeaf') {
                                console.error('debugId=128303 修改level', target, value);
                            }
                            target[property] = value;
                            return true;
                        }
                    });
                    return item;
                });
            },
        ),
    );
    const updateListData = async (
        vehicleList: TreeData[],
        fleetList: TreeData[],
        fleetListMap: Record<string, TreeData>,
        firstLoad = false,
    ) => {
        const vIdParents = {};
        await getVIdParents(vehicleList, vIdParents);
        // 后端支持不了排序，前端对vehicleList进行name升序
        const st = new Date().getTime();
        const orderedVehicleList = firstLoad
            ? orderBy(vehicleList, 'name', 'asc')
            : vehicleList;
        // console.warn('车辆排序时间', (new Date().getTime()) - st);
        fleetOnlineNumberStore.getState().setVIdParents(vIdParents);
        treeDataStore.getState().setVIdParents(vIdParents);
        treeDataStore.getState().updateFleetListMap(fleetListMap);
        treeDataStore.getState().updateVehicleList(orderedVehicleList);
        await treeDataStore
            .getState()
            .updateFleetChildrenMap(fleetList, orderedVehicleList);
        // 计算车组下车辆总数
        if (countVehicle) {
            const result = await countFleetVehicle(
                fleetList,
                orderedVehicleList,
                vIdParents,
            );
            treeDataStore.getState().updateFleetTotalNumberMap(result);
        }
        treeDataStore.getState().updateLoaded(true);
        // 触发渲染
        treeDataStore.getState().refresh();
    };
    const getOrderVehicleState = async (type: DeviceStateType) => {
        const data = await getOrderVehicleStatePage({
            type,
        });
        return orderBy(data?.list || [], 'num');
    };
    useAsyncEffect(async () => {
        try {
            performanceMeasureUtil.tree.start('normalTree.load');
            await safeTransactionRequest(async ({ validate }) => {
                const fleetListPromise = queryFleets();
                const vehicleListPromise = queryVehicles();
                const stateConfig = await getOrderVehicleState(
                    DeviceStateType.LIVE,
                );
                deviceStore.getState().updateStateConfig(stateConfig);
                const fleetList = await fleetListPromise;
                const fIdParentsPathMap = getListParentsPath(fleetList);
                let newFleetList: TreeData[] = await mergeFleetList.current(
                    fleetList,
                    fIdParentsPathMap,
                );
                let newFleetListMap = {};
                for (let i = 0; i < newFleetList.length; i++) {
                    const item = newFleetList[i];
                    newFleetListMap[item.id] = item;
                }
                if (!validate()) return;
                treeDataStore.getState().updateFleetList(newFleetList);
                treeDataStore.getState().updateFleetListMap(newFleetListMap);
                treeDataStore
                    .getState()
                    .updateFleetChildrenMap(newFleetList, []);

                /**
                 * 处理车辆 start
                 */
                let vehicleList = await vehicleListPromise;
                if (!validate()) return;
                // 过滤 车组id为空 且此车的车组id有不为空的情况。要求有车组的不展示到 未分组车组.
                // 如后端返回了2个车辆数据，其中一个车辆的车组不在车组列表中。另一个在车组列表中。此时就去除不在车组列表中的车辆
                vehicleList = vehicleList.filter((item) => {
                    // 如果找不到车辆的车组，就展示到 未分组车组
                    if (!newFleetListMap[item.parentId]) {
                        item.parentId = UN_GROUP_FLEET_ID;
                    }
                    return !(
                        item.parentId === unGroupFleet.id &&
                        vehicleList.some(
                            (i) =>
                                i.parentId !== unGroupFleet.id &&
                                i.id === item.id,
                        )
                    );
                });
                // 添加未分组车组
                if (vehicleList.some((i) => i.parentId === unGroupFleet.id)) {
                    newFleetList = newFleetList.slice();
                    newFleetList.push(unGroupFleet);
                    treeDataStore.getState().updateFleetList(newFleetList);
                    newFleetListMap = Object.assign(newFleetListMap, {
                        [unGroupFleet.id]: unGroupFleet,
                    });
                    treeDataStore
                        .getState()
                        .updateFleetListMap(newFleetListMap);
                }

                // 触发渲染
                treeDataStore
                    .getState()
                    .loadFleets(
                        newFleetList
                            .filter((i) => i.parentId === TOP_FLEET_PARENT_ID)
                            .map((i) => i.id),
                    );

                if (!validate()) return;
                // console.log('debugId=127702 mergeVehicleList');
                const newVehicleList: TreeData[] =
                    await mergeVehicleList.current(
                        vehicleList,
                        newFleetListMap,
                    );
                await updateListData(
                    newVehicleList,
                    newFleetList,
                    newFleetListMap,
                    true,
                );
            });
        } catch (e) {
            // cancel
        } finally { 
            performanceMeasureUtil.tree.end('normalTree.load');
        }
    }, []);
    useSafeStore(treeDataStore, treeDataStore.getState().resetStore);
    // 需要删除停用的车
    const disableVehicle = useRecordFilterFnWrap(
        'vehicleId',
        async (data: SOCKET_DATA['BASE_VEHICLE_STATUS']) => {
            if (data.vehicleState !== VEHICLE_STATE.ENABLE) {
                const treeDataStoreState = treeDataStore.getState();
                console.log("updateFleetTotalNumberMap", result, treeDataStoreState.vehicleList.length);
                const result = treeDataStoreState.deleteVehicle(
                    data.vehicleId,
                );
                console.log("updateFleetTotalNumberMap1", result, treeDataStoreState.vehicleList.length);
                if (!result) return;

                if (countVehicle) {
                    const result = await countFleetVehicle(
                        treeDataStoreState.fleetList,
                        treeDataStoreState.vehicleList,
                        treeDataStoreState.vIdParents,
                    );
                    treeDataStore
                        .getState()
                        .updateFleetTotalNumberMap(result);
                   
                    debugger;
                    const fleetOnlineNumberStore = getFleetOnlineNumberStore(instanceId);
                    await fleetOnlineNumberStore
                        .getState()
                        .updateFleetOnlineNumberMap();
                    // 移除车辆的父级车组
                    const newVidParents = fleetOnlineNumberStore.getState().vIdParents;
                    const updatedVidParents = Object.assign({}, newVidParents);
                    delete updatedVidParents[data.vehicleId];
                    fleetOnlineNumberStore.getState().setVIdParents(newVidParents);
                }
                // 触发渲染
                treeDataStore.getState().refresh();
            }
        },
    );

    // 订阅车辆被停用
    useSubscribe(SOCKET_TOPIC.BASE_VEHICLE_STATUS, disableVehicle);
    useSafeStore(
        fleetOnlineNumberStore, fleetOnlineNumberStore.getState().resetStore,
    );
    return {
        loaded,
    };
};
export default useTreeDataManager;
