import { create } from 'zustand';
import { useDeviceStore, getDeviceStore } from '@/modules/device/useRTDeviceData';
import LoadFleetsManager from './LoadFleetsManager';
import { useFleetOnlineNumberStore, getFleetOnlineNumberStore } from '../realtime-data/useRTFleetOnlineNumber';
import { ONLINE_STATE, TOP_FLEET_PARENT_ID } from '@/const/vehicle';
import { UseTreeDataStoreConfig } from '.';
import { inPlaceSort } from 'fast-sort';
import { withSafeStore } from '@/modules/common/withSafeStore';
import StoreFactory from '@/class/StoreFactory';

export enum NodeType {
    FLEET = 'fleet',
    VEHICLE = 'vehicle',
    OPERATION = 'operation',
}
export interface BaseNode {
    name: string;
    type: NodeType;
    parentId: string;
    id: string;
    extends?: Record<string, any>;
}
export interface TreeData extends BaseNode {
    // 树节点的层级，0开始
    level: number;
    // 树节点在同级别下的下标信息
    pos: string;
    // 树节点的唯一标识
    key: string;
    parentKey: string;
    parentIds?: string[];
    isLeaf: boolean;
    // 兼容字段，避免大量修改，后续优化删除
    vId?: string;
    fId: string;
    vNo?: string;
    // 对应 parentIds
    path: string[];
    nameIndex: number;
    vStates?: string[];
}

type State = {
    fleetLoaded: boolean;
    vehicleLoaded: boolean;
    // 数据加载完成且相关state已设置
    loaded: boolean;
    // 已经加载的节点数据的key
    loadedKeys: string[];
    // 用于给Tree渲染的数据，根据 loadedKeys 计算而得
    // treeData: TreeData[];
    treeNodes: StarryTree.TreeNode[];
    fleetList: TreeData[];
    fleetListMap: Record<string, TreeData>;
    fleetChildrenMap: Record<
        string,
        {
            fleets: TreeData[];
            vehicles: TreeData[];
        }
    >;
    // 车组车辆总数
    fleetTotalNumberMap: {
        [id: string]: number;
    };
    // Map 形式的车辆列表
    vehicleListMap: Record<string, TreeData>;
    // 数组形式的车辆列表
    vehicleList: TreeData[];
    // 过滤后的全量车辆列表
    filteredVehicleList: TreeData[];
    vIdParents: Record<string, Set<string>>;
    loadFleetsManager: LoadFleetsManager;
    treeRef: any;
    showVehicle: boolean;
    // 去重后的车辆列表
    // uniqueVehicleList: [];
};

type Actions = {
    updateLoaded: (loaded: boolean)=>void;
    updateFleetList: (fleetList: TreeData[]) => void;
    updateFleetListMap: (fleetListMap: Record<string, TreeData>) => void;
    updateFleetChildrenMap: (fleetList: TreeData[], vehicleList: TreeData[]) => Promise<void>;
    orderFleetChildrenMap: () => Promise<void>;
    updateFleetTotalNumberMap: (fleetTotalNumberMap: Record<string, number>) => void;

    updateVehicleList: (vehicleList: TreeData[]) => void;
    updateFilteredVehicleList: (vehicleList: TreeData[]) => Promise<void>;
    deleteVehicle: (vId: string) => Promise<boolean>;

    setLoadedKeys: (keys: string[]) => void;
    // 加载指定树节点，不传就是刷新
    loadFleets: (fIds?: string[]) => Promise<TreeData[]>;
    refresh: () => Promise<TreeData[]>;
    setVIdParents: (vIdParents: Record<string, Set<string>>) => void;
    updateTreeNode: (treeNodes: StarryTree.TreeNode[]) => void;
    config: (
        options: Pick<UseTreeDataStoreConfig, 'convertTreeData2Node' | 'filterVehicleCallbacks'>,
    ) => void;
    resetStore: () => void;
    setTreeRef: (ref: any) => void;
    scrollToNode: (key: string) => void;
};
/**
 * 创建TreeData Store的函数
 * @param instanceId 实例ID
 * @returns TreeData Store实例
 */
const createTreeDataStore = (instanceId: string =  'default') => {
    return create<State & Actions>()(
        withSafeStore(
            (set, get) => ({
            fleetLoaded: false,
            vehicleLoaded: false,
            loaded: false,
            loadedKeys: [],
            treeNodes: [],
            fleetList: [],
            fleetListMap: {},
            fleetChildrenMap: {},
            fleetTotalNumberMap: {},
            vehicleList: [],
            vehicleListMap: {},
            filteredVehicleList: [],
            vIdParents: {},
            loadFleetsManager: new LoadFleetsManager({ instanceId }),
            treeRef: null,
        // uniqueVehicleList: [],
        updateLoaded(loaded: boolean){
            set((state) => {
                return {
                    loaded
                };
            });
        },
        updateFleetList: (fleetList: TreeData[]) => {
            set((state) => {
                return {
                    fleetLoaded: true,
                    fleetList
                };
            });
        },
        updateFleetListMap: (fleetListMap: Record<string, TreeData>) => {
            set((state) => {
                return {
                    fleetListMap
                };
            });
        },
        // 删除某个车辆
        async deleteVehicle(vId: string): Promise<boolean> {
            return new Promise((resolve) => {
                const vehicleList: TreeData[] = [];
                const fIds = new Set<string>();
                get().vehicleList.forEach(item=>{
                    if(item.id === vId) {
                        fIds.add(item.fId);
                    }else {
                        vehicleList.push(item);
                    }
                });
                // 没有可删除的
                if(fIds.size === 0) {
                    resolve(false);
                    return;
                }
                const vehicleListMap = get().vehicleListMap;
                delete vehicleListMap[vId];
                const fleetChildrenMap = get().fleetChildrenMap;
                fIds.forEach(fId=>{
                    fleetChildrenMap[fId].vehicles = fleetChildrenMap[fId].vehicles.filter(i=>i.id !== vId);
                })
                set((state) => {
                    return {
                        vehicleList,
                        vehicleListMap,
                        fleetChildrenMap
                    };
                });
                resolve(true);
            });
        },
        updateFleetChildrenMap: async (fleetList: TreeData[], vehicleList: TreeData[]) => {
            const result = {};
            for (let index = 0; index < fleetList.length; index++) {
                const fleet = fleetList[index];
                if (!result[fleet.id]) {
                    result[fleet.id] = {
                        fleets: [],
                        vehicles: [],
                    };
                }
                if (!result[fleet.parentId]) {
                    result[fleet.parentId] = {
                        fleets: [],
                        vehicles: [],
                    };
                }
                fleet.nameIndex = result[fleet.parentId].fleets.length
                result[fleet.parentId].fleets.push(fleet);
            }
            let st = new Date().getTime();            
            for (let index = 0; index < vehicleList.length; index++) {
                const vehicle = vehicleList[index];
                vehicle.nameIndex  = result[vehicle.parentId].vehicles.length;
                result[vehicle.parentId].vehicles.push(vehicle);
            }
            // console.warn((new Date().getTime())- st);
            set((state) => {
                return {
                    fleetChildrenMap: result
                };
            });
        },
        async orderFleetChildrenMap() {                        
            const deviceStore = getDeviceStore(instanceId);
            const stateChanged = deviceStore?.getState().stateChanged || {};
            const fleetOnlineNumberStore = getFleetOnlineNumberStore(instanceId);
            const vIdParents = fleetOnlineNumberStore?.getState().vIdParents;
            const fleetOnlineNumberMap = fleetOnlineNumberStore?.getState().fleetOnlineNumberMap;
            const deviceMap = deviceStore?.getState().deviceMap;
            let orderVehicleFIds: string[] = [];
            let orderFleetFIds: string[] = [TOP_FLEET_PARENT_ID];
            for (let index = 0; index < stateChanged[ONLINE_STATE]?.length; index++) {
                const { vId } = stateChanged[ONLINE_STATE][index];
                const parentIds = vIdParents[vId];
                if (parentIds?.size > 0) {
                    orderVehicleFIds.push(...parentIds);
                    // 直接父级车组下的车组并不会收到车辆状态变化而变化。这里有个问题，如果一个车辆挂载多个车组，那后续车组会被算进来。后续优化
                    orderFleetFIds.push(...(Array.from(parentIds).slice(1)));
                }
            };
            const fleetChildrenMap = get().fleetChildrenMap;
            const newFleetChildrenMap = {...fleetChildrenMap};
            const orderVehicleFIdsArray = Array.from(new Set(orderVehicleFIds));
            for (let index = 0; index < orderVehicleFIdsArray.length; index++) {
                const fId = orderVehicleFIdsArray[index];
                // 对此车组按照车辆在线数排序
                // 动态数据需要从动态数据模块获取
                let tmpVehicles = fleetChildrenMap[fId].vehicles.slice(0);
                // 默认排序 15 ms
                let st = Date.now();
                // inPlaceSort 排序
                st = Date.now();
                inPlaceSort(tmpVehicles).by([                    
                    { desc: (u: TreeData) => (deviceMap[u.id]?.vOnlineNumber || 0)},
                    { asc: (u: TreeData) => u.nameIndex },
                  ]);
                newFleetChildrenMap[fId].vehicles = tmpVehicles;
            }
            new Set(orderFleetFIds).forEach((fId) => {
                // 对此车组按照车辆在线数排序
                // 动态数据需要从动态数据模块获取
                const fleet = fleetChildrenMap[fId].fleets.slice(0);
                fleet.sort(
                    (a, b) => {
                        const aNumber = fleetOnlineNumberMap[a.id] || 0;
                        const bNumber = fleetOnlineNumberMap[b.id] || 0;
                        if (aNumber !== bNumber) {
                            // vOnlineNumber 降序
                            return bNumber - aNumber;
                        }
                        return a.nameIndex - b.nameIndex;
                    },
                );
                newFleetChildrenMap[fId].fleets = fleet;
            });
            set((state) => {
                return {
                    fleetChildrenMap: newFleetChildrenMap
                };                
            });
        },
        updateFleetTotalNumberMap: (fleetTotalNumberMap: Record<string, number>) => {
            set((state) => {
                return {
                    fleetTotalNumberMap
                };
            });
        },
        updateVehicleList: (vehicleList: TreeData[]) => {
            set((state) => {
                const vehicleListMap = vehicleList.reduce((map, item) => {
                    map[item.id] = item;
                    return map;
                }, {} as Record<string, TreeData>);
                return {
                    vehicleLoaded: true,
                    vehicleList,
                    vehicleListMap
                };
            });
        },        
        updateFilteredVehicleList: async(filteredVehicleList: TreeData[]) => {
            set((state) => {
                return {
                    filteredVehicleList
                };
            });
        },
        setLoadedKeys: (loadedKeys: string[]) => {
            set((state) => {
                return {
                    loadedKeys
                };
            });
        },
        loadFleets: async (fIds?: string[]) => {
            await get().loadFleetsManager.loadFleets(fIds);
            // set((state) => {
            //     state.vehicleLoaded = true;
            //     state.vehicleList = vehicleList;
            // });
            return [] as TreeData[];
        },
        refresh: async () => {
            await get().loadFleetsManager.loadFleets([], true);
            return [] as TreeData[];
        },
        setVIdParents(vIdParents: Record<string, Set<string>>) {
            set((state) => {
                return {
                    vIdParents
                };
            });
        },
        updateTreeNode(treeNodes: StarryTree.TreeNode[]) {
            set((state) => {
                // state.treeNodes = treeNodes.slice(0);
                return {
                    treeNodes
                };
            });
        },
        config(
            options: Pick<
                UseTreeDataStoreConfig,
                'convertTreeData2Node' | 'filterVehicleCallbacks'
            >,
        ) {
            get().loadFleetsManager.config(options);
        },
        initLoadFleetsManager: (loadFleetsManager: LoadFleetsManager) => {
            set((state) => {
                return {
                    loadFleetsManager
                };
            });
        },
        resetStore() {
            // 重置，防止内存释放不了
            get().loadFleetsManager.reset();
            set((state) => {
                return {
                    loaded: false,
                    vehicleLoaded: false,
                    loadedKeys: [],
                    treeNodes: [],
                    fleetList: [],
                    fleetListMap: {},
                    fleetChildrenMap: {},
                    fleetTotalNumberMap: {},
                    vehicleList: [],
                    vehicleListMap: {},
                    filteredVehicleList: [],
                    vIdParents: {},
                    treeRef: null,
                }
            })
        },
        setTreeRef(treeRef){
            set((state) => {
                return {
                    treeRef
                };
            });
        },
        scrollToNode(key: string){
            get().treeRef?.current?.scrollTo({
                key: key
            });
        },
        })
        )
    );
};

window.useTreeDataStore = createTreeDataStore;

// 创建StoreFactory实例
export const treeDataStoreFactory = new StoreFactory<ReturnType<typeof createTreeDataStore>>(createTreeDataStore);

/**
 * 获取指定instanceId的TreeData Store实例
 * @param instanceId 实例ID，默认为'default'
 * @returns TreeData Store实例
 */
export const getTreeDataStore = (instanceId: string = 'default') => {
    const treeDataStore = treeDataStoreFactory.getInstance(instanceId)
    treeDataStore.getState().loadFleetsManager.setStore(treeDataStore);
    return treeDataStore;
};

// 为了兼容现有代码，保留useTreeDataStore的导出
export const useTreeDataStore = getTreeDataStore('default');
window.useTreeDataStore = useTreeDataStore;
export default useTreeDataStore;
