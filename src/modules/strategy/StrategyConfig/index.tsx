import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Auth, getAppGlobalData, i18n, RouterPrompt } from '@base-app/runtime-lib';
import { Button, Container, Empty, Form, message, Space, Spin } from '@streamax/poppy';
import { IconRequest, IconSwitch02Fill } from '@streamax/poppy-icons';
import { StarryModal } from '@base-app/runtime-lib';
import AlarmList from './AlarmList';
import type { ListItem, GroupItem, AlarmSearchListRefProps } from './AlarmList';
import CopyLinkSetting from './CopyLinkSetting';
import classNames from 'classnames';
import { getAlarmTypeAuthorityPage } from '@/service/alarm';
import { useAsyncEffect, useDebounceEffect, useDebounceFn, useDeepCompareEffect } from '@streamax/hooks';
import { cloneDeep, isNil } from 'lodash';
import useAppList from '@/hooks/useAppList';
import { STRATEGY_TYPE } from '@/utils/constant';
import cn from "classnames";
import { withSharePropsHOC } from '@streamax/page-sharing-core';
import type { StrategyConfigBlock } from '@/types/pageReuse/startegy';
import type { FormEdit } from '@/types/pageReuse/pageReuseBase';
import { getCustomItems } from '@/utils/pageReuse';
import './index.less';
import { localeCompare } from '@/utils/alarmTypeSort';
import { RspDrawerTemplate } from '@streamax/responsive-layout';

const { Left, Right } = RspDrawerTemplate;
export type StrategyConfigShareProps = StrategyConfigBlock & FormEdit

interface Child {
    ref: any; // form ref
    renderForm: (status?: boolean, currentType?: string) => React.ReactNode; // form表单
    renderDetail?: (data?: any) => React.ReactNode; // 详情
    key?: string;
}
interface Result {
    currentType: string | undefined;
    currentTypeInGroup: boolean;
    groupData: (ListItem | GroupItem)[];
}
interface AuthCode {
    editCode: string;
    copyCode?: string;
}
interface StrategyConfigProps {
    openIsWhen?: (flag: boolean) => void;
    activeKey?: string;
    className?: string;
    title?: string | ((editing: boolean) => React.ReactNode);
    hasAlarmList?: boolean; // 是否带左边报警列表，默认无
    configData: Record<string, any>;
    inDetailPage: boolean; // 是否是详情页
    childs: Child[]; // 用于在内部渲染的form[]
    authCode: AuthCode; // 按钮的auth code
    getAlarmGroup?: (
        list: Record<'label' | 'value', string>[],
        data: Record<string, any>,
    ) => Result; // 获取同配置的报警类型的分组数据
    onSubmit: (values: Record<string, any>, isCopy?: boolean) => any; // 提交回调
    onAlarmTypeChange?: (alarmType: any) => void; // 处理报警类型改变
    checkEdit?: boolean; // 未选中报警类型时禁用编辑按钮
    mailForm?: any;
    modalType?: string;
    onCancel?: () => void;
    onEdit?: () => void;
    onSave?: () => void;
    type?: number;
    loading?: boolean;
    appId: number;
    configureType?: string; //策略类型
    customGetAlarmTypeAuthorityPage?: (requestData: Record<string, any>) => Promise<any>

    // 某些时机点击事件回调，上层可在事件回调中继续或终止逻辑，以及完成自定义逻辑
    /** 点击取消按钮时事件回调，返回true则会继续执行内部逻辑，返回false则终止内部逻辑 */
    onClickCancel?: () => boolean;
    /** 点击保存按钮时事件回调，返回true则会继续执行内部逻辑，返回false则终止内部逻辑 */
    onClickSave?: () => boolean;
    /** 点击报警类型时事件回调，返回true则会继续执行内部逻辑，返回false则终止内部逻辑 */
    onClickAlarmType?: () => boolean;
    onEditClick?: () => Promise<boolean>;
    onTypeChange?: (data) => void;
    defaultSetting?: any;
    modalPlaceholder?: {
        placeholder?: string;
        checkText?: string;
        label?: string;
        asyncActionText?: string;
        oldLabel?: string;
        showOld?: boolean;// 是否展示被替换的表单
    }
    listPlaceholder?: {
        placeholder?: string;
        title?: string;
    }
    initFieldsData?: boolean; //是否在StrategyConfig中初始化数据
}

const StrategyConfig = (props: StrategyConfigProps & StrategyConfigShareProps) => {
    /**定制详情渲染list**/
    const { getStrategyDetailBlock, changeSubmitData }  = props;
    /**end**/
    const {
        className,
        title,
        hasAlarmList = false,
        configData,
        childs,
        inDetailPage,
        authCode: { copyCode, editCode },
        getAlarmGroup,
        onSubmit,
        openIsWhen,
        activeKey,
        onAlarmTypeChange,
        checkEdit,
        mailForm,
        modalType,
        type,
        onCancel,
        onEdit,
        onSave,
        loading,
        appId,
        onClickCancel,
        onClickSave,
        onClickAlarmType,
        customGetAlarmTypeAuthorityPage,
        modalPlaceholder,
        listPlaceholder,
        defaultSetting,
        onTypeChange,
        onEditClick,
        initFieldsData = true
    } = props;
    const [alarmTypes, setAlarmTypes] = useState<ListItem[]>([]);
    const [editing, setEditing] = useState(false);
    const [currentType, setCurrentType] = useState('');
    const [alarmTypeGroup, setAlarmTypeGroup] = useState<(ListItem | GroupItem)[]>([]);
    const [typeSetting, setTypeSetting] = useState<Record<string, any>>({}); // 新增时，临时存储配置参数
    const [copySettingVisible, setCopySettingVisible] = useState(false);
    const [isResetSearch, setIsResetSearch] = useState(false); // 是否重置报警列表的筛选
    const [when, setWhen] = useState(false);
    const [newChildren, setNewChildren] = useState<any>([]);
    const isSaveRef = useRef<boolean>();
    const editingRef = useRef<boolean>(false);
    const configDataRef = useRef<any>({});
    const alarmListRef = useRef<AlarmSearchListRefProps>(null);
    const { inSaaS, appList } = useAppList({}, [0, 66666]);
    const [initLoading, setInitLoading] = useState<boolean>(false);
    const drawerRef = useRef<any>(null);
    useEffect(() => {
        setEditing(false);
    }, [activeKey]);

    useEffect(() => {
        setEditing(false);
        setWhen(false);
        openIsWhen && openIsWhen(false);
    }, [appId]);

    // 获取报警类型
    useAsyncEffect(async () => {
        setInitLoading(true);
        if ((!inSaaS || appList.length) && hasAlarmList && !isNil(appId)) {
            let list = [];
            try {
                if(customGetAlarmTypeAuthorityPage) {
                    list = await customGetAlarmTypeAuthorityPage({
                        page: 1,
                        pageSize: 1e8,
                        state: 1,
                        tenantId: getAppGlobalData('APP_USER_INFO')['tenantId'],
                        appId,
                    });
                } else {
                    const data = await getAlarmTypeAuthorityPage({
                        page: 1,
                        pageSize: 1e8,
                        state: 1,
                        tenantId: getAppGlobalData('APP_USER_INFO')['tenantId'],
                        appId,
                    });
                    
                    list = data.list?.map((i: any) => {
                        return {
                            value: i['alarmType'],
                            label: i18n.t(
                                `@i18n:@alarmType__${i['alarmType']}`,
                                i['typeName'],
                            ),
                        };
                    });
                }
                const types = list.sort((a, b) => localeCompare(a.label, b.label));
                setAlarmTypes(types);
                setInitLoading(false);
            } catch (error) {
                setInitLoading(false);
            }
        } else {
            setInitLoading(false);
        }
    }, [appList, appId]);
    // 定制start
    useDeepCompareEffect(() => {
        const customItems = getCustomItems(getStrategyDetailBlock, childs);
        setNewChildren(customItems);
    },[childs]);
    // 定制end
    // 切换报警类型初始化表单
    useDeepCompareEffect(() => {
        configDataRef.current = configData;
        initializeForm();
    }, [currentType, configData, newChildren.length]);

    // 当前报警类型变化时，抛出相应的事件回调
    useEffect(() => {
        onAlarmTypeChange && onAlarmTypeChange(currentType);
    }, [currentType]);

    const currentAlarmTypeInGroup = useMemo(() => {
        if ([undefined, null, ''].includes(currentType)) return false;
        return alarmTypeGroup.some((p) =>
            ((p as GroupItem).alarmTypes || []).includes(currentType),
        );
    }, [currentType, alarmTypeGroup, alarmTypes]);
    
    
    useDeepCompareEffect(() => {
        // 有报警列表，设置分组；反之，直接给表单赋值
        const cloneConfig = cloneDeep(configData);
        configDataRef.current = configData;
        if (hasAlarmList && getAlarmGroup && alarmTypes.length > 0) {
            const {
                currentType: type,
                groupData,
            } = getAlarmGroup(alarmTypes, cloneConfig);
            setAlarmTypeGroup(groupData);
            if (isSaveRef.current) {
                const notEdit =
                    groupData.find((item) => item.config === undefined) ||
                    groupData[0].alarmTypeList[0];
                if (notEdit) {
                    setCurrentType(notEdit.value);
                    setIsResetSearch(true);
                }
            } else {
                type == undefined ? setCurrentType(currentType) : setCurrentType(type);
            }
            
        } else {
            setAlarmTypeGroup([]);
            setCurrentType("");
            initializeForm();
        }
    }, [alarmTypes, configData, newChildren.length]);
    // 初始化表单
    const initializeForm = () => {
        const cloneConfig = cloneDeep(configDataRef.current);
        newChildren.forEach((item) => {
            // setFieldsValue是增量的，先重置整个form
            item.ref?.resetFields?.();
            if (hasAlarmList) {
                if (!cloneConfig[currentType]) {
                    cloneConfig[currentType] = defaultSetting || {
                        mailSend: false,
                        messagePush: false,
                        messageSend: false,
                        timeZone: null,
                    };
                }
                onTypeChange?.(cloneConfig[currentType]);
                initFieldsData && cloneConfig[currentType] && item.ref?.setFieldsValue(cloneConfig[currentType]);
            } else {
                // hasAlarmList = false，configData就是整个form的数据
                item.ref?.setFieldsValue(cloneConfig);
            }
        });
    };

    // 更新分组，报警列表筛选时需要调用此方法
    const updateGroup = (alarmList: Record<'label' | 'value', string>[], setNoEdit = true) => {
        if (getAlarmGroup) {
            const {
                currentType: type,
                groupData,
            } = getAlarmGroup(alarmList, cloneDeep(configDataRef.current));
            // 更新时也需要进行排序
            groupData.forEach((item: any) => {
                if (item?.alarmTypeList) {
                    item.alarmTypeList.sort((aItem,bItem)=>{
                        return localeCompare(aItem.label, bItem.label);
                    });
                }
            });
            groupData.sort((a: any, b: any)=>{
                if(a?.alarmTypeList && b?.alarmTypeList){
                    return localeCompare(a.alarmTypeList[0].label, b.alarmTypeList[0].label);
                }else if(a?.label && b?.label){
                    return localeCompare(a.label, b.label);
                }
                return 0;
            });
            if (type != undefined) {
                if (
                    modalType === 'autoHandle' ||
                    modalType === 'evidence' ||
                    modalType === 'alarmLinkage' ||
                    modalType === 'sound' ||
                    modalType === 'channelSetting'
                ) {
                    let currentItem: any = null;
                    // 编辑后需要选中第一个未被编辑的，其余情况搜索始终定位到第一个
                    if (setNoEdit) {
                        currentItem =
                            groupData.find((item) => item.config === undefined) ||
                            groupData[0].alarmTypeList[0];
                    } else {
                        // groupData[0]?.value 可能为0
                        currentItem =
                            groupData[0]?.value || groupData[0]?.value === 0
                                ? groupData[0]
                                : groupData[0].alarmTypeList[0];
                    }
                    setCurrentType(currentItem.value);
                } else {
                    setCurrentType(type);
                }
            } else {
                setCurrentType('');
            }
            setAlarmTypeGroup(groupData);
        }
    };
    //
    const onFocus = () => {
        if(editing && editingRef.current){
            alarmListRef.current?.inputBlur();
            saveModal(() => {
                editingRef.current = false;
                handleCancel();
            });
        }
    };
    const saveModal = (onkFun: () => void) => {
        StarryModal.confirm({
            centered: true,
            title: i18n.t('name', '提示'),
            content: i18n.t('message', '您确认不保存当前设置？'),
            okText: i18n.t('action', '确定'),
            cancelText: i18n.t('action', '取消'),
            icon: <IconRequest />,
            onOk: () => {
                onkFun?.();
            },
        });
    };

    const handleCancel = () => {
        if (onClickCancel && !onClickCancel()) return;

        setEditing(false);
        setWhen(false);
        openIsWhen && openIsWhen(false);
        initializeForm();
        onCancel?.();
    };

    const detailPageEdit = async () => {
        if(onEditClick) {
            const data = await onEditClick();
            if(!data) return;
        }
        setEditing(true);
        editingRef.current = true;
        openIsWhen && openIsWhen(true);
        setWhen(true);
        onEdit?.();
        setIsResetSearch(false);
    };

    const switchAlarmType = (selectedItem: any) => {
        drawerRef.current.closeDrawer();
        if(onClickAlarmType && !onClickAlarmType()) return;

        if (selectedItem.value === currentType) {
            return;
        }
        if (inDetailPage) {
            if (editing) {
                saveModal(() => {
                    handleCancel();
                    setCurrentType(selectedItem.value);
                });
            } else {
                setCurrentType(selectedItem.value);
            }
        } else {
            let result = {};
            newChildren.forEach(async (item) => {
                const values = await item.ref?.validateFields();
                result = { ...result, ...values };
            });
            // 将当前配置保存到对应的报警类型数据中，更新typeSetting
            setTypeSetting((prev) => ({
                ...prev,
                ...{
                    [currentType]: { ...result },
                },
            }));
            setCurrentType(selectedItem.value);
        }
    };
    const _handleSave = async (modifySameGroup?: boolean) => {
        if (onClickSave && !onClickSave()) return;
        isSaveRef.current = true;
        let formData = {};
        const cloneConfig = cloneDeep(configData);
        for (let i = 0; i < newChildren.length; i++) {
            const item = newChildren[i];
            try {
                item?.ref?.submit?.(); // 触发scrollToFirstError
                const values = await item?.ref?.validateFields?.();
                formData = { ...formData, ...values };
            } catch (e) {
                // console.log(e);
                return;
            }
        }

        /*** 定制修改数据 changeSubmitData****/
        formData = getCustomItems(changeSubmitData, formData);
        /*** 定制修改数据 changeSubmitData****/

        // 同步修改同配置报警
        const groupSetting = {};
        if (modifySameGroup) {
            const { alarmTypes } = findAlarmTypeGroup(currentType) as GroupItem;
            if (alarmTypes) {
                alarmTypes.forEach((type) => {
                    groupSetting[type] = { ...formData };
                });
            }
        }
        // 带报警列表的设置，以报警类型维度存配置(表单数据)；反之，直接存整个配置(表单数据）
        const data = hasAlarmList
            ? {
                  ...groupSetting, // 同一配置的报警组
                  [currentType]: formData,
              }
            : formData;

        const res = await onSubmit({
            ...cloneConfig,
            ...typeSetting, // 新增才有typeSetting
            ...data,
        });
        if (!res) {
            setEditing(false);
            setWhen(false);
            openIsWhen && openIsWhen(false);
            // 提交时若有筛选报警，应该还原搜索
            setIsResetSearch(true);
            updateGroup(alarmTypes, true);
            onSave?.();
        }
    };
    const { run: handleSave} = useDebounceFn(_handleSave, {
        wait: 300
    });
   
    const handleClickCopy = async () => {
        if (inDetailPage) {
            setCopySettingVisible(true);
            setIsResetSearch(false);
        } else {
            try {
                for (let i = 0; i < newChildren.length; i++) {
                    const item = newChildren[i];
                    try {
                        await item?.ref?.validateFields?.();
                    } catch (e) {
                        // console.log(e);
                    }
                }
                setCopySettingVisible(true);
                setIsResetSearch(false);
            } catch (err) {
                message.error(i18n.t('message', '表单校验未通过'));
            }
        }
    };

    // 寻找报警类型所在的分组
    const findAlarmTypeGroup = (key: string): GroupItem | ListItem | undefined => {
        return alarmTypeGroup.find(({ alarmTypes = [] }: GroupItem | any) =>
            alarmTypes.includes(key),
        );
    };

    const getCopyDisabledKeys = () => {
        const groupData: Record<string, any> = findAlarmTypeGroup(currentType) || {};
        const keys = groupData.alarmTypes || [];
        return keys.concat([currentType]);
    };

    // 复制配置
    const handleCopyConfig = async (types: number[]) => {
        const setting = cloneDeep(configData);
        if (inDetailPage) {
            try {
                // 找到分组的配置currentType
                const { config } = findAlarmTypeGroup(currentType) as GroupItem;
                types.forEach((type) => {
                    setting[type] = JSON.parse(config);
                });
                await onSubmit(setting, true);
            } catch (error) {
                console.error(error);
            }
        } else {
            let formData = {};
            for (let i = 0; i < newChildren.length; i++) {
                const item = newChildren[i];
                try {
                    const values = await item?.ref?.validateFields?.();
                    formData = { ...formData, ...values };
                } catch (e) {
                    // console.log(e);
                }
            }

            // 带报警列表的设置
            if (hasAlarmList) {
                types.forEach((type) => {
                    setting[type] = formData;
                });
                setting[currentType] = formData;
            }

            setTypeSetting(setting);
        }
        setCopySettingVisible(false);
        setIsResetSearch(true);
    };

    // 渲染title
    const renderTitle = () => {
        if (typeof title === 'string') {
            return title;
        } else {
            // 因为图片抓拍设置在标题额外加了个switch开关，而且需要通过编辑状态控制disabled
            return title?.(editing);
        }
    };
    // 渲染右边的form表单
    const renderRightForm = () => {
        if (hasAlarmList && !currentType && currentType !== 0) {
            return (
                <div className="center-icon">
                    <Empty
                        imageStyle={{
                            height: 80,
                        }}
                    />
                </div>
            );
        }
        // 编辑状态展示表单
        if (editing) {
            return newChildren.map((item) => item?.renderForm(editing, currentType));
        } else {
            // 非编辑状态展示详情，提供了renderDetail就以renderDetail渲染，默认以表单(不可操作的)形式展示详情
            return newChildren.map((item) => {
                if (item?.renderDetail) {
                    return item.renderDetail(configData);
                }
                return item?.renderForm(editing, currentType);
            });
        }
    };

    const getCopyEle = function () {
        if (!currentAlarmTypeInGroup || currentType === undefined || currentType === null) return '';
        // 有复制权限控制，则走权限控制，没有则直接展示复制按钮
        if (copyCode) {
            return (
                <Auth code={copyCode as string}>
                    <a onClick={handleClickCopy}>{i18n.t('action', '复制')}</a>
                </Auth>
            );
        } else {
            return <a onClick={handleClickCopy}>{i18n.t('action', '复制')}</a>;
        }
    };

    const renderEmpty = () => {
        return (
            <div className="mail-empty-container">
                <Empty
                    image={require('@/assets/icons/icon_state_flow.svg')}
                    imageStyle={{
                        height: 80,
                    }}
                    description={<span>{i18n.t('message', '发件邮箱未配置')}</span>}
                >
                    <Button onClick={detailPageEdit} type="primary">
                        {i18n.t('action', '配置邮箱')}
                    </Button>
                </Empty>
            </div>
        );
    };
    const renderContent = () => {
        return (
            <>
                <div
                    className="setting-header"
                    style={{
                        marginBottom: hasAlarmList? 0 : 24,
                    }}
                >
                    <div
                        className={cn('setting-title', {
                            'before-mark': !hasAlarmList,
                        })}
                    >
                        {renderTitle()}
                    </div>
                    {editing ? (
                        <Space size={24}>
                            <a onClick={handleCancel}>{i18n.t('action', '取消')}</a>
                            <a onClick={() => handleSave()}>{i18n.t('action', '保存')}</a>
                            {currentAlarmTypeInGroup && (
                                <a onClick={() => handleSave(true)}>
                                    {modalPlaceholder?.asyncActionText ||i18n.t('action', '同步修改同配置报警')}
                                </a>
                            )}
                        </Space>
                    ) : (
                        <Space size={24}>
                            {currentType === undefined ||
                            currentType === null ||
                            (currentType === '' && hasAlarmList) ? null : (
                                <>
                                    {getCopyEle()}
                                    <Auth code={editCode}>
                                        <a
                                            onClick={detailPageEdit}
                                            className={
                                                checkEdit &&
                                                !String(currentType)
                                                    ? 'disable-a'
                                                    : ''
                                            }
                                        >
                                            {i18n.t('action', '编辑')}
                                        </a>
                                    </Auth>
                                </>
                            )}
                        </Space>
                    )}
                </div>
                {hasAlarmList ? (<RspDrawerTemplate leftWidth={300} breakpoint="lg" gutter={0}>
                    <Left
                        drawerTrigger={
                            <div className="alarm-switch-container" >
                                {
                                    alarmTypes.find(
                                        (item) => item.value === currentType,
                                    )?.label
                                }
                                <IconSwitch02Fill
                                    className='switch-icon'
                                />
                            </div>
                        }
                        drawerProps={{className:'alarm-setting-drawer',width:400}}
                        ref={drawerRef}
                    >
                        <div className="left">
                            {hasAlarmList && (
                                <AlarmList
                                    value={currentType}
                                    onFocus={onFocus}
                                    ref={alarmListRef}
                                    list={alarmTypes}
                                    groupList={alarmTypeGroup}
                                    onChange={switchAlarmType}
                                    updateGroup={updateGroup}
                                    isResetSearch={isResetSearch}
                                    placeholder={listPlaceholder?.placeholder}
                                    title={listPlaceholder?.title}
                                />
                            )}
                        </div>
                    </Left>
                    <Right>
                        <div className="right">{renderRightForm()}</div>
                    </Right>
                </RspDrawerTemplate>):(<div className="right">{renderRightForm()}</div>)}
            </>
        );
    };
    const renderDom = () => {
        if (loading) {
            return (
                <span className="loading-status">
                    <Spin size="large" />
                </span>
            );
        } else if (
            (!configData || Object.keys(configData).length <= 1) &&
            type === STRATEGY_TYPE.MAIL_SEND &&
            !editing
        ) {
            return renderEmpty();
        } else {
            return renderContent();
        }
    };

    const baseLayout = <div
        className={classNames('strategy-config-layout-container', {
            // 没有报警列表的设置，header样式不一样
            'no-alarm-list': !hasAlarmList,
        })}
    >
        {renderDom()}
    </div>;

    return (
        <div className={className}>
            <RouterPrompt
                when={when}
                message={i18n.t('message', '离开当前页？系统可能不会保存您所做的更改。')}
            />
            <Spin spinning={initLoading}>
                {hasAlarmList ? <Container>{ baseLayout}</Container>:baseLayout }
                {hasAlarmList && (
                    <CopyLinkSetting
                        alarmTypeList={alarmTypes}
                        visible={copySettingVisible}
                        disabledKeys={getCopyDisabledKeys()}
                        currentType={currentType}
                        onOk={handleCopyConfig}
                        onCancel={() => setCopySettingVisible(false)}
                        placeholder={modalPlaceholder?.placeholder}
                        checkText={modalPlaceholder?.checkText}
                        label={modalPlaceholder?.label}
                        oldLabel={modalPlaceholder?.oldLabel}
                        showOld={modalPlaceholder?.showOld}
                    />
                )}
            </Spin>
        </div>
    );
};
export default withSharePropsHOC<StrategyConfigProps, StrategyConfigShareProps>(StrategyConfig);
