/*
 * @LastEditTime: 2025-07-03 10:08:02
 */
/*
 * @LastEditTime: 2025-02-26 19:04:00
 */
import { Divider, Form, message, Switch } from '@streamax/poppy';
import {
    Auth,
    getAppGlobalData,
    i18n,
    mosaicManager,
    useSystemComponentStyle,
} from '@base-app/runtime-lib';
import { cloneDeep, isEqual, isNil, pickBy } from 'lodash';
import StrategyConfig from '../StrategyConfig';
import { paramListTransfer, validateChannelMode } from './util';
import { formatAlarmTypeGroup } from '../utils';
import { editChannelStrategy } from '@/service/strategy';
import { getDeviceModelList } from '@/service/device';
import type { StrategyComponentProps } from '../types';
import DynamicChannelTableForm, { EffectiveChannelModeEnum, PRIVATE_SETTING_CODE } from '@/components/DynamicChannelTableForm';
import { useEffect, useRef, useState } from 'react';
import { fetchParameterDetail } from '@/runtime-lib/service';
// 表单默认值
export const defaultSetting = {
    channelSettingList: [
        {
            channelName: 'CH1',
            channelNumber: 1,
            enable: 1,
        },
        {
            channelName: 'CH2',
            channelNumber: 2,
            enable: 1,
        },
        {
            channelName: 'CH3',
            channelNumber: 3,
            enable: 1,
        },
        {
            channelName: 'CH4',
            channelNumber: 4,
            enable: 1,
        },
        {
            channelName: 'CH5',
            channelNumber: 5,
            enable: 1,
        },
        {
            channelName: 'CH6',
            channelNumber: 6,
            enable: 1,
        },
        {
            channelName: 'CH7',
            channelNumber: 7,
            enable: 1,
        },
        {
            channelName: 'CH8',
            channelNumber: 8,
            enable: 1,
        },
    ],
    vehicleChannelSettingList: [
        {
            channelNumber: 1,
            channelName: 'CH1',
        },
        {
            channelNumber: 2,
            channelName: 'CH2',
        },
        {
            channelNumber: 3,
            channelName: 'CH3',
        },
        {
            channelNumber: 4,
            channelName: 'CH4',
        },
        {
            channelNumber: 5,
            channelName: 'CH5',
        },
        {
            channelNumber: 6,
            channelName: 'CH6',
        },
        {
            channelNumber: 7,
            channelName: 'CH7',
        },
        {
            channelNumber: 8,
            channelName: 'CH8',
        },
    ],
};
export default (props: StrategyComponentProps) => {
    const {
        type,
        title,
        data = {},
        updateData,
        authCode,
        openIsWhen,
        activeKey,
        appId,
        strategyType
    } = props;
    const inDetailPage = true;
    const { channelTypeCode = '' } = authCode || {};
    const [channelEnable, setChannelEnable] = useState<boolean>(false);
    const cloneDefaultSetting = cloneDeep(defaultSetting);
    const { platformOrMix } = mosaicManager.useTenantMosaicTypeParams();
    const isShowChanelTypeColumns = Auth.check(channelTypeCode);
    const showMosaicSetting = Auth.check(PRIVATE_SETTING_CODE) && platformOrMix;
    const [channelForm] = Form.useForm();
    const formRef = useRef(null);
    const onTypeChange = async (values) => {
        formRef.current?.setFieldsValue(
            values?.channelSettingList || cloneDefaultSetting.channelSettingList,
        );
    };
    const getAlarmGroup = (
        list: Record<'label' | 'value', string>[] = [],
        settings: Record<string, any>,
    ) => {
        // 已设置了的报警类型数据
        const settingMap: any = pickBy(settings, (item) => {
            const parseItem = (item?.channelSettingList || [])?.map((item) => {
                if(isNil(item.channelTypeId)) {
                    delete item.channelTypeId;
                }
                if(isNil(item.algorithmModel)) {
                    delete item.algorithmModel;
                }
                return item;
            });
            if (
                Object.keys(item || {}).length &&
                !(isEqual(
                        cloneDefaultSetting.channelSettingList.sort(),
                        parseItem.sort(),
                    ) || isEqual(
                        cloneDefaultSetting.vehicleChannelSettingList.sort(),
                        parseItem.sort(),
                    ))
            ) {
                return true;
            }
            return false;
        });
        return formatAlarmTypeGroup(list, settingMap);
    };
    const onSubmit = async (values: Record<string, any>) => {
        const { configureId = '', configureName, configureType } = data;
        const paramList = paramListTransfer.toSubmit(values, data.paramList, {showMosaicSetting, channelEnable, isShowChanelTypeColumns});
        await editChannelStrategy({
            configureId,
            configureName,
            configureType,
            paramList,
        });
        message.success(i18n.t('message', '操作成功'));
        const result = await onEditClick();
        if(!result) return;
        await updateData?.(type);
    };
    const queryDeviceModelList = async () => {
        let list = await getDeviceModelList({
            filterPermission: true,
            appId,
        });
        return list;
    };
    const onEditClick = async () => {
        const { configureType } = data;
        return await validateChannelMode({
            appId: appId,
            configureType: configureType
        });
    };
    /**查询优先级配置，平台优先级或设备优先级，平台优先级时需要展示通道使能字段***/ 
    const fetchTenantMosaicTypeParams = async () => {
        const parameter = await fetchParameterDetail(
            { parameterKey: 'DEVICE.EFFECTIVE.CHANNEL.MODE' },
            false,
        );
        setChannelEnable(EffectiveChannelModeEnum.PLAT === Number(parameter?.parameterValue));
    };
    useEffect(() => {
        fetchTenantMosaicTypeParams();
    }, []);
    return (
        <StrategyConfig
            activeKey={activeKey}
            title={title}
            hasAlarmList
            configData={paramListTransfer.toRender(data.paramList || [], {showMosaicSetting, channelEnable, isShowChanelTypeColumns})}
            customGetAlarmTypeAuthorityPage={queryDeviceModelList}
            inDetailPage={inDetailPage}
            authCode={authCode}
            configureType={data.configureType}
            onTypeChange={onTypeChange}
            initFieldsData={false}
            onEditClick={onEditClick}
            childs={
                appId
                    ? [
                          {
                              renderForm: (editing?: boolean) => (
                                  <DynamicChannelTableForm
                                      channelTypeSelectProps={{
                                          getPopupContainer: () =>
                                              document.getElementsByClassName(
                                                  'right',
                                              )[0],
                                      }}
                                      mosaicTypeSelectProps={{
                                          getPopupContainer: () =>
                                              document.getElementsByClassName(
                                                  'right',
                                              )[0],
                                      }}
                                      appId={appId}
                                      openIsWhen={openIsWhen}
                                      authCodes={authCode}
                                      editing={editing}
                                      operateType={strategyType}
                                      ref={formRef}
                                      configureType={data.configureType}
                                      channelForm={channelForm}
                                  />
                              ),
                              ref: channelForm,
                              key: 'channelTableForm',
                          },
                      ]
                    : []
            }
            getAlarmGroup={getAlarmGroup}
            onSubmit={onSubmit}
            openIsWhen={openIsWhen}
            checkEdit={true}
            modalType="channelSetting"
            appId={appId}
            className="autoHandle-scope"
            defaultSetting={cloneDefaultSetting}
            modalPlaceholder={{
                placeholder: i18n.t('message', '请选择设备型号'),
                checkText: i18n.t('name', '已选型号'),
                label: i18n.t('name', '复制设备机型'),
                oldLabel: i18n.t('name', '原设备机型'),
                asyncActionText: i18n.t('name', '同步修改同配置型号'),
                showOld: true,
            }}
            listPlaceholder={{
                placeholder: i18n.t('message', '请输入设备型号或设备机型名称'),
                title: i18n.t('name', '设备型号'),
            }}
        />
    );
};
