/*
 * @LastEditTime: 2025-07-01 21:38:16
 */
import { Action, getAppGlobalData, i18n, StarryModal } from '@base-app/runtime-lib';
import type { ParamItem } from '../types';
import { getCurrentChannelMode } from '@/service/channel';
import { isNil } from 'lodash';
export const channelMode: Record<string, any> = {
    device: 20,
    vehicle: 18,
};
// 转换paramList参数
export const paramListTransfer = {
    toSubmit: (setting: any, oldData: any[], otherConfig?: Record<string, any>) => {
        // 组装为数组结构
        const paramList: ParamItem[] = [];
        const { showMosaicSetting, isShowChanelTypeColumns } = otherConfig || {};
        /**组装老数据，需要还原老数据的起停用**/ 
        const oldMapData = {};
        (oldData || [])?.forEach(item => {
            oldMapData[item.paramName] = JSON.parse(item.paramValue || '[]');
        });
        Object.keys(setting).forEach((alarmType) => {
            const itemVal: any = setting[alarmType];
            const oldDataItem = oldMapData?.[alarmType] || [];
            Object.keys(itemVal).forEach((key) => {
                const paramValue = (itemVal[key] || []).filter((item) => item);
                const formatParamValue = paramValue?.map((item, index) => {
                    const oldItem = oldDataItem?.[index];
                    const enable = item.enable ?? oldItem?.enable ?? 1;
                    let channelTypeId = item.channelTypeId;
                    let algorithmModel = item.algorithmModel;
                    // 没有展示马赛克类型时去要取老值
                    if(!isNil(showMosaicSetting) && !showMosaicSetting) {
                        algorithmModel = oldItem?.algorithmModel;
                    }
                    // 没有展示马赛克类型时去要取老值
                    if(!isNil(showMosaicSetting) && !isShowChanelTypeColumns) {
                        channelTypeId = oldItem?.channelTypeId;
                    }
                    return {
                        ...item,
                        channelTypeId,
                        algorithmModel,
                        enable,
                    };
                });
                // 转为字符串
                if (alarmType) {
                    paramList.push({
                        paramName: alarmType, //设备机型名称
                        paramValue: JSON.stringify(formatParamValue),
                    });
                }
            });
        });
        return paramList;
    },
    toRender: (paramList: ParamItem[], otherConfig?: Record<string, any>) => {
        const setting: any = {};
        const { showMosaicSetting, isShowChanelTypeColumns, channelEnable } = otherConfig || {};
        paramList.forEach((item: ParamItem) => {
            const { paramName } = item;
            let paramValue = [];
            try {
                paramValue = JSON.parse(item.paramValue);
            } catch (error) {
                console.error(error);
            }
            const value = (paramValue || []).map((item: any) => {
                item.enable = item.enable ?? 1;
                if(!showMosaicSetting) {
                    delete item.algorithmModel;
                }
                if(!channelEnable) {
                    delete item.enable;
                }
                if(!isShowChanelTypeColumns) {
                    delete item.channelTypeId;
                }
                return item;
            });
            if (!setting.hasOwnProperty(paramName)) {
                setting[paramName as string] = {
                    channelSettingList: value,
                };
            }
        });
        return setting;
    },
};

export const validateChannelMode = async (data: {
    configureType: any;
    appId: any;
    code?: string;
}) => {
    const { configureType, appId, code } = data;
    let resData = {};
    try {
        resData = await getCurrentChannelMode({ appId: appId ?? getAppGlobalData('APP_ID') });
    } catch (error) {
        return true;
    }
    if (channelMode[resData.channelSettingType] == configureType) {
        return true;
    }
    StarryModal.warning({
        title: i18n.t('message', '模式已切换'),
        content: i18n.t(
            'message',
            '模式切换提示。当前模式已变更，即将刷新页面以同步数据',
        ),
        onOk: () => {
            Action.openActionUrl({
                code: code || '@base:@page:channel.setting',
                // @ts-ignore
                history,
                url: '/CH-set',
                params: {
                    choosedAppId: appId || '',
                },
                replace: true,
            });
        },
    });
    return false;
};
