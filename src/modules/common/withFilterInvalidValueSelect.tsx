/*
 * @LastEditTime: 2025-07-03 11:40:49
 */
import { Select } from '@streamax/poppy';
import { RefSelectProps, SelectProps, SelectValue } from '@streamax/poppy/lib/select';
import { isNil, pick } from 'lodash';
import React, { useEffect, useState } from 'react';

interface FilterInvalidValueSelectProps extends SelectProps<SelectValue> {
    [key: string]: any;
    filter: boolean;
}
type FilterInvalidValueSelectType = typeof Select & { filter: boolean };
/**
 * Creates a higher-order component that filters out invalid values from a Select component.
 *
 * @param {typeof Select} SelectComponent - The Select component to be wrapped. Defaults to the default Select component.
 * @return {FilterInvalidValueSelectType} - The wrapped Select component with the filtering functionality.
 */
const withFilterInvalidValueSelect = (SelectComponent: typeof Select = Select) => {
    const FilterInvalidValueSelect = React.forwardRef<
        RefSelectProps,
        FilterInvalidValueSelectProps
    >((props, ref) => {
        const { value, onChange, options, fieldNames, filter = true, ...reset } = props;
        const [innerValue, setInnerValue] = useState<SelectValue>(undefined);
        const valueKey = fieldNames?.value || 'value';
        useEffect(() => {
            if (!filter) {
                value !== null && setInnerValue(value);
                return;
            }
            // 过滤掉下拉选项不存在的，一般存在于当前值是动态设置，而且下拉选项中已经删除的情况
            if (options && options?.length > 0 && !isNil(value)) {
                if (Array.isArray(value)) {
                    const newValue = value!.filter((item: any) =>
                        options.some((option) => option[valueKey] === item),
                    );
                    setInnerValue(newValue);
                } else {
                    const newValue = options.some((option) => option[valueKey] === value)
                        ? value
                        : undefined;
                    setInnerValue(newValue);
                }
            } else if(isNil(value)){
                setInnerValue(value);
            }
        }, [filter, value, JSON.stringify(options)]);
        return (
            <SelectComponent
                {...reset}
                value={innerValue}
                onChange={onChange}
                options={options}
                fieldNames={fieldNames}
                ref={ref}
            />
        );
    });
    Object.assign(FilterInvalidValueSelect, pick(SelectComponent, ['SECRET_COMBOBOX_MODE_DO_NOT_USE', 'Option' ,'OptGroup']));
    return FilterInvalidValueSelect as FilterInvalidValueSelectType;
};
export default withFilterInvalidValueSelect;
