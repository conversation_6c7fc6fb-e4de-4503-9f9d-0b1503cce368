import useEvidencePreviewData from './useEvidencePreviewData';
import {EvidenceData, EvidenceStateEnum, FileCleanStateEnum} from '../types';
import { useRef, useState } from 'react';
import classNames from 'classnames';
import { EvidenceListCardShareProps } from '@/runtime-pages/evidence-list/components/Card';
import DisableRightMouseClickImage from '@/components/DisableRightMouseClickImage';
import { aspectRatioImage } from '@base-app/runtime-lib';
import ImageView from '@/runtime-pages/evidence-list/components/ImageView';
import Icon from '@streamax/poppy-icons/lib/Icon';
import { Auth, H5VideoWithStatus, i18n, MosaicTypeEnum } from '@base-app/runtime-lib';
import { ReactComponent as Downloading } from '@/assets/icons/black_state_downloading.svg';
// @ts-ignore
import { ReactComponent as FileDamage } from '@/assets/icons/black_state_fileDamage.svg';
// @ts-ignore
import { ReactComponent as Nodata } from '@/assets/icons/black_state_nodata.svg';

// @ts-ignore
import { ReactComponent as FileClean } from '@/assets/icons/icon_cleaned_dark.svg';
// @ts-ignore
import { ReactComponent as Waiting } from '@/assets/icons/black_state_waiting.svg';
import { Empty, Tooltip } from '@streamax/poppy';
import { getEvidenceStatesDisplayOptions } from '../utils';

import './EvidencePreView.scoped.less';
import { renderEvidenceCardType } from '@/runtime-lib/modules/Evidence/utils';

const EvidencePreview: React.FC<
    { evidence: Partial<EvidenceData> } & EvidenceListCardShareProps & {
            videoDrawFrameShow: '1' | '0';
            forcePlayH264?: boolean;
            fileClean?: boolean;
            evidenceVideoCanPlay?: boolean
        }
> = (props) => {
    const { forcePlayH264, osdRenderFn, videoDrawFrameShow, evidence = {} ,fileClean, evidenceVideoCanPlay = true} = props;
    const { readPlayVideo, images, existH264 } = useEvidencePreviewData(evidence, {
        videoDrawFrameShow,
    });
    const { AspectRatioImage } = aspectRatioImage.AspectRatioImageV1;

    const [isPlay, setIsPlay] = useState(false);
    const [showImages, setShowImages] = useState(false);
    const historyPlayerRef: any = useRef();
    const renderEvidenceCardTypeObject = renderEvidenceCardType(readPlayVideo, images, evidence.fileChannelAuth, evidence.displayType);
    const noDataFunction = () => {
        switch (evidence?.state) {
            case EvidenceStateEnum.LOADING:
                return (
                    <Empty
                        description={<span>{i18n.t('message', '加载中...')}</span>}
                        image={<Waiting />}
                    />
                );
            case EvidenceStateEnum.DOING:
                return (
                    <Empty
                        description={<span>{i18n.t('message', '进行中...')}</span>}
                        image={<Downloading />}
                    />
                );
            case EvidenceStateEnum.FAIL:
                return (
                    <Empty
                        description={<span>{i18n.t('message', '下载失败')}</span>}
                        image={<FileDamage />}
                    />
                );
            default:
                return (
                    <Empty
                        description={<span>{i18n.t('message', '暂无数据')}</span>}
                        image={<Nodata />}
                    />
                );
        }
    };
    const videoViewCom = (readPlayVideo: any) => {
        return (
            <>
                <H5VideoWithStatus
                    ref={historyPlayerRef}
                    evidenceData={evidence}
                    playChannel={readPlayVideo?.channelNo}
                    mosaicSourcode={MosaicTypeEnum.alarm}
                    fileType={readPlayVideo?.fileType}
                    videoInfo={readPlayVideo}
                    existH264={existH264}
                    hasProgress
                    isPreView
                    hasFileSize
                    osdRenderFn={osdRenderFn}
                    forcePlayH264={forcePlayH264}
                    evidenceVideoCanPlay={evidenceVideoCanPlay}
                />
            </>
        );
    };
    // 注意 getEvidenceStatesDisplayOptions 函数参数。因为这里不展示证据下载成功的label，所以参数未填写真实的权限code和跳转逻辑。如果使用到了label，就需要在这里填写真实的权限code和跳转逻辑
    const viewEvidenceAuthCode = '';
    const onViewEvidence = () => {};
    const evidenceStatesDisplayOptions = getEvidenceStatesDisplayOptions(
        viewEvidenceAuthCode,
        onViewEvidence,
    );
    const evidenceStateDisplayOption = evidenceStatesDisplayOptions.find(
        (i) => i.value === evidence?.state,
    );
    return (
        <div className={classNames('evidence-preview', { play: isPlay })}>
            {/* 证据下载完成才展示，否则展示证据状态 */}
            {
                fileClean ? (
                    <div className="center-icon no-data-wrapper">
                        <span className="no-data-icon">
                              <Empty
                                  description={<span>{i18n.t('message', '证据被清理')}</span>}
                                  image={<FileClean />}
                              />
                        </span>
                    </div>
                ): (
                    evidence?.state && evidence?.state != EvidenceStateEnum.DONE ? (
                        <div className="center-icon">
                            <Icon component={evidenceStateDisplayOption?.background} />
                            <div className="center-icon-label">{evidenceStateDisplayOption?.cardLabel}</div>
                        </div>
                    ) : (
                        <>
                            {renderEvidenceCardTypeObject.video && videoViewCom(readPlayVideo)}
                            {renderEvidenceCardTypeObject.image ? (
                                <DisableRightMouseClickImage>
                                    <AspectRatioImage
                                        style={{
                                            cursor: 'pointer'
                                        }}
                                        preview={false}
                                        src={images[0].url}
                                        onClick={() => {
                                            setShowImages(true);
                                        }}
                                    />
                                </DisableRightMouseClickImage>
                            ) : null}
                            {renderEvidenceCardTypeObject.noData && (
                                <div className="center-icon no-data-wrapper">
                                    <span className="no-data-icon">{noDataFunction()}</span>
                                </div>
                            )}
                        </>
                    )
                )
            }
            <ImageView vehicleData={evidence}  visible={showImages} list={images} onClose={() => setShowImages(false)} />
        </div>
    );
};
export default EvidencePreview;
