/*
 * @LastEditTime: 2025-07-03 11:34:16
 */
import { Select } from "@streamax/poppy";
import { SelectProps } from "@streamax/poppy/lib/select";
import { i18n } from "@base-app/runtime-lib";
import { Key, useEffect, useState } from "react";
import withFilterInvalidValueSelect from "@/modules/common/withFilterInvalidValueSelect";
const FilterSelect = withFilterInvalidValueSelect();
export default ({ value, onChange, channelTypeOptions, restProps }: any) => {
    const handleSelectChange: SelectProps<Key[]>['onChange'] = (value) => {
        onChange?.(value);
    };
    return (
        <FilterSelect
            value={value}
            showSearch
            allowClear
            options={channelTypeOptions}
            onChange={handleSelectChange}
            style={{ maxWidth: 340 }}
            optionFilterProp="label"
            placeholder={i18n.t('message', '请选择通道类型')}
            {...(restProps || {})}
        />
    );
};