//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';

.dynamic-channel-table-form-container {
    padding-bottom: 24px;
    overflow-y: auto;
    .dynamic-channel-table-form-tip {
        margin-bottom: 24px;
    }
    .dynamic-channel-table-form-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;
        .channel-number {
            font-size: 500;
            font-size: 18px;
        }
    }
    .add-channel-button {
        width: 100%;
        margin-top: 16px;
    }
    .default-icon-information {
        color: @starry-text-color-secondary;
    }
    .default-icon-information:hover {
        color: @primary-color;
    }
    ::global {
        .poppy-table-cell:has(.form-item-vertical-align-top) {
            vertical-align: top !important;
        }
        .form-item-vertical-not-margin {
            margin-bottom: 0;
        }
    }
    .channel-state-warp {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
        .state-icon {
            display: inline-table;
            width: 6px;
			height: 6px;
            margin-right: 8px;
            vertical-align: middle;
            background: #bfbfbf;
            border-radius: 6px;
            &.active {
                background: #52c41a;
            }
        }
        //启用禁用文字换行展示
        .state-text {
            position: relative;
			display: inline-block;
			margin: 4px 8px 4px 0;
        }
		.poppy-switch-small {
			margin-top: -2px;
		}
    }
}
