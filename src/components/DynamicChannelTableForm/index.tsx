import { Badge, Button, Form, Input, message, Popconfirm, Select, Space, Switch, Table, Tooltip } from "@streamax/poppy";
import React, { forwardRef, Key, useEffect, useImperativeHandle, useState } from "react";
import type { FormInstance } from "@streamax/poppy/lib/form";
import { useDynamicList, useToggle } from "ahooks";
import { i18n, StarryAbroadOverflowEllipsisContainer, StarryAbroadIcon, Auth, getAppGlobalData, mosaicManager, InfoBack } from '@base-app/runtime-lib';
import { IconDelete, IconInformation, IconRequest } from "@streamax/poppy-icons";
import { CHANNEL_LIMIT, createChannelList, MAX_LIMIT, MIN_LIMIT } from "./constants";
import { useSubmitFn } from "@streamax/hooks";
import type { ButtonProps } from "@streamax/poppy/lib/button";
import type { ColumnsType } from '@streamax/poppy/lib/table';
import { editChannelStrategy, editStrategy, STRATEGY } from "@/service/strategy";
import { SelectProps } from "@streamax/poppy/lib/select";
import { useMergeChannelData } from "@/hooks/useChannelData";
import { formatChannelParams } from "@/hooks/useChannelData/utils";
import './index.scoped.less';
import ChannelSelectSingle from "./ChannelSelectSingle";
import { isNil } from "lodash";
import { fetchParameterDetail } from "@/service/parameter";

// 查询通道信息
export interface ChannelSettingItem {
    channelName: string;
    channelNumber: number;
    channelTypeId?: string;
    algorithmModel?: string;
    enable?: boolean;

}

export interface DynamicChannelTableFormProps {
    authCodes: {
        channelTypeCode: string;
        editCode?: string;
    };
    buttonOperateType?: ButtonProps['type'];
    showOperate?: boolean;
    operateType?:
        | 'default'
        | 'customAdd'
        | 'customCopy'
        | 'customEdit'
        | 'templateEdit';
    openIsWhen?: (open: boolean) => void;
    appId?: string | number;
    configureId?: string;
    channelList: ChannelSettingItem[];
    channelForm?: any; // 通过传入的form获取表单
    editing?: boolean; // 通过状态控制表单是否编辑
    configureType?: string | number;
    onHandleEdit?: () => Promise<boolean>;
    onHandleSave?: () => Promise<boolean>;
    channelTypeSelectProps?: any;
    mosaicTypeSelectProps?: any;
    infoBackStyle?: React.CSSProperties;
}
/****通道使能状态*/
enum ChannelStateEnum {
    /**1 开启**/ 
    OPEN = 1, 
    /**2 关闭*/ 
    CLOSE = 0
}
/****通道生效优先级 1 设备优先 2平台优先*/
export enum EffectiveChannelModeEnum {
    /**1 设备优先**/ 
    DEVICE = 1, 
    /**2 平台优先*/ 
    PLAT = 2
}
// 用户隐私配置资源code
export const PRIVATE_SETTING_CODE ='@base:@page:tenant.detail@action:tab.tenant.config:tab.user.privacy.config';
const OnlyShow = ({ value }: { value?: number }) => {
    return value ?? null;
};

export default forwardRef<FormInstance, DynamicChannelTableFormProps>((props, ref) => {
    const {
        buttonOperateType,
        authCodes,
        showOperate = true,
        openIsWhen,
        operateType = 'default',
        appId,
        configureId,
        channelList,
        channelForm,
        editing,
        configureType,
        onHandleEdit,
        onHandleSave,
        channelTypeSelectProps,
        mosaicTypeSelectProps,
        infoBackStyle
    } = props || {};
    const { channelTypeCode, editCode="" } = authCodes || {};
    const isShowChanelTypeColumns = Auth.check(channelTypeCode);
    const [defaultForm] = Form.useForm();
    const form = channelForm || defaultForm;
    const [editStatus, { toggle: editStatusToggle, setLeft: editStatusToggleAdd, setRight: editStatusToggleEdit }] = useToggle<'add', 'edit'>('add', 'edit');
    const [submitLoading, setSubmitLoading] = useState<boolean>(false);
    const [channelEnable, setChannelEnable] = useState<boolean>(false);
    const { platformOrMix } = mosaicManager.useTenantMosaicTypeParams();
    const showMosaicSetting = Auth.check(PRIVATE_SETTING_CODE) && platformOrMix;
    const { list, getKey, push, resetList,replace } =
        useDynamicList<ChannelSettingItem>(channelList);
    const mosaicOptions = [
        {
            value: 2,
            label: i18n.t('name', '车内人脸保护打码'),
        },
        {
            value: 1,
            label: i18n.t('name', '车外行人/车牌保护打码'),
        },
    ];
    useEffect(() => {
        // 自定义添加状态切换编辑状态
        if (['customAdd', 'customCopy', 'templateEdit'].includes(operateType)) editStatusToggle();
        if (operateType === 'customAdd') {
            resetList(createChannelList());
        } else if (operateType === 'customEdit') {
            // TODO 数据获取到，但list的个数没有更新
            resetList([]);
        } else if(operateType === 'templateEdit') {
            resetList(channelList);
        }
    }, [operateType]);
    useEffect(() => {
        if(editing != undefined) {
            if (editing) {
                editStatusToggleEdit();
            } else {
                editStatusToggleAdd();
            }
        }
    }, [editing]);
    /**查询优先级配置，平台优先级或设备优先级，平台优先级时需要展示通道使能字段***/ 
    const fetchTenantMosaicTypeParams = async () => {
        const parameter = await fetchParameterDetail(
            { parameterKey: 'DEVICE.EFFECTIVE.CHANNEL.MODE' },
            false,
        );
        setChannelEnable(EffectiveChannelModeEnum.PLAT === Number(parameter?.parameterValue));
    };
    useEffect(() => {
        fetchTenantMosaicTypeParams();
    },[]);
    const {
        channelDataFinishLoading,
        channelTypeMap,
        channelData,
        refreshChannelData,
        channelTypeOptions,
    } = useMergeChannelData({
        appId,
        configureId,
        configureLevelType: operateType === 'default' ? STRATEGY.DEFAULT : STRATEGY.NORMAL,
        onFinish: ({ list }) => {
            !channelForm && resetList(list)
        },
        manual: operateType === 'customAdd' || operateType === 'templateEdit',
        configureType,
    });
    useEffect(() => {
        configureId && operateType !== 'templateEdit' && refreshChannelData();
    }, [configureId]);

    const inSaaS = !getAppGlobalData('APP_ID');
    useImperativeHandle(ref, () => {
        return {
            setFieldsValue: (values) => {
                resetList(values);
            },
            resetFields: () => {
                form.resetFields();
            },
            validateFields: async () => {
                return await form.validateFields();
            }
        };
    });
    const columns = [
        {
            title: i18n.t('name', '通道序号'),
            dataIndex: 'channelNumber',
            ellipsis: true,
            render: (text: string, _: ChannelSettingItem, index: number) => {
                return editStatus === 'edit' || editing ? (
                    <Form.Item
                        name={[
                            'channelSettingList',
                            getKey(index),
                            'channelNumber',
                        ]}
                        initialValue={text}
                        noStyle
                    >
                        <OnlyShow />
                    </Form.Item>
                ) : (
                    <StarryAbroadOverflowEllipsisContainer>
                        {text}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        {
            title: (
                <Space size={8}>
                    <div>{i18n.t('name', '默认通道名称 ')}</div>
                    <Tooltip
                        overlay={i18n.t(
                            'message',
                            '设置通道默认名称，新增设备时会应用该名称，同时涉及到展示通道时，会展示默认通道名称',
                        )}
                    >
                        <span>
                            <StarryAbroadIcon>
                                <IconInformation className="default-icon-information" />
                            </StarryAbroadIcon>
                        </span>
                    </Tooltip>
                </Space>
            ),
            dataIndex: 'channelName',
            ellipsis: true,
            render: (text: string, _: ChannelSettingItem, index: number) => {
                return editStatus === 'edit' || editing ? (
                    <Form.Item
                        rules={[
                            {
                                required: true,
                                message: i18n.t(
                                    'message',
                                    '默认通道名称不能为空',
                                ),
                            },
                        ]}
                        name={[
                            'channelSettingList',
                            getKey(index),
                            'channelName',
                        ]}
                        initialValue={text}
                        style={{ marginBottom: 0 }}
                        normalize={(value: string) => value?.trim()}
                        className="form-item-vertical-align-top"
                    >
                        <Input
                            onChange={(e) =>
                                replace(index, {
                                    ...list[index],
                                    channelName: e.target.value?.trim(),
                                })
                            }
                            allowClear
                            maxLength={100}
                            style={{ maxWidth: 340 }}
                            placeholder={i18n.t(
                                'message',
                                '请输入默认通道名称',
                            )}
                        />
                    </Form.Item>
                ) : (
                    <StarryAbroadOverflowEllipsisContainer>
                        {text || '-'}
                    </StarryAbroadOverflowEllipsisContainer>
                );
            },
        },
        isShowChanelTypeColumns
            ? {
                  title: i18n.t('name', '通道类型'),
                  dataIndex: 'channelTypeId',
                  ellipsis: true,
                  render: (
                      text: string,
                      _: ChannelSettingItem,
                      index: number,
                  ) => {
                      const data = channelTypeMap.get(text) || '';
                      return editStatus === 'edit' || editing ? (
                          <Form.Item
                              name={[
                                  'channelSettingList',
                                  getKey(index),
                                  'channelTypeId',
                              ]}
                              initialValue={text}
                              style={{ marginBottom: 0 }}
                              className="form-item-vertical-align-top"
                          >
                              <ChannelSelectSingle
                                  onChange={(value) =>
                                      replace(index, {
                                          ...list[index],
                                          channelTypeId: value,
                                      })
                                  }
                                  initialValue={text}
                                  channelTypeOptions={channelTypeOptions}
                                  restProps={channelTypeSelectProps}
                                  
                              />
                          </Form.Item>
                      ) : (
                          <StarryAbroadOverflowEllipsisContainer>
                              {data || '-'}
                          </StarryAbroadOverflowEllipsisContainer>
                      );
                  },
              }
            : false,
        showMosaicSetting
            ? {
                  title: (
                      <Space size={8}>
                          <div>{i18n.t('name', '马赛克设置')}</div>
                          <Tooltip
                              overlay={i18n.t(
                                  'message',
                                  '设置通道的平台打码类型',
                              )}
                          >
                              <span>
                                  <StarryAbroadIcon>
                                      <IconInformation className="default-icon-information" />
                                  </StarryAbroadIcon>
                              </span>
                          </Tooltip>
                      </Space>
                  ),
                  dataIndex: 'algorithmModel',
                  ellipsis: true,
                  render: (
                      text: number,
                      _: ChannelSettingItem,
                      index: number,
                  ) => {
                      const dataItem = mosaicOptions.find(item => item.value == text);
                      return editStatus === 'edit' || editing ? (
                          <Form.Item
                              name={[
                                  'channelSettingList',
                                  getKey(index),
                                  'algorithmModel',
                              ]}
                              initialValue={text}
                              style={{ marginBottom: 0 }}
                              className="form-item-vertical-align-top"
                          >
                              <Select
                                  allowClear
                                  onChange={(value) =>
                                      replace(index, {
                                          ...list[index],
                                          algorithmModel: value,
                                      })
                                  }
                                  options={mosaicOptions}
                                  style={{ maxWidth: 340 }}
                                  placeholder={i18n.t(
                                      'message',
                                      '请选择马赛克设置',
                                  )}
                                  {...(mosaicTypeSelectProps || {})}
                              />
                          </Form.Item>
                      ) : (
                          <StarryAbroadOverflowEllipsisContainer>
                              {dataItem?.label || '-'}
                          </StarryAbroadOverflowEllipsisContainer>
                      );
                  },
              }
            : false,
        channelEnable
            ? {
                  title: i18n.t('name', '通道使能'),
                  dataIndex: 'enable',
                  ellipsis: true,
                  render: (
                      text: string[],
                      _: ChannelSettingItem,
                      index: number,
                  ) => {
                      return (
                            <div className="channel-state-warp">
                                <span
                                    className={`state-icon ${text ? 'active' : ''}`}
                                />
                                <span className="state-text">
                                    {text
                                            ? i18n.t('action', '启用')
                                            : i18n.t('action', '停用')}
                                </span>
                                <Form.Item
                                    name={[
                                        'channelSettingList',
                                        getKey(index),
                                        'enable',
                                    ]}
                                    noStyle
                                    initialValue={text}
                                    valuePropName="checked"
                                    className="form-item-vertical-align-top form-item-vertical-not-margin"
                                    getValueFromEvent={(checked) => {
                                        return checked
                                            ? ChannelStateEnum.OPEN
                                            : ChannelStateEnum.CLOSE;
                                    }}
                                >
                                    <Switch
                                        onChange={(value) =>
                                            replace(index, {
                                                ...list[index],
                                                enable: value,
                                            })
                                        }
                                        size={'small'}
                                        disabled={
                                            !(
                                                editStatus === 'edit' ||
                                                editing
                                            )
                                        }
                                    />
                                </Form.Item>
                            </div>
                      );
                  },
              }
            : false,
        {
            title: i18n.t('name', '操作'),
            width: 110,
            dataIndex: 'channelNumber',
            ellipsis: true,
            iconResize: true,
            render: (_Text: string, _: ChannelSettingItem, index: number) => {
                return (
                    <Popconfirm
                        icon={<IconRequest />}
                        title={i18n.t('message', '确认要删除“{name}”通道吗？', {
                            name: _.channelNumber,
                        })}
                        onConfirm={() => handleDelete(index)}
                        disabled={
                            editStatus === 'add' || list.length <= MIN_LIMIT
                        }
                    >
                        <Button
                            disabled={
                                editStatus === 'add' || list.length <= MIN_LIMIT
                            }
                            type="link"
                            style={{ padding: 0 }}
                        >
                            <IconDelete />
                        </Button>
                    </Popconfirm>
                );
            },
        },
    ].filter(Boolean) as ColumnsType<ChannelSettingItem>;
    const handleDelete = (index: number) => {
        // 按照产品要求删除之后索引变化；其他项向上移动
         const removeList = list.filter((_, i) => i !== index);
        const newList = removeList.map((item, index) => {
            if (item.channelNumber > index + 1) {
                item.channelNumber = item.channelNumber - 1;
            }
            return item;
        });
        resetList(newList);
    };

    const handleEdit = async () => {
        if(onHandleEdit) {
            const data = await onHandleEdit();
            if(!data) return;
        }
        editStatusToggle();
        openIsWhen?.(true);
    };

    const handleCancel = () => {
        editStatusToggle();
        refreshChannelData();
        openIsWhen?.(false);
		setSubmitLoading(false);
    };
	const [handleSave] = useSubmitFn(async () => {
        setSubmitLoading(true);
        form.validateFields().then(async (res) => {
             const { channelSettingList = [] } = res || {};
             const { configureId, configureName, configureType, paramList } = channelData || {};
             const params = {
                 configureId,
                 configureName,
                 configureType,
                 editMulLanguage: false,
                 paramList: formatChannelParams.toFormatBackParams(
                     channelSettingList.filter(Boolean),
                     {
                         oldData: paramList,
                     },
                 ),
             };
             await editChannelStrategy(params);
             message.success(i18n.t('message', '操作成功'));
             handleCancel();
        }).finally(() => {
            setSubmitLoading(false);
            onHandleSave?.();
        });
	});
	
	const handleAdd = () => {
        const index = list.length + 1;
        const item = {
            channelName: `CH${index}`,
            channelNumber: index,
            enable: 1
        };
        push(item);
    };
    return (
        <div className={`dynamic-channel-table-form-container`}>
            <div className="dynamic-channel-table-form-tip">
                <InfoBack
                    style={infoBackStyle}
                    title={channelEnable ? i18n.t(
                        'message',
                        '平台配置的通道信息生效优先级 > 设备上报的配置信息。',
                    ) : i18n.t(
                        'message',
                        '通道名称和通道个数会优先按设备上报的配置生效。',
                    )}
                />
            </div>
            <div className="dynamic-channel-table-form-header">
                <div className="left-button">
                    {`${i18n.t('message', '默认通道数')}：`}
                    <span className="channel-number">
                        {(isNil(appId) && inSaaS ? 0 : list.length) || 0}
                    </span>
                </div>
                {showOperate && !channelForm ? <Auth code={editCode}>
                      <div className="right-button">
                        {editStatus === 'add' ? (
                            <Button
                                type={buttonOperateType}
                                style={{
                                    padding:
                                        buttonOperateType === 'link'
                                            ? 0
                                            : undefined,
                                }}
                                onClick={handleEdit}
                            >
                                {i18n.t('action', '编辑')}
                            </Button>
                        ) : (
                            <Space size={16}>
                                <Button
                                    type={buttonOperateType}
                                    style={{
                                        padding:
                                            buttonOperateType === 'link'
                                                ? 0
                                                : undefined,
                                    }}
                                    onClick={handleCancel}
                                >
                                    {i18n.t('action', '取消')}
                                </Button>
                                <Button
                                    type={buttonOperateType || 'primary'}
                                    style={{
                                        padding:
                                            buttonOperateType === 'link'
                                                ? 0
                                                : undefined,
                                    }}
                                    onClick={handleSave}
                                    loading={submitLoading}
                                >
                                    {i18n.t('action', '保存')}
                                </Button>
                            </Space>
                        )}
                    </div>
                </Auth> : (
                    <div />
                )}
            </div>
            <Form form={form} ref={ref}>
                <Table
                    loading={channelDataFinishLoading}
                    columns={columns}
                    dataSource={isNil(appId) && inSaaS ? [] : list}
                    //@ts-ignore
                    rowKey={(_r: ChannelItem, index: number) =>
                        getKey(index).toString()
                    }
                    pagination={false}
                    style={{ overflow: 'auto' }}
                />
                {(editStatus === 'edit' || editing) && (
                    <Button
                        disabled={list.length >= MAX_LIMIT}
                        type="dashed"
                        onClick={handleAdd}
                        className="add-channel-button"
                    >
                        {i18n.t('name', '添加')}
                    </Button>
                )}
            </Form>
        </div>
    );
});