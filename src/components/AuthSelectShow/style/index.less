//之前的颜色变量
@import '~@streamax/poppy-themes/starry/index.less';
//海外风格的颜色变量
@import '~@streamax/poppy-themes/starry/abroad.less';
// .poppy-select-multiple,
.auth-poppy-select .poppy-select-selection-overflow-item {
    max-width: 100%;
}

// fix #119868多选换行
.auth-poppy-select .poppy-select-selector .poppy-select-selection-overflow {
    flex-wrap: nowrap;
}
.poppy-select-multiple .auth-select-tag-item {
    display: flex;
    align-items: center;
    position: relative;
    box-sizing: border-box;
    // max-width: 100%;
    margin-top: 2px;
    margin-bottom: 2px;
    line-height: 1.834;
    background: var(--poppy-select-selection-item-bg);
    border: var(--poppy-border-color-split);
    border-radius: var(--poppy-border-radius-base);
    cursor: default;
    transition: font-size 0.3s, line-height 0.3s, height 0.3s;
    .auth-select-tag-item-text{
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        // width: 92%;
    }
}
.poppy-select-multiple .auth-select-tag-disable-item{
    background: rgba(0, 0, 0, 0.1);
}
.poppy-select-disabled .auth-select-tag-item {
    color: @starry-bg-color-component-active-disabled;
    border-color: @starry-bg-color-specialcomponent;
    cursor: not-allowed;
}
.poppy-select-disabled .auth-select-tag-disable-item {
    color: @starry-bg-color-component-active-disabled;
    border-color: @starry-bg-color-specialcomponent;
    background: var(--poppy-select-selection-item-bg);
    cursor: not-allowed;
}

.auth-select-placeholder  {
    position: absolute;
    top: 0;
    left: 12px;
    line-height: 32px;
    color: @input-placeholder-color;
    display: none;
    pointer-events: none;
}
.auth-select-placeholder::abroad {
    line-height: 56px;
    font-size: 16px;
}
.show-auth-select-placeholder {
    display: block;
}
.auth-poppy-select-dropdown {
    .rc-virtual-list-holder {
        .poppy-select-item-option-disabled {
            display: none;
        }
    }
    .rc-virtual-list-holder::-webkit-scrollbar {
        width: 8px; /* 设置滚动条的宽度 */
    }
    .rc-virtual-list-holder::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.5); /* 设置滚动条滑块的颜色 */
        border-radius: 5px; /* 设置滚动条滑块的圆角 */
    }
    .rc-virtual-list-holder::-webkit-scrollbar-thumb:hover {
        background-color: rgba(0, 0, 0, 0.5);
    }
    .rc-virtual-list-holder::-webkit-scrollbar-track {
        background-color: transparent; /* 设置滚动条背景的颜色 */
    }
    .dropdown-empty-wrap {
        padding: 12px 0;
    }
}
.auth-poppy-select {
    .poppy-tag {
        &::abroad {
            height: 48px !important;
            line-height: 46px!important;
            vertical-align: middle;
        }
    }
    .auth-select-tag-item-text,
    .anticon-close{
        vertical-align: middle;
    }
    .poppy-select-selection-overflow-item-rest{
        .poppy-select-selection-item::abroad {
            height: 48px !important;
            line-height: 46px!important;
            vertical-align: middle;
        }
    }
}

.auth-poppy-select-lazy-dropdown {
    .dropdown-empty-wrap {
        padding: 50px 0;
    }
    .poppy-empty {
        margin: 50px 0;
    }
    .auth-select-loading,
    .auth-select-load-more,
    .auth-select-no-more {
        text-align: center;
        padding: 8px 0;
        color: @starry-text-color-secondary;
        font-size: 14px;
    }

    .auth-select-loading {
        .auth-select-loading-text {
            margin-left: 8px;
            color: @starry-text-color-secondary;
        }
    }
    .position-auth-select-loading {
        position: absolute;
        bottom: 0;
        width: 100%;
    }
    .position-auth-select-reload {
        position: absolute;
        bottom: 16px;
        left: 50%;
        transform: translateX(-50%);
    }
    .auth-select-reload {
        display: inline-block;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-size: 14px;
        color: @starry-text-color-secondary;
        padding: 4px 8px;
        border-radius: @border-radius-4;
        cursor: auto;
        background: @starry-bg-color-component-elevated;
        box-shadow:  0 1px 10px 0 #0000000d, 0 4px 5px 0 #00000014, 0 2px 4px -1px #0000001f;
        .auth-select-reload-button {
            cursor: pointer;
        }
        .anticon {
            color: @primary-color;
        }
        .auth-select-reload-text {
            margin-left: 8px;
            color: @primary-color;
        }
    }
    .auth-select-reload::abroad {
        border-radius: @border-radius-6;
    }
}
.auth-poppy-select-lazy-dropdown-loading {
    .poppy-select-item-empty{
        display: none;
    }
    .poppy-select-item-empty::abroad {
        display: none;
    }
}
.auth-poppy-select-lazy-load {
    .poppy-select-selector {
        .auth-select-tag-item::abroad {
            display: block;
        }
    }
}
.poppy-select-multiple .hide-auth-select-tag-item{
    display: none;
}
.auth-poppy-select-lazy-load .poppy-select-selector .hide-auth-select-tag-item::abroad {
    display: none;
}