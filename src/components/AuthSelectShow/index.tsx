/*
 * @LastEditTime: 2025-07-03 09:07:19
 */
import { Tag, Select, Empty, Spin, message, Space, Button } from '@streamax/poppy';
import type {
    SelectProps as AntdSelectProps,
    LabeledValue,
    RefSelectProps,
} from '@streamax/poppy/lib/select';
import type { TagProps } from '@streamax/poppy/lib/tag';
import React, {
    useState,
    useEffect,
    useImperativeHandle,
    useRef,
    useCallback,
} from 'react';
import debounce from 'lodash/debounce';
import './style/index.less';
import { IconRetry } from '@streamax/poppy-icons';
import { StarryAbroadIcon, i18n } from '@base-app/runtime-lib';
import { NO_AUTH_TEXT, SELECT_PAGE_SIZE } from '@/utils/constant';
import { useDebounceFn } from '@streamax/hooks';
const SELECT_ITEM_HEIGHT = 32;
const SEARCH_DELAY_LOADING = 100;
const { Option, OptGroup, SECRET_COMBOBOX_MODE_DO_NOT_USE } = Select;
// eslint-disable-next-line @typescript-eslint/no-empty-interface
type SelectItem = {
    value: string;
    label: string;
    disabled?: boolean;
    show?: boolean;
};
type BaseList = {
    list: any[]; // 当前页数据
    total: number; // 总数据量，可选
    hasMore?: boolean; // 是否还有更多数据，可选
};
interface SelectProps extends AntdSelectProps {
    noAuthShowText?: string; //无权限时展示值
    hasAuth?: boolean; //是否有权限展示
    openAuth?: boolean; //是否开启权限展示
    onInitDone?: () => void;
    keepSort?: boolean; // 保持值得回显顺序

    // 新增懒加载相关属性
    lazyLoad?: boolean; // 是否启用懒加载功能，默认false
    pageSize?: number; // 每页加载数量，默认50
    loadThreshold?: number; // 触发加载的阈值，默认10
    onLoadMore?: (
        searchParams: {
            searchValue?: string;
            page?: number;
            pageSize?: number;
            lastObjectId?: any;
        },
        valueIds?: string[],
    ) => Promise<{
        baseList: BaseList;
        customList: {
            list: SelectItem[]; // 当前数据
            noAuthList: string[]; //无权限数据的id
        };
    }>; // 加载更多数据的回调函数（远程数据源模式）
    debounceTime?: number; // 搜索防抖时间，默认300ms
    searchValue?: string; // 外部控制的搜索值
    maxCount?: number;
}
const InternalAuthSelect = React.forwardRef<RefSelectProps, SelectProps>(
    (props, ref) => {
        const {
            noAuthShowText = '******',
            options,
            openAuth = true,
            hasAuth = true,
            mode,
            value,
            onInitDone,
            keepSort,
            lazyLoad = false,
            pageSize = SELECT_PAGE_SIZE,
            loadThreshold = 10,
            onLoadMore,
            maxCount,
            debounceTime = 300,
            onBlur,
            onChange,
            onDeselect,
            showPlaceholder
        } = props;
        // 原有状态
        const [disabled, setDisabled] = useState(false);
        const [modeDisabled, setModeDisabled] = useState(false);
        const [newOptions, setNewOptions] = useState<any>(props.options);
        const [selectValue, setSelectValue] = useState(
            lazyLoad ? undefined : props.value,
        );

        // 新增状态
        const [loading, setLoading] = useState(false);
        const [searchLoading, setSearchLoading] = useState(false);
        // 普通分页的页数记录
        const [page, setPage] = useState(1);
        const [hasMore, setHasMore] = useState(true);
        const [displayOptions, setDisplayOptions] = useState<any[]>([]);
        // 搜索的分页页数记录
        const [searchPage, setSearchPage] = useState(1);
        const [searchHasMore, setSearchHasMore] = useState(true);
        const [searchDisplayOptions, setSearchDisplayOptions] = useState<any[]>(
            [],
        );
        // 搜索的条件值
        const [searchValue, setSearchValue] = useState(props.searchValue || '');
        // 是否第一次加载查询完成
        const loadedRef = useRef(false);
        // 下拉的ref
        const selectRef = useRef(null);
        // 存储props的value值到ref
        const valueRef = useRef<any>(null);
        // 回显时有权限的数据对象，value为key
        const lazyLoadBackDataRef = useRef<Record<string, any>>({});
        // 回显时无权限的数据对象，value为key
        const lazyLoadBackNoAuthDataRef = useRef<Record<string, any>>({});
        const lazyLoadBackNoAuthDataListRef = useRef<SelectItem[]>([]);
        // 加载失败
        const [loadError, setLoadError] = useState(false);
        // 搜索的值存ref，搜索数据是否已成功返回
        const searchModeRef = useRef({
            searchValue: '',
            searched: false,
        });
        // 记录offset
        const lastObjectIdRef = useRef<string | number>(0);
        const searchLastObjectIdRef = useRef<string | number>(0);
        const [firstSearch, setFirstSearch] = useState(true);
        // 初始化
        useEffect(() => {
            valueRef.current = value;
            if (lazyLoad) {
                // 懒加载模式初始化
                initLazyLoadOptions();
            } else {
                // 原有逻辑
                selectAuthShow();
            }
        }, [lazyLoad, options, value]);

        // 原有的全量数据下拉逻辑
        const selectAuthShow = () => {
            if (lazyLoad) {
                // 懒加载模式下，初始化时已经处理了权限控制
                return;
            }
            if (openAuth && value?.toString()) {
                const storeValue: any = [];
                const storeOptions: any[] = options?.slice(0) || [];
                if (Array.isArray(options)) {
                    const valueList = storeOptions.map((item) => {
                        return item.value;
                    });
                    if (mode === 'multiple' || mode === 'tags') {
                        // @ts-ignore
                        const auth = (value || []).every((id: string) =>
                            valueList.includes(id),
                        );
                        Array.isArray(value) &&
                            value.forEach((item, index) => {
                                if (!valueList.includes(item)) {
                                    storeOptions.push({
                                        value: item,
                                        label: noAuthShowText,
                                        disabled: true,
                                    });
                                    keepSort
                                        ? (storeValue[index] = item)
                                        : storeValue.unshift(item);
                                } else {
                                    if (!hasAuth) {
                                        // 手动设置无权限
                                        storeOptions.push({
                                            value: item,
                                            label: noAuthShowText,
                                            disabled: true,
                                        });
                                        keepSort
                                            ? (storeValue[index] = item)
                                            : storeValue.unshift(item);
                                    } else {
                                        keepSort
                                            ? (storeValue[index] = item)
                                            : storeValue.push(item);
                                    }
                                }
                            });
                        setModeDisabled(!auth);
                        setNewOptions(storeOptions);
                        setSelectValue(storeValue);
                    } else {
                        setDisabled(false);
                        if (!valueList.includes(value) || !hasAuth) {
                            // 手动设置无权限
                            storeOptions.push({
                                value: value,
                                label: noAuthShowText,
                                disabled: true,
                            });
                            setNewOptions(storeOptions);
                            setDisabled(true);
                        } else {
                            setNewOptions(storeOptions);
                            setDisabled(false);
                        }
                        setSelectValue(value);
                    }
                }
            } else {
                if (Array.isArray(value)) {
                    setSelectValue(value.filter((item) => item !== ''));
                } else {
                    setSelectValue(value);
                }
                setNewOptions(options?.slice(0) || []);
            }
        };

        /*****************************新增远程数据懒加载逻辑 start**************************************/
        // 初始化第一次懒加载数据
        const _initLazyLoadOptions = async () => {
            if (!lazyLoad) return;
            try {
                let initialOptions: any[] = displayOptions || [];
                let hasMoreData = false;
                if (onLoadMore && !loadedRef.current) {
                    setLoading(true);
                    setLoadError(false);
                    // 远程数据源模式
                    let valueIds: string[] = [];
                    if (Array.isArray(value)) {
                        valueIds = (value || [])?.map((item) => item.value);
                    }
                    const result = await onLoadMore(
                        {
                            page: 1,
                            pageSize,
                            lastObjectId: 0,
                        },
                        valueIds,
                    );
                    const { baseList, customList } = result;
                    initialOptions = baseList.list || [];
                    hasMoreData = calcHasMore(baseList, 1, pageSize);
                    // 第一次加载完成
                    loadedRef.current = true;
                    setBackDataRef(customList, value);
                    storeSelectData({
                        page: 1,
                        newPageOptions: initialOptions,
                        initData: true,
                        hasMoreData: hasMoreData,
                    });
                }
                // 应用权限控制
                applyAuthControlToOptions(initialOptions, value);
            } catch (error) {
                console.error('Failed to initialize lazy load options:', error);
                setDisplayOptions([]);
                setLoadError(true);
            } finally {
                setLoading(false);
            }
        };
        const { run: initLazyLoadOptions } = useDebounceFn(
            _initLazyLoadOptions,
            { wait: 100 },
        );
        // 模糊搜索实现
        const handleSearchImpl = async (value: string) => {
            if (!lazyLoad) {
                // 非懒加载模式，使用原有搜索逻辑
                props.onSearch && props.onSearch(value);
                return;
            }
            // 没有搜索值时不进行搜索
            if (!searchModeRef.current.searchValue) {
                setSearchLoading(false);
                return;
            };
            setSearchLoading(true);
            // 解决搜索数据太快导致闪动问题
            const startTime = performance.now();
            let endTime = 0;
            try {
                let searchResults: any[] = [];
                let hasMoreData = false;
                setLoadError(false);
                setSearchDisplayOptions([]);
                if (onLoadMore) {
                    // 远程搜索
                    const result = await onLoadMore({
                        searchValue: value,
                        page: 1,
                        pageSize,
                        lastObjectId: 0
                    });
                    const { baseList } = result;
                    searchResults = baseList.list || [];
                    hasMoreData = calcHasMore(baseList, 1, pageSize);
                }
                endTime = performance.now();
                const executionTime = endTime - startTime > SEARCH_DELAY_LOADING ? 0 : (SEARCH_DELAY_LOADING - (endTime - startTime));
                setTimeout(() => {
                    const propsValue = valueRef.current;
                    // 应用权限控制到搜索结果
                    const processedOptions =
                        openAuth && propsValue
                            ? applyAuthControlToOptions(searchResults, propsValue)
                            : searchResults;
                    storeSelectData({
                        page: 1,
                        newPageOptions: processedOptions,
                        initData: true,
                        hasMoreData: hasMoreData,
                        search: true,
                    });
                    loadedRef.current = true;
                    setSearchLoading(false);
                    searchModeRef.current.searched = false;
                }, executionTime);
            } catch (error) {
                console.error('Search failed:', error);
                endTime = performance.now();
                const executionTime = endTime - startTime > SEARCH_DELAY_LOADING ? 0 : (SEARCH_DELAY_LOADING - (endTime - startTime));
                setTimeout(() => {
                    setSearchDisplayOptions([]);
                    setLoadError(true);
                    setSearchLoading(false);
                }, executionTime);
            }
        };
        // 分页加载更多数据
        const loadMoreData = async (searchParams?: Record<string, any>) => {
            // 第一次搜索值的时候不应该执行加载数据
            const searchedValue = searchModeRef.current.searchValue;
            let hasMoreData = searchedValue ? searchHasMore : hasMore;
            setFirstSearch(false);
            if (
                !hasMoreData ||
                (!searchedValue && loading) ||
                (searchedValue && searchLoading) ||
                !lazyLoad ||
                (!loadError && searchModeRef.current.searched)
            )
                return;
            if (searchedValue) {
                setSearchLoading(true);
            } else {
                setLoading(true);
            }
            setLoadError(false);
            try {
                let nextPageData: any[] = [];
                const currentSearchPage = searchedValue ? searchPage : page;
                const lastObjectId = searchedValue ? searchLastObjectIdRef.current : lastObjectIdRef.current;
                if (onLoadMore && hasMoreData) {
                    // 远程数据源模式
                    const result = await onLoadMore({
                        searchValue: searchedValue,
                        page: currentSearchPage,
                        pageSize,
                        lastObjectId,
                        ...searchParams,
                    });
                    const { baseList } = result;
                    nextPageData = baseList.list || [];
                    hasMoreData = calcHasMore(
                        baseList,
                        currentSearchPage,
                        pageSize,
                    );
                }
                // 应用权限控制到新加载的选项
                const processedOptions =
                    openAuth && valueRef.current
                        ? applyAuthControlToOptions(
                              nextPageData,
                              valueRef.current,
                          )
                        : nextPageData;
                storeSelectData({
                    page: currentSearchPage,
                    newPageOptions: processedOptions,
                    hasMoreData: hasMoreData,
                    search: searchedValue ? true : false,
                });
            } catch (error) {
                setLoadError(true);
                console.error('Failed to load more options:', error);
            } finally {
                setSearchLoading(false);
                setLoading(false);
            }
        };

        // 防抖处理搜索
        const debouncedSearch = useCallback(
            debounce((value: string) => {
                handleSearchImpl(value);
            }, debounceTime),
            [onLoadMore, page, pageSize, value],
        );

        // 处理外部搜索值变化
        useEffect(() => {
            if (
                lazyLoad &&
                props.searchValue !== undefined &&
                props.searchValue !== searchValue
            ) {
                setSearchValue(props.searchValue);
                handleSearchImpl(props.searchValue);
            }
        }, [props.searchValue]);

        // 组件卸载时清理
        useEffect(() => {
            return () => {
                debouncedSearch.cancel();
            };
        }, [debouncedSearch]);

        /**
         * @description: 存储有权限和无权限的数据
         * @param {*} customList
         * @return {*}
         */
        const setBackDataRef = (customList, value: SelectItem[]) => {
            // 暂存一份要回显的数据为对象
            lazyLoadBackDataRef.current = (customList.list || []).reduce(
                (acc, item) => {
                    acc[item.value] = item;
                    return acc;
                },
                {},
            );
            // 暂存一份要回显的数据为对象
            lazyLoadBackNoAuthDataRef.current = (
                customList.noAuthList || []
            ).reduce((acc, item) => {
                acc[item] = item;
                return acc;
            }, {});
            //
            const noAuthList: SelectItem[] = [];
            value?.forEach((item) => {
                if (lazyLoadBackNoAuthDataRef.current[item.value || item]) {
                    noAuthList.push({
                        disabled: true,
                        label: NO_AUTH_TEXT,
                        value: item.value || item,
                    });
                }
            });
            lazyLoadBackNoAuthDataListRef.current = noAuthList;
        };

        /**
         * @description: 查找并返回回显数据label的list
         * @return {*}
         */
        const parseBackDataListLabel = (value: SelectItem[] | SelectItem) => {
            const newValue: SelectItem[] = [];
            // 多选时
            if (Array.isArray(value)) {
                (value || []).forEach((item) => {
                    if (lazyLoadBackDataRef.current[item.value]) {
                        item.label =
                            lazyLoadBackDataRef.current[item.value].label;
                        newValue.push(item);
                    }
                    // if (lazyLoadBackNoAuthDataRef.current[item.value]) {
                    //     item.label = noAuthShowText;
                    //     item.disabled = true;
                    //     newValue.unshift(item);
                    // }
                    if (
                        !lazyLoadBackDataRef.current[item.value] &&
                        !lazyLoadBackNoAuthDataRef.current[item.value]
                    ) {
                        // 兼容外部传入时没有传label的情况
                        if (!item.label) {
                            item.label = undefined;
                        }
                        newValue.push(item);
                    }
                });
                // 单选时格式化权限
            } else if (!Array.isArray(value) && value) {
                if (lazyLoadBackDataRef.current[value.value]) {
                    value.label =
                        lazyLoadBackDataRef.current[value.value]?.label;
                }
                if (lazyLoadBackNoAuthDataRef.current[value.value]) {
                    value.label = noAuthShowText;
                    value.disabled = true;
                }
                return value;
            }
            return newValue;
        };
        // 权限控制应用到懒加载选项
        const applyAuthControlToOptions = (options: any[], value: any) => {
            if (!openAuth || !value) return options;
            const storeOptions: any[] = [...options];
            const parsedValue = parseBackDataListLabel(value);
            if (Array.isArray(options)) {
                const auth = Boolean(
                    Object.keys(lazyLoadBackNoAuthDataRef.current).length,
                );
                setModeDisabled(auth);
                setDisabled(auth);
                // 最大选择n个，ant4.0不支持 5.0支持
                setSelectValue([
                    ...lazyLoadBackNoAuthDataListRef.current,
                    ...parsedValue,
                ]);
            }
            return storeOptions;
        };
        // 滚动加载实现
        const handlePopupScroll = useCallback(
            (e: React.UIEvent<HTMLDivElement>) => {
                const { target } = e;
                // 第一次搜索值的时候不应该执行加载数据
                const searchedValue = searchModeRef.current.searchValue;
                const hasMoreData = searchedValue ? searchHasMore : hasMore;
                if (
                    lazyLoad &&
                    hasMoreData &&
                    !loading &&
                    !searchLoading &&
                    target instanceof HTMLElement
                ) {
                    const { scrollTop, clientHeight, scrollHeight } = target;
                    // 当滚动到距离底部loadThreshold条数据时，加载更多
                    const itemHeight = SELECT_ITEM_HEIGHT;
                    const threshold = loadThreshold * itemHeight;
                    if (scrollHeight - scrollTop - clientHeight <= threshold) {
                        !loadError && loadMoreData();
                    }
                }
            },
            [lazyLoad, hasMore, loading, searchLoading, loadThreshold, page],
        );

        /**
         * @description: 存储搜索条件和非搜索条件时的数据,两种数据分开保存
         * @return {*}
         */
        const storeSelectData = (data: {
            newPageOptions: SelectItem[]; //新返回的下拉数据
            page: number;
            hasMoreData: boolean; //是否还有下一页数据
            initData?: boolean; //是否是初始化，需要重置数据
            search?: boolean;
        }) => {
            const { newPageOptions, page, hasMoreData, initData, search } =
                data;
            const nextPage = hasMoreData ? page + 1 : page;
            if (search) {
                initData || page === 1
                    ? setSearchDisplayOptions(newPageOptions)
                    : setSearchDisplayOptions((prev) => [
                          ...prev,
                          ...newPageOptions,
                      ]);
                if(newPageOptions.length) {
                    searchLastObjectIdRef.current = newPageOptions[newPageOptions.length - 1].value;
                }
                setSearchPage(nextPage);
                setSearchHasMore(hasMoreData);
            } else {
                initData || page === 1
                    ? setDisplayOptions(newPageOptions)
                    : setDisplayOptions((prev) => [...prev, ...newPageOptions]);
                if(newPageOptions.length) {
                    lastObjectIdRef.current = newPageOptions[newPageOptions.length - 1].value;
                }
                setPage(nextPage);
                setHasMore(hasMoreData);
            }
        };
        const calcHasMore = (
            baseList: BaseList,
            page: number,
            pageSize: number,
        ) => {
            if (baseList.hasMore !== undefined) return baseList.hasMore;
            if (page * pageSize < baseList.total) {
                return true;
            }
            return false;
        };

        // 搜索处理
        const handleSearch = useCallback(
            (value: string) => {
                if (value?.trim()) {
                    searchModeRef.current.searchValue = value?.trim();
                    searchModeRef.current.searched = true;
                    setSearchLoading(true);
                    setFirstSearch(true);
                    setLoadError(false);
                    setSearchDisplayOptions([]);
                } else {
                    searchModeRef.current.searchValue = '';
                }
                // 滚动到第一个选项
                selectRef.current?.scrollTo(0);
                setSearchValue(value);
                debouncedSearch(value);
            },
            [debouncedSearch],
        );
        const filterShowOptions = (listOptions: SelectItem[]) => {
            return listOptions.filter((item) => {
                if (item.hasOwnProperty('show')) {
                    return item.show;
                }
                return true;
            });
        };
        // 根据当前类型渲染下拉数据项
        const renderLazyOptions = () => {
            if (lazyLoad) {
                if (searchValue) {
                    return filterShowOptions(searchDisplayOptions);
                }
                return filterShowOptions(displayOptions);
            }
            return newOptions;
        };
        const innerOnBlur = (e) => {
            setSearchValue('');
            searchModeRef.current.searchValue = '';
            onBlur?.(e);
        };
        const reloadData = () => {
            loadMoreData();
        };
        // 自定义下拉菜单渲染
        const customDropdownRender = (menu: React.ReactNode) => {
            // 检查是否为空
            const isEmpty =
                displayOptions.filter((item: any) => !item.disabled).length ===
                0;
            // 自定义下拉菜单
            return (
                <div>
                    {isEmpty ? (
                        <div className="dropdown-empty-wrap">
                            <Empty
                                imageStyle={{
                                    height: 45,
                                }}
                            />
                        </div>
                    ) : (
                        menu
                    )}
                    {lazyLoad && searchValue && searchLoading && (
                        <div className={`auth-select-loading ${firstSearch ? null : 'position-auth-select-loading'}`}>
                            <Spin size="small" />
                            <span className="auth-select-loading-text">
                                {i18n.t('message', '加载中')}
                            </span>
                        </div>
                    )}
                    {lazyLoad && !searchValue && loading && (
                        <div className="auth-select-loading position-auth-select-loading">
                            <Spin size="small" />
                            <span className="auth-select-loading-text">
                                {i18n.t('message', '加载中')}
                            </span>
                        </div>
                    )}
                    {lazyLoad && loadError && (
                        <div className="auth-select-reload position-auth-select-reload">
                            <span
                                className="auth-select-reload-button"
                                onClick={reloadData}
                            >
                                <StarryAbroadIcon>
                                    <IconRetry />
                                </StarryAbroadIcon>
                                <span className="auth-select-reload-text">
                                    {i18n.t('action', '重新加载')}
                                </span>
                            </span>
                        </div>
                    )}
                </div>
            );
        };
        // 获取Select类名
        const getSelectClassName = () => {
            return `auth-poppy-select ${
                lazyLoad ? 'auth-poppy-select-lazy-load' : ''
            } ${props.className || ''}`;
        };
        const onInnerChange = (e, option) => {
            setSearchValue('');
            searchModeRef.current.searchValue = '';
            onChange?.(e, option);
        };
        const onInnerDeselect = (value: string | LabeledValue | number, options: LabeledValue[]) => {
            if (value && lazyLoadBackNoAuthDataRef.current[value?.value || value]) {
                // 关键：调用外部 onChange，传递包含 disabled 选项的完整值 防止键盘删除的情况
                onChange?.(selectValue, searchModeRef.current.searchValue ? searchDisplayOptions : displayOptions);
                return;
            }
            onDeselect?.(value, options);
        };
        /*****************************新增远程数据懒加载逻辑 end**************************************/

        // 原有的初始化完成回调
        useEffect(() => {
            onInitDone && onInitDone();
        }, [disabled, modeDisabled]);
        // 原有的useImperativeHandle，添加新的方法
        useImperativeHandle(ref, () => {
            return {
                getAuth: disabled,
                getModeAuth: modeDisabled,
                // 新增方法
                refresh: initLazyLoadOptions,
                search: handleSearchImpl,
            };
        });
        // 根据id找到对应的item，业务中用item中的show属性用于判断tag的显示隐藏
        const findOptionByValue = (value: string) => {
            if (lazyLoad) {
                return searchDisplayOptions.find((option) => option.value === value) || displayOptions.find((option) => option.value == value);
            }
            return null;
        };
        

        // 判断当前选中的下拉数据时否是只有一个且有show属性是false，并且不是在搜索条件下，此时需要增加class展示placeholder
        const onlyOneHideValue = () => {
            if(Array.isArray(selectValue) && selectValue?.length === 1 && !searchValue) {
                const item = displayOptions.find(item => item.value === selectValue?.[0]?.value);
                if (item?.hasOwnProperty('show') && !item.show) {
                    return 'show-auth-select-placeholder';
                }
            }
            return '';
        };
        // 原有的标签渲染
        const tagRender = (props: TagProps) => {
            // @ts-ignore
            const { label, onClose, value } = props;
            const item = findOptionByValue(value);
            let showTag = true;
            let { closable } = props;
            // 懒加载时，若无权限就不允许关闭
            if (lazyLoad && lazyLoadBackNoAuthDataRef.current[value]) {
                closable = false;
            }
            const onPreventMouseDown = (
                event: React.MouseEvent<HTMLSpanElement>,
            ) => {
                event.preventDefault();
                event.stopPropagation();
            };
            if(item) {
                showTag = item.show ?? true;
            }
            return (
                <Tag
                    onMouseDown={onPreventMouseDown}
                    closable={closable}
                    onClose={onClose}
                    style={{ marginRight: 3 }}
                    className={`${'auth-select-tag-item'} ${
                        closable && !disabled
                            ? ''
                            : 'auth-select-tag-disable-item'
                    } ${!showTag ? 'hide-auth-select-tag-item' : ''} ${props?.className}`}
                >
                    <span className="auth-select-tag-item-text" title={label}>
                        {label}
                    </span>
                </Tag>
            );
        };

        // 原有的下拉菜单渲染
        const dropdownRender = (node: React.ReactNode) => {
            return (newOptions || []).filter((item: any) => !item.disabled)
                .length ? (
                node
            ) : (
                <Empty imageStyle={{
                    height: 45,
                }} />
            );
        };
        // 渲染组件
        if (openAuth && (disabled || mode === 'multiple' || mode === 'tags')) {
            const { className, suffixIcon, filterOption, ...otherProps } =
                props || {};
            return (
                <>
                <Select
                    ref={selectRef}
                    disabled={disabled}
                    className={`${getSelectClassName()}`}
                    maxTagCount="responsive"
                    virtual={false}
                    {...otherProps}
                    options={renderLazyOptions()}
                    value={selectValue}
                    tagRender={tagRender}
                    dropdownRender={
                        lazyLoad ? customDropdownRender : dropdownRender
                    }
                    onBlur={lazyLoad ? innerOnBlur : undefined}
                    onPopupScroll={lazyLoad ? handlePopupScroll : undefined}
                    onSearch={
                        lazyLoad && props.showSearch ? handleSearch : undefined
                    }
                    filterOption={lazyLoad ? false : props.filterOption} // 懒加载模式下禁用内置过滤
                    showSearch={props.showSearch}
                    dropdownClassName={`${
                        props?.dropdownClassName || 'auth-poppy-select-dropdown'
                    } ${lazyLoad ? 'auth-poppy-select-lazy-dropdown' : ''} ${
                        (loading || searchLoading)
                            ? 'auth-poppy-select-lazy-dropdown-loading'
                            : ''
                    }`}
                    showArrow
                    onDeselect={onInnerDeselect}
                    onChange={onInnerChange}
                />
                {showPlaceholder && <span className={`auth-select-placeholder ${onlyOneHideValue()}`}>{props.placeholder}</span>}
                </>
            );
        } else {
            const { suffixIcon, ...otherProps } = props || {};
            return (
                <Select
                    className={getSelectClassName()}
                    ref={selectRef}
                    {...otherProps}
                    options={renderLazyOptions()}
                    onPopupScroll={lazyLoad ? handlePopupScroll : undefined}
                    onSearch={
                        lazyLoad && props.showSearch ? handleSearch : undefined
                    }
                    filterOption={props.filterOption}
                    showSearch={lazyLoad ? true : props.showSearch}
                    dropdownRender={
                        lazyLoad ? customDropdownRender : props.dropdownRender
                    }
                    dropdownClassName={`${props?.dropdownClassName || ''} ${
                        lazyLoad ? 'auth-poppy-select-lazy-dropdown' : ''
                    }`}
                />
            );
        }
    },
);
type SelectRefType = typeof InternalAuthSelect;

interface SelectInterface extends SelectRefType {
    SECRET_COMBOBOX_MODE_DO_NOT_USE: string;
    Option: typeof Select.Option;
    OptGroup: typeof Select.OptGroup;
}

const AuthSelect = InternalAuthSelect as SelectInterface;

Select.SECRET_COMBOBOX_MODE_DO_NOT_USE = SECRET_COMBOBOX_MODE_DO_NOT_USE;
Select.Option = Option;
Select.OptGroup = OptGroup;
export { RefSelectProps, SelectProps };

export type {
    OptionProps,
    BaseOptionType,
    DefaultOptionType,
    LabeledValue,
    InternalSelectProps,
} from '@streamax/poppy/lib/select';
export default AuthSelect;
