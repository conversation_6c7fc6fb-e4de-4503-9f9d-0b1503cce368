import {
    useAsyncEffect,
    useDebounceFn,
    useInterval,
    useLatest,
} from '@streamax/hooks';
import { Tabs, Spin, Empty, StarryTree, Al<PERSON>, Button } from '@streamax/poppy';
import { Auth, i18n, performanceMeasureUtil } from '@base-app/runtime-lib';
import { assign, cloneDeep } from 'lodash';
import _ from 'lodash';
import React, {
    useState,
    useEffect,
    useRef,
    forwardRef,
    useImperativeHandle,
    useContext,
    useCallback,
} from 'react';
import FocusVehicles, { FocusVehiclesRef } from './compontents/FocusVehicles';
import DetailPanel from './DetailPanel';
import { getVehicleDetail } from '../../service/vehicle';
import { getSetting, saveSetting } from '../FleetVichileSort';
import type { SearchModuleNameType } from '@/components/MultiSearch';
import MultiSearch from '@/components/MultiSearch';
import VehicleStatusSelect from '@/components/VehicleStatusSelect';
import { ONLINE_STATE, UN_GROUP_FLEET_ID } from '@/const/vehicle';
import useOnceFn from '@/hooks/useOnceFn';
import { PageCacheContext } from '@/hooks/usePageDataCache/PageCacheProvider';
import type { Fleet } from '@/hooks/useRTData/useFleetVehicleData';
import type { Vehicle } from '@/hooks/useRTData/useRealtimeMonitoring';
import type {
    VehicleTreeType,
    VehicleDetailType,
} from '@/types/pageReuse/realtimeMonitoring';
import { getCustomJsx } from '@/utils/pageReuse';
import './index.less';
import MonitorModeButton, { ModeSelectProps } from './MonitorModeButton';
import { useResetState } from 'ahooks';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { scrollToTreeNode } from './compontents/utils';
import { getOffsetInfo } from '@/utils/commonFun';
import useRMTreeTitleStore from './useRMTreeTitleStore';
import useTreeDataStore, { getTreeDataStore } from '@/modules/vehicle-tree/data/useTreeDataManager/useTreeDataStore';
import { useRTDataStore, getRTDataStore } from '@/modules/vehicle-tree/data/realtime-data/useRTDataManager';
import FocusMonitoringTree from './compontents/FocusMonitoringTree';
import MonitoringGroupTree from './compontents/MonitoringGroupTree';
import type { TreeProps } from '@streamax/rc-tree';
import { LoadFailErrorTips } from './compontents/LoadFailErrorTips';
import { useDeviceStore, getDeviceStore } from '@/modules/device/useRTDeviceData';
import { withSafeStore, useSafeStore } from '@/modules/common/withSafeStore';
import { useTreeContainerHeight } from '@/hooks/tree/useTreeContainerHeight';
const ClassNamePrefix = 'vehicle-tree';

/**页面定制 */
export type RealtimeMonitoringShareProps = VehicleTreeType & VehicleDetailType;
export type ExtraModuleType =
    | 'normalVehicle'
    | 'focusVehicle'
    | 'normalVideoVehicle'
    | 'monitorVideoVehicle';
export type TreeNodeType = 'fleet' | 'vehicle' | 'invented-fleet' | '';
export type ModeType = 'mapMode' | 'videoMode'; //监控模式

export type VehicleTreeProProps = RealtimeMonitoringShareProps & {
    placeholder?: string;
    showOperation?: boolean; // 是否展示相关操作
    showDetail?: boolean; // 是否展示详情按钮
    hasOperation?: boolean; //车组上是否有操作按钮
    showSearch?: boolean; // 是否展示搜索框
    searchModule?: SearchModuleNameType[]; // 支持搜索的模块
    onSelect?: TreeProps['onSelect']; // 选择车辆回调
    onClickOperation?: (type: string, data: any) => void; // 点击车辆操作
    refreshTime?: number; // 定时刷新时间
    focusVehicles?: any[]; // 重点监控车辆
    // 树可视范围变动回调
    onVisibleChange: (renderList: any[], mergedData: any[]) => void;
    fleetList: Fleet[];
    loadFleets: any;
    // 数据是否初始化完成
    loaded: boolean;
    refresh: () => void;
    vehicleStateConfig: any;
    onChangeTab: (v: ExtraModuleType) => void; // 切换Tab
    extraModule?: ExtraModuleType[]; // 额外的模块
    [params: string]: any;
    simpleTree?: boolean; //流量管理车组树不需要展示信息
    showSort?: boolean; //是否要展示排序设置
    showNormalVehicleStatusFilter?: boolean; // 是需要展示筛选组件
    onFilter: (condition: Record<string, any>) => void; // 筛选回调
    condition: Record<string, any>; // 当前筛选条件
    filteredVehicleList?: Vehicle[]; // 筛选后的车辆列表数据
    // filterVehicleStatus?: string; // 当前筛选状态得stateId
    isExpandFirstGroup?: boolean; // 是否展开一级车组
    unGroupFleetId?: string; // 未分组车组id
    // 当树节点组装完毕时调用
    onTreeNodeGenerate?: () => void;
    // 模式类型
    monitorMode?: ModeType;
    onMonitorModeChange?: (monitorMode: ModeType) => void;
    onClickMonitorGroupPlay?: (
        vehicles: {
            vehicleId: string;
            fleetId: string;
            deviceIdList?: string[];
        }[],
        groupId: string,
        e: React.MouseEvent,
    ) => void;
    resetTreeStatus?: () => void;
    onOutsideOperateStatus?: ModeSelectProps['onOutsideOperateStatus'];
    onTreeMounted?: () => void;
    filterVehicleCallbacks: (focusVehicleList: Vehicle[]) => Vehicle[];
    instanceId?: string; // 实例ID
};

export interface VehicleTreeProRefProps {
    reload: () => void;
    refresh: () => void;
    setSelectedNode: (data: SetSelectNodeProps) => void;
    openDetail: (id: string, type: 'vehicle' | 'fleet') => void;
}

interface SetSelectNodeProps {
    type?: 'fleet' | 'vehicle';
    fleetId?: number | string;
    vehicleId?: number | string;
    openDetail?: boolean;
}

interface TabItemType {
    title: string;
    key: ExtraModuleType;
    content: any;
}

// 车队前缀，避免车队车辆id冲突
export const fleetPrefix = 'fleet-';

const VehicleTreePro: React.ForwardRefRenderFunction<
    VehicleTreeProRefProps,
    VehicleTreeProProps
> = (props, ref) => {
    
    /***页面定制项 */
    const {
        getVehicleTreeSort,
        getVehicleOperateButtons,
        getVehicleOperateMenu,
        getVehicleDriver,
        getVehicleDetailAlarmViewMoreDom,
        getVehicleDetailBlocks,
        getVehicleDetailDeviceButtons,
        getVehicleDetailDeviceMenu,
        getVehicleDetailAlarmButtons,
        getVehicleDetailAlarmMenu,
        onDrawerStateChange,
        getVehicleTreeDetailButton,
        getVehicleDetailTitle,
        getVehicleDetailDeviceInfoFooter,
        onOperationHover,
        getVehicleNumber,
        getVehicleSearchBlock,
        getDetailDeviceStateTitle,
        getVehicleDetailDeviceInfoExtendBlocks,
        getBaseVehicleTreeFilterBtn,
        getBaseVehicleTreeFilter,
        onVehicleDetailAlarmDetailClick,
    } = props;
    const {
        placeholder,
        onClickOperation = () => {},
        onSelect = () => {},
        showOperation = false,
        showDetail = false,
        hasOperation,
        focusVehicles = [],
        searchModule,
        fleetList,
        extraModule = [],
        onChangeTab = () => {},
        loaded,
        onlineIds,
        vehicleList,
        simpleTree,
        showNormalVehicleStatusFilter = false,
        onFilter,
        condition,
        filteredVehicleList,
        isExpandFirstGroup = false,
        unGroupFleetId = '0',
        onTreeNodeGenerate,
        monitorMode,
        onMonitorModeChange,
        onClickMonitorGroupPlay,
        onOutsideOperateStatus,
        onTreeMounted,
        // 车辆数据过滤函数，这里仅传递给重点车辆监控进行过滤，因为其数据获取不在useRealtimeMonitoring中，所以暂时这么处理一波
        filterVehicleCallbacks,
        onVisibleChange,
    } = props;

    const treeRef = useRef<any>(null);
    const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
    const [detailVisible, setDetailVisible] = useState<boolean>(false); // 显示面板信息
    const [viewDetailId, setViewDetailId] = useState<string>(''); // 查看的车辆或车队ID
    const [viewDetailInfo, setViewDetailInfo] = useState<any>(); // 存储点击车辆对应的设备信息，当设备状态变更时更新
    const latestDetailInfoRef = useLatest(viewDetailInfo); // 查看的车辆信息ref
    const [detailPanelInfoType, setDetailPanelInfoType] =
        useState<TreeNodeType>(''); // 信息面板类型
    const [isInitDataFinish, setIsInitDataFinish] = useState(false); // 初始化数据是否完成
    const [selectQueue, setSelectQueue] = useState<any[]>([]); // 待选择队列
    const [sortRule, setSortRule] = useState(getSetting());
    const [loadingFleetList, setLoadingFleetList] = useState<string[]>([]); // 正在加载的车队节点
    const multiSearchValueRef = useRef<any>(); // 存储搜索框得onChange值（选中和删除才会抛出值）
    // const [filterStatus, setFilterStatus] = useState<string | undefined>(vehicleStatus); // 监听车辆筛选状态
    const { loadFleets, refresh, vehicleStateConfig } = props;
    const { instanceId = 'default' } = props;
    const treeDataStore = getTreeDataStore(instanceId);
    const loadedKeys = treeDataStore((state) => state.loadedKeys);
    const treeData = treeDataStore((state) => state.treeNodes);
    const [activeTab, setActiveTab, resetActiveTab] =
        useResetState<ExtraModuleType>('normalVehicle');
    const [videoActiveTab, setVideoActiveTab, resetVideoActiveTab] =
        useResetState<ExtraModuleType>('normalVideoVehicle');
    
    // /动态计算容器高度
    const treeContainerRef = useRef<HTMLDivElement>(null);
    const { height: containerHeight, isInitialized } =
        useTreeContainerHeight(treeContainerRef);
    
    // 更新tree引用，以便treeStore做树定位和滚动等操作
    useEffect(() => {
        treeDataStore.getState().setTreeRef(treeRef);
        return () => {
            treeDataStore.getState().setTreeRef(null);
        };
    }, []);
    const [cacheFocusData, setCacheFocusData] = useState<Record<string, any>>(
        {},
    );
    const [cacheGroupData, setCacheGroupData] = useState<Record<string, any>>(
        {},
    );

    

    /************************ 组件数据缓存逻辑 开始 ************************/
    const { register: pageDataCacheRegister, existCache } =
        useContext(PageCacheContext);
    const cacheExpandedKeysRef = useRef<string[] | null>(null);
    const cacheLastNodeKeyInView = useRef<string>('');

    // ==== 恢复滚动位置逻辑（详情页面） 开始
    const [checkInterval, setCheckInterval] = useState<number | undefined>(
        undefined,
    );
    const scrollElementRef = useRef<any>();
    const scrollTopRef = useRef(0);
    const getScrollElement = () => {
        return document.querySelector(
            '.vehicle-tree-detail-wrapper .poppy-drawer-body',
        );
    };
    const rtDataStore = getRTDataStore(instanceId);
    const vehicleStatusList = rtDataStore((state) => state.stateCount);
    // 采用定时任务，当判断可以设置滚动位置时，完成设置并取消定时任务
    useInterval(() => {
        if (!scrollElementRef.current) {
            scrollElementRef.current = getScrollElement();
        }
        if (
            scrollElementRef.current &&
            (scrollElementRef.current as HTMLDivElement).scrollHeight
        ) {
            setTimeout(() => {
                (scrollElementRef.current as HTMLDivElement).scrollTop =
                    scrollTopRef.current;
            }, 500);
            // 设置为undefined为取消定时任务
            setCheckInterval(undefined);
        }
    }, checkInterval);
    // ==== 恢复滚动位置逻辑（详情页面） 结束
    // 是否分发缓存完成，2个情况，有缓存，在分发函数中设置为true，无缓存，通过existCache判断设置为true
    const [distributed, setDistributed] = useState(false);
    useAsyncEffect(async () => {
        if (existCache) {
            const exit = await existCache('realtimeMonitorTree');
            if (!exit) setDistributed(true);
        } else {
            setDistributed(true);
        }
    }, []);
    const collect = async () => {
        // 由于跳转回车辆监控页面时需要恢复跳转前的滚动位置，故需要搜集滚动位置（针对车辆/车组详情页面）
        let scrollTop = 0;
        const scrollElement = getScrollElement();
        if (scrollElement) {
            scrollTop = scrollElement.scrollTop;
        }

        // 搜集车组树的滚动条位置
        const treeNodeList = document.querySelectorAll(
            '.vehicle-tree-body .poppy-tree-treenode',
        );
        let cacheLastTreeNodeKey = '';
        const wrapperDom = document.querySelector(
            '.vehicle-tree-body .poppy-tree-list-holder',
        );
        if (treeNodeList.length && wrapperDom) {
            let lastNodeInView;
            const scrollTop = wrapperDom.scrollTop;
            const { height } = wrapperDom.getBoundingClientRect();
            // 寻找目前在可视区域内的最后一个树节点
            for (let i = treeNodeList.length - 1; i >= 0; i--) {
                const offsetTop = (treeNodeList[i] as HTMLDivElement).offsetTop;
                if (offsetTop - scrollTop <= height) {
                    lastNodeInView = treeNodeList[i];
                    break;
                }
            }
            // 记录这个树节点的key，用于恢复缓存数据时，使滚动条滚动到这个节点
            if (lastNodeInView) {
                const child =
                    lastNodeInView.querySelector('.tree-node-content');
                const key = child?.getAttribute('data-treekey');
                cacheLastTreeNodeKey = key || '';
            }
        }
        return {
            cacheVehicleTreeActiveTab: activeTab,
            cacheVideoVehicleTreeActiveTab: videoActiveTab,
            cacheDetailPanelInfoType: detailPanelInfoType,
            cacheViewDetailId: viewDetailId,
            cacheDetailVisible: detailVisible,
            cacheTreeDetailScrollTop: scrollTop,
            cacheExpandedKeys: expandedKeys,
            cacheSelectedKeys: selectedKeys,
            cacheLastTreeNodeKey,
        };
    };
    const distribute = async (data: Record<string, any>) => {
        const {
            cacheVehicleTreeActiveTab,
            cacheVideoVehicleTreeActiveTab,
            cacheDetailPanelInfoType,
            cacheDetailVisible,
            cacheViewDetailId,
            cacheTreeDetailScrollTop,
            cacheExpandedKeys,
            cacheSelectedKeys,
            cacheLastTreeNodeKey,
        } = data || {};
        setActiveTab(cacheVehicleTreeActiveTab);
        setVideoActiveTab(cacheVideoVehicleTreeActiveTab);
        setViewDetailId(cacheViewDetailId);
        setDetailPanelInfoType(cacheDetailPanelInfoType);
        setDetailVisible(cacheDetailVisible);
        setSelectedKeys(cacheSelectedKeys);
        cacheExpandedKeysRef.current = cacheExpandedKeys;
        // 当跳转前存在滚动时，需要恢复滚动的位置，由于子元素是异步加载数据
        // 故必须要等到子元素数据加载完，将滚动元素的滚动高度撑起来后再设置滚动位置才有效
        // 此处由于子元素分布散乱，且无法判断何时数据加载渲染完成，故只能设置定时任务定时去检查状态
        if (cacheTreeDetailScrollTop) {
            scrollTopRef.current = cacheTreeDetailScrollTop;
            setCheckInterval(1000);
        }
        cacheLastNodeKeyInView.current = cacheLastTreeNodeKey;
        setDistributed(true);
    };

    const distributeFocus = async (data) => {
        setCacheFocusData(data);
    };

    const distributeGroup = async (data) => {
        setCacheGroupData(data);
    };

    /**
     * @description 背景：车辆详情页面目前是使用查询接口，设备状态不会实时变更
     * @description 实现方式：
     * @description 1、保存点击车辆详情时车辆数据
     * @description 2、通过监听传入数据变更时更新数据
     */
    useEffect(() => {
        if (detailPanelInfoType !== 'vehicle') return;
        if (!vehicleList.length) return;
        const findVehicle = vehicleList.find(
            (item: any) => item.vId === viewDetailId?.split('-')[2],
        );
        if (!findVehicle) return;
        if (!latestDetailInfoRef.current) {
            setViewDetailInfo(findVehicle.deviceList);
            latestDetailInfoRef.current = findVehicle;
            return;
        }
        if (
            !_.isEqual(
                latestDetailInfoRef.current.deviceList,
                findVehicle.deviceList,
            )
        ) {
            setViewDetailInfo(findVehicle.deviceList);
            latestDetailInfoRef.current = findVehicle;
        }
    }, [viewDetailId, detailPanelInfoType, vehicleList]);

    // 主要用于在从车辆监控页面跳转到其他页面后，再返回到车辆监控页面时，可以恢复跳转前的数据
    // collect是用于收集这个组件中需要缓存的数据
    // distribute是当返回此页面时，将缓存的数据分发至此组件，此组件需要自行用这些数据做恢复数据的处理
    useEffect(() => {
        pageDataCacheRegister?.('realtimeMonitorTree', { collect, distribute });
        // @ts-ignore
        pageDataCacheRegister?.('realtimeMonitorFocusTree', {
            distribute: distributeFocus,
        });
        // @ts-ignore
        pageDataCacheRegister?.('realtimeMonitoringGroupTree', {
            distribute: distributeGroup,
        });
    }, [
        activeTab,
        videoActiveTab,
        detailPanelInfoType,
        viewDetailId,
        detailVisible,
        selectedKeys,
        expandedKeys,
    ]);

    // 恢复tree滚动条的位置
    useEffect(() => {
        if (cacheLastNodeKeyInView.current && loaded) {
            setTimeout(() => {
                treeRef.current?.scrollTo({
                    key: cacheLastNodeKeyInView.current,
                    align: 'bottom',
                    offset: 0,
                });
                cacheLastNodeKeyInView.current = '';
            }, 1000);
        }
    }, [loaded, cacheLastNodeKeyInView.current]);

    /************************ 组件数据缓存逻辑 结束 ************************/

    useEffect(() => {
        if (treeData.length > 0 && distributed) {
            setIsInitDataFinish(true);
        }
    }, [treeData, distributed]);
    const setFirstExpand = useOnceFn(async (treeDataNode) => {
        // 展开一级车组
        // 如果有缓存数据, 以缓存的为主
        let expandedKeys = [];
        if (cacheExpandedKeysRef.current) {
            expandedKeys = cacheExpandedKeysRef.current;
            cacheExpandedKeysRef.current = null;
        } else {
            expandedKeys = treeDataNode
                .filter((node) => !node.parentKey)
                .map((item) => item.key);
        }
        if (expandedKeys.length > 0) {
            // 最后执行异步操作，防止异步操作阻塞时，展开车组不成功
            setExpandedKeys(expandedKeys);
            await loadFleets(
                expandedKeys.map((key) => key.replace('fleet-', '')),
            );
        }
    });
    useAsyncEffect(async () => {
        if (isExpandFirstGroup && isInitDataFinish && treeData.length > 0) {
            await setFirstExpand(treeData);
        }
        // 模式切换需要重新恢复展开状态
    }, [isExpandFirstGroup, isInitDataFinish]);

    useEffect(() => {
        if (isInitDataFinish && selectQueue.length > 0) {
            selectQueue.forEach((item: any) => {
                setSelectedNode(item);
            });
        }
    }, [isInitDataFinish]);


    const resetTreeExpand = async () => {
        if (!isExpandFirstGroup) {
            setExpandedKeys([]);
        }
        const newExpandKeys = treeData
            ?.filter((node) => !node.parentKey)
            .map((item) => item.key);
        if (newExpandKeys.length > 0) {
            await loadFleets(
                expandedKeys.map((key) => key?.replace('fleet-', '')),
            );
            setExpandedKeys(newExpandKeys);
        }
    };

    // 切换模式重置车组树的内部状态
    const resetTreeStatus = () => {
        // 模式切换清空选中车辆
        setSelectedKeys([]);
        // 切换模式重置展开操作
        resetTreeExpand();
        resetVideoActiveTab();
        resetActiveTab();
    };

    useImperativeHandle(ref, () => ({
        reload,
        refresh: refreshData,
        setSelectedNode,
        openDetail,
        closeDetail: () => setDetailVisible(false),
        treeLinkScroll,
        resetTreeStatus,
        setVideoActiveTab
    }));
    // 当前打开车辆的详情
    const [openedVehcile, setOpenedVehcile] = useState<Vehicle | undefined>();
    const hasGps = (gps: any) => {
        if (gps.gpsTime && gps.speed !== null && gps.speed !== undefined) {
            return true;
        }
        return false;
    };
    useEffect(() => {
        if (detailVisible && viewDetailId) {
            const vehicleId = viewDetailId.split('-')[2];
            let vehicleItem = filteredVehicleList?.find(
                (item) => item.vId === vehicleId,
            );
            vehicleItem =
                vehicleItem && rtDataStore.getState().rtDataMap[vehicleId];
            // 在线才会更新 fix: 97879
            if (
                (vehicleItem?.vStates || []).includes(ONLINE_STATE) &&
                hasGps(vehicleItem?.gps || {})
            ) {
                setOpenedVehcile(vehicleItem);
            }
        } else {
            setOpenedVehcile(undefined);
        }
    }, [detailVisible, filteredVehicleList]);

    // 打开详情
    function openDetail(id: string, type: TreeNodeType): void {
        setViewDetailId(id);
        setDetailPanelInfoType(type);
        setDetailVisible(true);
    }

    // 设置选中节点
    function setSelectedNode(data: SetSelectNodeProps): void {
        if (!isInitDataFinish) {
            setSelectQueue((list: any[]) => {
                list.push(data);
                return list;
            });
            return;
        }
        const {
            type = 'vehicle',
            fleetId,
            vehicleId,
            openDetail = false,
        } = data;
        if (type === 'fleet') {
            selectFleetInTree({
                fleetId,
            });
        } else {
            selectVehicleInTree({
                vehicleId,
                fleetList: [{ fleetId }],
            });
        }
        openDetail && setDetailVisible(true);
    }

    // 重新加载
    function reload() {}

    // 定位到指定车队或车辆
    function positionVehicle(key: string) {
        scrollToTreeNode(key);
        setSelectedKeys([key]);
        setTimeout(() => {
            treeRef.current?.scrollTo({ key, offset: 30 });
        }, 50);
    }
    // 选择地图中的车，联动选中车辆树
    async function treeLinkScroll(data: any) {
        const { vId, fId } = data;
        const fleetId = fId || unGroupFleetId;
        const key = `vehicle-${fleetId}-${vId}`;
        const fleet = fleetList.find((i) => i.fId === fleetId);
        // 未分组的车辆不需要加载车队、车辆数据
        fleet && (await loadFleets(fleet?.path));
        setExpandedKeys((expandedKeys: any[]) => {
            const newKeys = expandedKeys.concat(
                fleet?.path.map((fId) => `fleet-${fId}`),
            );
            return [...new Set(newKeys)];
        });

        onSelect?.([key], null);
        positionVehicle(key);
    }

    // // 刷新状态数据
    async function refreshData() {
        // loadFleets(loadedKeys);
    }

    // 查询车辆详情
    async function fetchVehicleDetail(vehicleId: string | number) {
        const data =
            (await getVehicleDetail({
                vehicleId,
                fields: 'fleet',
            })) || {};
        return data;
    }

    // 加载车队下的车辆数据
    async function loadVehicleData(node: any, callback?: any) {
        return new Promise<void>((resolve) => {
            const { key, children } = node;
            const id = key.replace(fleetPrefix, '');
            if ((children && children.length > 0) || loadedKeys.includes(key)) {
                resolve();
                return;
            }
            loadFleets([id]).then(() => {
                resolve();
            });
        });
    }

    // 选择搜索结果中的车辆后，在树中进行选中操作
    async function selectVehicleInTree(data: any) {
        const { vehicleId, fleetList: dataFleetList = [] } = data;
        const fleetId = dataFleetList[0]?.fleetId || unGroupFleetId;
        const key = `vehicle-${fleetId}-${vehicleId}`;
        const fleet = fleetList.find((i) => i.fId === fleetId);
        // 未分组的车辆不需要加载车队、车辆数据
        setExpandedKeys((expandedKeys: any[]) => {
            const newKeys = expandedKeys.concat(
                fleet?.path.map((fId) => `fleet-${fId}`),
            );
            return [...new Set(newKeys)];
        });
        fleet && (await loadFleets(fleet?.path));
        onSelect?.([key], null);
        positionVehicle(key);
    }
    // 设置选择车队
    async function selectFleetInTree(data: any, position = true) {
        // UN_GROUP_FLEET_ID 当车辆属于未分组时，点击搜索车辆，此时data为null。
        const { fleetId = UN_GROUP_FLEET_ID } = data || {};
        const key = `fleet-${fleetId}`;
        const fleet = fleetList.find((i) => i.fId === fleetId);
        // 有可能查询出的车队是在页面进入后新增加的。此时  fleetList 里面没有此车队。此情况暂不处理
        if (fleet) {
            await loadFleets(fleet?.path);
            setExpandedKeys((expandedKeys: any[]) => {
                const newKeys = expandedKeys.concat(
                    fleet?.path.map((fId) => `fleet-${fId}`),
                );
                return [...new Set(newKeys)];
            });
            setSelectedKeys([key]);
            onSelect?.([key], null);
            position && positionVehicle(key);
        }
    }

    // 选择搜索结果中的司机
    async function selectVehicleInTreeByDriver(data: any) {
        const { vehicleList = [] } = data;
        if (vehicleList[0]) {
            const { vehicleId } = vehicleList[0];
            selectVehicleInTree(await fetchVehicleDetail(vehicleId));
        }
    }
    const lockTargetFromTree = async (data: any) => {
        performanceMeasureUtil.tree.start('search');
        const fId = data?.fleetList?.[0]?.fleetId || unGroupFleetId;
        const findedVehicle = await treeDataStore
            .getState()
            .loadFleetsManager.selectVehicleItem(data?.vehicleId, fId);
        if (findedVehicle) {
            await selectVehicleInTree(data);
        } else {
            await selectFleetInTree(data?.fleetList[0]);
        }
        performanceMeasureUtil.tree.start('search');
    };

    // 异常场景处理: 在搜索框有值的情况下（tree中对应节点会处于选中状态），进行过滤
    // 尝试在过滤后定位到该节点，如果该节点不存在，则定位到其父级上
    const lockTargetFromTreeRef = useRef(lockTargetFromTree);
    lockTargetFromTreeRef.current = lockTargetFromTree;
    useEffect(() => {
        setTimeout(() => {
            if (
                multiSearchValueRef.current &&
                Object.keys(condition).length > 0
            ) {
                lockTargetFromTreeRef.current(multiSearchValueRef.current);
            }
        }, 50);
    }, [condition]);

    // 选择搜索结果
    async function onSelectSearchResult(
        e: React.MouseEvent,
        module: SearchModuleNameType,
        data: any,
    ) {
        switch (module) {
            case 'fleet':
                selectFleetInTree(data);
                break;
            case 'vehicle':
                lockTargetFromTree(data);
                break;
            case 'driver':
                selectVehicleInTreeByDriver(data);
                break;
            case 'device':
                selectVehicleInTree(await fetchVehicleDetail(data.vehicleId));
                break;
            default:
                break;
        }
    }

    const openDetailRef = useRef(openDetail);
    openDetailRef.current = openDetail;
    // 点击查看详情
    const handelViewDetail = useCallback(
        (e: any, key: string, type: TreeNodeType) => {
            e.stopPropagation();
            e.preventDefault();
            setDetailVisible(true);
            // setSelectedKeys([key]);
            setDetailPanelInfoType(type);
            openDetailRef.current(key, type);
        },
        [],
    );

    // 把变动数据同步到store，GenerateTreeTitle 再从store获取渲染
    const { run: setStoreDebounce } = useDebounceFn(
        () => {
            useRMTreeTitleStore.getState().updateState({
                monitorMode,
                showDetail,
                selectedKeys,
                showOperation,
                // 默认值为true
                hasOperation: hasOperation === undefined ? true : hasOperation,
                focusVehicles,
                simpleTree,
                onClickOperation,
                onClickMonitorGroupPlay,
                onClickDetail: handelViewDetail,
                // 定制属性
                getVehicleOperateButtons,
                getVehicleTreeDetailButton,
                getVehicleOperateMenu,
                getVehicleDriver,
                getVehicleNumber,
            });
        },
        { wait: 100 },
    );
    useEffect(() => {
        setStoreDebounce();
    }, [
        monitorMode,
        showDetail,
        selectedKeys,
        showOperation,
        hasOperation,
        focusVehicles,
        simpleTree,
        onClickOperation,
        onClickMonitorGroupPlay,
        handelViewDetail,
        getVehicleOperateButtons,
        getVehicleTreeDetailButton,
        getVehicleOperateMenu,
        getVehicleDriver,
        getVehicleNumber,
    ]);
    useSafeStore(
        useRMTreeTitleStore,
        useRMTreeTitleStore.getState().resetStore,
    );
    const removeExpandedKeys = (val: string, arr: string[]) => {
        const index = arr.indexOf(val);
        if (index > -1) {
            arr.splice(index, 1);
        }
        return arr;
    };

    const { run: selectTreeNode } = useDebounceFn(
        async (selectedKeys, e) => {
             performanceMeasureUtil.tree.start('normalTree.onSelect');
            if (selectedKeys[0] && selectedKeys[0].startsWith(fleetPrefix)) {
                // 添加加载动画
                setLoadingFleetList((list: string[]) => {
                    !list.includes(selectedKeys[0]) &&
                        list.push(selectedKeys[0]);
                    return list;
                });
                await selectFleetInTree(
                    { fleetId: selectedKeys[0].split('-')[1] },
                    false,
                );
            } else {
                setSelectedKeys(selectedKeys);
            }
            if (e.node.key && e.node.key.startsWith(fleetPrefix)) {
                // 取消选中，收起车组
                if (!e.selected || expandedKeys.includes(e.node.key)) {
                    const tempData = [...new Set(cloneDeep(expandedKeys))];
                    const newExpandedKeys = removeExpandedKeys(
                        e.node.key,
                        tempData,
                    );
                    setExpandedKeys(newExpandedKeys);
                }
                // 选中展开车组
                if (!expandedKeys.includes(e.node.key)) {
                    setExpandedKeys((expandedKeys: any[]) => {
                        return [...new Set(expandedKeys.concat([e.node.key]))];
                    });
                }
            }
            onSelect(selectedKeys, e);
            performanceMeasureUtil.tree.end('normalTree.onSelect');
        },
        { wait: 80 },
    );

    const selectTreeNodeRef = useRef(selectTreeNode);
    selectTreeNodeRef.current = selectTreeNode;
    // 选择树节点
    const onSelectTreeNode = useCallback(
        async (selectedKeys: string[], e: any) => {
            // 如果选中的是车队，则需要展开此车队
            selectTreeNodeRef.current(selectedKeys, e);
        },
        [],
    );

    const sortChange = (values: any) => {
        setSortRule(values);
        refresh();
    };

    const getMultiSearchValue = (value: any) => {
        if (value && value.module === 'vehicle') {
            multiSearchValueRef.current = value.data;
        } else {
            multiSearchValueRef.current = undefined;
        }
    };
    const renderTree = () => {
        // console.error('debugId=127702 -------------StarryTree',treeData, new Date().getTime());
        return treeData.length > 0 ? (
            <StarryTree
                // motion={{}}  // 禁用动画
                expandedKeys={expandedKeys}
                onExpand={(keys) => setExpandedKeys(keys)}
                onVisibleChange={onVisibleChange}
                loadData={loadVehicleData}
                ref={treeRef}
                // @ts-ignore
                onSelect={onSelectTreeNode}
                loadedKeys={loadedKeys}
                selectedKeys={selectedKeys}
                height={containerHeight}
                treeData={treeData}
            />
        ) : (
            <Empty />
        );
    };
    const searchEnable = treeDataStore((state) => state.loaded);
    const renderSearch = ({
        cacheComponentName,
        key,
    }: {
        key: string;
        cacheComponentName: string;
    }) => {
        return (
            <div
                key={key}
                className={`${ClassNamePrefix}-search ${
                    showNormalVehicleStatusFilter ? 'filter-flex' : ''
                } `}
            >
                {getCustomJsx(
                    getVehicleSearchBlock,
                    [
                        <MultiSearch
                            placeholder={placeholder}
                            mountedLoad={false}
                            enable={searchEnable}
                            // @ts-ignore
                            onSelect={onSelectSearchResult}
                            searchModule={searchModule}
                            simpleTree={simpleTree}
                            onChange={getMultiSearchValue}
                            cacheComponentName={cacheComponentName}
                        />,
                    ],
                    {
                        placeholder,
                        simpleTree,
                        onSelect: onSelectSearchResult,
                        searchModule,
                        onChange: getMultiSearchValue,
                    },
                )}

                {showNormalVehicleStatusFilter
                    ? getCustomJsx(
                          getBaseVehicleTreeFilterBtn,
                          [
                              <VehicleStatusSelect
                                  key="baseVehicleTreeFilterBtn"
                                  onChange={onFilter}
                                  vehicleStatusList={vehicleStatusList}
                                  value={condition}
                                  customContent={(defaultContent) => {
                                      return getCustomJsx(
                                          getBaseVehicleTreeFilter,
                                          [defaultContent],
                                          {
                                              vehicleList,
                                              vehicleStatusList,
                                              onFilter,
                                              condition,
                                          },
                                      );
                                  }}
                              />,
                          ],
                          {
                              vehicleList,
                              vehicleStatusList,
                              condition, // 当前筛选结果
                              handleFilter: onFilter,
                          },
                      )
                    : null}
            </div>
        );
    };

    const deviceStore = getDeviceStore(instanceId);
    const stateLoadFailed = deviceStore((state) => state.stateLoadFailed);

    // 常规车辆模块
    const normalVehicle = (params: {
        key: string;
        cacheComponentName: string;
    }) => {
        const { key, cacheComponentName } = params || {};

        return (
            <div className={`${ClassNamePrefix}-index`} key={key}>
                {renderSearch({
                    cacheComponentName,
                    key: 'normalVehicleSearch',
                })}
                {stateLoadFailed && <LoadFailErrorTips />}
                <div className={`${ClassNamePrefix}-body`} ref={treeContainerRef}>
                    {loaded && isInitialized ? (
                        renderTree()
                    ) : (
                        <Spin style={{ width: '100%', marginTop: '20px' }} />
                    )}
                </div>
            </div>
        );
    };
    // 兼容老的排序设置，把排序设置为 置顶+名称升序，方便其他模块还是从原来的位置读取排序设置
    useEffect(() => {
        saveSetting(JSON.parse('{"top":true,"sort":"title","order":"asc"}'));
    }, []);
    const vehicleModeSwitch = () => {
        return (
            <Auth code="@base:@page:realtime.monitoring@action:mode.switch">
                <div className={`${ClassNamePrefix}-mode-switch`}>
                    <MonitorModeButton
                        onOutsideOperateStatus={onOutsideOperateStatus}
                        onMonitorModeChange={onMonitorModeChange}
                        monitorMode={monitorMode}
                    />
                </div>
            </Auth>
        );
    };

    const mapTabList: TabItemType[] = [
        {
            title: i18n.t('name', '常规车辆'),
            key: 'normalVehicle',
            content: normalVehicle({
                key: 'normalVehicle',
                cacheComponentName: 'realtimeMonitorTreeSearchInput',
            }),
        },
        {
            title: i18n.t('name', '重点监控'),
            key: 'focusVehicle',
            content: loaded ? (
                <FocusMonitoringTree
                    cacheData={cacheFocusData}
                    onSelect={onSelect}
                    filterVehicleCallbacks={filterVehicleCallbacks}
                />
            ) : (
                <Spin style={{ width: '100%', marginTop: '20px' }} />
            ),
        },
    ];
    window.hideTree && mapTabList.splice(0, 1);
    window.hideFocusTree && mapTabList.splice(1, 1);
    window.hideAllTree && (mapTabList.length = 0);

    const videoTabList: TabItemType[] = [
        {
            title: i18n.t('name', '常规播放'),
            key: 'normalVideoVehicle',
            content: normalVehicle({
                key: 'normalVideoVehicle',
                cacheComponentName: 'normalTreeVideoSearchInput',
            }),
        },
        {
            title: i18n.t('name', '监控组播放'),
            key: 'monitorVideoVehicle',
            content: loaded ? (
                <MonitoringGroupTree
                    cacheData={cacheGroupData}
                    onSelect={onSelect}
                    onFilter={onFilter}
                    condition={condition}
                    filterVehicleCallbacks={filterVehicleCallbacks}
                    showNormalVehicleStatusFilter={
                        showNormalVehicleStatusFilter
                    }
                    getBaseVehicleTreeFilterBtn={getBaseVehicleTreeFilterBtn}
                    getBaseVehicleTreeFilter={getBaseVehicleTreeFilter}
                />
            ) : (
                <Spin style={{ width: '100%', marginTop: '20px' }} />
            ),
        },
    ];

    const mapModeTree = () => {
        return (
            <>
                {extraModule.length > 0 ? (
                    <Tabs
                        destroyInactiveTabPane
                        className={`${ClassNamePrefix}-tab-wrapper`}
                        // id={`${ClassNamePrefix}-tab-wrapper`}
                        onChange={(tab) => {
                            setActiveTab(tab as ExtraModuleType);
                            onChangeTab(tab as ExtraModuleType);
                        }}
                        activeKey={activeTab}
                    >
                        {mapTabList.map((tab) => {
                            const { key, content, title } = tab;
                            return extraModule.includes(key) ? (
                                <Tabs.TabPane
                                    tab={
                                        <OverflowEllipsisContainer>
                                            {title}
                                        </OverflowEllipsisContainer>
                                    }
                                    key={key}
                                    forceRender={true}
                                >
                                    {content}
                                </Tabs.TabPane>
                            ) : null;
                        })}
                    </Tabs>
                ) : (
                    <div
                        className={`${ClassNamePrefix}-tab-wrapper ${ClassNamePrefix}-no-tab`}
                    >
                        {normalVehicle({
                            key: 'normalVehicle',
                            cacheComponentName:
                                'realtimeMonitorTreeSearchInput',
                        })}
                    </div>
                )}
            </>
        );
    };

    const videoModeTree = () => {
        return (
            <>
                {extraModule.length > 0 ? (
                    <Tabs
                        destroyInactiveTabPane
                        className={`${ClassNamePrefix}-tab-wrapper`}
                        // id={`${ClassNamePrefix}-tab-wrapper`}
                        onChange={(tab) => {
                            setVideoActiveTab(tab as ExtraModuleType);
                            onChangeTab(tab as ExtraModuleType);
                        }}
                        activeKey={videoActiveTab}
                    >
                        {videoTabList.map((tab) => {
                            const { key, content, title } = tab;
                            return extraModule.includes(key) ? (
                                <Tabs.TabPane
                                    tab={
                                        <OverflowEllipsisContainer>
                                            {title}
                                        </OverflowEllipsisContainer>
                                    }
                                    key={key}
                                    forceRender={true}
                                >
                                    {content}
                                </Tabs.TabPane>
                            ) : null;
                        })}
                    </Tabs>
                ) : (
                    <div
                        className={`${ClassNamePrefix}-tab-wrapper ${ClassNamePrefix}-no-tab`}
                    >
                        {normalVehicle({
                            key: 'normalVehicle',
                            cacheComponentName:
                                'realtimeMonitorTreeSearchInput',
                        })}
                    </div>
                )}
            </>
        );
    };
    return (
        <div className={ClassNamePrefix}>
            {/* 如果显示多个模块，则使用tabs的显示方式 */}
            {/* 地图模式树和视频模式树 */}
            {monitorMode === 'videoMode' ? videoModeTree() : mapModeTree()}
            <div className={`${ClassNamePrefix}-tab-right`}>
                {/* 模式切换 */}
                {monitorMode && vehicleModeSwitch()}
            </div>
            {detailVisible && (
                <div className={`${ClassNamePrefix}-detail-wrapper`}>
                    <DetailPanel
                        monitorMode={monitorMode}
                        infoType={detailPanelInfoType}
                        vehicleStateConfig={vehicleStateConfig}
                        onClose={() => setDetailVisible(false)}
                        selectedKey={viewDetailId}
                        detailDevices={viewDetailInfo}
                        vehicleDetail={openedVehcile}
                        visible={detailVisible}
                        onClickOperation={onClickOperation}
                        getVehicleDetailAlarmViewMoreDom={getVehicleDetailAlarmViewMoreDom}
                        getVehicleDetailBlocks={getVehicleDetailBlocks}
                        getVehicleDetailDeviceButtons={
                            getVehicleDetailDeviceButtons
                        }
                        getVehicleDetailDeviceMenu={getVehicleDetailDeviceMenu}
                        getVehicleDetailAlarmButtons={
                            getVehicleDetailAlarmButtons
                        }
                        getVehicleDetailAlarmMenu={getVehicleDetailAlarmMenu}
                        onDrawerStateChange={onDrawerStateChange}
                        getVehicleDetailTitle={getVehicleDetailTitle}
                        onOperationHover={onOperationHover}
                        getDetailDeviceStateTitle={getDetailDeviceStateTitle}
                        getVehicleDetailDeviceInfoExtendBlocks={
                            getVehicleDetailDeviceInfoExtendBlocks
                        }
                        getVehicleDetailDeviceInfoFooter={
                            getVehicleDetailDeviceInfoFooter
                        }
                        onVehicleDetailAlarmDetailClick={
                            onVehicleDetailAlarmDetailClick
                        }
                    />
                </div>
            )}
        </div>
    );
};

const VehicleTreeProWrapper = forwardRef<
    VehicleTreeProRefProps,
    VehicleTreeProProps
>(VehicleTreePro) as (
    props: React.PropsWithChildren<VehicleTreeProProps> & {
        ref?: React.Ref<VehicleTreeProRefProps>;
    },
) => React.ReactElement;

export default VehicleTreeProWrapper;
