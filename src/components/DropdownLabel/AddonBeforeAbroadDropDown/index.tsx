import { Select } from "@streamax/poppy"
import { IMenuOption } from "@/components/DropdownLabel";
import { DropDownProps } from "antd/lib/dropdown/dropdown";



const { Option } = Select;

interface DropdownLabelProps{
    menuOptions: IMenuOption[],
    onMenuSelect: (value: string) => void,
    selectKey: string,
    dropdownConfig?: any
}
export const AddonBeforeAbroadDropDown = (props: DropdownLabelProps)=>{
    const  {menuOptions , onMenuSelect, selectKey, dropdownConfig } = props;


    const handleSelect = ( key : any) => {
        onMenuSelect(key);
    };

    return (
        <Select
            className={'addon-before-abroad-select'}
            dropdownMatchSelectWidth={false}
            value={selectKey}
            onChange={(type) => handleSelect(type)} style={{ width: '100px' }}
            { ...dropdownConfig }
        >
            {
                menuOptions.map((menuOption, i) => <Option value={menuOption.value}>{menuOption.label}</Option>)
            }
        </Select>
    )
}
