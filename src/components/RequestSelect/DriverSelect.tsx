/*
 * @LastEditTime: 2025-05-17 18:07:56
 */
import { i18n } from '@base-app/runtime-lib';
import SelectWithPage from './components/SelectWithPage';
import { postQueryDriverList } from '@/service/driver';
import { useEffect, useState } from 'react';
type Props = Record<string, any>;
export default ({ valueItem = 'driverId', ...reset }: Props) => {
    const { initOption } = reset;
    const [init, setInit] = useState(true);
    useEffect(() => {
        setInit(true);
    }, [initOption]);
    const getList = async (driver?: string) => {
        const searchText = driver?.trim() || '';
        try {
            const data = await postQueryDriverList({
                companyIds: { includeSubFleet: 0 },
                includeSubFleet: 0,
                page: 1,
                pageSize: 1e9,
                fields: 'fleet',
                needDriverPicture: false,
                driverName: init || !searchText ? '' : searchText,
            });
            const options =
                data?.list.map((item: Props) => {
                    const { driverName = '' } = item;
                    return {
                        value: item[valueItem],
                        label: `${driverName}`,
                    };
                }) || [];
            setInit(false);
            return options || [];
        } catch {
            return [];
        }
    };
    return (
        <SelectWithPage
            // @ts-ignore
            getOptions={getList}
            placeholder={i18n.t('message', '请输入司机姓名')}
            {...reset}
        />
    );
};
