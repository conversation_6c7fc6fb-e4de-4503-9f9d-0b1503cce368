import { FileChannelAuthEnum, noChannelAuth } from '@/runtime-lib/utils/commonFun';
import { DisplayAuthEnum } from '@/runtime-lib/utils/evidenceFormatData';
import { groupBy } from 'lodash';

export enum FileType {
    'image' = 9,
    'video' = 1,
    'h264' = 13,
}

enum EvidenceTypeEnum {
    alarmEvidence = 1, // 报警证据
    videoEdit = 2, //设备视频剪辑
    facePicture = 3, //人脸图片
    cardPicture = 4, //刷卡图片
    serviceEdit = 5, //人脸图片
    serviceUpload = 6, //人脸图片
    alarmPicture = 7, //报警事件图片
    videoRecord = 8, //边看边录
    platformAlarm = 9, //平台报警
    historyCapturePicture = 10, //历史抓拍图片
    timedSnapshotPicture = 11 //定时抓拍
}
// 1,9表示报警证据类型，其余表示视频类型
const evidenceTypes = [EvidenceTypeEnum.alarmEvidence, EvidenceTypeEnum.platformAlarm];
export interface FileItem {
    channelNo: number; // 通道号
    endTime: number; // 文件结束时间，时间戳
    startTime: number; // 文件开始时间，时间戳
    streamType: number; // 文件码流类型
    fileId: string; // 文件id
    fileSize: number; // 文件大小，单位byte
    pickFrame: number; // 是否抽帧视频；0-不抽帧，1-抽帧，只对视频/264有效
    url: string; // 证据下载链接，fields.file_url存在时返回
    fileUuid: string; // S17文件唯一uuid
    fileType: FileType; // 文件类型，1-视频文件，2-GPS文件，3-告警信息，4-ACC信息，9-图片文件
    mosaicType?: number;
    videoTime?: number
}

export interface GetVideoResult {
    videoList: FileItem[]; // 排序后的视频文件列表
    videoPriority: number[]; // 视频文件的通道优先级
}

/**
 * 计算视频文件的顺序
 * @param fileVideoList 要计算的视频文件列表
 * @param channelPriority 通道优先级
 * @param sortByH264 是否按H264优先排序
 * @returns 按通道优先级排序后文件列表和视频文件通道优先级
 */
const baseCalcVideoList = (
    fileVideoList: FileItem[],
    channelPriority: number[],
    sortByH264: boolean,
) => {
    const groupFileListByChannel = groupBy(fileVideoList, 'channelNo');
    // 视频文件计算后的通道优先级顺序
    const videoPriority: number[] = [];
    // 按通道优先级reduce计算排序fileList在通道优先级中涉及的通道
    const channelPriorityFileList = (channelPriority || [])?.reduce((prev: FileItem[], current) => {
        const currentChannelFileList = groupFileListByChannel[current] || [];
        // 按H264优先排序
        if (sortByH264) {
            currentChannelFileList.sort((a) => (a.fileType === FileType.h264 ? -1 : 1));
        }
        if (currentChannelFileList.length) {
            videoPriority.push(current);
        }
        return prev.concat(currentChannelFileList);
    }, []);
    // 过滤出不在通道优先级中的通道，按通道号从小到大排序
    const otherFileList: FileItem[] = [];
    Object.keys(groupFileListByChannel)
        .sort((a, b) => Number(a) - Number(b))
        .forEach((channel) => {
            if (!channelPriority?.includes(+channel)) {
                const otherChannelFileList = groupFileListByChannel[channel] || [];
                if (sortByH264) {
                    otherChannelFileList.sort((a) => (a.fileType === FileType.h264 ? -1 : 1));
                }
                if (otherChannelFileList.length) {
                    videoPriority.push(+channel);
                }
                otherFileList.push(...otherChannelFileList);
            }
        });
    return {
        videoList: channelPriorityFileList.concat(otherFileList),
        videoPriority,
    };
};
/**
 * 获取按通道优先级排序后的全部H264、MP4视频文件
 * @param fileList 证据文件列表
 * @param channelPriority 通道优先级，[1, 2, 3]
 * @returns 按通道优先级排序后的H264、MP4文件列表和视频文件通道优先级
 */
export const getAllVideoList = (
    fileList: FileItem[],
    channelPriority: number[],
): GetVideoResult => {
    // 过滤H264和MP4文件
    const fileVideoList = fileList
        .filter((i) => (i.fileType === FileType.video || i.fileType === FileType.h264) && i.url)
        .sort((a, b) => Number(a.startTime) - Number(b.startTime));
    // 按通道优先级计算H264和MP4文件
    const { videoList, videoPriority } = baseCalcVideoList(fileVideoList, channelPriority, true);
    return {
        videoList,
        videoPriority,
    };
};

/**
 * 获取按通道优先级排序后全部H264视频文件
 * @param fileList 证据文件列表
 * @param channelPriority 通道优先级，[1, 2, 3]
 * @returns 按通道优先级聚合后的H264文件列表和视频文件通道优先级
 */
export const getH264VideoList = (
    fileList: FileItem[],
    channelPriority: number[],
): GetVideoResult => {
    // 过滤H264文件
    const fileVideoList = fileList
        .filter((i) => i.fileType === FileType.h264 && i.url)
        .sort((a, b) => Number(a.startTime) - Number(b.startTime));
    // 按通道优先级计算H264文件
    const { videoList, videoPriority } = baseCalcVideoList(fileVideoList, channelPriority, false);
    return {
        videoList,
        videoPriority,
    };
};

/**
 * 获取按通道优先级排序后的全部MP4视频文件
 * @param fileList 证据文件列表
 * @param channelPriority 通道优先级，[1, 2, 3]
 * @returns 按通道优先级聚合后的MP4文件列表和视频文件通道优先级
 */
export const getMP4VideoList = (
    fileList: FileItem[],
    channelPriority: number[],
): GetVideoResult => {
    // 过滤MP4文件
    const fileVideoList = fileList
        .filter((i) => i.fileType === FileType.video && i.url)
        .sort((a, b) => Number(a.startTime) - Number(b.startTime));
    // 按通道优先级计算MP4文件
    const { videoList, videoPriority } = baseCalcVideoList(fileVideoList, channelPriority, false);
    return {
        videoList,
        videoPriority,
    };
};

/**
 * 获取按通道优先级排序后的全部图片文件
 * @param fileList 证据文件列表
 * @param channelPriority 通道优先级，[1, 2, 3]
 * @param evidenceType 默认证据类型进行优先级排序
 * @returns 按通道优先级聚合后图片文件列表
 */
export const getImageList = (fileList: FileItem[], channelPriority: number[], evidenceType = 1): FileItem[] => {
    // 过滤图片文件
    const fileImageList = fileList
        .filter((i) => i.fileType === FileType.image && i.url)
        .sort((a, b) => Number(a.startTime) - Number(b.startTime));
    const groupFileListByChannel = groupBy(fileImageList, 'channelNo');
    // 1,9是报警证据类型，其余的是视频库视频，只有报警证据才进行报警顺序排序, 
    // 默认要按报警展示排序，如果是视频类型就不传入排序优先级
    const newChannelPriority = evidenceTypes.includes(evidenceType) ? channelPriority : [];
    // 按通道优先级排序
    const channelPriorityFileList = (newChannelPriority || [])?.reduce((pre: FileItem[], cur) => {
        return [...pre, ...(groupFileListByChannel[cur] || [])];
    }, []);
    // 其他不在通道优先级中的通道的图片文件
    const otherChannelFileList: FileItem[] = [];
    Object.keys(groupFileListByChannel).forEach((channel) => {
        if (!newChannelPriority?.includes(+channel)) {
            otherChannelFileList.push(...(groupFileListByChannel[channel] || []));
        }
    });
    return [...channelPriorityFileList, ...otherChannelFileList];
};

/**
 * 获取按通道优先级排序后的视频文件
 * @param param
 *  fileList 证据文件列表
 *  channelPriority 通道优先级，[1, 2, 3]
 *  isN9M 是否是N9M设备，true是N9M设备，false是非N9M设备，默认为true
 *  isForceH264 是否强制使用H264，true强制使用H264，false使用MP4，默认为false
 *  evidenceType 默认证据类型进行优先级排序
 * @returns 返回按通道优先级聚合后的视频文件列表和视频文件通道优先级
 */
export const getVideoList = ({
    fileList,
    channelPriority,
    isN9M = true,
    isForceH264,
    evidenceType = 1,
}: {
    fileList: FileItem[];
    channelPriority: number[];
    isN9M: boolean;
    isForceH264: boolean;
    evidenceType?: number;
}): GetVideoResult => {
    // 1,9是报警证据类型，其余的是视频库视频，只有报警证据才进行报警顺序排序, 
    // 默认要按报警展示排序，如果是视频类型就不传入排序优先级
    const newChannelPriority = evidenceTypes.includes(evidenceType) ? channelPriority : [];
    const existH264 = fileList.some(item => item.fileType === FileType.h264);
    if (isN9M || existH264) {
        // N9M设备不区分H264和MP4视频文件，返回全部视频类型文件
        return getAllVideoList(fileList, newChannelPriority);
    } else if (isForceH264) {
        // 非N9M设备，强制播放H264视频文件的情况，返回H264类型文件
        return getH264VideoList(fileList, newChannelPriority);
    } else {
        // 非N9M设备，不强制播放H264视频文件的情况，返回MP4类型文件
        return getMP4VideoList(fileList, newChannelPriority);
    }
};
/**
 * @description: "Whether to display the snapshot image. 0 do not display, 1 display"
 * @param {FileItem} imageList
 * @param {*} videoDrawFrameShow "1" | "0"
 * @return {*}
 */
export const filterVideoDrawFrameShowImage = (imageList: FileItem[], videoDrawFrameShow: '1' | '0') => {
    let filterImageList = imageList || [];
    // 
    if (videoDrawFrameShow != '1') {
        filterImageList = filterImageList.filter(
            (p: any) => p.subType != '3' && p.subType != '6',
        );
    }
    return filterImageList;
};

type EvidenceCardTypeType = {
    video: boolean; //是否渲染视频
    image: boolean; //是否渲染图片
    noData: boolean; //是否渲染无数据
}
/**
 * @param firstVideo: 证据视频信息
 * @param imageList: 证据的图片list
 * @param fileChannelAuth: 通道权限信息
 * @description: 返回对应类型是否需要渲染
 * @return {*} EvidenceCardTypeType
 */
export const renderEvidenceCardType = (
    firstVideo: Record<string, string>,
    imageList: FileItem[],
    fileChannelAuth: FileChannelAuthEnum,
    displayType: DisplayAuthEnum,
): EvidenceCardTypeType => {
    return {
        video: firstVideo || noChannelAuth(fileChannelAuth, false) || displayType === DisplayAuthEnum.PRIVATE_PROTECT,
        image: !firstVideo && imageList?.length > 0,
        noData: !firstVideo &&
            imageList?.length === 0 &&
            !noChannelAuth(fileChannelAuth, false) && displayType !== DisplayAuthEnum.PRIVATE_PROTECT
    };
};