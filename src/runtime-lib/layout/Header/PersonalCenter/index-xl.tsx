import React, {
    useState,
    useEffect,
    useImperativeHandle,
    forwardRef,
    useRef,
} from 'react';
import {
    OverflowEllipsisContainer,
    UserAvatar,
} from '@streamax/starry-components';
import {
    Container,
    List,
} from '@streamax/poppy';
import type { DrawerProps } from '@streamax/poppy/lib/drawer';
import i18n from '../../../i18n';
import './style.less';
import useConfigParameter from '@/runtime-lib/hooks/useConfigParameter';
import { getCustomItems } from '@/utils/pageReuse';
import Icon from '@streamax/poppy-icons/lib/Icon';
import { ReactComponent as SecurityBg } from '@/assets/icons/icon_ identifi_ security_b.svg';
import {
    getAppGlobalData,
    mosaicManager,
    setAppGlobalData,
    StarryAbroadIcon,
    StarryInfoBlock,
    StarryModal,
    useSafetyModeStore,
} from '@/runtime-lib';
import {
    BusIconDownload,
    IconInformationFill,
    IconMessageFill,
    IconRepairFill,
    IconRequestFill,
    IconUser01Fill,
    IconUser02Fill,
    IconRealTimeVideoFill,
    IconMosaic01
} from '@streamax/poppy-icons';
import {
    getTenantList,
    getUserInfo,
    getUserRealDetail,
    getUserTenantConfig,
} from '@/runtime-lib/service';
import { getImgUrl } from '../utils/getImgUrl';
import { DesensitEmailNumber } from './PersonalInfo/components/DesensitEmailNumber';
import TenantExchange from '../tenantExchange';

const clsPrefix = 'starry-layout-header';
const clsPrefixPC = `${clsPrefix}-personal-center`;

export type PersonalCenterMenuKeys =
    | 'personalInfo'
    | 'messageList'
    | 'toolsCenter'
    | 'about'
    | 'fileExport'
    | 'performanceSetting'
    | 'helpCenter'
    | 'TenantExchange'
    | 'mosaicSetting';

export interface contentItem {
    key: PersonalCenterMenuKeys;
    title: string;
    img: React.ReactElement | null | string;
    divider?: boolean;
}

export type OpenPersonalCenter = (
    key: PersonalCenterMenuKeys,
    params?: any,
) => void;

interface PersonalCenterProps {
    drawerProps?: DrawerProps;
    visible?: boolean;
    showDocsBtn?: boolean;
    onLogout: () => void;
    onMenuClick?: (key: string) => void;
    getPersonalCenterContentList?: (params: contentItem[]) => contentItem[];
}

const PersonalCenter = (props: PersonalCenterProps, ref: any) => {
    const {
        getPersonalCenterContentList,
        visible,
        showDocsBtn,
        onMenuClick,
        onLogout,
    } = props;
    const oldRealUserInfo = getAppGlobalData('REAL_USER_INFO');
    const [userDetail, setUserDetail] = useState<any>({});
    const [tenantList, setTenantList] = useState<any>([]);
    const { account } = oldRealUserInfo;
    const themeColor = getAppGlobalData('APP_THEME');
    const [avatarInfo, setAvatarInfo] = useState<{
        avatarUrl: string | boolean;
        account: string;
    }>({
        account,
        avatarUrl: false,
    });

    const safeMode = useSafetyModeStore.useDataStore((state) => state.safeMode);

    const fileExportRef = useRef<any>(null);
    const personalCenterRef = useRef<any>(null);
    // 获取展示下载中心的租户参数
    const { data } = useConfigParameter<string>({
        key: 'HEADER.TAB.DOWNLOAD',
        options: {
            defaultValue: '1',
            formatFn: (params) => {
                return params;
            },
        },
    });
    const tenantIcon = () => {
        return (
            <span className="tenant-icon">
                <IconUser02Fill />
            </span>
        );
    };
    const mosaicVisible = mosaicManager.useUserCustomMosaicVisible();
    // 只有一个时，不展示切换租户的下拉框
    const contentList: contentItem[] = [
        tenantList.length > 1 && {
            key: 'TenantExchange',
            title: <span className="tenant-title"><TenantExchange /></span>,
            img: tenantIcon(),
            divider: true,
        },
        {
            key: 'personalInfo',
            title: i18n.t('name', '个人信息'),
            img: <IconUser01Fill />,
        },
        {
            key: 'messageList',
            title: i18n.t('name', '消息通知'),
            img: <IconMessageFill />,
        },
        {
            key: 'toolsCenter',
            title: i18n.t('name', '工具'),
            img: <IconRepairFill />,
        },
        {
            key: 'about',
            title: i18n.t('name', '关于平台'),
            img: <IconInformationFill />,
        },
        mosaicVisible && {
            key: 'mosaicSetting',
            title: i18n.t('name', '马赛克设置'),
            img: <IconMosaic01 />,
        },
        data != 0 && {
            key: 'fileExport',
            title: i18n.t('name', '下载中心'),
            img: <BusIconDownload />,
        },
        {
            key: 'performanceSetting',
            title: i18n.t('name', '性能设置'),
            img: <IconRealTimeVideoFill />,
        },
        {
            key: 'helpCenter',
            title: i18n.t('name', '帮助中心'),
            img: <IconRequestFill />,
            divider: true
        },
    ].filter(Boolean) as contentItem[];

    function onSelectMenu(key: any) {
        onMenuClick?.(key);
    }
    useEffect(() => {
        useSafetyModeStore.getSafetyMode();
        (async () => {
            const tenantList = await getTenantList();
            setTenantList(tenantList);
            // 获取localStorage切换租户的用户信息
            const { userId: realUserId } = oldRealUserInfo || {};
            // 定义获取真实用户信息方法
            // eslint-disable-next-line prefer-const
            let [userInfo = {}, realUserInfo] = await Promise.all([
                getUserInfo(true),
                getUserRealDetail({
                    userId: realUserId,
                    desensitize: 0,
                }),
            ]);
            /***************主流程,2.15.2后端不支持从用户返回，后端来不及优化，2.16版本要优化到用户信息上时，删除此逻辑**************/
            const storageConfig = await getUserTenantConfig({
                tenantId: userInfo.tenantId,
                keys: 'tenant.storage.config',
            });
            if (storageConfig['tenant.storage.config']) {
                try {
                    const storage = JSON.parse(
                        storageConfig['tenant.storage.config'],
                    );
                    userInfo.storageSwitch = storage.open;
                } catch (error) {}
            }
            /**************end***************/
            // 如果未获取用户信息，那重新再获取一次父租户（userId不存在，即accountType不存在，默认是父租户，故只有父租户的情况存在realUserInfo为空）
            // 获取脱敏email信息
            realUserInfo.desensEmail = userInfo.email;
            realUserInfo.thirdAccountBindList = userInfo.thirdAccountBindList;
            setAppGlobalData('APP_USER_INFO', userInfo);
            setAppGlobalData('REAL_USER_INFO', realUserInfo);
            // 个人中心展示的用户信息，都为真实用户的信息
            setUserDetail(realUserInfo);
            if (realUserInfo) {
                let url = true;
                try {
                    if (realUserInfo.avatarId)
                        url = await getImgUrl(realUserInfo.avatarId);
                } catch {
                    //
                } finally {
                    setAvatarInfo((data) => ({ ...data, avatarUrl: url }));
                }
            }
        })();
    }, []);
    const logOut = () => {
        onLogout?.();
    };
    const renderHeader = () => {
        return (
            <>
                {avatarInfo.avatarUrl ? (
                    <div className="user-avatar">
                        <UserAvatar
                            bgColor={themeColor}
                            account={avatarInfo.account}
                            avatarUrl={avatarInfo.avatarUrl}
                            size={88}
                            avatarTextStyle={{ fontSize: 48 }}
                        />
                        {safeMode && (
                            <Icon
                                component={SecurityBg}
                                className="security-identifier"
                            />
                        )}
                    </div>

                ) : null}
                <div className="user-account">{userDetail.account}</div>
                <div className="user-email">
                    <DesensitEmailNumber
                        desensText={userDetail.desensEmail || ''}
                        userId={userDetail.userId}
                    />
                </div>
            </>
        );
    };
    const renderFooter = () => {
        return (
            <div className="logout-button" onClick={logOut}>
                {i18n.t('name', '退出登录')}
            </div>
        );
    };

    return (
        <div
            className={`personal-info-modal ${
                visible ? '' : 'personal-info-modal-hidden'
            }`}
            ref={personalCenterRef}
        >
            <Container className="personal-info-modal-container">
                <StarryInfoBlock>
                    <div className={clsPrefixPC}>
                        <div className={`${clsPrefixPC}-menu-xl`}>
                            <List
                                itemLayout="horizontal"
                                split={false}
                                dataSource={getCustomItems(
                                    getPersonalCenterContentList,
                                    contentList,
                                    {
                                        fileExportInstance: fileExportRef,
                                        openPersonalCenter: onMenuClick,
                                    },
                                )}
                                header={
                                    <div>
                                        <div
                                            className={`${clsPrefixPC}-menu-xl-header`}
                                        >
                                            {renderHeader()}
                                        </div>
                                    </div>
                                }
                                footer={renderFooter()}
                                renderItem={(item, index) => (
                                    <List.Item
                                        onClick={() => onSelectMenu(item.key)}
                                        className={`${
                                            item.divider
                                                ? 'poppy-list-item-divider'
                                                : ''
                                        }`}
                                    >
                                        <div
                                            className={`${clsPrefixPC}-menu-xl-item`}
                                        >
                                            <span
                                                className={`${clsPrefixPC}-menu-xl-icon`}
                                            >
                                                {item.img}
                                            </span>
                                            <span className={`${clsPrefixPC}-menu-xl-title`}>{item.title}</span>
                                        </div>
                                    </List.Item>
                                )}
                            />
                        </div>
                    </div>
                </StarryInfoBlock>
            </Container>
        </div>
    );
};

export default forwardRef(PersonalCenter);
