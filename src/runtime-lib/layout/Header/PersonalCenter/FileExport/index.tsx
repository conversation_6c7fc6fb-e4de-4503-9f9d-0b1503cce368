import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import i18n from '../../../../i18n';
import _ from 'lodash';
import { Tabs, Button, Space, Table, message, Modal, Select, Tooltip } from '@streamax/poppy';
import { IconDeleteFill, IconDownloadFill, IconRequestFill } from '@streamax/poppy-icons';
import { deleteTaskMainBatch, fetchApplicationPageList } from './service';
import { fetchFileUrls, fetchTaskMainPage } from '../../../../service';
import { ListDataContainer, TableColumnSetting } from '@streamax/starry-components';
import { zeroTimeStampToFormatTime } from '../../../../utils/formator';
import { getAppGlobalData } from '../../../../global-data';
import { APPID_BASEBUSINESS, APPID_BASEPLATFORM } from '../../../../utils/constant';
import { getFmsChangeAppId } from '../../utils/getFtmInfo';
import { taskTypesFMS } from '../../type';
import './index.less';
import { fetchServerTime } from '../../../../service/prompt-carousel';
import moment from 'moment';
import { useGetState } from '@streamax/hooks';
import { fetchFilesDetailPost } from '@/runtime-lib/modules/Alarm/service/fms';
import { useCurrentAppStore } from '@/runtime-lib/entrance-manage/currentApp';

interface AppList {
    value: number;
    label: string;
}
interface Entry {
    appId: number;
    iconUrl: string;
    releaseTime: string;
    resourceCode: string;
    resourceId: number;
    resourceName: string;
    serviceEntrance: string;
    translation: string;
    version: string;
}

const { TabPane } = Tabs;
const V3_DOWNLOAD_URL = '/fms/v3/download/file/';
export const FILE_EXPORT_TASK_STATE = {
    SUCCESS: 100,
    DOING: 1,
    WAITING: 0,
    FAIL: 50,
};

const FileExport = (props: any, ref: any) => {
    const appUserInfo = getAppGlobalData('APP_USER_INFO');
    const [appList, setAppList] = useState<AppList[]>([]);
    const loginApp = Number(getAppGlobalData('APP_ID'));
    const inSaaS = loginApp === APPID_BASEBUSINESS || loginApp === APPID_BASEPLATFORM;
    const [currentApp, setCurrentApp] = useState<AppList | undefined>(undefined);
    const [defineAppId, setDefineAppId] = useState<number | string>(); // 指定选中的应用
    const [serverTime, setServerTime, getServerTime] = useGetState<number>(moment().unix()); // 服务器时间戳
    const tableRef = useRef<any>(null);
    const STATUS = {
        PROCESSING: 1,
        WAITING: 0,
        FAIL: 50,
        SUCCESS: 100,
        list: [
            {
                label: i18n.t('name', '成功'),
                value: FILE_EXPORT_TASK_STATE.SUCCESS,
            },
            {
                label: i18n.t('name', '进行中'),
                value: FILE_EXPORT_TASK_STATE.DOING,
            },
            {
                label: i18n.t('name', '等待中'),
                value: FILE_EXPORT_TASK_STATE.WAITING,
            },
            {
                label: i18n.t('name', '失败'),
                value: FILE_EXPORT_TASK_STATE.FAIL,
            },
        ],
    };
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [activeTab, setActiveTab] = useState(FILE_EXPORT_TASK_STATE.SUCCESS.toString());

    useImperativeHandle(ref, () => ({
        settingActiveTab: (params: { tabKey: string | number; appId: string | number }) => {
            const { tabKey, appId } = params || {};
            setActiveTab(tabKey?.toString());
            setDefineAppId(appId);
        },
    }));

    const appEntry = useCurrentAppStore((state) => state.currentAppEntry);
    useEffect(() => {
        if (appList.length > 0) {
            const selectApp = appList.find((app) => app.value === defineAppId);
            selectApp && setCurrentApp(selectApp);
            if (selectApp) {
                setCurrentApp(selectApp);
            } else {
                // 根据当前服务入口查找应用
                const entry = appEntry;
                const entrys: Entry[] = getAppGlobalData('APP_ENTRYS');
                const currEntry = entrys.find((item: any) => item.serviceEntrance === entry);
                // 默认选中当前服务入口所属应用
                if (currEntry) {
                    const currApp = appList.find((app) => app.value === currEntry.appId);
                    setCurrentApp(currApp);
                } else {
                    setCurrentApp(appList[0]);
                }
            }
        }
    }, [appEntry, appList, defineAppId]);

    const deleteConfirm = (records: any[], batch = false) => {
        const name = records.map((i) => i.taskName).join(',');
        const ids = records.map((i) => i.taskId);
        Modal.confirm({
            centered: true,
            title: i18n.t('name', '删除确认'),
            content: !batch
                ? i18n.t('message', '确认删除文件“{name}”的导出任务？', {
                      name,
                  })
                : `${i18n.t('message', '确定删除勾选的所有任务')}?`,
            okText: i18n.t('name', '确定'),
            cancelText: i18n.t('name', '取消'),
            closable: true,
            icon: <IconRequestFill />,
            onOk: async () => {
                await deleteTaskMainBatch({ taskIds: ids.join(',') });
                // 重新加载表格数据和修改已选列表
                const newKeys = selectedRowKeys.filter((i) => !ids.includes(i));
                const newRows = selectedRows.filter((i) => !ids.includes(i));
                setSelectedRowKeys(newKeys);
                setSelectedRows(newRows);
                tableRef.current?.loadDataSource();
                message.success(i18n.t('message', '删除成功'));
            },
        });
    };
    const deleteRecord = (record: any) => {
        deleteConfirm([record]);
    };
    const batchDeleteHandler = () => {
        deleteConfirm(selectedRows, true);
    };
    const download = async (record: Record<string, any>) => {
        await fetchAndSetServerTime();
        const isExpire = getIsExpire(record);
        if (isExpire) {
            // 已过期，提示文件已过期不下载
            message.error(i18n.t('message', '文件已清理'));
            return;
        }
        const fetchFileUrlsParams = {
            fileUuids: record.bizId,
            fileIdList: record.bizId,
        };

        const fetchFilesDetailPostParams = {
            fileInfos: [
                {
                    fileUuid: record.bizId,
                    fileName: record.taskName,
                }
            ]
        };

        const finallyParams = taskTypesFMS.includes(record.taskType)
        ? fetchFileUrlsParams
        : fetchFilesDetailPostParams;

        const fetchName = taskTypesFMS.includes(record.taskType)
            ? fetchFileUrls
            : fetchFilesDetailPost;
        fetchName(
            {
                ...finallyParams
            },
            {
                _appId: getFmsChangeAppId(record.taskType, currentApp?.value),
            },
        ).then((rs: any) => {
            let url;
            if (taskTypesFMS.includes(record.taskType)) {
                url = rs[0]?.fileUrl || rs[0]?.url;
            } else {
                url = rs[0]?.urls[0]?.url;
            }
            if (rs && rs[0] && url) {
                 // 如果链接是/fms/v3/download/file/uuid，在get参数中增加download=1标识直接下载，而不是预览
                 if (url.indexOf(V3_DOWNLOAD_URL) > -1) {
                     if (url.indexOf('?') > -1) {
                         url = url + '&';
                     } else {
                         url = url + '?';
                     }
                     url = url + 'download=1';
                 }
                 window.open(url, '_blank');
            } else {
                message.error(i18n.t('message', '获取文件下载链接为空'));
            }
        });
    };

    const getIsExpire = (record) => {
        const { taskParam, createTime } = record;
        let paramObj = {
            evidenceQueryParams: {},
        };
        try {
            paramObj = JSON.parse(taskParam);
        } catch (error) {
            console.log('【任务参数错误】taskParam', error);
        }

        const evidenceQueryParams = paramObj?.evidenceQueryParams;
        // 若没有正确参数字段，返回不过期
        if (!evidenceQueryParams) return false;
        // 证据水印未开启的情况，才去计算是否过期，否则直接返回不过期
        if (Number(evidenceQueryParams?.watermarkFlag) !== 1) return false;
        const expireDay = Number(evidenceQueryParams?.expireDay) || 0;
        const isExpire = createTime + expireDay * 24 * 60 * 60 < getServerTime();
        return isExpire;
    };

    const downloadBtn = (record) => {
        const isDeleted = !record.isFileExist;
        const isExpire = getIsExpire(record);
        const tipInfo = (isExpire || isDeleted)? i18n.t('message', '文件已清理') : i18n.t('action', '下载');
        const btn = record.taskStatus === STATUS.SUCCESS && (
            <Tooltip placement="top" title={tipInfo}>
                <Button
                    disabled={isExpire || isDeleted}
                    type="link"
                    onClick={() => {
                        download(record);
                    }}
                    style={{ padding: 0 }}
                >
                    <IconDownloadFill />
                </Button>
            </Tooltip>
        );
        return btn;
    };
    const columns: any = [
        {
            title: i18n.t('name', '文件名称'),
            dataIndex: 'taskName',
            key: 'taskName',
            ellipsis: true,
        },
        {
            title: i18n.t('name', '创建时间'),
            dataIndex: 'createTime',
            ellipsis: true,
            render(text: number) {
                return zeroTimeStampToFormatTime(text);
            },
        },
        ...(Number(activeTab) === STATUS.FAIL
            ? [
                  {
                      title: i18n.t('name', '失败原因'),
                      dataIndex: 'failCode',
                      ellipsis: true,
                      render: (text: any) => {
                          return text ? (
                              <span title={i18n.t(`@base:@return__${text}`, '-')}>
                                  {i18n.t(`@base:@return__${text}`, '-')}
                              </span>
                          ) : (
                              '-'
                          );
                      },
                  },
              ]
            : []),
        ...(Number(activeTab) !== STATUS.PROCESSING
            ? [
                  {
                      title: i18n.t('name', '操作'),
                      dataIndex: 'operate',
                      key: 'operate',
                      width: '120px',
                      ellipsis: true,
                      render: (text: any, record: any) => (
                          <Space size={24} className="operate-btn">
                              {downloadBtn(record)}
                              <Tooltip placement="top" title={i18n.t('action', '删除')}>
                                  <Button
                                      type="link"
                                      onClick={() => {
                                          deleteRecord(record);
                                      }}
                                      style={{ padding: 0 }}
                                  >
                                      <IconDeleteFill />
                                  </Button>
                              </Tooltip>
                          </Space>
                      ),
                  },
              ]
            : []),
    ];
    const fetchData = async (data: any) => {
        if (inSaaS && _.isNil(currentApp)) {
            return {
                list: [],
                total: 0,
            };
        }
        return fetchTaskMainPage({
            ...data,
            taskStatus: Number(activeTab),
            appId: currentApp?.value !== undefined ? currentApp.value : getAppGlobalData('APP_ID'),
            // 传入参数获取所有导出列表
            jobType: 2,
            selfOnly: 1, // 个人中心的下载中心，仅展示当前用户自己创建的
            checkFile: 1, // 是否校验文件是否存在,0-否 1-是 默认为0；（为1的情况，会校验当前文件是否存在S17，返回值中isFileExist体现结果）
        }).then((rs: any) => ({
            ...rs,
            total: Number(rs.total),
        }));
    };
    const fetchAndSetServerTime = async () => {
        const data = await fetchServerTime();
        setServerTime(data);
    };
    useEffect(() => {
        console.warn(appUserInfo);
        fetchAndSetServerTime();
        fetchApplicationPageList({
            page: 1,
            pageSize: 999999999, // 需要查询全部，接口只支持分页查询，所以pageSize需要很大
            userId: appUserInfo?.userId,
            states: '1', // 1-运行中
        }).then((rs: any) => {
            let list: AppList[] = [];
            (rs.list || []).forEach((item: any) => {
                // 过滤运维应用
                if (![66666].includes(item.applicationId)) {
                    list.push({
                        label: i18n.t(`@i18n:@app__${item.applicationId}`, item.applicationName),
                        value: item.applicationId,
                    });
                }
            });
            list = list.sort((a, b) => a.value - b.value);
            setAppList(list);
        });
    }, []);

    const selectApp = (value: number, option: AppList) => {
        setCurrentApp(option);
    };
    useEffect(() => {
        tableRef.current.loadDataSource({ page: 1 });
    }, [activeTab, currentApp]);

    const { tableColumns, tableColumnSettingProps } = TableColumnSetting.useSetting([...columns], {
        storageKey: `@base:@page:task.file.export.type${activeTab}`,
        disabledKeys: ['taskName', 'taskId'],
    });
    const setKey = (key: any) => {
        setActiveTab(key);
    };
    return (
        <>
            <Tabs activeKey={activeTab} onTabClick={(key) => setKey(key)}>
                {STATUS.list.map((item) => (
                    <TabPane tab={item.label} key={item.value.toString()} />
                ))}
            </Tabs>
            <ListDataContainer
                ref={tableRef}
                getDataSource={fetchData}
                loadDataSourceOnMount={false}
                renderContainerStyle={{
                    toolbarAndListRender: false,
                    queryForm: false,
                }}
                toolbar={{
                    hasReload: true,
                    extraLeft:
                        Number(activeTab) === STATUS.PROCESSING ? (
                            <span></span>
                        ) : (
                            <Space>
                                <Button
                                    onClick={batchDeleteHandler}
                                    disabled={selectedRowKeys.length === 0}
                                >
                                    <IconDeleteFill />
                                    {i18n.t('action', '批量删除')}
                                </Button>
                            </Space>
                        ),
                    // 有多个应用的时候才展示下拉列表
                    extraRight: appList.length > 1 && (
                        inSaaS &&
                        <Select
                            value={currentApp?.value}
                            defaultValue={defineAppId}
                            onChange={selectApp as any}
                            placeholder={i18n.t('name', '请选择归属应用')}
                            style={{ width: 280 }}
                            options={appList}
                        />
                    ),
                    extraIconBtns: [
                        // @ts-ignore
                        <TableColumnSetting key={activeTab} {...tableColumnSettingProps} />,
                    ],
                }}
                listRender={(data) => {
                    return (
                        <Table
                            draggableTable={false}
                            className="file-export-table"
                            bordered="around"
                            columns={tableColumns}
                            dataSource={data}
                            pagination={false}
                            scroll={{ y: 'calc(100vh - 326px)' }}
                            rowSelection={
                                Number(activeTab) !== STATUS.PROCESSING
                                    ? {
                                          selectedRowKeys,
                                          onChange: (keys: React.Key[], selected) => {
                                              setSelectedRowKeys(keys);
                                              setSelectedRows(selected);
                                          },
                                      }
                                    : undefined
                            }
                            rowKey={'taskId'}
                        />
                    );
                }}
            />
        </>
    );
};

export default forwardRef(FileExport);
