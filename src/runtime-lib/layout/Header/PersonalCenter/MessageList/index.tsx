import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import {
    Tabs,
    Modal,
    Pagination,
    Space,
    Tooltip,
    Spin,
    Empty,
    message,
} from '@streamax/poppy';
import {
    IconDeleteLine,
    IconRefresh,
    IconSuccessLine,
    IconRequest,
    IconCheckCircleLine,
} from '@streamax/poppy-icons';
import MessageCollapse from './components/MessageCollapse';
// @ts-ignore
import { ReactComponent as NoData } from '@/assets/nodata.svg';
import { MessageNotificationConfig } from './type';
import {
    fetchMessageNoticePage,
    getTenantDetail,
    messageNoticeDelete,
    batchUpdateReadStatus,
    fetchMsgHistoryTTL,
} from '../../../../service';
import i18n from '../../../../i18n';
import { OpenPersonalCenter } from '..';
import './index.less';
import {
    NotificationTypeMap,
    SYT_NOTICE_EVENT_EMIT,
    TENANT_VALIDATE_TIME_NOTIFY_TYPE,
} from '../../../../MessageModule/MessageNotification/constant';
import { fetchServerTime } from '../../../../service/prompt-carousel';
import { getAppGlobalData } from '../../../../global-data';
import { g_emmiter } from './../../../../emmiter';
import moment from 'moment';
import { useAsyncEffect, useGetState } from '@streamax/hooks';
import { RspShow, useBreakpoint, useResponsiveShow } from '@streamax/responsive-layout';
import { StarryAbroadIcon } from '@/runtime-lib';
import InfoBack from '../../../../components/InfoBack';
import { useRequest } from 'ahooks';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import useMessageRetentionWidth from './hooks/useMessageRetentionWidth';

const { TabPane } = Tabs;

type TabKeyType = 'unread' | 'read' | 'all';
type PageInfoType = {
    page: number;
    pageSize: number;
};

type TabType = {
    key: TabKeyType;
    name: string;
    dataList: MessageNotificationConfig[];
};

interface MessageListProps {
    openPersonalCenter: OpenPersonalCenter;
}
const { SUB_TENANT_EXPIRE, SUB_TENANT_RENEW } =
    TENANT_VALIDATE_TIME_NOTIFY_TYPE;

const MessageList = (props: MessageListProps, ref: any) => {
    const { openPersonalCenter } = props;

    const [activeTab, setActiveTab] = useState<TabKeyType>('unread');
    const [readList, setReadList] = useState<MessageNotificationConfig[]>([]); // 已读列表
    const [unReadList, setUnReadList] = useState<MessageNotificationConfig[]>(
        [],
    ); // 未读列表
    const [allList, setAllList] = useState<MessageNotificationConfig[]>([]); // 全部列表
    const [loading, setLoading] = useState<boolean>(false);
    const [serverTime, setServerTime, getServerTime] = useGetState<number>(
        moment().unix(),
    ); // 服务器时间戳

    const [pageInfo, setPageInfo] = useState<PageInfoType>({
        page: 1,
        pageSize: 10,
    });
    const [total, setTotal] = useState<number>(0);

    const getBreakWidth = useMessageRetentionWidth();

    const { data: messageRetentionDays = "-" } = useRequest(fetchMsgHistoryTTL);

    useAsyncEffect(async () => {
        setPageInfo({
            ...pageInfo,
            page: 1,
        });
        const time = await fetchServerTime();
        setServerTime(time);
        getMessagePageList({
            ...pageInfo,
            page: 1,
        });
    }, [activeTab]);

    const messageStatusMap = {
        unread: false,
        read: true,
        all: null,
    };

    // Tab列表
    const tabList: TabType[] = [
        {
            key: 'unread',
            name: i18n.t('name', '未读'),
            dataList: unReadList,
        },
        {
            key: 'read',
            name: i18n.t('name', '已读'),
            dataList: readList,
        },
        {
            key: 'all',
            name: i18n.t('name', '全部'),
            dataList: allList,
        },
    ];

    // 查询消息列表
    function getMessagePageList(pageParams?: PageInfoType) {
        setLoading(true);
        fetchMessageNoticePage({
            ...pageInfo,
            ...pageParams,
            read: messageStatusMap[activeTab],
        })
            .then(async (res: any) => {
                const { list, total } = res;
                const dataList: any = [];
                for (let i = 0; i < list.length; i++) {
                    let item = list[i];
                    try {
                        /*****todo 前端暂时解析[] 数据，后端后续修改为对象*****/
                        const [, , , data, stateObj] = item;
                        const [id, readState, createTime] = stateObj;
                        item = {
                            ...data,
                            readState,
                            id,
                            createTime: Math.round((createTime || 0) / 1000),
                        };
                        /**********/
                        item.serverTime = getServerTime();
                        if (typeof item.modelData === 'string') {
                            item.modelData = JSON.parse(item.modelData) || {};
                        }
                        // 兼容老数据，有id的是新数据
                        item.modelData =
                            {
                                ...item.modelData,
                                contentText: item.modelData?.contentId
                                    ? i18n.t(item.modelData?.contentId, '')
                                    : item.modelData?.content, //系统消息
                                alarmTypeName: item.modelData?.alarmTypeNameId
                                    ? i18n.t(
                                          item.modelData?.alarmTypeNameId,
                                          '',
                                      )
                                    : item.modelData?.alarmTypeName, //报警
                            } || {};

                        // eslint-disable-next-line no-empty
                    } catch (error) {
                        item.modelData = {};
                    }

                    if (
                        item?.notificationType ===
                            NotificationTypeMap.tenantValidateTimeNotify &&
                        (item?.modelData?.msgType === SUB_TENANT_EXPIRE ||
                            item?.modelData?.msgType === SUB_TENANT_RENEW)
                    ) {
                        const subTenantId = item.modelData?.subTenantId;
                        const info = await getTenantDetail(subTenantId);
                        item.modelData.subTenantName = info?.tenantName;
                        dataList.push(item);
                    } else {
                        dataList.push(item);
                    }
                }

                switch (activeTab) {
                    case 'read':
                        setReadList(dataList);
                        break;
                    case 'unread':
                        setUnReadList(dataList);
                        break;
                    case 'all':
                        setAllList(dataList);
                        break;
                }
                setTotal(total);
            })
            .finally(() => {
                setLoading(false);
            });
    }

    // 一键删除已读
    function deleteAllReadMsg() {
        Modal.confirm({
            icon: <IconRequest />,
            title: i18n.t('name', '删除确认'),
            content: i18n.t('message', '确定要删除“全部已读消息”吗？'),
            cancelText: i18n.t('name', '取消'),
            okText: i18n.t('name', '确定'),
            onOk: () => {
                messageNoticeDelete({
                    mode: 1, //1：按条件过滤删除（read）2：按id列表删除
                    read: 1, //0：删除所有未读消息 1：删除所有已读消息 2：删除所有消息，含已读和未读
                }).then(() => {
                    getMessagePageList();
                    message.success(i18n.t('message', '操作成功'));
                });
            },
        });
    }

    // 切换分页
    function handlePageChange(page: number, pageSize: number) {
        setPageInfo({ page, pageSize });
        getMessagePageList({ page, pageSize });
    }

    // 标记为已读
    function handleReadAll() {
        // 全部标记为已读
        batchUpdateReadStatus({
            all: true, //已读用户所有消息 false：不做处理 null： 默认等同于false
        }).then((data) => {
            g_emmiter.emit(SYT_NOTICE_EVENT_EMIT, { count: data });
            getMessagePageList();
            message.success(i18n.t('message', '操作成功'));
        });
    }

    // 刷新
    function handleRefresh() {
        setPageInfo({
            ...pageInfo,
            page: 1,
        });
        getMessagePageList({
            ...pageInfo,
            page: 1,
        });
    }
    useImperativeHandle(ref, () => ({
        handleRefresh,
    }));
    const renderPublicOperation = (
        <div className="public-operation">
            <Space size={24}>
                {activeTab === 'read' && (
                    <Space size={8} onClick={deleteAllReadMsg}>
                        <span className='public-operation-icon'>
                            <Tooltip
                                title={i18n.t('action', '一键删除已读')}
                                placement="left"
                            >
                                <StarryAbroadIcon>
                                    <IconDeleteLine />
                                </StarryAbroadIcon>
                            </Tooltip>
                        </span>
                        <OverflowEllipsisContainer maxWidth={getBreakWidth()}>
                        <span className='public-operation-text'>{i18n.t('action', '一键删除已读')}</span>
                       </OverflowEllipsisContainer>
                    </Space>
                )}
                {activeTab === 'unread' && (
                    <Space size={8} onClick={handleReadAll}>
                        <span className='public-operation-icon'>
                            <Tooltip
                                title={i18n.t('action', '一键标记已读')}
                                placement="left"
                            >
                                <StarryAbroadIcon>
                                    <IconCheckCircleLine />
                                </StarryAbroadIcon>
                            </Tooltip>
                        </span>
                        <OverflowEllipsisContainer maxWidth={getBreakWidth()}>
                            <span className='public-operation-text'>{i18n.t('action', '一键标记已读')}</span>
                            </OverflowEllipsisContainer>
                    </Space>
                )}
                <span onClick={handleRefresh}>
                    <Tooltip title={i18n.t('action', '刷新')} placement="left">
                        <StarryAbroadIcon>
                            <IconRefresh />
                        </StarryAbroadIcon>
                    </Tooltip>
                </span>
            </Space>
        </div>
    );
    return (
        <div className="message-notification-list">
            <InfoBack className="message-notification-list-tip" title={i18n.t("message", "默认仅保留最近 {messageRetentionDays} 天的消息记录", { messageRetentionDays: <span data-primary-color
            >{ messageRetentionDays}</span> })} />
            <Spin spinning={loading}>
                <Tabs
                    activeKey={activeTab}
                    onChange={(key) => setActiveTab(key as TabKeyType)}
                    tabBarExtraContent={
                        {
                            right: renderPublicOperation
                        }
                    }
                >
                    <>
                        {tabList.map((tab: any) => (
                            <TabPane tab={tab.name} key={tab.key}>
                                <div
                                    className="message-notification-list-wrap"
                                >
                                    {tab.dataList.map((item: any) => (
                                        <MessageCollapse
                                            key={item.id}
                                            messageInfo={item}
                                            openPersonalCenter={
                                                openPersonalCenter
                                            }
                                        />
                                    ))}
                                </div>
                            </TabPane>
                        ))}
                    </>
                </Tabs>
                {total > 0 ? (
                    <div className="pagination-wrapper">
                        <Pagination
                            defaultCurrent={1}
                            current={pageInfo.page}
                            pageSize={pageInfo.pageSize}
                            total={total}
                            onChange={handlePageChange}
                            showSizeChanger
                            showTotal={(total: number) => {
                                return i18n.t('message', '共{total}条', {
                                    total: (
                                        <span className="pagination-total-number">
                                            {total}
                                        </span>
                                    ),
                                });
                            }}
                        />
                    </div>
                ) : (
                    <Empty
                        description={
                            <span>{i18n.t('message', '暂无数据')}</span>
                        }
                        image={<NoData />}
                    />
                )}
            </Spin>
        </div>
    );
};
export default forwardRef(MessageList);
