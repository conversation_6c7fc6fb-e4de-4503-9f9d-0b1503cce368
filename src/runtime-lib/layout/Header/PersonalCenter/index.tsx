import React, {
    useState,
    useEffect,
    useImperativeHandle,
    forwardRef,
    useRef,
} from 'react';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { Drawer, Dropdown, Menu } from '@streamax/poppy';
import type { DrawerProps } from '@streamax/poppy/lib/drawer';
import ToolsCenter from './DownloadCenter';
import MessageList from './MessageList';
import FileExport from './FileExport';
import PersonalInfo from './PersonalInfo';
import i18n from '../../../i18n';
import './style.less';
import useConfigParameter from '@/runtime-lib/hooks/useConfigParameter';
import { PerformanceSetting } from './PerformanceSetting';
import { getCustomItems } from '@/utils/pageReuse';
import {
    RspShow,
    useResponsiveShow,
} from '@streamax/responsive-layout';
import { IconArrow02Down, IconArrow02Up, IconInformationFill } from '@streamax/poppy-icons';
import { isNil } from 'lodash';
import MosaicSetting from '@/runtime-lib/modules/mosaic/ui/MosaicSetting';
import { g_emmiter, mosaicManager } from '@base-app/runtime-lib';
import { MESSAGE_DRAWER_TOGGLE_EMMITER } from '../consants';
import { About } from './About';

const clsPrefix = 'starry-layout-header';
const clsPrefixPC = `${clsPrefix}-personal-center`;

export type PersonalCenterMenuKeys =
    | 'personalInfo'
    | 'messageList'
    | 'toolsCenter'
    | 'about'
    | 'fileExport'
    | 'performanceSetting'
    | 'helpCenter'
    | 'mosaicSetting';

export interface contentItem {
    key: PersonalCenterMenuKeys;
    title: string;
    content: React.ReactElement | null | string;
}

export type OpenPersonalCenter = (
    key: PersonalCenterMenuKeys,
    params?: any,
) => void;

interface PersonalCenterProps {
    drawerProps?: DrawerProps;
    getPersonalCenterContentList?: (params: contentItem[]) => contentItem[];
}

const PersonalCenter = (props: PersonalCenterProps, ref: any) => {
    const { drawerProps, getPersonalCenterContentList } = props;
    const [activeContent, setActiveContent] = useState<any>({});
    const [dropdownVisible, setDropdownVisible] = useState(false);
    const fileExportRef = useRef<any>(null);
    const messageListRef = useRef<any>(null);
    const showXxl = useResponsiveShow({
        xxl: true,
        xl: true,
    });
    // 获取展示下载中心的租户参数
    const { data } = useConfigParameter<string>({
        key: 'HEADER.TAB.DOWNLOAD',
        options: {
            defaultValue: '1',
            formatFn: (params) => {
                return (isNil(params) || params === '') ? '1' : params;
            },
        },
    });

    const mosaicVisible = mosaicManager.useUserCustomMosaicVisible();

    const contentList: contentItem[] = [
        {
            key: 'personalInfo',
            title: i18n.t('name', '个人信息'),
            content: <PersonalInfo />,
        },
        {
            key: 'messageList',
            title: i18n.t('name', '消息通知'),
            content: (
                <MessageList
                    ref={messageListRef}
                    openPersonalCenter={openPersonalCenter}
                />
            ),
        },
        {
            key: 'toolsCenter',
            title: i18n.t('name', '工具'),
            content: <ToolsCenter />,
        },
        {
            key: 'about',
            title: i18n.t('name', '关于平台'),
            content: <About />,
        },
        mosaicVisible && {
            key: 'mosaicSetting',
            title: i18n.t('name', '马赛克设置'),
            content: <MosaicSetting />,
        },
        data != 0 && {
            key: 'fileExport',
            title: i18n.t('name', '下载中心'),
            content: <FileExport ref={fileExportRef} />,
        },
        {
            key: 'performanceSetting',
            title: i18n.t('name', '性能设置'),
            content: <PerformanceSetting />,
        },
    ].filter(Boolean) as contentItem[];
    const newContentList = getCustomItems(
        getPersonalCenterContentList,
        contentList,
        {
            fileExportInstance: fileExportRef,
            openPersonalCenter: openPersonalCenter,
        },
    );

    useImperativeHandle(ref, () => ({
        openPersonalCenter,
    }));

    useEffect(() => {
        setActiveContent(contentList[0]);
    }, []);

    function openPersonalCenter(key: any, params?: any) {
        // 查询马赛克配置
        mosaicManager.fetchMosaicConfig();

        const menu = contentList.find((item) => item.key === key);
        menu && setActiveContent(menu);
        if (key === 'fileExport') {
            // 这里需要等待文件导出DOM渲染后再设置选中TAB
            setTimeout(() => {
                fileExportRef.current?.settingActiveTab(params);
            }, 0);
        }
        if (
            key === 'messageList' &&
            drawerProps?.visible &&
            messageListRef.current
        ) {
            //已经打开时刷新数据
            messageListRef.current.handleRefresh();
        }
    }

    function onSelectMenu({ key }: any) {
        const result = contentList.find((item) => item.key === key);
        // 如果是消息列表类型，发出消息列表打开事件
        g_emmiter.emit(MESSAGE_DRAWER_TOGGLE_EMMITER, { isOpen: key === 'messageList' });
        setActiveContent(result);
    }

    const handleSelect = (item: contentItem) => {
        setDropdownVisible(false);
        openPersonalCenter(item.key);
    };
    const renderMenuList = () => {
        return newContentList.map((item) => {
            return (
                <Menu.Item onClick={() => handleSelect(item)} key={item.key}>
                    <OverflowEllipsisContainer>
                        {item.title}
                    </OverflowEllipsisContainer>
                </Menu.Item>
            );
        });
    };
    const renderTitle = () => {
        if (showXxl) {
            return i18n.t('name', '个人中心');
        } else {
            return (
                <Dropdown
                    overlay={<Menu selectable={true}>{renderMenuList()}</Menu>}
                    trigger={['click']}
                    placement="bottomRight"
                    overlayClassName="header-layout-person-dropdown"
                    className="header-layout-person-dropdown"
                    onVisibleChange={(show: boolean) => {
                        setDropdownVisible(show);
                    }}
                >
                    <span
                        className="header-layout-person-dropdown-text"
                        onClick={(e: React.MouseEvent<HTMLSpanElement>) => {
                            e.stopPropagation();
                            e.preventDefault();
                            return;
                        }}
                    >
                        {activeContent.title}
                        <span className="header-layout-person-dropdown-icon">{dropdownVisible ?
                            <IconArrow02Up className="expand-icon" /> : <IconArrow02Down />}</span>
                    </span>
                </Dropdown>
            );
        }
    };
    return (
        <Drawer
            {...drawerProps}
            contentWrapperStyle={{
                width: showXxl ? '70vw' : '100vw',
                height: '100%',
                zIndex: 1000,
            }}
            bodyStyle={{ boxSizing: 'border-box' }}
            title={<div className="poppy-drawer-title-wrap">{renderTitle()}</div>}
            headerStyle={{ minHeight: '64px' }}
        >
            <div className={clsPrefixPC}>
                <RspShow xl xxl>
                    <div className={`${clsPrefixPC}-menu`}>
                        <Menu
                            mode="inline"
                            theme="dark"
                            // defaultSelectedKeys={[contentList[0].key]}
                            onSelect={onSelectMenu}
                            selectedKeys={[activeContent?.key]}
                        >
                            {newContentList.map((item: any) => (
                                <Menu.Item key={item.key}>
                                    <OverflowEllipsisContainer>
                                        {item.title}
                                    </OverflowEllipsisContainer>
                                </Menu.Item>
                            ))}
                        </Menu>
                    </div>
                </RspShow>

                <div className={`${clsPrefixPC}-content`}>
                    <div className={`${clsPrefixPC}-content-body`}>
                        {activeContent.content}
                    </div>
                </div>
            </div>
        </Drawer>
    );
};

export default forwardRef(PersonalCenter);
