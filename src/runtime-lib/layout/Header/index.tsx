import React, { ReactNode, useEffect, useRef, useState, useMemo } from 'react';
import { Modal, Tooltip, Dropdown, message, Space, Form, Popover } from '@streamax/poppy';
import {
    IconDrag,
    BusIconDownload,
    IconClose01Fill,
    IconClose02Line,
    IconRequestFill,
    IconMenu, IconSearch02Line, IconMoreDotH, IconMoreDropdownFill,
} from '@streamax/poppy-icons';
import i18n from '../../i18n';
import classNames from 'classnames';
import { StarOutlined, StarFilled, CaretDownFilled } from '@ant-design/icons';
import PersonalCenter, { contentItem } from './PersonalCenter/index';
import PersonalCenterXl from './PersonalCenter/index-xl';
import MessageNotification from '../../MessageModule/MessageNotification';
import type { SortEnd } from 'react-sortable-hoc';
import {
    SortableContainer,
    SortableElement,
    SortableHandle,
} from 'react-sortable-hoc';
import {
    getAppDetailById,
    doLogout,
    getCollectedEntrance,
    doCollectEntrance,
} from '../../service';
import arrayMove from 'array-move';
import { getAppGlobalData } from '../../global-data';
import { g_emmiter } from '../../emmiter';
import { UserAvatar } from '@streamax/starry-components';
import { ReactComponent as SecurityBg } from '@/assets/icons/icon_ identifi_ security_b.svg';
import Icon from '@streamax/poppy-icons/lib/Icon';
import CollectedEntry from '../CollectedEntry';
import type { OpenPersonalCenter } from './PersonalCenter';
import { tokenToVoucher, resourceEntranceUserSort } from '../../service';
import type { MessageNotificationRefType } from '../../MessageModule/MessageNotification';
import { OverflowEllipsisContainer } from '@streamax/starry-components';
import { switchServiceEntrance } from '../../entrance-manage';
import TenantExchange from './tenantExchange';
import { removeTenantExchangeUserInfo } from '../../utils/handleTenantExchangeUserInfo';
import { ClearPromptActionKey } from '../../RouterPrompt';
import { getImgUrl } from './utils/getImgUrl';
import { close as websocketMessageClose } from '../../hooks/useWebsocketMessage/controller';
import KeywordSearch from './KeywordSearch';
import { FILE_EXPORT_TASK_STATE } from './PersonalCenter/FileExport';
import { useShowHelpCenter } from './hooks/useShowHelpCenter';
import { HELP_CENTER_PATH } from './constants';
import { calcAppLogo } from '../../utils/general';
import { fetchParameterDetail } from '../../service';
import './style.less';
import { urlSkipTargetReplace } from '../utils';
import useGetS17FileUrl from '../../hooks/useGetS17FileUrl';
import useConfigParameter from '@/runtime-lib/hooks/useConfigParameter';
import { IconApplicationMenu } from '@streamax/poppy-icons';
import StackNotificationBox from './components/StackNotificationBox';
import { useResponsiveShow } from '@streamax/responsive-layout';
import ResponsiveShow from "@streamax/responsive-layout/lib/rsp-show";
import {ResponsiveDrawerLayoutRefProps} from "@streamax/responsive-layout/lib/rsp-drawer-layout";
import { LanguageSelect, lifecycle, StarryAbroadIcon, useSafetyModeStore } from "@/runtime-lib";
import useAppInfo from "@/runtime-lib/layout/useAppInfo";
import {useBreakpoint} from "@streamax/responsive-layout";
import { useCurrentAppStore } from '@/runtime-lib/entrance-manage/currentApp';
import { MESSAGE_DRAWER_TOGGLE_EMMITER } from './consants';
import { refreshCache } from '@/runtime-entry/main/utils/refreshCache';

const DragHandle: any = SortableHandle(() => (
    <IconDrag style={{ cursor: 'grab', color: '#999' }} />
));

export interface CustomHeaderProps {
    /**
     * 定制化头部右侧区域
     * 将自定义部件插入到已有的功能部件列表中，并返回，即可完成定制
     * @param comps 当前已有的功能部件列表
     * @returns
     */
    customHeaderRight?: (comps: ReactNode[], data?: Record<string, any>) => ReactNode[];
    customHeaderLeft?: (
        comps: ReactNode[],
        data: Record<string, any>,
    ) => ReactNode[];
    customHeaderStyle?: () => React.CSSProperties;
    // 定制个人中心tab
    getPersonalCenterContentList?: (params: contentItem[]) => contentItem[];
}

export interface IStarryHeaderProps extends CustomHeaderProps {
    userDropdownItems?: React.ReactNode[];
    appLogo?: string;
    setLoading: (loading: boolean) => void;
    menuDrawer?: ResponsiveDrawerLayoutRefProps
}

const clsPrefix = 'starry-layout-header';

const AppHeader: React.FC<IStarryHeaderProps> = (props) => {
    const { customHeaderRight, customHeaderLeft, customHeaderStyle,getPersonalCenterContentList, menuDrawer } = props;
    const size = useBreakpoint();
    const personalCenterRef = useRef<any>(null);
    const rightRef = useRef<any>(null);
    const [appInfo, setAppInfo] = useState<any>(null);
    const [personalCenterVisible, setPersonalCenterVisible] =
        useState<boolean>(false);
    const [personalCenterXlVisible, setPersonalCenterXlVisible] =
        useState<boolean>(false);
    const [collectedEntrance, setCollectedEntrance] = useState<any[]>([]);
    const [entrys, setEntrys] = useState(getAppGlobalData('APP_ENTRYS') || []);
    const [entryListVisible, setEntryListVisible] = useState<boolean>(false);
    const appId = getAppGlobalData('APP_ID') as string;
    const currentAppEntry = useCurrentAppStore(
        (state) => state.currentAppEntry,
    );
    const MsgRef = useRef<MessageNotificationRefType>(null);
    const hasOpenLogout = useRef<boolean>(false);

    const {appInfoDom,appLogo} = useAppInfo({appLogo: props.appLogo,mode: 'header'});

    const { account } = getAppGlobalData('REAL_USER_INFO');
    const themeColor = getAppGlobalData('APP_THEME');
    const [avatarInfo, setAvatarInfo] = useState<{
        avatarUrl: string | boolean;
        account: string;
    }>({
        account,
        avatarUrl: false,
    });
    const [showDocsBtn] = useShowHelpCenter();
    const show = useResponsiveShow({
        xxl: true,
        xl: true,
    });

    const safeMode = useSafetyModeStore.useDataStore((state) => state.safeMode);
    
    const entryIcons = useMemo(() => {
        return (getAppGlobalData('APP_ENTRYS') || []).map((i) => i.icon);
    }, []);
    const iconUrlMap = useGetS17FileUrl(entryIcons);
    // 更新iconUrl字段
    useEffect(() => {
        const keys = Object.keys(iconUrlMap);
        if (keys.length > 0) {
            const newEntrys = [];
            entrys.forEach((entry) => {
                const url = iconUrlMap[entry?.icon];
                newEntrys.push({
                    ...entry,
                    iconUrl: url,
                });
            });
            setEntrys(newEntrys);
        }
    }, [iconUrlMap]);
    const setAvatar = async () => {
        const { avatarId } = getAppGlobalData('REAL_USER_INFO');
        let url = true;
        try {
            if (avatarId) {
                url = await getImgUrl(avatarId);
            }
        } catch {
            //
        } finally {
            setAvatarInfo((data) => ({ ...data, avatarUrl: url }));
        }
    };

    useEffect(() => {
        useSafetyModeStore.getSafetyMode();
        const closeEntryList = () => setEntryListVisible(false);
        window.addEventListener('click', closeEntryList);
        // 设置用户头像
        setAvatar();
        return () => {
            window.removeEventListener('click', closeEntryList);
        };
    }, []);

    const handleHelpCenterSkip = () => {
        const entry = getAppGlobalData('APP_ENTRY');
        window.open(`${entry}${HELP_CENTER_PATH}?appId=${appId}`, 'target');
    };

    const getEntrances = () => {
        getCollectedEntrance({
            entrances: (entrys || []).map((p: any) => {
                return {
                    appId: p.appId,
                    resourceCode: p.resourceCode,
                };
            }),
        }).then((rs) => {
            const collectList: any[] = [];
            rs.forEach((item: any) => {
                entrys.forEach((ele: any) => {
                    item.resourceCode == ele.resourceCode &&
                        item.appId == ele.appId &&
                        collectList.push(ele);
                });
            });
            setCollectedEntrance(collectList);
        });
    };

    useEffect(() => {
        // 支持其他组件通过全局消息的形式控制个人中心显示或隐藏
        g_emmiter.on(
            '@starry:@event:@action:global.personal.modal.toggle.visible',
            (nextVisible: boolean) => {
                setPersonalCenterVisible(nextVisible);
            },
        );
    }, [appId, currentAppEntry]);

    useEffect(() => {
        if (entrys.length > 0) {
            getEntrances();
        }
    }, [JSON.stringify(entrys.map((i) => i.resourceId))]);

    useEffect(() => {
        if (personalCenterVisible) {
            g_emmiter.emit('@starry:@event:global.modal.opened');
        } else {
            g_emmiter.emit('@starry:@event:global.modal.closed');
        }
    }, [personalCenterVisible]);

    // 获取退出登录租户参数，
    const getLogoutTenantParams = async () => {
        try {
            const parameterData = await fetchParameterDetail(
                {
                    parameterKey: 'LOGOUT.TYPE',
                },
                false,
            );
            if (parameterData) {
                return parameterData.parameterValue;
            }
            return '';
        } catch (error) {
            // eslint-disable-next-line no-console
            console.log('不存在参数LOGOUT.TYPE', error);
            return '';
        }
    };

    const logout = async () => {
        const loginUrl = (window as any).APP_CONFIG['login.web.public.url'];
        const logoutParam = await getLogoutTenantParams();
        // 若租户参数存在并且配置了值为1，则设置跳转redirect_url为空，再次登录访问时，访问首页
        // 租户参数默认为0，退出时拼接当前访问路径，再次登录时访问当前路径
        // 本地localhost访问时，按默认场景拼接redirect_url
        const redirectUrl =
            Number(logoutParam) === 1 &&
            window.location.hostname !== 'localhost'
                ? ''
                : `?redirect_url=${window.encodeURIComponent(
                      window.location.href,
                  )}`;
        const callbackUrl = `${loginUrl}${redirectUrl}`;

        doLogout()
            .then(() => {
                websocketMessageClose();
                window.localStorage.removeItem('AUTH_TOKEN');
                window.sessionStorage.clear();
                removeTenantExchangeUserInfo();
                g_emmiter.emit(ClearPromptActionKey);
                setTimeout(async () => {
                    await lifecycle.logout.executeStep('before-navigation');
                    window.location.replace(callbackUrl);
                }, 0);
            })
            .catch(() => {
                message.error(i18n.t('message', 'token过期,请重新登录'));
            });
    };
    const onLogout = () => {
        // 避免重复打开
        if (!hasOpenLogout.current) {
            g_emmiter.emit('@starry:@event:global.modal.opened');
            hasOpenLogout.current = true;
            Modal.confirm({
                centered: true,
                title: i18n.t('name', '退出提示'),
                content: i18n.t('message', '确认退出当前系统？'),
                okText: i18n.t('action', '确定'),
                cancelText: i18n.t('action', '取消'),
                onOk: logout,
                onCancel: () => {
                    hasOpenLogout.current = false;
                    g_emmiter.emit('@starry:@event:global.modal.closed');
                },
            });
        }
    };
    const [innerLoading, setInnerLoading] = useState(false);
    useEffect(() => {
        props.setLoading(innerLoading);
    }, [innerLoading]);
    const handleSelect = async (params: any) => {
        try {
            const { serviceEntrance, openNewTab } = params || {};
            const currentEntry = getAppGlobalData('APP_ENTRY');
            if (serviceEntrance === currentEntry || innerLoading) return;
            setInnerLoading(true);
            setEntryListVisible(false);
            if (openNewTab) {
                const redirectUrl = await urlSkipTargetReplace(serviceEntrance);
                window.open(redirectUrl, '_blank');
                return;
            }
            await switchServiceEntrance(serviceEntrance);
        } finally {
            setInnerLoading(false);
        }
    };

    const handleToggleCollect = (
        e: React.MouseEvent<HTMLSpanElement>,
        resourceCode: number,
        appId: number,
        action: 0 | 1, // 0-收藏，1-取消收藏
    ) => {
        e.stopPropagation();
        e.preventDefault();
        doCollectEntrance({ resourceCode, appId, delFlag: action }).then(() => {
            getEntrances();
        });
    };

    const SortableItem: any = SortableElement((props: any) => {
        const {
            serviceEntrance,
            iconUrl,
            collected,
            resourceId,
            resourceCode,
            resourceName,
            translation,
            appId,
        } = props;
        return (
            <li
                key={serviceEntrance}
                className="entry-item"
                style={{
                    opacity: 1,
                    visibility: 'visible',
                }}
                onClick={() => handleSelect(props)}
            >
                {iconUrl ? <img src={iconUrl} alt="icon" /> : null}
                <span className="entry-name">
                    <OverflowEllipsisContainer>
                        {/* 需要根据应用来查询服务入口的国际化（translation是后端直接拼好的）*/}
                        {translation
                            ? translation
                            : i18n.t(
                                  `@i18n:@menu__${resourceCode}`,
                                  resourceName,
                              )}
                    </OverflowEllipsisContainer>
                </span>
                <Space>
                    <span
                        className="icon-drag-wrapper"
                        onClick={(e: React.MouseEvent<HTMLSpanElement>) => {
                            e.stopPropagation();
                            e.preventDefault();
                            return;
                        }}
                    >
                        <DragHandle />
                    </span>
                    <span
                        className={classNames('entry-collect-flag', {
                            collected,
                        })}
                        onClick={(e: React.MouseEvent<HTMLSpanElement>) =>
                            handleToggleCollect(
                                e,
                                resourceCode,
                                appId,
                                collected ? 1 : 0,
                            )
                        }
                    >
                        {collected ? <StarFilled /> : <StarOutlined />}
                    </span>
                </Space>
            </li>
        );
    });

    function onSortEnd({ oldIndex, newIndex }: SortEnd) {
        if (oldIndex !== newIndex) {
            const newData: any[] = arrayMove(
                [].concat(entrys as any),
                oldIndex,
                newIndex,
            );
            const sortData = newData.map((item) => {
                return {
                    appId: item?.appId,
                    resourceCode: item?.resourceCode,
                };
            });
            resourceEntranceUserSort({
                sort: sortData,
            })
                .then(() => {
                    setEntrys(newData);
                    refreshCache();
                })
                .catch(() => {
                    message.error(i18n.t('message', '操作失败'));
                });
        }
    }

    const SortableList: any = SortableContainer((props: any) => {
        return (
            <div>
                {(props.data || []).map((item: any, index: number) => {
                    const collected =
                        collectedEntrance.findIndex(
                            (p: any) =>
                                p.resourceCode === item.resourceCode &&
                                p.appId === item.appId,
                        ) !== -1;
                    return (
                        <SortableItem
                            key={item.resourceId}
                            index={index}
                            collected={collected}
                            {...item}
                        />
                    );
                })}
            </div>
        );
    });

    const renderEntryList = (entrys: any) => {
        return (
            <ul
                className={`${clsPrefix}-entry-list`}
                onClick={(e: React.MouseEvent<HTMLSpanElement>) => {
                    e.stopPropagation();
                    e.preventDefault();
                    return;
                }}
            >
                <SortableList
                    useDragHandle
                    helperClass="entry-item-dragging"
                    onSortEnd={onSortEnd}
                    data={entrys}
                />
            </ul>
        );
    };

    // 打开消息通知
    const openPersonalCenter: OpenPersonalCenter = (key, params) => {
        personalCenterRef.current?.openPersonalCenter(key, params);
        if (show) {
            setPersonalCenterVisible(true);
            // 如果是消息列表类型，发出抽屉打开事件
            if (key === 'messageList') {
                g_emmiter.emit(MESSAGE_DRAWER_TOGGLE_EMMITER, { isOpen: true });
            }
        } else {
            setPersonalCenterXlVisible(true);
        }
    };
    // 打开消息通知的下载中心
    const openDownLoad = () => {
        personalCenterRef.current?.openPersonalCenter('fileExport', {
            tabKey: FILE_EXPORT_TASK_STATE.SUCCESS,
        });
        setPersonalCenterVisible(true);
    };

    const onDrawerClose = () => {
        setAvatar();
        setPersonalCenterVisible(false);
        g_emmiter.emit(MESSAGE_DRAWER_TOGGLE_EMMITER, { isOpen: false });
    };

    const getPopupContainer = () => {
        return rightRef.current;
    };

    const DocsBtn = (
        <span>
            {showDocsBtn ? (
                <div
                    title={i18n.t('name', '帮助中心')}
                    style={{ display: 'flex', alignItems: 'center' }}
                    className={`right-wrapper-opt-icon ${clsPrefix}-help-center-icon`}
                >
                    <IconRequestFill
                        onClick={handleHelpCenterSkip}
                        style={{ fontSize: 20 }}
                    />
                </div>
            ) : null}
        </span>
    );

    // 获取展示下载中心的租户参数
    const { data } = useConfigParameter<string>({
        key: 'HEADER.TAB.DOWNLOAD',
        options: {
            defaultValue: '1',
            formatFn: (params) => {
                return params;
            },
        },
    });

    const DownLoadCenter = (
        <span>
            {data != 0 ? (
                <div
                    style={{ display: 'flex', alignItems: 'center' }}
                    title={i18n.t('name', '下载中心')}
                    onClick={openDownLoad}
                    className={`right-wrapper-opt-icon ${clsPrefix}-download-icon`}
                >
                    <BusIconDownload style={{ fontSize: '20px' }} />
                </div>
            ) : null}
        </span>
    );

    const MessageCenter = (
        <>
            <div
                className={`${clsPrefix}-message-count`}
                onClick={() => openPersonalCenter('messageList')}
                title={i18n.t('name', '消息通知')}
            >
                <MessageNotification
                    openPersonalCenter={openPersonalCenter}
                    ref={MsgRef}
                />
            </div>
            <StackNotificationBox openPersonalCenter={openPersonalCenter} />
        </>
    );

    const UserCenter = (
        <span
            className={`${clsPrefix}-user`}
            onClick={() => openPersonalCenter('personalInfo')}
        >
            {avatarInfo.avatarUrl ? (
                <>
                    <UserAvatar
                        bgColor={themeColor}
                        avatarUrl={avatarInfo.avatarUrl}
                        account={avatarInfo.account}
                    />
                    {safeMode && (
                        <Icon
                            component={SecurityBg}
                            className="security-identifier"
                        />
                    )}
                </>            
            ) : null}
        </span>
    );

    const LoginOutBtn = (
        <Tooltip
            title={i18n.t('name', '退出登录')}
            getPopupContainer={getPopupContainer}
        >
            <div
                onClick={onLogout}
                style={{ display: 'flex', alignItems: 'center' }}
                className={`right-wrapper-opt-icon ${clsPrefix}-logout`}
            >
                <IconClose01Fill style={{ fontSize: '20px' }} />
            </div>
        </Tooltip>
    );
    const onMenuClick = (key) => {
        if (key === 'helpCenter') {
            handleHelpCenterSkip();
        } else if (key === 'TenantExchange') {
        } else {
            setPersonalCenterVisible(true);
            personalCenterRef.current?.openPersonalCenter(key);
            setPersonalCenterXlVisible(false);
        }
    };
    const personCenter = () => {
        return (
            <PersonalCenterXl
                visible={true}
                showDocsBtn={showDocsBtn}
                onLogout={onLogout}
                onMenuClick={onMenuClick}
            />
        );
    };

    const content = [
        <KeywordSearch key="KeywordSearch" />,
        /* 切换租户 */
        <ResponsiveShow xl xxl key="TenantExchange">
            <TenantExchange />
        </ResponsiveShow>,
        /* 语言切换 */
        <LanguageSelect key="LanguageSelect" />,
        /* 帮助中心 */
        <ResponsiveShow xl xxl key="DocsBtn">
            {DocsBtn}
        </ResponsiveShow>,
        <ResponsiveShow xl xxl key="DownLoadCenter">
            {DownLoadCenter}
        </ResponsiveShow>,
        <ResponsiveShow xl xxl key="MessageCenter">
            {MessageCenter}
        </ResponsiveShow>,
        <ResponsiveShow xl xxl key="UserCenter">
            {UserCenter}
        </ResponsiveShow>,
        <ResponsiveShow xl xxl key="LoginOutBtn">
            {LoginOutBtn}
        </ResponsiveShow>,
        <ResponsiveShow xs sm md lg key="UserCenter">
            <Popover
                overlayClassName="header-person-info-tooltip"
                trigger={'click'}
                placement="bottomRight"
                content={
                    <PersonalCenterXl
                        visible={true}
                        showDocsBtn={showDocsBtn}
                        onLogout={onLogout}
                        onMenuClick={onMenuClick}
                    />
                }
                getPopupContainer={getPopupContainer}
            >
                <IconMoreDotH style={{ fontSize: 20 }} />
            </Popover>
        </ResponsiveShow>,
    ];

    const menuDrawerTrigger = (
        <ResponsiveShow xs sm>
            <IconMenu className={'menu-drawer-trigger'} onClick={()=>{
                menuDrawer?.openDrawer();
            }}/>
        </ResponsiveShow>
    );

    const AppDropDown = (
        <div
            key="appDropDown"
            className={classNames(`${clsPrefix}-console`, {
                [`${clsPrefix}-console-small`]: ['xs','sm'].includes(size),
            })}
            id={`${clsPrefix}-console`}
        >
            <Dropdown
                overlay={() => renderEntryList(entrys)}
                trigger={['click']}
                placement="bottomLeft"
                overlayClassName="entrys-dropdown"
                getPopupContainer={() =>
                    document.getElementById(`${clsPrefix}-console`) as any
                }
                visible={entryListVisible}
                onVisibleChange={(show: boolean) => {
                    if (show) {
                        g_emmiter.emit('@starry:@event:global.console.opened');
                    } else {
                        g_emmiter.emit('@starry:@event:global.console.closed');
                    }
                }}
            >
                <span
                    onClick={(e: React.MouseEvent<HTMLSpanElement>) => {
                        setEntryListVisible(!entryListVisible);
                        e.stopPropagation();
                        e.preventDefault();
                        return;
                    }}
                >
                    <div
                        className={classNames('console-icon', {
                            active: entryListVisible,
                        })}
                    >
                        <IconApplicationMenu style={{ fontSize: 16}}/>
                    </div>
                </span>
            </Dropdown>
        </div>
    );
    const AppCollected = (
        <div key="appCollected" className={`${clsPrefix}-collected-entrys`}>
            <CollectedEntry
                list={collectedEntrance}
                handleSelect={handleSelect}
            />
        </div>
    );
    const renderLeft = () => {
        const Header = [
            appInfoDom,
            AppDropDown,
            <ResponsiveShow md lg xl xxl>{AppCollected}</ResponsiveShow>
        ];
        if (customHeaderLeft instanceof Function) {
            console.warn(
                `customHeaderLeft：【customHeaderLeft】-${customHeaderLeft}`,
            );
            return customHeaderLeft(Header, {
                handleSelect,
                entrys,
                setEntrys,
                collectedEntrance,
                handleToggleCollect,
                onSortEnd,
                appInfo: {
                    hideAppName: appInfo?.hideAppName,
                    applicationId: appInfo?.applicationId,
                    applicationName: appInfo?.applicationName,
                    appLogo,
                    appFavicon: appInfo?.appFavicon,
                },
            });
        } else {
            return Header;
        }
    };

    return (
        <div
            className={`${clsPrefix}-wrapper`}
            style={{ ...customHeaderStyle?.() }}
        >
            <div className={clsPrefix}>
                <div className={`${clsPrefix}-left-wrapper`}>
                    {menuDrawerTrigger}
                    {renderLeft()}
                </div>
                <div className={`${clsPrefix}-right-wrapper`} ref={rightRef}>
                    {customHeaderRight ? customHeaderRight(content, {openPersonalCenter}) : content}
                </div>
            </div>

            {/* xl时二次弹出框使用 */}
            <PersonalCenter
                ref={personalCenterRef}
                getPersonalCenterContentList={getPersonalCenterContentList}
                drawerProps={{
                    destroyOnClose: true,
                    visible: personalCenterVisible,
                    onClose: onDrawerClose,
                    closeIcon: <IconClose02Line className="closeIcon" />,
                }}
            />
        </div>
    );
};
export default AppHeader;
