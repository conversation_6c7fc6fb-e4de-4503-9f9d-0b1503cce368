/*
 * 安全校验弹窗组件 - 验证码验证
 */

import React, { useRef, useState, useEffect } from 'react';
import { Modal, Button } from '@streamax/poppy';
import { AuthFormV1 } from '../../components/auth-form/index';
/**
 * TODO 迁移组件后替换runtime-lib
 */
import { i18n, InfoBack, Provider } from '@/runtime-lib';
import './SecurityVerifyModal.scope.less';
import { getSafetyMode } from '../utils';
const PR_CLASS_NAME = 'security-verify-modal';
const SecurityVerifyModal = ({ visible, onCancel, onSuccess }) => {
    const [loading, setLoading] = useState(false);
    const securityFormRef = useRef(null);

    const handleVerify = async () => {
        try {
            setLoading(true);
            await securityFormRef.current.securitySubmit();
            onSuccess(true);
            setLoading(false);
            //获取并设置安全模式
            getSafetyMode();
        } catch (error) {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        onCancel();
    };

    return (
        <Provider>
            <Modal
                title={i18n.t('name', '操作安全验证')}
                visible={visible}
                onCancel={handleCancel}
                destroyOnClose
                footer={[
                    <Button key="cancel" onClick={handleCancel}>
                        {i18n.t('action', '取消')}
                    </Button>,
                    <Button
                        key="verify"
                        type="primary"
                        loading={loading}
                        onClick={handleVerify}
                    >
                        {i18n.t('action', '安全验证')}
                    </Button>,
                ]}
                maskClosable={false}
                keyboard={false}
                width={520}
                className={`${PR_CLASS_NAME}`}
                zIndex={1062}
            >
                <>
                    <InfoBack
                        title={i18n.t('name', '为确保个人操作，请进行安全验证')}
                        className={`${PR_CLASS_NAME}-info`}
                    />
                    <AuthFormV1.SecurityForm ref={securityFormRef} />
                    <div className={`${PR_CLASS_NAME}-tip`}>
                        {i18n.t('name', '收不到验证码，请联系管理员')}
                    </div>
                </>
            </Modal>
        </Provider>
    );
};

export { SecurityVerifyModal };
