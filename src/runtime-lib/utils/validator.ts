/*
 * @LastEditTime: 2025-06-30 17:19:43
 */
import i18n from '../i18n';

// 校验密码
export const password = (rule: any, value: any) => {
    if (value && !/^(?=[a-zA-Z\d])./g.test(value.trim())) {
        return Promise.reject(i18n.t('message', '不能以符号开头'));
    }

    if (
        value &&
        // eslint-disable-next-line no-useless-escape
        !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[()`~!@#$%^&*\-+=_|{}[\]:;'<>,\.?/])[A-Za-z\d()`~!@#$%^&*\-+=_|{}[\]:;'<>,\.?/]{8,20}/g.test(
            value.trim(),
        )
    ) {
        return Promise.reject(i18n.t('message', '需要包含数字、符号、大小写字母'));
    }

    return Promise.resolve();
};

export const illegalCharacter = (rule: any, value: string) => {
    if (value && /[\\/*,？?‘’''“”""<>|｜]/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};

//放开对|\字符的校验，用于标签的添加和编辑
export const validateCharacters = (rule: any, value: string) => {
    if (value && /[/*,？?‘’''“”""<>｜]/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};

// 只能输入数字或字母
export const numberOrLetter = (rule: any, value: string) => {
    if (value && !/^[0-9a-zA-Z]+$/gi.test(value)) {
        return Promise.reject(i18n.t('message', '只能输入字母或数字'));
    }
    return Promise.resolve();
};

/**
 * - 只能输入英文字母、数字、下划线、破折号、邮箱字符
 * @param rule
 * @param value
 * @returns
 */
export const name = (rule: any, value: string) => {
    if (value && !/^[0-9a-zA-Z_\s@'._-]+$/g.test(value)) {
        return Promise.reject(i18n.t('message', '只能输入英文字母、数字、下划线、破折号、单引号、邮箱字符'));
    }
    return Promise.resolve();
};

/**
 * - 车组，用户，司机不能输入&__
 * @param rule
 * @param value
 * @returns
 */
export const validateBaseData = (rule: any, value: string) => {
    if (value && !/^(?!.*&__).*$/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};
/**
 * - 用户不能输入&__和{}
 * @param rule
 * @param value
 * @returns
 */
export const validateBaseUser = (rule: any, value: string) => {
    if (value && !/^(?!.*(&__|\{\s*})).*$/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};
/**
 * - 车牌号码不能输入&__和,
 * @param rule
 * @param value
 * @returns
 */
export const validateBaseVehicleNumber = (rule: any, value: string) => {
    if (value && !/^(?!.*(&__|,)).*$/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};