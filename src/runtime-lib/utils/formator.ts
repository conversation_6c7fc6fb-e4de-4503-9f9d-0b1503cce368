import moment, { Moment } from 'moment';
import { getAppGlobalData, setAppGlobalData } from '../global-data';
import { getCountryDetailByCode, getSummerTimePage } from '../service';
import { CacheType } from '../request';
import { KILOMETERS_PER_MILE } from './constant';

type SummerTimeItem = {
    startTime: string | number; //夏令时开始时间
    endTime: string | number; //夏令时结束时间
};
/**
 * byte 大小转为 G M K 等显示格式
 * @param {number} fileSize  待转换时间的文件大小 单位 byte
 * @param {boolean} showGB 可选，传入ture后，展示GB，否正为G
 * @return {string} 大于1G的转为G,其次转为M,剩余转为K
 */
export const formatByte = (fileSize: number, showGB?: boolean): string => {
    // byte 转为 Gib
    const gib = 1024 * 1024 * 1024;
    const mb = 1024 * 1024;
    const kb = 1024;
    if (fileSize > gib) {
        if (showGB) {
            return `${(fileSize / gib).toFixed(2)}GB`;
        }else {
            return `${(fileSize / gib).toFixed(2)}G`;
        }
    }
    if (fileSize > mb) {
        return `${(fileSize / mb).toFixed(2)}MB`;
    }
    return `${(fileSize / kb).toFixed(2)}KB`;
};

/**
 * @deprecated 已废弃，不建议使用
 */
export const formatTime = (times: any, correctToSec?: boolean) => {
    if (!times || typeof times !== 'number') return;
    const type = correctToSec !== false ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
    return moment(times * 1000).format(type);
};
/**
 * @description: 根据时间计算时间是否在夏令时范围内，返回夏令时偏移量
 * @param {number} sourceTime 传入时间
 * @param {number} datumTime 基准时间
 * @param {boolean} dstDisable 是否禁用夏令时
 * @param {boolean} minusOffset 夏令时偏移正负 默认正
 * @return {*}
 */
const calculateSummerTimeOffset = (
    sourceTime: number,
    datumTime: number | Moment | undefined,
    dstDisable: boolean | undefined,
    minusOffset: boolean = true,
): number => {
    // 夏令时判断开始
    if (dstDisable) return 0; //禁用夏令时则不需要夏令时偏移
    // dst 是否实行夏令时1实行 0关闭  summerTimeList 实行的夏令时范围  summerTimeOffset 夏令时偏移量 单位：分钟
    const { dst, summerTimeList, summerTimeOffset } = getAppGlobalData('APP_USER_CONFIG');
    let dstTime = 0;
    if (parseInt(dst, 10) === 1) {
        // 实行夏令时
        if (summerTimeList) {
            // 夏令时范围时间段转为number，方便后面比较
            summerTimeList.forEach((item: SummerTimeItem) => {
                item.startTime = parseInt(item.startTime as string, 10);
                item.endTime = parseInt(item.endTime as string, 10);
            });
            // 基准时间
            let startStamp = datumTime || sourceTime;
            // datumTime如果是moment对象转化为时间戳
            if (moment.isMoment(startStamp)) {
                const format = 'YYYY-MM-DD HH:mm:ss';
                startStamp = moment.utc(startStamp.format(format)).unix();
            }
            // @ts-ignore 基准时间10进制number类型
            startStamp = parseInt(startStamp, 10);
            const datumTimeInSummerTime = summerTimeList.some(
                (item: SummerTimeItem) =>
                    // @ts-ignore
                    startStamp > item.startTime && startStamp < item.endTime,
            );
            // 在夏令时范围内
            if (datumTimeInSummerTime) {
                dstTime = parseInt(summerTimeOffset, 10) * 60;
                // 判断夏令时偏移的正负
                dstTime = minusOffset ? dstTime : 0 - dstTime;
            }
        }
    } else {
        // 未实行夏令时
        dstTime = 0;
    }
    return dstTime;
    // 夏令时判断结束
};

/**
 * 将指定时区时间转换成0时区时间
 * @param {number | Moment} sourceTime  待转换时间的时间戳(s)或moment对象
 * @param {number} sourceZone  待转换时间的时区（s），默认取 用户策略的timeZone
 * @param {dstDisable} boolean 是否禁用夏令时处理，默认false
 * @param {number | Moment} datumTime 基准时间，若传入该时间，处理sourceTime的夏令时以datumTime判断是否处理
 */
export const timestampToZeroTimeStamp = (
    sourceTime: number | Moment,
    sourceZone?: number,
    dstDisable?: boolean,
    datumTime?: number | Moment,
) => {
    const config = getAppGlobalData('APP_USER_CONFIG');
    if (sourceZone === undefined || sourceZone === null) {
        sourceZone = parseInt(config.timeZone, 10) || 0;
    }
    if (!sourceTime) {
        return sourceTime;
    }
    if (moment.isMoment(sourceTime)) {
        const format = 'YYYY-MM-DD HH:mm:ss';
        sourceTime = moment.utc(sourceTime.format(format)).unix();
    }

    // @ts-ignore
    sourceTime = parseInt(sourceTime, 10);
    const dstTime = calculateSummerTimeOffset(sourceTime, datumTime, dstDisable);
    return sourceTime - dstTime - sourceZone;
};

/**
 * 0时区格林威治时间转换成指定时区时间
 * @param {timestamp} sourceTimeStamp  待转换时间的时间戳(s)
 * @param {number} targetZone  转换目标时区(s)，默认取 window.APP_USER_CONFIG.timeZone
 * @param {dstDisable} boolean 是否禁用夏令时处理，默认false
 * @param {datumTimeStamp} datumTimeStamp 基准时间，若传入该时间，处理sourceTimeStamp的夏令时以datumTimeStamp判断是否处理
 */
export const zeroTimeStampToZoneTime = (
    sourceTimeStamp: number,
    targetZone?: number,
    dstDisable?: boolean,
    datumTimeStamp?: number,
) => {
    const config = getAppGlobalData('APP_USER_CONFIG');
    if (targetZone === undefined || targetZone === null) {
        targetZone = parseInt(config.timeZone, 10) || 0;
    }
    if (!sourceTimeStamp) {
        return sourceTimeStamp;
    }
    // @ts-ignore
    sourceTimeStamp = parseInt(sourceTimeStamp, 10);
    const dstTime = calculateSummerTimeOffset(sourceTimeStamp, datumTimeStamp, dstDisable, false);
    return sourceTimeStamp - dstTime + targetZone;
};

// 秒转为时间格式显示
/**
 *
 * @param sec
 * @returns
 * @deprecated 已废弃，建议使用`secondsToTime`替换
 */
export const getShowTime = (sec: number) => {
    const time = moment.duration(sec, 'seconds');
    const showTime = [time.hours(), time.minutes(), time.seconds()].reduce(
        (re: string, item: any) => {
            if (item) {
                re += `${item.toString().padStart(2, '0')}:`;
            } else {
                re += `00:`;
            }
            return re;
        },
        '',
    );
    return showTime ? showTime.slice(0, -1) : '';
};

/**
 * 0时区时间戳转换成指定时区日期格式
 * @param {timestamp} sourceTimeStamp  0时区时间戳
 * @param {number} targetZone 转换目标时区，默认window.APP_USER_CONFIG.timeZone
 * @param {string} dateFormat 日期格式
 * @param {boolean} dstDisable 禁用夏令时 默认 false
 * @param {datumTimeStamp} datumTimeStamp  0时区时间戳,基准时间，若传入该时间，处理sourceTimeStamp的夏令时以datumTimeStamp判断是否处理
 */
export const zeroTimeStampToFormatTime = (
    sourceTimeStamp: number,
    targetZone?: number,
    dateFormat?: string,
    dstDisable?: boolean,
    datumTimeStamp?: number,
) => {
    const config = getAppGlobalData('APP_USER_CONFIG');
    if (targetZone === undefined || targetZone === null) {
        targetZone = parseInt(config.timeZone, 10) || 0;
    }
    if (!dateFormat) {
        dateFormat = `${config.timeFormat || 'YYYY-MM-DD'} HH:mm:ss`;
    }
    if (sourceTimeStamp === null || sourceTimeStamp === undefined || sourceTimeStamp === '') {
        return '';
    }
    // @ts-ignore
    sourceTimeStamp = parseInt(sourceTimeStamp, 10);
    const dstTime = calculateSummerTimeOffset(sourceTimeStamp, datumTimeStamp, dstDisable, false);
    const timestamp = sourceTimeStamp - dstTime + targetZone;
    return moment(timestamp * 1000)
        .utcOffset(0)
        .format(dateFormat);
};

/**
 *
 * @description 秒转换成时间
 * @param {number} seconds 秒数
 * @returns {string[]} [时, 分, 秒]
 */
export const secondsToTime = (seconds: number): string[] => {
    const time = moment.duration(seconds, 'seconds');
    return [time.hours(), time.minutes(), time.seconds()].map((item: number) => {
        if (item) {
            return `${item.toString().padStart(2, '0')}`;
        }
        return '00';
    });
};

const getUserTimeFormat = () => {
    return getAppGlobalData('APP_USER_CONFIG')?.timeFormat;
};
// 时区常量
export const TIMEZONE = new Proxy(
    {
        // 0 时区
        ZERO: 0,
        // 用户时区
        USER: null,
        // 平台时区
        PLATFORM: null,
    },
    {
        get(target, key: string) {
            switch (key) {
                case 'USER':
                    return Number(getAppGlobalData('APP_USER_CONFIG')?.timeZone) || 0;
                case 'PLATFORM':
                    return Number(getAppGlobalData('PLATFORM_TIME_ZONE') || 0);
                default:
                    return target[key];
            }
        },
    },
);

// 时间格式化常用常量

export const TIMEFORMAT = new Proxy(
    {
        // 用户策略时间格式
        USERDATETIME: '{userTimeFormat} HH:mm:ss',
        // 用户策略日期格式
        USERDATE: '{userTimeFormat}',
        // 默认时间格式
        DEFAULTDATETIME: `YYYY-MM-DD HH:mm:ss`,
        // 默认日期
        DEFAULTDATE: `YYYY-MM-DD`,
        // 默认时间
        DEFAULTTIME: `HH:mm:ss`,
    },
    {
        get(target, key: string) {
            if (key && key.startsWith instanceof Function && key.startsWith('USER')) {
                const userTimeFormat = getUserTimeFormat();
                return target[key].replace('{userTimeFormat}', userTimeFormat);
            }
        },
    },
);
export type DATETYPE = string | Moment | number;
/**
 *
 * @description 时区处理通用函数
 * @param {string | Moment | number} sourceTime  待转换时间的时间，支持字符串(格式与moment一致)、Moment、时间戳
 * @param {string} timeZone  待转换时间的时区，从 TIMEZONE 常量 获取
 * @param {string} resultTimeZone 返回时间的时区，从 TIMEZONE 常量 获取
 * @param {'moment' | 'timestamp' | 'string'} resultType 返回时间的类型'Moment' | 'Timestamp' | 'String',
 * @param {string} resultFormat 字符串类型的格式，默认用户策略格式, resultType为 `string` 时有效
 * @returns {Moment | number | string} result
 */
export const timeZoneTransfer = (
    sourceTime: string | Moment | number,
    timeZone: number,
    resultTimeZone: number,
    resultType: 'moment' | 'timestamp' | 'string',
    resultFormat?: string,
): DATETYPE => {
    // 将入参转为 moment 对象
    let sourceMoment;
    if (typeof sourceTime === 'string') {
        sourceMoment = moment(sourceTime);
    } else if (moment.isMoment(sourceTime || typeof sourceTime === 'number')) {
        sourceMoment = moment(sourceTime);
    } else {
        throw new Error('参数类型错误：sourceTime');
    }
    if (!sourceMoment.isValid()) {
        throw new Error('参数格式错误：sourceTime，请参考 moment 文档传入有效的时间');
    }
    // 将指定时区 moment 转为 0时区 时间戳
    const zoreTimestamp = timestampToZeroTimeStamp(sourceMoment, timeZone, true);
    console.log('zoreTimestamp' + zoreTimestamp);
    // 转为返回时区 时间戳
    const resultTimestamp = zeroTimeStampToZoneTime(zoreTimestamp, resultTimeZone, true);
    console.log('resultTimestamp' + resultTimestamp);
    // 格式化返回
    switch (resultType) {
        case 'moment':
            return moment(resultTimestamp * 1000);
        case 'timestamp':
            return resultTimestamp;
        case 'string':
            return zeroTimeStampToFormatTime(
                zoreTimestamp,
                resultTimeZone,
                resultFormat || TIMEFORMAT.DEFAULTDATETIME,
                true,
            );
    }
};
/**
 * @description: 根据用户配置获取国家地区配置，存入全局
 * @param {any} userConfig
 * @return {*}
 */
export const setUserInfoConfig = async (userConfig: any, CacheControl?: CacheType) => {
    let userConfigAndTime = {};
    // @ts-ignore
    const { areaCode } = userConfig;
    if (areaCode) {
        let summerTimeList = [];
        // 采用2个Promise，是为了同步请求，减少等待
        const countryDetailPromise = getCountryDetailByCode({ areaCode }, undefined, CacheControl);
        const getSummerTimePagePromise = getSummerTimePage(
            {
                areaCode,
                page: 1,
                pageSize: 1e6,
            },
            undefined,
            CacheControl,
        );
        const countryDetail = await countryDetailPromise;
        if (countryDetail?.summerTimeState) {
            const { list } = await getSummerTimePagePromise;
            summerTimeList = list || [];
        }
        userConfigAndTime = {
            // 地区code
            // @ts-ignore
            areaCode,
            // 地区时区偏移量
            timeZone: countryDetail?.areaTimeZone,
            // 地区经纬度
            areaLocation: countryDetail?.areaLocation,
            // 夏令时偏移量
            summerTimeOffset: countryDetail?.areaOffset,
            // 是否开启当前地区配置
            enableState: countryDetail?.enableState,
            // 是否实行夏令时
            dst: countryDetail?.summerTimeState,
            // 夏令时范围list
            summerTimeList,
        };
    }
    setAppGlobalData('APP_USER_CONFIG', { ...userConfig, ...userConfigAndTime });
};

/**
 * @description: 根据传入的时间返回当前的用户所在地区是否启用夏令时的偏移
 * @param {number} datumTime
 * @return {*}
 */
export const getSummerTimeOffsetByTime = (datumTime: number | Moment) => {
    // dst 是否实行夏令时1实行 0关闭  summerTimeList 实行的夏令时范围  summerTimeOffset 夏令时偏移量 单位：分钟
    // 如果没有传入时间，直接返回0
    if (!datumTime) return 0;

    // 获取配置信息
    const { dst, summerTimeList, summerTimeOffset } = getAppGlobalData('APP_USER_CONFIG');

    // 快速判断：如果未实行夏令时或没有夏令时列表，直接返回0
    if (parseInt(dst, 10) !== 1 || !summerTimeList || !summerTimeList.length) {
        return 0;
    }

    // 将datumTime转换为时间戳
    const timeStamp = moment.isMoment(datumTime)
        ? moment.utc(datumTime.format('YYYY-MM-DD HH:mm:ss')).unix()
        : parseInt(String(datumTime), 10);

    // 直接检查时间是否在任一夏令时范围内
    for (let i = 0; i < summerTimeList.length; i++) {
        const startTime = parseInt(String(summerTimeList[i].startTime), 10);
        const endTime = parseInt(String(summerTimeList[i].endTime), 10);

        if (timeStamp > startTime && timeStamp < endTime) {
            // 在夏令时范围内，返回偏移量
            return parseInt(String(summerTimeOffset), 10) * 60;
        }
    }

    // 不在任何夏令时范围内
    return 0;
};
/**
 * 0时区时间戳转为一个moment local 对象。用于时间选择器回显场景，之前的utc设置方式会导致此bug：https://rdms.streamax.com/index.php?m=bug&f=view&bugID=62716
 * @param timeStamp 0时区时间戳
 * @returns moment对象，以用户时区匹配
 */
export const getLocalMomentByZeroTimeStamp = (timeStamp: number) => {
    const format = 'YYYYMMDD HHmmss';
    // 采取先转为用户时区字符串格式，再使用 moment 初始化为电脑local时区的moment对象
    return moment(zeroTimeStampToFormatTime(timeStamp, undefined, format), format);
};

/**
 *
 * @param speed 速度
 * @param digits 小数点位数
 * @returns
 */
export const getSpeedFormat = (speed: number, digits: number = 1): string => {
    if (isNaN(speed)) {
        return (0).toFixed(digits);
    }
    const speedNum = Number(speed);
    const { speedUnit } = getAppGlobalData('APP_USER_CONFIG');
    if (parseInt(speedUnit, 10) === 1) {
        return speedNum?.toFixed(digits);
    }
    return (speedNum * KILOMETERS_PER_MILE).toFixed(digits);
};
