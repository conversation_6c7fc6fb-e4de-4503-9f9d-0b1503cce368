/* eslint-disable no-param-reassign */
import { getAppGlobalData, i18n } from '@base-app/runtime-lib';
import type { MapToolUserConfig } from '../components/MapLayerTool/type';
import { cloneDeep } from 'lodash';
import {
    DEFAULT_USER_SELECTION,
    MAP_LAYER_TYPE_ENUM,
} from '../components/MapLayerTool/const';
import { message } from '@streamax/poppy';
/**
 * 根据透明度数值获取对应的16进制的透明度
 * @param opacity 透明度
 */
export const getHexOpacity = (opacity: number) => {
    opacity = Math.max(opacity);
    opacity = Math.min(opacity, 1);
    let num = Math.round(255 * opacity);
    let str = '';
    const arrHex = [
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
    ];
    while (num > 0) {
        const mod = num % 16;
        num = (num - mod) / 16;
        str = arrHex[mod] + str;
    }
    if (str.length === 1) {
        str = `0${str}`;
    }
    if (str.length === 0) {
        str = '00';
    }
    return str;
};

/**
 * 将颜色转化成带透明度的颜色
 * @param color
 * @param opacity
 * @returns
 */
export const getHexOpacityColor = (color: string, opacity: number) => {
    if (!color) return '';
    const opacityHex = getHexOpacity(opacity);
    // @ts-ignore
    color = color.replace('#', '').toLocaleUpperCase();
    return `#${color}${opacityHex}`;
};

// 获取主题色的待透明度的颜色值
export const getPlatformThemeColorWithOpacity = (opacity: number) => {
    const themeColor = getAppGlobalData('APP_THEME') || '#597ef7';
    return getHexOpacityColor(themeColor, opacity);
};

// 全屏
export const fullScreen = (dom: Element) => {
    const rfs =
        dom.requestFullscreen ||
        //@ts-ignore
        dom.webkitRequestFullScreen ||
        //@ts-ignore
        dom.mozRequestFullScreen ||
        //@ts-ignore
        dom.msRequestFullScreen;
    if (rfs) {
        rfs.call(dom);
    } else if (typeof window.ActiveXObject !== 'undefined') {
        // for IE，这里其实就是模拟了按下键盘的F11，使浏览器全屏
        const wscript = new ActiveXObject('WScript.Shell');
        if (wscript != null) {
            wscript.SendKeys('{F11}');
        }
    }
};

// 退出全屏
export const exitFullScreen = () => {
    const dom = document;
    const cfs =
        dom.exitFullscreen ||
        //@ts-ignore
        dom.mozCancelFullScreen ||
        //@ts-ignore
        dom.webkitCancelFullScreen ||
        //@ts-ignore
        dom.msExitFullscreen;

    if (cfs) {
        cfs.call(dom);
    } else if (typeof window.ActiveXObject !== 'undefined') {
        // for IE，这里和fullScreen相同，模拟按下F11键退出全屏
        const wscript = new ActiveXObject('WScript.Shell');
        if (wscript != null) {
            wscript.SendKeys('{F11}');
        }
    }
};

export const int2latlng = (value: number) => value / Math.pow(10, 6);

// 转换经纬度 缩小10e6
export const transformIntList2LatLngList = (list: any[]) =>
    list.map((item) => {
        const { lat, lng } = { lat: item.lat, lng: item.lng };
        return {
            ...item,
            lat: int2latlng(lat),
            lng: int2latlng(lng),
        };
    });

/**
 * 根据提供的用户配置返回用户的选择。
 *
 * @param {MapToolUserConfig} userConfig - 用户对地图工具的配置
 * @return {UserSelection} 根据提供的用户配置返回用户的选择
 */
export const getUserSelection = (userConfig: MapToolUserConfig) => {
    const { mapLayerType, poiSwitch, roadSwitch } = userConfig;
    const curUserSelection = cloneDeep(DEFAULT_USER_SELECTION);
    if (mapLayerType === MAP_LAYER_TYPE_ENUM.VECTOR) {
        curUserSelection.vector = true;
    }
    if (mapLayerType === MAP_LAYER_TYPE_ENUM.SATELLITE) {
        curUserSelection.satellite = true;
        curUserSelection.road = roadSwitch;
    }
    if (mapLayerType === MAP_LAYER_TYPE_ENUM.TERRAIN) {
        curUserSelection.terrain = true;
    }
    if (poiSwitch !== undefined) {
        curUserSelection.lite = !poiSwitch;
    }
    return curUserSelection;
};
/**
 * 在图片上添加文字并返回新的图片URL
 * @param {string} imageUrl 原始图片URL
 * @param {string} text 要添加的文字
 * @returns {Promise<string>} 返回Promise，resolve带文字的图片Data URL
 */
export const addTextToImage = (imageUrl: string, text: string) => {
    return new Promise((resolve, reject) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        const BASE_HEIGHT = 80;
        // 处理跨域问题
        img.crossOrigin = 'Anonymous';
        img.onload = function () {
            // 设置canvas尺寸与图片相同
            canvas.width = img.width * 2;
            canvas.height = img.height + (BASE_HEIGHT / 2);
            // 绘制原始图片
            ctx?.drawImage(img, BASE_HEIGHT / 2, 0);
            // 设置文字样式
            ctx.fillStyle = 'rgba(255,255,255,0.45)';
            ctx.font = '14px "MyFont", Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            // 计算文字位置（居中）
            const x = canvas.width / 2;
            const y = canvas.height / 2 + (BASE_HEIGHT / 2);
            // 绘制文字
            ctx?.fillText(text, x, y);
            // 返回Data URL
            resolve(canvas.toDataURL('image/png'));
        };
        img.onerror = function () {
            reject(new Error(i18n.t('message', '图片加载失败')));
        };
        img.src = imageUrl;
    });
};
 /* @description: 通道权限枚举
 * @return {*}
 */
export enum FileChannelAuthEnum {
    /**
     *为空或者1：证据视频、图片全部通道均有权限
     */
    ALL_AUTH = 1, 
    /**
     *2：证据视频、图片部分通道有权限；
     */
    PART_AUTH = 2,
    /**
     *3：证据视频、图片全部通道均无权限
     */
    NO_AUTH = 3,
}
/**
 * 判断是否存在通道权限
 * @param {FileChannelAuthEnum | null | undefined} fileChannelAuth - 通道权限字段
 * @param {boolean} [messagePrompt=true] - 默认提示消息
 * @returns {boolean} 是否无权限，true 表示无权限，false 表示有权限
 */
export const noChannelAuth = (fileChannelAuth: FileChannelAuthEnum | undefined | null, messagePrompt = true): boolean => {
    if(fileChannelAuth === FileChannelAuthEnum.NO_AUTH) {
        messagePrompt && message.warn(i18n.t('message', '暂无通道权限，不支持下载/分享/导出操作'));
        return true;
    }
    return false;
};

/**
 * @description: 复制传入的内容
 * @param {*} text 要复制的内容
 * @return {*}
 */
export const copyToClipboard = async (text) => {
    if (text) {
        let textarea = null;
        try {
            // 现代浏览器，但不支持http
            if (navigator.clipboard?.writeText) {
                await navigator.clipboard.writeText(text);
                message.success(i18n.t('message', '复制成功'));
                return;
            }
            // 兼容http
            textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            const success = document.execCommand('copy');
            if (success) {
                message.success(i18n.t('message', '复制成功'));
            } else {
                message.warn(i18n.t('message', '复制失败'));
            }
        } catch (error) {
            message.warn(i18n.t('message', '复制失败'));
        }
        textarea && document.body.removeChild(textarea);
    }
};