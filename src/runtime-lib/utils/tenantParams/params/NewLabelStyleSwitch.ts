/**
 * 系统参数 - 控制是否开启新label交互 0-关闭 1-开启 默认开启
 */
import { TenantParamBase } from '../base/TenantParamBase';
import { TenantParamManager } from '../base/TenantParamManager';
import { TenantParamKeys } from '../constants/keys';

// 系统参数 - 控制是否开启新label交互 0-关闭 1-开启 默认开启枚举
export enum NEW_LABEL_STYLE_SWITCH {
  // 关闭新label交互
  DISABLE = '0',
  // 开启新label交互
  ENABLE = '1'
}

/**
 * 系统参数 - 控制是否开启新label交互 0-关闭 1-开启 默认开启类
 */
export class NewLabelStyleSwitchParam extends TenantParamBase<NEW_LABEL_STYLE_SWITCH> {
  readonly key = TenantParamKeys.NEW_LABEL_STYLE_SWITCH;
  readonly defaultValue = NEW_LABEL_STYLE_SWITCH.ENABLE;
  readonly type = 'string';
  readonly enumValues = NEW_LABEL_STYLE_SWITCH;

  constructor() {
    // 调用父类构造函数，传入默认值
    super(NEW_LABEL_STYLE_SWITCH.ENABLE);
    // 初始化存储，确保使用正确的默认值
    this.initStore();
  }

  /**
   * 解析方法
   * @param value 字符串值
   * @returns 解析后的值
   */
  parse(value: string): NEW_LABEL_STYLE_SWITCH {
      try {
      const parsedValue = value;

      // 检查值是否在枚举范围内
      if (
          parsedValue === NEW_LABEL_STYLE_SWITCH.ENABLE ||
          parsedValue === NEW_LABEL_STYLE_SWITCH.DISABLE
      ) {
          return parsedValue;
      }
          return this.defaultValue;
    } catch (error) {
      console.error(`解析参数 ${this.key} 失败:`, error);
      return this.defaultValue;
    }
  }

  /**
   * 判断是否是DISABLE
   * @returns 是否是DISABLE
   */
  isDisable(): boolean {
    return this.get() === NEW_LABEL_STYLE_SWITCH.DISABLE;
  }

  /**
   * 判断是否是ENABLE
   * @returns 是否是ENABLE
   */
  isEnable(): boolean {
    return this.get() === NEW_LABEL_STYLE_SWITCH.ENABLE;
  }
}

// 创建实例并注册
export const newLabelStyleSwitchParam = new NewLabelStyleSwitchParam();
TenantParamManager.getInstance().register(newLabelStyleSwitchParam);
