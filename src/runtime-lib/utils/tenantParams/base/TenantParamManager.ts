/**
 * 租户参数管理器
 * 负责批量获取租户参数
 */
import { TenantParamBase } from './TenantParamBase';
import { TenantParamItem, TenantParamPageResult } from './types';
import { fetchParameterPostPage } from '../../../../service/parameter';

export class TenantParamManager {
  private static instance: TenantParamManager;
  private paramMap: Map<string, TenantParamBase<any>> = new Map();
  private loadingPromise: Promise<void> | null = null;

  /**
   * 单例模式
   * @returns TenantParamManager 实例
   */
  static getInstance(): TenantParamManager {
    if (!TenantParamManager.instance) {
      TenantParamManager.instance = new TenantParamManager();
    }
    return TenantParamManager.instance;
  }

  /**
   * 注册租户参数
   * @param param 租户参数实例
   */
  register(param: TenantParamBase<any>): void {
    this.paramMap.set(param.key, param);
  }

  /**
   * 批量加载租户参数
   * @param cacheType 缓存类型
   * @returns Promise
   */
  async loadParams(): Promise<void> {
    // 确保只加载一次
    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = this._loadParams();
    return this.loadingPromise;
  }

  /**
   * 内部加载方法
   * @param cacheType 缓存类型
   * @returns Promise
   */
  private async _loadParams(): Promise<void> {
    try {
      // 获取所有注册的参数键
      const parameterKeys = Array.from(this.paramMap.keys());

      if (parameterKeys.length === 0) {
        return;
      }

      // 设置所有参数为加载中状态
      this.paramMap.forEach(param => {
        param.setLoading(true);
      });

      // 批量获取参数
      const result = await fetchParameterPostPage({
        parameterKeys,
        page: 1,
        pageSize: parameterKeys.length,
      }) as TenantParamPageResult;

      const list = result.list || [];

      // 更新参数值
      for (const item of list) {
          const param = this.paramMap.get(item.parameterKey);
          if (param) {
          try {
            // 使用参数自己的 parse 方法解析值
            const parsedValue = param.parse(item.parameterValue);
            param.setValue(parsedValue);
          } catch (error) {
            console.error(`解析参数 ${item.parameterKey} 失败:`, error);
            param.setError(error);
          }
        }
      }

      // 处理未返回的参数（使用默认值）
      const returnedKeys = list.map((item: TenantParamItem) => item.parameterKey);
      this.paramMap.forEach((param, key) => {
        if (!returnedKeys.includes(key)) {
          console.warn(`参数 ${key} 未返回，使用默认值`);
        }
        param.setLoading(false);
      });
    } catch (error) {
      console.error('加载租户参数失败:', error);
      // 设置所有参数为错误状态
      this.paramMap.forEach(param => {
        param.setError(error);
        param.setLoading(false);
      });
    }
  }

  /**
   * 获取指定参数
   * @param key 参数键名
   * @returns 参数实例
   */
  getParam<T>(key: string): TenantParamBase<T> | undefined {
    return this.paramMap.get(key) as TenantParamBase<T> | undefined;
  }
}
