/* eslint-disable no-template-curly-in-string */
import { FC } from 'react';
import { getAppGlobalData } from './global-data';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { ConfigProvider, Empty } from '@streamax/poppy';
import { ConfigProvider as ConfigProviderStarry } from '@streamax/starry-components';
import { RspProvider } from '@streamax/responsive-layout';
import * as tenantParams from './utils/tenantParams';
import { Form } from '@streamax/poppy';
import i18n from './i18n';
import zh_CN_POPPY from '@streamax/poppy/lib/locale/zh_CN';
import en_US_POPPY from '@streamax/poppy/lib/locale/en_US';
import es_ES_POPPY from '@streamax/poppy/lib/locale/es_ES';
import pt_BR_POPPY from '@streamax/poppy/lib/locale/pt_BR';
import fr_FR_POPPY from '@streamax/poppy/lib/locale/fr_FR';
import zh_HK_POPPY from '@streamax/poppy/lib/locale/zh_HK';
import ru_RU_POPPY from '@streamax/poppy/lib/locale/ru_RU';
import de_DE_POPPY from '@streamax/poppy/lib/locale/de_DE';
import ja_JP_POPPY from '@streamax/poppy/lib/locale/ja_JP';
import th_TH_POPPY from '@streamax/poppy/lib/locale/th_TH';
import ar_EG_POPPY from '@streamax/poppy/lib/locale/ar_EG';
import vi_VN_POPPY from '@streamax/poppy/lib/locale/vi_VN';
import tr_TR_POPPY from '@streamax/poppy/lib/locale/tr_TR';
import pl_PL_POPPY from '@streamax/poppy/lib/locale/pl_PL';
import sr_RS_POPPY from '@streamax/poppy/lib/locale/sr_RS';
import it_IT_POPPY from '@streamax/poppy/lib/locale/it_IT';
import tg_TJ_POPPY from '@streamax/poppy/lib/locale/tg_TJ';


import zh_CN_STARRY from '@streamax/starry-components/lib/locale/zh_CN';
import en_US_STARRY from '@streamax/starry-components/lib/locale/en_US';
import es_ES_STARRY from '@streamax/starry-components/lib/locale/es_ES';
import pt_BR_STARRY from '@streamax/starry-components/lib/locale/pt_BR';
import fr_FR_STARRY from '@streamax/starry-components/lib/locale/fr_FR';
import zh_HK_STARRY from '@streamax/starry-components/lib/locale/zh_HK';
import ru_RU_STARRY from '@streamax/starry-components/lib/locale/ru_RU';
import de_DE_STARRY from '@streamax/starry-components/lib/locale/de_DE';
import ja_JP_STARRY from '@streamax/starry-components/lib/locale/ja_JP';
import th_TH_STARRY from '@streamax/starry-components/lib/locale/th_TH';
import ar_EG_STARRY from '@streamax/starry-components/lib/locale/ar_EG';
import vi_VN_STARRY from '@streamax/starry-components/lib/locale/vi_VN';
import tr_TR_STARRY from '@streamax/starry-components/lib/locale/tr_TR';
import pl_PL_STARRY from '@streamax/starry-components/lib/locale/pl_PL';
import { useSystemComponentStyle } from './hooks';
import sr_RS_STARRY from '@streamax/starry-components/lib/locale/sr_RS';
import it_IT_STARRY from '@streamax/starry-components/lib/locale/it_IT';
import tg_TJ_STARRY from '@streamax/starry-components/lib/locale/tg_TJ';
import { ConfigProviderProps } from '@streamax/poppy/lib/config-provider';

const poppyLocales = {
    zh_CN: zh_CN_POPPY,
    en_US: en_US_POPPY,
    es_ES: es_ES_POPPY,
    pt_BR: pt_BR_POPPY,
    fr_FR: fr_FR_POPPY,
    zh_HK: zh_HK_POPPY,
    ru_RU: ru_RU_POPPY,
    de_DE: de_DE_POPPY,
    ja_JP: ja_JP_POPPY,
    th_TH: th_TH_POPPY,
    ar_EG: ar_EG_POPPY,
    vi_VN: vi_VN_POPPY,
    tr_TR: tr_TR_POPPY,
    pl_PL: pl_PL_POPPY,
    sr_RS: sr_RS_POPPY,
    it_IT: it_IT_POPPY,
    tg_TJ: tg_TJ_POPPY,
};
const starryLocales = {
    zh_CN: zh_CN_STARRY,
    en_US: en_US_STARRY,
    es_ES: es_ES_STARRY,
    pt_BR: pt_BR_STARRY,
    fr_FR: fr_FR_STARRY,
    zh_HK: zh_HK_STARRY,
    ru_RU: ru_RU_STARRY,
    de_DE: de_DE_STARRY,
    ja_JP: ja_JP_STARRY,
    th_TH: th_TH_STARRY,
    ar_EG: ar_EG_STARRY,
    vi_VN: vi_VN_STARRY,
    tr_TR: tr_TR_STARRY,
    pl_PL: pl_PL_STARRY,
    sr_RS: sr_RS_STARRY,
    it_IT: it_IT_STARRY,
    tg_TJ: tg_TJ_STARRY,
};

const RSP_PREFIX = 'base-platform';
const ENGLISH = 'en_US';

const formatValidateMessage = (message: string) => {
    return message.charAt(0).toLocaleUpperCase() + message.slice(1).toLocaleLowerCase();
};

// 将label的第一个字母大写，并将后面的字母全部小写
const FormItem = Form.Item;
Form.Item = (props) => {
    const { label, textFieldLabel, rules } = props;
    const lang = getAppGlobalData('APP_LANG');
    const messageVariables: Record<string, any> = {};
    const _label = label || textFieldLabel;
    if (lang === ENGLISH) {
        if(typeof _label === 'string' && _label) {
            messageVariables.label = formatValidateMessage(_label);
        }
        if (rules && rules.length > 0) {
            rules.forEach((rule) => {
                if (typeof rule.message === 'string' && rule.message) {
                    rule.message = formatValidateMessage(rule.message);
                }
                if (rule.validator) {
                    const originalValidator = rule.validator;
                    rule.validator = async (...args) => {
                        try {
                            await originalValidator(...args);
                            return Promise.resolve();
                        } catch (error) {
                            let message;
                            if (typeof error === 'string') {
                                /**
                                 * 自定义校验函数，返回字符串时
                                 * return Promise.reject(
                                 *  i18n.t('message', '最多选择{number}个推送角色', { number: 20 }),
                                 * );
                                 */
                                message = formatValidateMessage(error);
                            } else if (typeof error ==='object' && error?.message) {
                                /**
                                 * 自定义校验函数，返回Error类型时
                                 * return Promise.reject(
                                 *  new Error(
                                 *      i18n.t('message', '最多选择{number}个推送角色', { number: 20 }),
                                 *  ),
                                 * );
                                 */
                                message = formatValidateMessage(error.message);
                            }
                            return Promise.reject(message);
                        };
                    };
                }
            });
        }
    }
    return <FormItem messageVariables={messageVariables} {...props} />;
};
Object.keys(FormItem).forEach((key) => {
    (Form.Item as any)[key] = (FormItem as any)[key];
});
// 将label的第一个字母大写，并将后面的字母全部小写 end

export const _Provider: FC<
    ConfigProviderProps & { children: React.ReactNode }
> = ({ children, ...configProviderProps }) => {

    // 获取新label交互开关
    const showNewLabelStyle = tenantParams.newLabelStyleSwitchParam.isEnable();
    //表格接入拖拽表格
    const enableDraggable = tenantParams.tableVersion.isDraggableTable();    
    const { isAbroadStyle } = useSystemComponentStyle();
    const formItemWidth = isAbroadStyle ? '100%' : 340;
    const lang = getAppGlobalData('APP_LANG');
    const { timeFormat } = getAppGlobalData('APP_USER_CONFIG') || { timeFormat: 'YYYY-MM-DD' };
    const notNeedSpaceSeparateList = ['zh_CN', 'zh_HK', 'ja_JP'];
    const localeOfPoppy = poppyLocales[lang] || zh_CN_POPPY;
    const localeOfStarry = starryLocales[lang] || zh_CN_STARRY;
    let _typeTemplate = i18n.t('message', '格式错误，请重新输入');
    let _noEmpty = i18n.t('message', '不能为空');
     // 语言为英语时，转为小写，因为前面还会拼接上字段label，一个句子仅首字母需要大写。
    if (lang === ENGLISH) {
        _noEmpty = _noEmpty.toLocaleLowerCase();
        _typeTemplate = _typeTemplate.toLocaleLowerCase();
    }
    const typeTemplate = `\${label}${_typeTemplate}`;
    let noEmpty = `\${label}${_noEmpty}`;
    // 设置不使用空格作为分隔的语言，为空的提示不用空格分隔，其他语言使用空格分隔
    if (notNeedSpaceSeparateList.indexOf(lang) === -1) {
        noEmpty = `\${label} ${_noEmpty}`;
    }
    
    console.warn('Provider success', Date.now(), noEmpty);
    const validateMessages = {
        default: typeTemplate,
        required: noEmpty,
        enum: typeTemplate,
        whitespace: noEmpty,
        date: {
            format: typeTemplate,
            parse: typeTemplate,
            invalid: typeTemplate,
        },
        types: {
            string: typeTemplate,
            method: typeTemplate,
            array: typeTemplate,
            object: typeTemplate,
            number: typeTemplate,
            date: typeTemplate,
            boolean: typeTemplate,
            integer: typeTemplate,
            float: typeTemplate,
            regexp: typeTemplate,
            email: typeTemplate,
            url: typeTemplate,
            hex: typeTemplate,
        },
        string: {
            len: typeTemplate,
            min: i18n.t('message', '至少填写{number}个字符', {
                number: '${min}',
            }),
            max: i18n.t('message', '不能超过{number}个字符', {
                number: '${max}',
            }),
            range: typeTemplate,
        },
        number: {
            len: typeTemplate,
            min: i18n.t('message', '请输入大于{number}的数字', {
                number: '${min}',
            }),
            max: i18n.t('message', '请输入小于{number}的数字', {
                number: '${max}',
            }),
            range: typeTemplate,
        },
        array: {
            len: typeTemplate,
            min: typeTemplate,
            max: typeTemplate,
            range: typeTemplate,
        },
        pattern: {
            mismatch: typeTemplate,
        },
    };
    // function renderEmpty() {
    //     return (
    //         <Empty
    //             imageStyle={{
    //                 marginTop: '55px',
    //                 maxWidth: '100%',
    //             }}
    //             style={{ height: '280px' }}
    //             description={
    //                 <span style={{ color: 'rgba(0, 0, 0, .65)' }}>
    //                     {i18n.t('common.base.empty.message')}
    //                 </span>
    //             }
    //         />
    //     );
    // }
    return (
        <ConfigProviderStarry locale={localeOfStarry}>
            <ConfigProvider
                // renderEmpty={renderEmpty}
                form={{ validateMessages }}
                locale={localeOfPoppy}
                table={{
                    enableMinWidthColumn: true,
                    enableDraggable
                }}
                dateFormat={timeFormat}
                showNewLabelStyle={ showNewLabelStyle }
                {...configProviderProps}
            >
                <RspProvider formItemWidth={formItemWidth}>
                    {children}
                </RspProvider>
            </ConfigProvider>
        </ConfigProviderStarry>
    );
};
