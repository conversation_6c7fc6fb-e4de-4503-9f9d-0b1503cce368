/**
 * @description state文件，存储的需要组件之间需要共享的数据
 */

import type {
    ActionInfo,
    LayoutType,
    PlayerOptions,
    PlayerStatus,
    StoreType,
    StreamType,
    RecordStatus,
    FMPTYPE,
    PlayMode,
    Reconnect,
    ChannelStateInfo,
    WidgetNameType,
    hiddenWidgetName,
    AuthComponentNameType,
    PlaybackType,
    WaterConfig,
    PlayAspectRatio,
} from '../types';

export interface State {
    // 回放类型
    playbackType: PlaybackType | null;
    // 页面编码
    pageCode: string | null;
    // 需要控制权限的功能，为空的话表示都需要控制权限
    authWidgets: AuthComponentNameType[];
    // 是否是直通
    isLive?: boolean;
    // 播放器状态
    playerStatus: PlayerStatus;
    // 播放参数
    options: PlayerOptions;
    // 码流类型
    streamType: StreamType;
    // 存储器类型
    storeType: StoreType;
    // 音量
    volume: number;
    // 布局窗口数量
    layoutCount: number;
    // 当前页
    currentPage: number;
    // 总页数
    totalPage: number;
    // 当前播放时间, 以秒为单位的0时区时间戳
    currentPlayTime: number;
    // 当前播放倍速
    playSpeed: number;
    // 当前布局类型
    layoutType: LayoutType;
    // 选中待播放通道
    selectedChannels: string[];
    // 当前正在播放中的通道
    currentPlayChannels: string[];
    // 选中激活窗口
    activeChannel: string;
    // 播放模式
    playMode: PlayMode;
    // 播放比例
    playAspectRatio: PlayAspectRatio;
    // 自动重连
    reconnect: Reconnect;
    // 帧率、码率
    frameRateAndBitRate: FMPTYPE[];
    // 录像状态
    recordStatus: RecordStatus;
    // 播放器根节点
    playerRootDom?: HTMLElement;
    // 自动关闭时长
    autoCloseDuration?: number;
    // 倒计时剩余时长
    remainingTime?: number;
    // 全屏状态
    fullscreen: boolean;
    // 通道全屏
    channelFullScreen: boolean;
    // 禁用功能列表
    disabledWidgets: WidgetNameType[];
    // 按通道禁用功能
    channelDisabledWidgets: {
        [channelNo: string]: WidgetNameType[];
    };
    // 隐藏功能列表
    hiddenWidgets: hiddenWidgetName[];
    // 通道对象数据，存储每个通道需要一些相关数据
    channelInfoMap: {
        [channelNo: string]: ChannelStateInfo;
    };
    // 操作动作相关数据
    actionInfo?: ActionInfo;
    // 是否启用倒计时结束自动关闭
    autoCloseAvailable?: boolean;
    // 各个阶段的hooks
    hooks: {
        // 触发操作前的回调，可用于终止操作的执行
        onBeforeAction?: (actionInfo?: ActionInfo) => Promise<boolean> | boolean;
    }
    poster?: {
        pause?: boolean,
        stop?: boolean,
        strategy?: 'AUTO' | 'QUALITY' | 'PERFORMANCE'
    }
    // 将要seek到的时间点
    lastProgressTime?: number | null;
    // 关闭前的播放器状态
    beforeClosePlayerStatus?: PlayerStatus;
    // 准备播放
    beforePlay?: boolean;
    // 是否支持单sdk多通道播放
    deviceMultiplexStatus?: boolean;
    // 水印信息
    getSDKWaterConfig?: (() => Promise<WaterConfig>) | null;
    // 设备在线状态
    deviceOnlineState: boolean;
    // 通道全屏，channel&1 | channel&0
    fullChannel: string;
    // 是否有GPU不足报错
    gpuError: boolean;
    // 舱内通道
    cabinChannel: string[];
    // gpu 报错数据
    gpuErrorData: Record<string, boolean>;
}
const initialState: State = {
    actionInfo: undefined,
    playbackType: null,
    pageCode: null,
    authWidgets: [],
    isLive: false,
    playerStatus: 'idle',
    options: {},
    streamType: 'MINOR',
    storeType: 'MASTER',
    volume: 0.5,
    layoutCount: 4,
    currentPage: 1,
    totalPage: 1,
    currentPlayTime: 0,
    playSpeed: 1,
    layoutType: 'tile',
    selectedChannels: [],
    currentPlayChannels: [],
    activeChannel: '1',
    playMode: 'normal',
    playAspectRatio: 'origin',
    reconnect: true,
    frameRateAndBitRate: [],
    recordStatus: 'close',
    fullscreen: false,
    channelFullScreen: false,
    channelInfoMap: {},
    disabledWidgets: [],
    channelDisabledWidgets: {},
    getSDKWaterConfig: null,
    hiddenWidgets: [],
    autoCloseAvailable: true,
    hooks: {},
    lastProgressTime: null,
    beforeClosePlayerStatus: 'idle',
    beforePlay: false,
    deviceMultiplexStatus: true,
    deviceOnlineState: true,
    fullChannel: '1-0',
    poster: {
        strategy: 'AUTO'
    },
    gpuError: false,
    cabinChannel: [],
    gpuErrorData: {},
};

export default initialState;
