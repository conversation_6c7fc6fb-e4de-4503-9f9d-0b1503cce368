/**
 * @description reducer文件
 */

import type { Reducer, Dispatch } from "react";
import { cloneDeep } from "lodash";
import type { State } from "./state";
import type { ChannelStateInfo } from "../types";
import initialState from "./state";
import { zustandUsePlayerState } from ".";
import { PLAYER_STATUS } from "../constant";

type TranslateType<T> = {
  [K in keyof T]: {
    type: K;
    payload: T[K];
  };
}[keyof T];

export type PayloadMap = {
  reset: any;
  collectContext: any;
  broadCast: any;
  setActionInfo: State["actionInfo"];
  setOptions: State["options"];
  setPlayerStatus: State["playerStatus"];
  setLayoutType: State["layoutType"];
  setLayoutCount: State["layoutCount"];
  setAutoCloseDuration: State["autoCloseDuration"];
  setRemainingTime: State["remainingTime"];
  setVolume: State["volume"];
  setPage: State["currentPage"];
  setTotalPage: State["totalPage"];
  setPlaySpeed: State["playSpeed"];
  setFrameRateAndBitRate: State["frameRateAndBitRate"];
  setSelectedChannels: State["selectedChannels"];
  setCurrentPlayChannels: State["currentPlayChannels"];
  setStoreType: State["storeType"];
  setStreamType: State["streamType"];
  setActiveChannel: State["activeChannel"];
  setRecordStatus: State["recordStatus"];
  setChannelInfoMap: State["channelInfoMap"];
  setFullScreen: State["fullscreen"];
  setDisabledWidgets: State["disabledWidgets"];
  setChannelDisabledWidgets: State["channelDisabledWidgets"];
  setHiddenWidgets: State["hiddenWidgets"];
  setGetSDKWaterConfig: State["getSDKWaterConfig"];
  setPlayMode: State["playMode"];
  setPlayAspectRatio: State["playMode"];
  playAspectRatio: State["playAspectRatio"];
  setReconnect: State["reconnect"];
  setPageCode: State["pageCode"];
  setAuthWidgets: State["authWidgets"];
  setPlaybackType: State["playbackType"];
  setIsLive: State["isLive"];
  setHooks: State["hooks"];
  setPoster: State["poster"];
  updateChannelInfo: {
    channel: string;
    info: Partial<ChannelStateInfo>;
  };
  updateChannelsInfo: {
    infos: Partial<ChannelStateInfo>[];
  };
  setPlayerStatusSyncChannel: {
    syncChannels: string[];
    status: State["playerStatus"];
    updatePlayerStatus?: boolean;
    errorMsg?: string;
  };
  setCurrentTime: State["currentPlayTime"];
  setAutoCloseAvailable: State["autoCloseAvailable"];
  setChannelFullScreen: State["channelFullScreen"];
  setLastProgressTime: State["lastProgressTime"];
  setBeforeClosePlayerStatus: State["beforeClosePlayerStatus"];
  setBeforePlay: State["beforePlay"];
  setDeviceMultiplexStatus: State["deviceMultiplexStatus"];
  setDeviceOnlineState: State["deviceOnlineState"];
  setFullChannel: State["fullChannel"];
  setDefaultData: any;
  setGpuError: State['gpuError'];
  setCabinChannel: State['cabinChannel'];
  setGpuErrorData: State['gpuErrorData'];
};

export type Action = TranslateType<PayloadMap>;

export const reducer: Reducer<State, Action> = (prevState, action) => {
  const { type, payload } = action;
  switch (type) {
    case "reset":
      zustandUsePlayerState.setState(initialState);
      // 重置整个状态
      return {
        ...zustandUsePlayerState.getState(),
      };
    case "setActionInfo":
      // 更新操作信息
      return {
        ...prevState,
        actionInfo: payload,
      };
    case "setOptions":
      // 更新options
      return {
        ...prevState,
        options: payload,
      };
    case "setPlayerStatus":
      // 更新播放器状态
      return {
        ...prevState,
        playerStatus: payload,
      };
    case "setLayoutType":
      // 更新布局模式
      return {
        ...prevState,
        layoutType: payload,
      };
    case "setLayoutCount":
      // 更新布局数量
      return {
        ...prevState,
        layoutCount: payload,
      };
    case "setAutoCloseDuration":
      // 更新倒计时关闭视频的时间
      return {
        ...prevState,
        autoCloseDuration: payload,
      };
    case "setRemainingTime":
      // 更新倒计时还剩余时长
      if (typeof payload === "string" && payload === "mul") {
        const preTime: number = prevState.remainingTime || 0;
        return {
          ...prevState,
          remainingTime: preTime - 1,
        };
      } else {
        return {
          ...prevState,
          remainingTime: payload,
        };
      }
    case "setVolume":
      // 更新音量
      return {
        ...prevState,
        volume: payload,
      };
    case "setPage":
      // 更新当前页
      return {
        ...prevState,
        currentPage: payload,
      };
    case "setPlaySpeed":
      // 设置播放速度
      return {
        ...prevState,
        playSpeed: payload,
      };
    case "setFrameRateAndBitRate":
      // 设置帧率、码率
      return {
        ...prevState,
        frameRateAndBitRate: payload,
      };
    case "setDefaultData":
      return {
        ...prevState,
        ...payload,
      };
    case "setSelectedChannels":
      // 设置选中播放的通道
      return {
        ...prevState,
        selectedChannels: payload,
      };
    case "setStoreType":
      // 设置当前的存储器类型
      return {
        ...prevState,
        storeType: payload,
      };
    case "setStreamType":
      // 设置当前的码流类型
      return {
        ...prevState,
        streamType: payload,
      };
    case "setStreamType":
      // 设置当前的码流类型
      return {
        ...prevState,
        streamType: payload,
      };
    case "setActiveChannel":
      // 设置激活通道
      return {
        ...prevState,
        activeChannel: payload,
      };
    case "setRecordStatus":
      // 设置边看边录状态
      return {
        ...prevState,
        recordStatus: payload,
      };
    case "setFullScreen":
      // 设置全屏状态
      return {
        ...prevState,
        fullscreen: payload,
      };
    case "setChannelInfoMap":
      // 设置通道信息
      return {
        ...prevState,
        channelInfoMap: payload,
      };
    case "setDisabledWidgets":
      // 设置禁用信息
      return {
        ...prevState,
        disabledWidgets: payload,
      };
    case "setChannelDisabledWidgets":
      // 设置通道禁用信息
      return {
        ...prevState,
        channelDisabledWidgets: payload,
      };
    case "setHiddenWidgets":
      // 设置隐藏信息
      return {
        ...prevState,
        hiddenWidgets: payload,
      };
    case "setPageCode":
      // 设置页面编码
      return {
        ...prevState,
        pageCode: payload,
      };
    case "setAuthWidgets":
      // 设置需要资源控制的功能组件
      return {
        ...prevState,
        authWidgets: payload,
      };
    case "setPlayMode":
      // 播放模式
      return {
        ...prevState,
        playMode: payload,
      };
    case "setPlayAspectRatio":
      return {
        ...prevState,
        playAspectRatio: payload,
      };
    case "setReconnect":
      // 播放模式
      return {
        ...prevState,
        reconnect: payload,
      };
    case "setPlaybackType":
      // 回放类型
      return {
        ...prevState,
        playbackType: payload,
      };
    case "setIsLive":
      // 是否是直通
      return {
        ...prevState,
        isLive: payload,
      };
    case "setHooks":
      return {
        ...prevState,
        hooks: payload,
      };
    case "setPoster":
      return {
        ...prevState,
        poster: payload,
      };
    case "updateChannelInfo":
      // 更新通道信息
      const nextChannelInfoMap = { ...prevState.channelInfoMap };
      const { channel, info } = payload;
      nextChannelInfoMap[channel] = {
        ...nextChannelInfoMap[channel],
        ...info,
      };
      return {
        ...prevState,
        channelInfoMap: nextChannelInfoMap,
      };
    case "updateChannelsInfo":
      // 批量更新通道信息
      const nextChannelsInfoMap = { ...prevState.channelInfoMap };
      const { infos } = payload;
      infos.forEach((info) => {
        const { channel, ...restInfo } = info;
        nextChannelsInfoMap[channel] = {
          ...nextChannelsInfoMap[channel],
          ...restInfo,
        };
      });
      return {
        ...prevState,
        channelInfoMap: nextChannelsInfoMap,
      };
    case "setPlayerStatusSyncChannel":
      // 更新播放器状态，同时也会更新所有通道的状态
      const { status, updatePlayerStatus = true, errorMsg } = payload;
      let { syncChannels } = payload;
      const channelInfoMap = cloneDeep(prevState.channelInfoMap);
      if (!syncChannels || !syncChannels.length) {
        // 空数组会默认同步所有的通道
        syncChannels = Object.keys(channelInfoMap).map((item) => Number(item));
      }
      // @ts-ignore
      syncChannels.forEach((chan) => {
        if (channelInfoMap[chan]) {
          channelInfoMap[chan].status = status;
          channelInfoMap[chan].errorMsg = errorMsg;
        } else {
          channelInfoMap[chan] = {
            status,
            errorMsg,
          };
        }
        // 提示错误时，去掉loading
        if (errorMsg || status == PLAYER_STATUS.error) {
          channelInfoMap[chan].isLoading = false;
        }
      });
      if (!updatePlayerStatus) {
        return {
          ...prevState,
          channelInfoMap,
        };
      }
      return {
        ...prevState,
        playerStatus: status,
        channelInfoMap,
      };
    case "setCurrentTime":
      // 更新当前播放时间
      return {
        ...prevState,
        currentPlayTime: payload,
      };
    case "setAutoCloseAvailable":
      // 更新是否启用自动关闭
      return {
        ...prevState,
        autoCloseAvailable: payload,
      };
    case "setChannelFullScreen":
      // 设置通道全屏
      return {
        ...prevState,
        channelFullScreen: payload,
      };
    case "setLastProgressTime":
      // 设置当前的播放进度
      return {
        ...prevState,
        lastProgressTime: payload,
      };
    case "setBeforeClosePlayerStatus":
      // 设置当前的播放进度
      return {
        ...prevState,
        beforeClosePlayerStatus: payload,
      };
    case "setBeforePlay":
      return {
        ...prevState,
        beforePlay: payload,
      };
    case "setFullChannel":
      return {
        ...prevState,
        fullChannel: payload,
      };
    case "setGetSDKWaterConfig":
      // 设置隐藏信息
      return {
        ...prevState,
        getSDKWaterConfig: payload,
      };
    case "setDeviceMultiplexStatus":
      return {
        ...prevState,
        deviceMultiplexStatus: payload,
      };
    case "setDeviceOnlineState":
      return {
        ...prevState,
        deviceOnlineState: payload,
      };
    case "setCurrentPlayChannels":
      return {
        ...prevState,
        currentPlayChannels: payload,
      };
    case 'setGpuError':
      return {
        ...prevState,
        gpuError: payload,
      };
    case 'setGpuErrorData':
      if (payload) {
        return {
          ...prevState,
          gpuErrorData: {
            ...(prevState.gpuErrorData || {}),
            ...payload || {},
          },
        };
      } else {
        return {
          ...prevState,
          gpuErrorData: {},
        };
      }
    case 'setCabinChannel':
      return {
        ...prevState,
        cabinChannel: payload,
      };
    default:
      return prevState;
  }
};

export default reducer;
