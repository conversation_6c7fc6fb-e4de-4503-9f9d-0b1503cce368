/*
 * @LastEditTime: 2025-07-03 17:58:34
 */
/**
 * @description 基础单通道播放器dom节点组件，主要负责渲染挂载H5SDK player的dom节点和错误信息等
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import cn from 'classnames';
import { useDebounceFn, useThrottleFn, useDeepCompareEffect } from '@streamax/hooks';
import { IconLoading } from '@streamax/poppy-icons';
import { i18n } from '@base-app/runtime-lib';
import { COM_CLS_PREFIX } from '../../constant';
import { usePlayerState, usePlayerConfigState } from '../../store';
import { PLAYER_STATUS, PLAY_ASPECT_RATIO } from '../../constant';

import './index.less';

const IC_GPU_ERROR = require('@/assets/icon-status-conceal.svg');

const clsPrefix = `${COM_CLS_PREFIX}-basevideo`;
export interface BaseVideoProps {
    // 通道号
    channel: number | string;
    // 额外覆盖展示信息
    coverContent?: React.ReactNode;
    //展示加载失败信息
    errorContent?: React.ReactNode;
    // 展示加载成功的信息
    loadedContent?: React.ReactNode;
    // 加载完回调，返回用于挂载SDK Player的节点
    onLoaded?: (dom: HTMLDivElement) => void;
    className?: string;
}

interface PlayAspectRatioStyle {
    aspectRatio: string;
    width?: "100%";
    height?: "100%";
}

const BaseVideo: React.FC<BaseVideoProps> = (props) => {
    const {
        channel, coverContent, errorContent, onLoaded, className, loadedContent,
        channelItem, index,
    } = props;
    const [loading, setLoading] = useState(false);
    const [playing, setPlaying] = useState(false); // 是否正在播放中
    const [pause, setPause] = useState(false); // 是否正在暂停中
    const [poster, setPoster] = useState<string>(); // 存储截图poster
    const [showErrorMsg, setShowErrorMsg] = useState(false);
    const [showNetwork, setShowNetwork] = useState(false); // 是否展示网速和百分比
    const [showGpuError, setShowGpuError] = useState(false); // 是否展示GPU不足错误
    const networkTimerRef = useRef(undefined); //加载中3秒后展示网速和百分比定时器
    // 获取配置信息
    const {
        showNetworkDelayTime
    } = usePlayerConfigState((state) => ({
        showNetworkDelayTime: state.showNetworkDelayTime,
    }), true);
    const CHANGE_DELAY_SHOW_TIME = showNetworkDelayTime || (Number((window as any).APP_CONFIG['h5.player.start.loading.timeout']) * 1000) || 2000; //2秒后显示加载网速
    const playerMountDom = useRef<HTMLDivElement>(null);
    const { channelInfo, channelInfoMap, options, playAspectRatio, gpuError, cabinChannel, isLive, gpuErrorData } = usePlayerState((state) => ({
        channelInfo: state.channelInfoMap[channel],
        options: state.options,
        channelInfoMap: state.channelInfoMap,
        playAspectRatio: state.playAspectRatio,
        gpuError: state.gpuError,
        cabinChannel: state.cabinChannel,
        isLive: state.isLive,
        gpuErrorData: state.gpuErrorData,
    }), true);
    const innerOnLoaded =(dom: HTMLDivElement) => {
        onLoaded?.(dom, channelItem, index);
    };
    // const { run: myOnLoaded } = useDebounceFn(innerOnLoaded, {
    //     wait: 1
    // });
    useEffect(() => {
        innerOnLoaded(playerMountDom.current as HTMLDivElement);
        // myOnLoaded(playerMountDom.current as HTMLDivElement);
        return ()=>{
            clearTimeout(networkTimerRef.current);
        };
    }, []);

    useEffect(() => {
        if (gpuError) {
            // 回放一个通道GPU错误时，所有通道都需要展示为GPU不足
            if (!isLive) {
                setShowGpuError(true);
                return;
            }
            // 直通发生GPU错误时，需要判断是否是舱内通道，如果是舱内通道，则需要展示为GPU不足
            // 新增条件：如果是单个通道GPU资源不足，则处理对应通道的
            if (cabinChannel.includes(channel) && gpuErrorData[channel]) {
                setShowGpuError(true);
                return;
            }
            setShowGpuError(false);
        } else {
            setShowGpuError(false);
        }
    }, [gpuError, cabinChannel, channel, isLive, gpuErrorData]);
    useDeepCompareEffect(() => {
        if (channelInfo) {
            const { status, poster: channelPoster, isLoading } = channelInfo;
            setPoster(channelPoster);
            // 做loading的防抖，如果是loading状态就马上展示，无需防抖
            setLoading(status === PLAYER_STATUS.loading || isLoading === true);
            setPlaying(status === PLAYER_STATUS.playing);
            setPause(status === PLAYER_STATUS.pause);
            setShowErrorMsg(status === PLAYER_STATUS.error || status === PLAYER_STATUS.stopUse);
        }
    }, [`${channelInfo?.status}-${channelInfo?.poster}-${channelInfo?.isLoading}`]);
    // loading 3秒之后才展示网速和百分比
    useEffect(() => {
        // loading时设置3秒展示延时
        if (loading && !showNetwork) {
            //@ts-ignore
            networkTimerRef.current = setTimeout(() => {
                setShowNetwork(true);
            }, CHANGE_DELAY_SHOW_TIME);
        } else if (!loading) {
            // 非loading时 清除定时器
            clearTimeout(networkTimerRef.current);
            setShowNetwork(false);
        }
    }, [loading]);
    const showPoster = () => {
        if (options.liveVideoOptions) {
            return poster && !playing;
        } else if (options.playbackoptions) {
            return poster && !(playing || pause);
        }
        return false;
    };
    // 计算图片高宽比
    const observerRef = useRef<any>(null);
    const transformAspect: any = {
        "origin": "auto",
        "full": "",
        "4:3": "4/3",
        "16:9": "16/9",
        "1:1": "1/1",
    };
    const getAspectRatioStyle = (playAspectRatio: string, poster: string) => {
        // const defaultStyle: PlayAspectRatioStyle = {
        //     "aspectRatio": transformAspect[playAspectRatio],
        // };
        const defaultStyle: any = {};
        if (playAspectRatio === PLAY_ASPECT_RATIO.full) {
            defaultStyle["width"] = "100%";
            defaultStyle["height"] = "100%";
        }
        return defaultStyle;
    };
    /**
     * 获取修正后的显示比例
     */
    const getFixedAspectRatio = (
        originAspectRatio: string,  // 原始显示比例
        width: number,  // 视频原始宽度 
        height: number,  // 视频原始高度
        wrapper: HTMLElement,  // 视频通道容器
        threshold: number = 0.65  // 比例阈值
    ) => {
        if (originAspectRatio !== 'full' || !wrapper) return originAspectRatio;
        // 获取wrapper容器的宽高
        const wrapperWidth = wrapper.clientWidth;
        const wrapperHeight = wrapper.clientHeight;
        // 计算视频宽高和wrapper宽高的比例
        const widthRate = width / wrapperWidth;
        const heightRate = height / wrapperHeight;
        let rate = 0;
        if (widthRate > heightRate) {
            const realVideoHeight = height * wrapperWidth / width;
            rate = realVideoHeight / wrapperHeight;
        } else {
            const realVideoWidth = width * wrapperHeight / height;
            rate = realVideoWidth / wrapperWidth;
        }
        if (rate < threshold) {
            return 'origin';
        }
        return originAspectRatio;
    };
    const renderPoster = (e: any) => {
        const { naturalHeight, naturalWidth } = e.target;
        // const { aspectRatio } = e.target.style;
        let data = 1; // 默认是1:1
        // 处理当显示比例设置为全屏时，需要根据当前视频占比容器的比例来决定是否需要拉伸铺满（避免视频拉伸变形太严重）
        // 当占比大于65%时，则需要拉伸铺满，否则不拉伸铺满，按原始比例展示
        const newAspectRatio = getFixedAspectRatio(playAspectRatio, naturalWidth, naturalHeight, e.target.parentNode);
        if (["4:3", "16:9", "1:1"].includes(newAspectRatio)) {
            data = eval(transformAspect[newAspectRatio]);
        } else if (["origin"].includes(newAspectRatio)) {
            data = naturalWidth/naturalHeight;
        } else if (["full"].includes(newAspectRatio)) {
            e.target.style["width"] = "100%";
            e.target.style["height"] = "100%";
            e.target.style['display'] = 'inline-block';
            return;
        }
        const { clientHeight, clientWidth } = e.target.parentNode;
        if (data > clientWidth / clientHeight) {
            e.target.style["width"] = "100%";
            e.target.style["height"] = clientWidth/data + 'px';
        } else {
            e.target.style["height"] = "100%";
            e.target.style["width"] = data * clientHeight + 'px';
        }
        e.target.style['display'] = 'inline-block';
    };
    const { run: throttleRenderPoster } = useThrottleFn(renderPoster, {
        wait: 100,
    });
    function getObserver(e: any) {
        const target = e.target;
        const observer = new ResizeObserver(() => {
            throttleRenderPoster(e);
        });
        if (target.parentNode) {
            observer.observe(target.parentNode);
        }
        return observer;
    }
    const imgOnloadSet = (e: any) => {
        if (e.target) {
            renderPoster(e);
            // 增加监听事件
            if (observerRef.current) {
                // 原来有监听，则给去掉
                observerRef.current.unobserve(e.target.parentNode);
                observerRef.current = null;
            }
            observerRef.current = getObserver(e);
        }
    };
    // 组件销毁去掉监听
    useEffect(() => {
        return () => {
            if (observerRef.current) {
                observerRef.current?.disconnect?.();
            }
        };
    }, []);

    return (
        <div className={cn(clsPrefix, className)}>
            <div className={`${clsPrefix}-sdk-player`} ref={playerMountDom} />
            {loading && (
                <div className={`${clsPrefix}-notice-wrapper`}>
                    <div className="loading-icon-wrap">
                        <IconLoading spin className="loading-icon" />
                        {
                            <span className="loading-icon-wrap-speed">
                                {i18n.t('name', '视频加载中')}{' '}
                                {showNetwork
                                    ? `${channelInfo?.internetSpeed || 0}KB/s`
                                    : '...'}
                            </span>
                        }
                        {/* 回放才有加载百分比 */}
                        {options.playbackoptions && showNetwork ? (
                            <span className="loading-icon-wrap-percent">
                                {Math.ceil(channelInfo?.percent || 0)}%
                            </span>
                        ) : null}
                    </div>
                </div>
            )}
            {showPoster() && (
                <div className={`${clsPrefix}-last-img-wrapper`}>
                    <div
                        style={{
                            // backgroundImage: `url(${poster})`,
                            width: '100%', height: '100%', overflow: "hidden", textAlign: "center",
                            "placeContent": "center",
                        }}
                        className={`${clsPrefix}-cover-content-wrapper-poster`}
                    >
                       {
                        poster ?
                            <img
                                src={poster}
                                onLoad={imgOnloadSet}
                                style={{
                                    ...getAspectRatioStyle(playAspectRatio, poster),
                                    // 默认隐藏海报图片展示，解决需要图片加载完成后才调整图片的宽高等，导致图片先展示较小，然后突然变大的闪烁问题
                                    display: 'none',
                                    maxWidth: "100%",
                                    maxHeight: "100%"
                                }}
                            />: null
                    }
                    </div>
                </div>
            )}
            {loadedContent && (
                <div className={`${clsPrefix}-loaded-content-wrapper`}>{loadedContent}</div>
            )}
            {coverContent && (
                <div className={`${clsPrefix}-cover-content-wrapper`}>{coverContent}</div>
            )}
            {showErrorMsg && errorContent && (
                <span className={`${clsPrefix}-cover-content-wrapper ${clsPrefix}-error-msg`}>
                    {errorContent}
                </span>
            )}
            {showGpuError && (
                <span className={`${clsPrefix}-cover-content-wrapper ${clsPrefix}-gpu-error-msg`}>
                    <img src={IC_GPU_ERROR} />
                    <p className={`${clsPrefix}-gpu-error-msg-text`}>{i18n.t('message', '隐私保护')}</p>
                </span>
            )}
        </div>
    );
};

export default React.memo(BaseVideo);
