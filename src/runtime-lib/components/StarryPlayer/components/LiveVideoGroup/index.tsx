/**
 * @description 直通播放器组
 */
import {
  useAsyncEffect,
  useDebounceEffect,
  useDebounceFn,
  useLatest,
  useThrottleFn,
} from "@streamax/hooks";
import { message } from "@streamax/poppy";
import { IconPlayHollowFine } from "@streamax/poppy-icons";
import { i18n } from '@base-app/runtime-lib';
import * as utils from '@/runtime-lib/utils';
import { isNil, debounce, intersection, size, filter } from "lodash";
import React, { useEffect, useRef, useState, useCallback } from "react";
import useLiveVideoChannel from "./hooks/useLiveVideoChannel";
import {
    RECODING_STATUS,
    COM_CLS_PREFIX_LIVE_VIDEO_GROUP,
    PLAYER_STATUS,
    ENLARGE_STATE,
    PLAY_MODE,
    LAYOUT_TYPE,
} from '../../constant';
import { getErrorCode, getNeedMessageErrorCode } from '../../errorCode';
import { usePlayerState, usePlayerConfigState } from '../../store';
import type {
    PlayerStatus,
    LiveVideoOption,
    Channel,
    ChannelStateInfo,
    RecordStatus,
    FMPTYPE,
    DeviceItem,
    ChannelChangeItem,
} from '../../types';
import type { StreamType, LoadingData } from '../../types';
import { getPlayerRealStatus } from '../../utils/index';
import BaseVideo from '../BaseVideo';
// @ts-ignore
import Reload from "../Reload";
import {
    getDeviceDetail,
    recordOpen,
    recordClose,
    getTime,
    fetchParameterPostPage,
    checkMocisResource,
} from '../../../../service/starry-player';
import { PROTOCOL_TYPE } from '../../../../utils/evidenceFormatData';
import './index.less';
import {
    getDevicesChannels,
    globalMessageMention,
    getPosterCaptureParams
} from '../../utils/commonFun';
import CreateTaskQueueManager from './createTaskQueueManager';
import { mosaicManager, MosaicTypeEnum } from '@base-app/runtime-lib';
import { platformOrMix } from "@/runtime-lib/modules/mosaic/data";
export interface LiveVideoGroupProps {
  // 自定义播放器布局
  renderPlayerLayout?: (
    baseVideo: React.ReactNode,
    info: {
      channel: string;
      status: PlayerStatus;
    }
  ) => React.ReactNode;
  onPlayAspectRatioChange?: (aspectRatio: string) => void;
  // 通道布局改变回调；添加，关闭，布局个数，突出模式改变
  onChannelLayoutChange?: (item: ChannelChangeItem) => void;
  enableLoading?: boolean; // 是否允许播放中 loading
}
export interface ChannelInfo
  extends Omit<LiveVideoOption, "channelData">,
    Channel,
    DeviceItem {
  session?: string;
}
interface DomInfo {
  // DOM元素
  dom: HTMLElement;
  // 通道信息
  channelInfo: ChannelInfo;
  // 索引
  index: number;
  // 通道号
  channel: string;
  // flowLimit
  flowLimit?: boolean;
}

interface ChannelData {
  devId: string;
  channel: number | string;
}

enum NEED_IMAGE {
  "ONLY_IMG" = 1,
  "IMG_AND_MOSIC" = 3,
  "NO_IMG" = 0
}
const { uuid } = utils.general;
const prefix = COM_CLS_PREFIX_LIVE_VIDEO_GROUP;
const PLAYER_LIVE_DELAY_INTERVAL = 30000; //每30秒sdk上报一次是否直通延时
const PLAYER_LIVE_DELAY_DELAY_TIME = 3000; //延迟3秒才提示
// 通道loading等待时间
const LOADING_WAIT_TIME = 3000;
// 断线重连提前Loading时间，解决由于重连时间太短导致loading无足够时间展示，单位秒
const RECONNECT_LOADING_PRE_TIME = 1;
// n9m设备是否进行mp4转码
const N9M_VIDEO_TRANS_MP4_ENABLE = 'N9M.VIDEO.TRANS.MP4.ENABLE';
// 是否抽取首帧图
const VIDEO_DRAW_FRAME = 'VIDEO.DRAW.FRAME';
// 图片是否需要平台打码；1- 首尾帧&截图；2- 报警图片； 3- 抓拍图片
const EVIDENCE_DOWNLOAD_IMAGE_PLATFORM_MOSAIC_SWITCH = 'EVIDENCE.DOWNLOAD.IMAGE.PLATFORM.MOSAIC.SWITCH';
// 打码类型
const TENANT_MOSAIC_TYPE = 'TENANT.MOSAIC.TYPE';

// 默认重试次数
const defaultReconnectCount = 3;

let destroyTaskQueue: any[] = [];

let createTaskQueueManger: any = new CreateTaskQueueManager();

const LiveVideoGroup: React.FC<LiveVideoGroupProps> = (props) => {
  const { renderPlayerLayout, onPlayAspectRatioChange, onChannelLayoutChange } = props;
  const {
    seekTimeout,
    baseURL,
    defaultHeaders = {},
    platformTimeZone,
    loadH5SDK,
  } = usePlayerConfigState(
    (state) => ({
      seekTimeout: state.seekTimeout,
      baseURL: state.baseURL,
      defaultHeaders: state.defaultHeaders,
      platformTimeZone: state.platformTimeZone,
      loadH5SDK: state.loadH5SDK,
    }),
    true
  );
  const {
    channelInfoMap,
    options: { liveVideoOptions, config, headerConfig },
    volume,
    activeChannel,
    actionInfo,
    recordStatus,
    frameRateAndBitRate,
    playMode,
    reconnect,
    dispatch,
    selectedChannels,
    options,
    playerStatus,
    getSDKWaterConfig,
    poster,
    currentPage,
    layoutCount,
    playAspectRatio,
  } = usePlayerState(
    (state) => ({
      channelInfoMap: state.channelInfoMap,
      options: state.options,
      volume: state.volume,
      activeChannel: state.activeChannel,
      actionInfo: state.actionInfo,
      recordStatus: state.recordStatus,
      frameRateAndBitRate: state.frameRateAndBitRate,
      playMode: state.playMode,
      dispatch: state.dispatch,
      selectedChannels: state.selectedChannels,
      playerStatus: state.playerStatus,
      getSDKWaterConfig: state.getSDKWaterConfig,
      poster: state.poster,
      currentPage: state.currentPage,
      layoutCount: state.layoutCount,
      playAspectRatio: state.playAspectRatio,
      reconnect: state.reconnect,
    }),
    true
  );
  const latestFrameRateAndBitRateRef = useLatest(frameRateAndBitRate);
  const channels = useLiveVideoChannel();

    const [showEmptyPlaceholder, setShowEmptyPlaceholder] = useState(false);

    const channelsRef = useRef<string[]>([]);
    useEffect(() => {
        if (liveVideoOptions) {
            const layoutType = liveVideoOptions.layoutType;
            if (!channelsRef.current?.length || layoutType !== LAYOUT_TYPE.highlight) {
                channelsRef.current = [...selectedChannels];
            }
            const { channelData } = liveVideoOptions;
            // console.log('channel change', layoutCount, selectedChannels, liveVideoOptions, props);
            const deviceInfos = selectedChannels.map((item: string) => {
                const channelItem = channelData.find((item1: any) => item1.channel == item);
                return {
                    devId: channelItem.deviceId,
                    channel: item
                };
            });
            let highlightDevId = '';
            if (layoutType === LAYOUT_TYPE.highlight) {
                highlightDevId = channelData[0]?.deviceId;
            }
            const data = {
                deviceInfos,
                layoutCount,
                layoutType,
                highlightDevId,
                frameRateAndBitRate,
                playMode
            };
            // console.log('data', data);
            onChannelLayoutChange?.(data);
        }
    }, [selectedChannels, layoutCount, frameRateAndBitRate, playMode]);

    // 播放器实例
    const players = useRef<any>(null);
    // dom实例
    const baseVideos = useRef<Record<string, DomInfo>>({});
    // 边看边录session
    // 在边看边录开始时，记录边看边录数据，在边看边录结束时，从缓存数据中取值，避免从liveOptions数据中取，
    // 存在当边看边录进行中，重新播放其他车辆视频时，会直接结束边看边录，但是边看边录的结束事件有防抖处理，边看边录的结束事件调用时，车辆数据已经设置成功
    // 造成返回到外层的边看边录的车辆数据，是新播放的车辆，不是录制完成的车辆数据
    const recordInfo = useRef<{
        optId: string;
        channels: ChannelInfo[];
        vehicleNumber?: string;
        deviceNo?: string;
        deviceAlias?: string;
        authId?: string;
        deviceId?: string;
        vehicleId?: string;
    } | null>({
        optId: '',
        channels: []
    });
    // 边看边录session
    const copySession = useRef<string[]>([]);
    const recordTimeStamp = useRef<string[]>([]);
    // 边看边录时间
    const recordTime = useRef<string[]>([]);
    const syncTimesRef = useRef<number>(0); //直通未同步提示次数
    // 边看边录基准时间
    const startTimeRef = useRef(null);
    const [renderChannel, setRenderChannel] = useState<any[]>([]); // 再存channel，防止channel变化重新渲染拉流
    const channelInfoMapRef = useRef({});
    const configTimeout = (window as any).APP_CONFIG['seek.timeout'];
    // const seekTimeout = Number(configTimeout) || 30; // seek超时时间配置，单位s
    const lastCreateAuthIds = useRef('');
  useEffect(() => {
    createTaskQueueManger = new CreateTaskQueueManager();
    return () => {
        clearTimeout(createTaskQueueManger.timer);
        createTaskQueueManger?.clean?.();
      createTaskQueueManger = null;
    };
  }, []);

  // 存储上一次创建的通道数据
  const lastCreateChannelData = useRef<string[]>([]);
  const lastCreateFleetData = useRef<string>("");
  const optionChannelDataRef = useRef<ChannelInfo[]>([]);
  const channelLoadingWaitMap = useRef<Record<string, number>>({});
  const reconnectCountMapRef = useRef<Record<string, number>>({});
  const playerStatusRef = useRef(playerStatus);
  const newRenderChannelKeysRef = useRef<string[]>([]);

  const flagRef = useRef(false);

  // 清晰度恢复任务队列
  const recoverClarityTask = useRef<{channel: string, clarity: StreamType} | null>(null);

  const checkDestroyTaskQueue = async () => {
    return new Promise((resolve) => {
      if (!destroyTaskQueue.length) {
        resolve(true);
      } else {
        setTimeout(async () => {
          resolve(await checkDestroyTaskQueue());
        }, 0);
      }
    });
  };

  const getAllChannels = () => {
    const { liveVideoOptions } = options;
    const channelData = liveVideoOptions?.channelData || [];
    return channelData.map((item: ChannelInfo) => item.channel);
  };

  const getBaseVideos = () => {
    return Object.keys(baseVideos.current).map(
      (key) => baseVideos.current[key]
    );
  };

  // 销毁所有的播放器;结束边看边录
  useEffect(() => {
    return () => {
      destroy(undefined, "idle", UpdatePosterAction.CLEAR);
      if (recordStatus === "start") handleEndRecord();
      dispatch({
        type: "setPlayerStatus",
        payload: PLAYER_STATUS.idle,
      });
      dispatch({
        type: "setChannelInfoMap",
        payload: {},
      });
      dispatch({
        type: "setFrameRateAndBitRate",
        payload: [],
      });
      dispatch({
        type: "setPlayMode",
        payload: PLAY_MODE.normal,
      });
    };
  }, []);
  useEffect(() => {
    playerStatusRef.current = playerStatus;
  }, [playerStatus]);

  // 判断是否是所有通道都被流量管控了
  const checkFlowLimit = () => {
    const flowLimitChannel = (optionChannelDataRef.current || []).filter(
      (item) => item.flowLimit
    );
    return (
      flowLimitChannel.length &&
      flowLimitChannel.length === optionChannelDataRef.current.length
    );
  };

  useAsyncEffect(async () => {
    if (isNil(liveVideoOptions)) return;
    if (
      liveVideoOptions?.channelData &&
      liveVideoOptions.channelData.length === 0
    ) {
      message.warn(i18n.t("message", "暂无通道数据"));
      return;
    }
    optionChannelDataRef.current = liveVideoOptions?.channelData || [];
    // 提前进入加载状态转圈
    liveVideoOptions?.channelData.forEach((item) => {
      if (!channelInfoMapRef.current[item.channel]) {
        channelInfoMapRef.current[item.channel] = {
          status: PLAYER_STATUS.loading,
        };
      }
    });
    dispatch({
      type: "setChannelInfoMap",
      payload: channelInfoMapRef.current,
    });
    // 提前进入加载状态结束
    const authIds = [
      ...new Set(liveVideoOptions?.channelData.map((item) => item.authId)),
    ].join();
    syncTimesRef.current = 0;
    // 流量管控
    if (checkFlowLimit()) {
      dispatch({
        type: "setPlayerStatus",
        payload: PLAYER_STATUS.stopUse,
      });
      return;
    }
    if (lastCreateAuthIds.current !== authIds) {
      // 初始化时清空历史的数据通道的海报
      if (channelInfoMapRef.current) {
        for (const key in channelInfoMapRef.current) {
          if (
            channelInfoMapRef.current[key].poster &&
            channelInfoMapRef.current[key].status === PLAYER_STATUS.playing
          ) {
            channelInfoMapRef.current[key].poster = null;
          }
        }
        dispatch({
          type: "setChannelInfoMap",
          payload: channelInfoMapRef.current,
        });
      }
    } else if (
      lastCreateAuthIds.current === authIds &&
      playerStatus !== PLAYER_STATUS.playing
    ) {
      const { channelData } = liveVideoOptions;
      // 根据所选通道进行过滤
      const startIndex = (currentPage - 1) * layoutCount;
      const newOpenChannel = channelData
        .filter((c: any) => selectedChannels.includes(c.channel))
        .slice(startIndex, startIndex + layoutCount)
        .map((item: any) => item.channel)
        .sort()
        .join(",");
      const currentOpenChannel = renderChannel
        .map((p) => p.channel)
        .sort()
        .join(",");
      // 此if分支逻辑中是为了解决再次点击相同车辆时，没有重新加载视频，必须是两次打开的通道是一致的情况下
      if (newOpenChannel === currentOpenChannel) {
        createTaskQueueManger.addTask({
          type: "create",
          exec: async () => {
            const oldRenderChannel = [...renderChannel];
            if (players.current) {
              await players.current.destroy();
              players.current = null;
            }
            // 1. 解决再次点击相同车辆时，需要触发BaseVideo强制渲染，所以将renderChannel设置为空
            // 2. 解决当把renderChannel设置为空后会闪现“暂无通道”，故设置showEmptyPlaceholder为true
            setShowEmptyPlaceholder(true);
            setRenderChannel(() => []);
            Promise.resolve().then(() => {
              newRenderChannelKeysRef.current = oldRenderChannel.map(
                (p) => p.channel
              );
              setRenderChannel(() => oldRenderChannel);
              setShowEmptyPlaceholder(() => false);
            });
          },
        });
        flagRef.current = false;
      } else {
        // 再次点击打开相同车辆，但是打开的通道不相同时，会触发channels的变化，此变化会重新初始化播放器，故此处设置标志位，交由监听channels变化的hook处理
        flagRef.current = true;
      }
    }
  }, [liveVideoOptions]);

    // 计算是否是动态添加或移除通道
    const isDynamicChangeChannel = (channels: ChannelInfo[]) => {
        // if (channels.some(p => p.dynamicAddChannel)) return true;
        const channelKeys = channels.map((p) => p.channel);
        const length = intersection(
            channelKeys,
            lastCreateChannelData.current,
        ).length;
        if (
            // 在上一次打开通道的基础上增加了部分通道
            (length <= lastCreateChannelData.current.length && length > 0) ||
            // 在上一次打开通道的基础上移除了部分通道
            length === channelKeys.length
        )
            return true;
        return false;
    };

  useAsyncEffect(async () => {
    if (!channels.length) return;
    createTaskQueueManger.addTask({
      type: "create",
      exec: async () => {
        const channelKeys = channels.map((p) => p.channel);
        const newFleetData = Array.from(new Set(channels.map((p) => p.fleetId)))
          .sort()
          .join(",");
        const isDiffFleet =
          newFleetData !== lastCreateFleetData.current &&
          channels.every((p: any) => p.groupPlay);
        const removedChannels = lastCreateChannelData.current.filter(
          (key: string) => {
            return (
              channels.findIndex((oneChan: ChannelInfo) => {
                return oneChan.channel === key;
              }) == -1
            );
          }
        );
        const dynamicChange = isDynamicChangeChannel(channels);
        if (!dynamicChange || isDiffFleet) {
          await destroy(undefined, "idle", UpdatePosterAction.KEEP);
        } else if (flagRef.current) {
          await destroy(undefined, "loading", UpdatePosterAction.CLEAR);
          lastCreateChannelData.current = [];
        } else if (removedChannels && removedChannels.length) {
          // 处理删除单个设备导致不关闭的问题
          const destroyedData: ChannelData[] = [];
          removedChannels.forEach((key: string) => {
            const data = renderChannel.find((chan: ChannelInfo) => {
              return chan.channel === key;
            });
            if (data) {
              destroyedData.push({
                devId: data.authId,
                channel: data.channelNo,
              });
            }
          });
          await players.current?.destroy(destroyedData);
        }
        if (isDiffFleet) {
          lastCreateChannelData.current = [];
        }
        // 记录本次需要新渲染的通道数量，用于后续BaseVideo触发onLoad事件时用来判断是否所有BaseVideo都已渲染完毕
        newRenderChannelKeysRef.current = channelKeys.filter((p) => {
          let isPass = true;
          // 如果已经在创建的通道内，则过滤掉
          if (lastCreateChannelData.current.includes(p)) {
            isPass = false;
          }
          // 判断通道是否是 flowLimit
          const channel = channels.find((item) => item.channel === p);
          if (channel && channel.flowLimit === 1) {
            isPass = false;
          }
          return isPass;
        });
        lastCreateChannelData.current = channelKeys;
        lastCreateFleetData.current = Array.from(
          new Set(channels.map((p) => p.fleetId))
        )
          .sort()
          .join(",");
        if (flagRef.current || isDiffFleet) {
          // 1. 解决再次点击相同车辆时，需要触发BaseVideo强制渲染，所以将renderChannel设置为空
          // 2. 解决当把renderChannel设置为空后会闪现“暂无通道”，故设置showEmptyPlaceholder为true
          setShowEmptyPlaceholder(true);
          setRenderChannel(() => []);
          Promise.resolve().then(() => {
            setRenderChannel(() => channels);
            setShowEmptyPlaceholder(() => false);
          });
          flagRef.current = false;
        } else {
          // 修改逻辑开始
          const privacyChannels = channels.filter((p) => p.privacyState).map(p => p.channel);
          privacyChannels.forEach((channel) => {
            const index = newRenderChannelKeysRef.current.indexOf(channel);
            if (index === -1) {
              newRenderChannelKeysRef.current.push(channel);
            }
          });
          setRenderChannel(channels);
        }
        if (!newRenderChannelKeysRef.current.length) {
          createTaskQueueManger.doneTask();
        }
      },
    });
  }, [channels]);
  useEffect(() => {
    channelInfoMapRef.current = channelInfoMap;
  }, [channelInfoMap]);

  const volumeRef = useRef<number>(volume);
  useEffect(() => {
    volumeRef.current = volume;
  }, [volume]);

  // 有闭包问题，会导致执行时获取不到最新 store 上的 playAspectRatio 数据
  const playAspectRatioRef = useRef<number>(playAspectRatio);
  useEffect(() => {
    playAspectRatioRef.current = playAspectRatio;
  }, [playAspectRatio]);

    useDebounceEffect(
        () => {
            handleVolume(volume);
        },
        [activeChannel, volume],
        {
            wait: 100,
        },
    );
    const activeChannelRef = useRef(activeChannel);
    useEffect(() => {
      activeChannelRef.current = activeChannel;
    }, [activeChannel]);
    // 监听actionInfo
    useAsyncEffect(async () => {
        if (actionInfo) {
            const { actionName, actionChannel, actionPayload } = actionInfo;
            switch (actionName) {
                case 'closeVideo':
                    await destroy(
                        actionChannel,
                        actionPayload,
                        poster?.stop && UpdatePosterAction.CLEAR,
                    );
                    if (!isNil(actionChannel)) {
                        dispatch({
                            type: 'setSelectedChannels',
                            payload: selectedChannels.filter(
                                (item) => item !== actionChannel,
                            ),
                        });
                    } else {
                        createTaskQueueManger.doneTask();
                    }
                    break;
                case 'reload':
                    reload(actionChannel);
                    break;
                case 'screenshot':
                    if (actionChannel && players.current) {
                        const { watermarkFlag, ...rest } =
                            (await getSDKWaterConfig?.()) || {};
                        const [deviceId, channelNo] = actionChannel.split('-');
                        const authId = optionChannelDataRef.current.find(
                            (p) => p.deviceId === deviceId,
                        )?.authId;
                        const { generateFileName } = actionPayload || {};
                        let fileName;
                        if (generateFileName) {
                            const item = optionChannelDataRef.current.find(p => p.channel === actionChannel);
                            fileName = generateFileName(item, actionChannel);
                        }
                        players.current?.pluginMap.snapshot.capture(
                            {
                                devId: authId,
                                channel: Number(channelNo),
                            },
                            {
                                autodownload: true,
                                watermark: watermarkFlag ? rest : null,
                                fileName
                            },
                        );
                    }
                    break;
                case 'enlarge':
                    actionChannel &&
                        handleChangeEnlarge(actionChannel, 'enlarge');
                    break;
                case 'cancelEnlarge':
                    actionChannel &&
                        handleChangeEnlarge(actionChannel, 'cancelEnlarge');
                    break;
                case 'startRecord':
                    startRecord();
                    break;
                case 'endRecord':
                    handleEndRecord();
                    break;
                case 'changeClarity':
                    handleChangeClarity(actionPayload, actionChannel);
                    break;
                case 'frameRateAndBitRate':
                    handleFrameRateAndBitRate(actionPayload);
                    break;
                case 'pauseVideo':
                    const channelInfo = (
                        liveVideoOptions?.channelData || []
                    ).find(
                        (oneChannel: ChannelInfo) =>
                            oneChannel.channel === actionChannel,
                    );
                    destroy(channelInfo, 'stop');
                    break;
                case 'playVideo':
                    reload(actionChannel);
                    // 重播逻辑
                    // if (players.current) {
                    //     const channelInfoArr = actionChannel.split("-");
                    //     const data = {
                    //         devId: channelInfoArr[0],
                    //         channel: Number(channelInfoArr[1]),
                    //     };
                    //     players.current.replay([data]);
                    //     setTimeout(() => {
                    //         players.current.pluginMap.aspectRatio.set('origin', data);
                    //     });
                    // }
                    break;
                case 'playMode':
                    reload(actionChannel);
                    break;
                case 'playAspectRatio':
                    setAspectRatio(actionPayload);
                    break;
                case 'reconnect':  // 自动重连逻辑
                    reload(actionChannel);
                    break;
                default:
                    break;
            }
        }
    }, [actionInfo]);
    const setAspectRatio = (ratio: string) => {
        if (ratio) {
            // 设置显示比例
            players.current?.pluginMap?.aspectRatio?.set(ratio);
            onPlayAspectRatioChange?.(ratio);
        }
    };
    /**
     * @description: 更新poster
     * @param {number} actionChannel 更新通道
     * @param {boolean} clear 是否清除海报
     * @return {*}
     */
    const getPosterBase64 = (
        channelData: ChannelData,
        actionChannel?: number | string,
        clear?: boolean,
    ) => {
        try {
            // 截图
            if (actionChannel && players.current && !clear) {
                let pictureData = {
                    base64:
                        channelInfoMapRef.current[actionChannel]?.poster || '',
                };
                // 默认保留上一张图片，只有在加载中才去截图更新，否则截图是黑色
                const renderList = players.current.status || [];
                // 得到正在播放的通道数量
                const playingLength = size(filter(renderList, { state: 'playing' }))
                const channelStatus = renderList.find((oneInfo: any) => {
                    const { info } = oneInfo || {};
                    if (
                        channelData.devId == info.devId &&
                        channelData.channel == info.channel
                    ) {
                        return true;
                    }
                    return false;
                });
                if (channelStatus?.state === getPlayerRealStatus()?.playing) {
                    const dataTmp = players.current?.pluginMap.snapshot.capture(
                        channelData,
                        getPosterCaptureParams(poster?.strategy, playingLength),
                    );
                    if (dataTmp[0] && dataTmp[0].base64) {
                        pictureData = dataTmp;
                        // 更新数据
                        if (channelInfoMapRef.current[actionChannel]) {
                            channelInfoMapRef.current[actionChannel].poster =
                                dataTmp[0]?.base64;
                        }
                    }
                }
                return pictureData[0]?.base64 || '';
            }
        } catch (error) {
            console.warn(error);
        }
        return '';
    };
    // 初始化播放器
    const initPlayer = async (
        domList: HTMLElement[],
        channelInfoList: ChannelInfo[],
        destroyStatus?: PlayerStatus,
    ) => {
        // 对比判断，如果有通道相同的，需要停止以后再做播放
        if (players.current) {
            const channelInfoArr: any = [];
            const renderedChannelList =
                players.current?.getRenderInfoList?.() || [];
            const updateChannelList: any[] = [];
            domList.forEach((dom: HTMLElement, index: number) => {
                const channelNow = channelInfoList[index];
                // 若是隐私通道，则不初始化播放器
                const channelInfoTarget = liveVideoOptions?.channelData.find(
                    (item) => item.channel === channelNow.channel,
                );
                // 若是已经渲染了的通道，则不需要再添加
                const renderedChannel = renderedChannelList.find(
                    (p: any) =>
                        p.realChannel == channelNow.channelNo &&
                        p.realDevId == channelNow.authId,
                );
                if (!channelInfoTarget?.privacyState && !renderedChannel) {
                    channelInfoArr.push({
                        dom,
                        devId: channelNow.authId,
                        channel: channelNow.channelNo,
                    });
                    updateChannelList.push({
                      ...channelNow
                    });
                }
            });
            players.current.addStream(channelInfoArr);
            createTaskQueueManger.doneAndExecLatestTask(() => [PLAYER_STATUS.error, PLAYER_STATUS.playing].includes(playerStatusRef.current));
            setTimeout(() => {
                players.current?.pluginMap.aspectRatio.set(playAspectRatioRef.current || 'origin');
                // 恢复原有关闭再打开通道样式，去掉原有放大按钮设置
                const updateQueue = updateChannelList.map(
                    (oneData: ChannelInfo) => {
                        return {
                            channel: oneData.channel,
                            enlarge: ENLARGE_STATE.close,
                        };
                    },
                );
                updateChannelsInfo(updateQueue);
            }, 0);
        } else {
          const getState = (channelData: ChannelInfo) => {
            if (channelData.flowLimit) return PLAYER_STATUS.stopUse;
            if (channelData.privacyState) return PLAYER_STATUS.idle;
            return PLAYER_STATUS.loading;
          };
            const updateQueue = liveVideoOptions?.channelData?.map(
                (oneData: ChannelInfo) => {
                    return {
                        channel: oneData.channel,
                        status: getState(oneData),
                        enlarge: ENLARGE_STATE.close,
                        // poster: "",
                    };
                },
            );
            updateChannelsInfo(updateQueue);
            createLivingPlayer(domList, channelInfoList);
        }
    };

  /** @description 直通视频每一个通道都是一个播放器，触发错误时，会短时间触发多次，因此使用防抖来规避 */
  const { run: debounceGlobalMessage } = useDebounceFn(globalMessageMention, {
    wait: 500,
  });
  /** @description 直通视频每一个通道都是一个播放器，触发 afterPlay ，会短时间触发多次，因此使用防抖来规避 */
  const setVolume = (volumeList: any[]) => {
    if (players.current) {
      players.current.volume = [...volumeList];
    }
  };
  const { run: debounceSetVolume } = useDebounceFn(setVolume, { wait: 10 });
  const nextChannelInfo = useRef<ChannelStateInfo[]>([]);
  const playerCurrent = useRef<any>(null);
  const playerJSRef = useRef<any>(null);
  // 创建播放器
  const createLivingPlayer = (
    domList: HTMLElement[],
    channelInfoList: ChannelInfo[]
  ) => {
    // @ts-ignore
    loadH5SDK()
      .then(({ SPlayer }) => {
        recoverClarityTask.current = null;
        playerJSRef.current = SPlayer;
        const channelInfo = channelInfoList[0] || {};
        const { channel, streamType } = channelInfo;
        const authIds = [
          ...new Set(liveVideoOptions?.channelData?.map((item) => item.authId)),
        ].join();
        lastCreateAuthIds.current = authIds;

        const deviceChannelMap: Record<string, number[]> = {};
        const domeListFiltered = domList.filter(
          (dom: HTMLElement, index: number) => {
            const channelNow = channelInfoList[index].channel;
            // 若是隐私通道，则不初始化播放器
            const channelInfoTarget = liveVideoOptions?.channelData.find(
              (item) => item.channel === channelNow
            );
            if (channelInfoTarget?.privacyState) {
              return false;
            } else {
              // const targetChannel = channelInfoTarget.channel;
              // const channel = typeof targetChannel === 'number' ? `${targetChannel}` : targetChannel?.split('-')[1];
              const { authId, channelNo } = channelInfoList[index];
              if (!deviceChannelMap[authId]) {
                deviceChannelMap[authId] = [channelNo];
              } else {
                deviceChannelMap[authId].push(channelNo);
              }
              return true;
            }
          }
        );

            // 使用业务层传入的参数
            let {
                reconnectAgainCount,
                reconnectAgainTime,
                reconnectTime,
            } = config;
            // 默认重连时间间隔
            if (config && config.reconnectTime) {
                // 由于内部断线重连时会提前开始loading，为保证外部业务层设置的间隔时间准确，故需要加上提前loading的时间
                reconnectTime += RECONNECT_LOADING_PRE_TIME;
            }
            // 如果打开重连逻辑，使用配置的再次重连次数
            if (reconnect) {
                if (typeof reconnectAgainCount === "number") {
                    reconnectAgainCount = reconnectAgainCount;
                }
                if (typeof reconnectAgainTime === "number") {
                    reconnectAgainTime = reconnectAgainTime + RECONNECT_LOADING_PRE_TIME;
                }
            } else {
                reconnectAgainCount = undefined;
                reconnectAgainTime = undefined;
            }

            SPlayer.config = {
                baseURL,
                adas: playMode === PLAY_MODE.algorithm,
                dms: playMode === PLAY_MODE.algorithm,
                bucket: playMode === PLAY_MODE.algorithm,
                keyFrame: playMode === PLAY_MODE.save ? 1 : 0,
                operationTimeout: seekTimeout, //超时hook报错时间 秒
                ...config,
                reconnectAgainCount,
                reconnectAgainTime,
                reconnectTime,
            };
            SPlayer.defaultHeaders = {
                ...defaultHeaders,
                ...headerConfig,
            };
            const reqParams = {
                // devId: authId,
                deviceInfos: Object.keys(deviceChannelMap).map((authId) => ({
                    devId: authId,
                    channels: deviceChannelMap[authId].join(','),
                })),
                streamType: channelInfoMap[channel]?.streamType || streamType,
                // channel: typeof channel === 'number' ? channel : channel?.split('-')[1],
                optId: '123',
                // wasmPath: '/player',
                // 打码类型  0 模糊 1方块
                mosaicType: 1,
                mosaic: isNil(config?.mosaic) ? 2 : config?.mosaic,
                playMode: isNil(config?.playMode) ? 0 : config?.playMode,
                headers: {
                  ...config?.headers
                }
            };
            // if (players.current) {
            // players.current[channel]?.destory();
            // players.current?.destroy();
            // }
            // console.log("====================reqParams:", reqParams);
            players.current = SPlayer.createLivePlayer(
                [...domeListFiltered],
                reqParams,
            );
            // to delete 测试代码
            // (window as any)?.playerjs?.logger.setPrintLevel(1);

        // playerCurrent.current = SPlayer.createLivePlayer([domeListFiltered], reqParams);
        //todo load时会阻塞卡顿，后续看如何优化
        if (
          players.current?.load &&
          typeof players.current?.load === "function"
        ) {
          players.current?.load();
        }
        const currentPlayer = players.current;
        // 重置gpu错误数据
        dispatch({
          type: 'setGpuErrorData',
          payload: null,
        });

        // 默认设置一遍音量
        const volumeList: any[] = [];
        channelInfoList.forEach((channelItem) => {
          // const volumeNow = (channelItem.channel === activeChannel) ? volumeRef.current : 0;
          // 默认全部静音
          const volumeNow = 0;
          volumeList.push({
            volume: volumeNow,
            channel: channelItem.channelNo,
            devId: channelItem.authId,
          });
        });
        setVolume(volumeList);

        if (players.current) {
          currentPlayer.pluginMap.aspectRatio.set(playAspectRatioRef.current || "origin");
          // 播放过程中不显示loading，这里无需对接，初始化阶段的loading已有另外的机制实现
          // 加载loading事件 done 是否loading结束
          currentPlayer.hooks?.onLoading?.tap(
            'on loading',
            (list: LoadingData[], done: boolean) => {
              if (!props.enableLoading) return;
              if (playerStatusRef.current === PLAYER_STATUS.stop) return;
              const updateArr: any = [];
              list.forEach((item: any) => {
                const { devId: authId, channel, network, percent } = item;
                const key = `${authId}-${channel}`;
                const channelItem = optionChannelDataRef.current.find(
                  (p) => p.authId === authId && p.channelNo === Number(channel)
                );
                if (percent < 100) {
                  // 此通道处于loading中
                  const currentTime = Date.now();
                  if (!channelLoadingWaitMap.current[key]) {
                    channelLoadingWaitMap.current[key] = currentTime;
                  }
                  if (
                    currentTime - channelLoadingWaitMap.current[key] >
                    LOADING_WAIT_TIME
                  ) {
                    // 判断一下现有状态，防止stop状态触发逻辑
                    const statusNow = channelItem
                      ? channelInfoMapRef.current[channelItem.channel]?.status
                      : "";
                    const notAllowStatus = [
                      PLAYER_STATUS.stop,
                      PLAYER_STATUS.error,
                    ].includes(statusNow);
                    if (channelItem && !notAllowStatus) {
                      updateArr.push({
                        channel: channelItem.channel,
                        status: PLAYER_STATUS.loading,
                        internetSpeed: network,
                      });
                    }
                  }
                } else {
                  channelLoadingWaitMap.current[key] = 0;
                  if (channelItem) {
                    updateArr.push({
                      channel: channelItem.channel,
                      status: PLAYER_STATUS.playing,
                      internetSpeed: network,
                    });
                  }
                }
              });
              throttleUpdateChannelsInfo(updateArr, PLAYER_STATUS.stop);
            }
          );
          // ready之后设置播放器状态，删除海报
          currentPlayer?.hooks.afterReady.tap(
            "after ready",
            (player: any, readyData: any) => {
              if (playerStatusRef.current === PLAYER_STATUS.stop) return;
              dispatch({
                type: "setPlayerStatus",
                payload: PLAYER_STATUS.playing,
              });
              const {
                data: { devId: authId, channel },
              } = readyData;
              const channelItem = optionChannelDataRef.current.find(
                (p) => p.authId === authId && p.channelNo == channel
              );
              if (channelItem) {
                updateChannelInfo(channelItem.channel, {
                  status: PLAYER_STATUS.playing,
                  errorMsg: "",
                });
              }
            }
          );
          // 开始播放事件
          currentPlayer?.hooks.afterPlay.tap("begin play", () => {
            // 关闭选中通道以外的其他通道声音
            if (players.current) {
              // 设置默认音量
              const channelInfo = currentPlayer?.getRenderInfoList?.() || [];
              const volumeList: any[] = [];
              channelInfo.forEach((oneInfo: any) => {
                const { devId, channel } = oneInfo;
                const channelItem = optionChannelDataRef.current.find(
                  (p) => p.authId === devId && p.channelNo == channel
                );
                if (channelItem) {
                  const volumeNow =
                    channelItem.channel === activeChannelRef.current
                      ? volumeRef.current
                      : 0;
                  volumeList.push({
                    volume: volumeNow,
                    channel,
                    devId,
                  });
                }
              });
              // currentPlayer.volume = [...volumeList];
              debounceSetVolume([...volumeList]);
              // setVolume([...volumeList]);
            }
            if (players.current) {
              currentPlayer?.pluginMap?.videoInfoLayer.setInfoList(
                latestFrameRateAndBitRateRef.current || []
              );

              if (recoverClarityTask.current) {
                const { channel, clarity } = recoverClarityTask.current;
                handleChangeClarity(clarity, channel);
                recoverClarityTask.current = null;
              }
            }
          });
          // SDK断线重连
          currentPlayer?.hooks.startReconnect.tap(
            "start reconnecting",
            (player: any, eventData: any) => {
              if (playerStatusRef.current === PLAYER_STATUS.stop) return;
              // 断线重连时，展示loading动画
              const {
                data: {
                  info: {
                    channel,
                    devId: authId,
                    reconnectCountdown,
                    reconnectNum,
                  },
                },
              } = eventData;
              // 倒计时还剩一秒的时候开始loading，防止由于重试时间太短导致业务层loading展示不完全，呈现出闪烁一下的效果
              if (reconnectCountdown === RECONNECT_LOADING_PRE_TIME) {
                const channelItem = optionChannelDataRef.current.find(
                  (p) => p.authId === authId && p.channelNo === Number(channel)
                );
                if (channelItem) {
                  reconnectCountMapRef.current[channelItem.channel] =
                    reconnectNum;
                  updateChannelInfo(channelItem.channel, {
                    status: PLAYER_STATUS.loading,
                    errorMsg: "",
                  });
                }
              }
            }
          );
          currentPlayer?.hooks.reconnectSuccess.tap(
            "reconnect success",
            (player: any, eventData: any) => {
              if (playerStatusRef.current === PLAYER_STATUS.stop) return;
              const {
                data: {
                  info: { channel, devId: authId },
                },
              } = eventData;
              const channelItem = optionChannelDataRef.current.find(
                (p) => p.authId === authId && p.channelNo === Number(channel)
              );
              if (channelItem) {
                reconnectCountMapRef.current[channelItem.channel] = 0;
                updateChannelInfo(channelItem.channel, {
                  status: PLAYER_STATUS.playing,
                  errorMsg: "",
                });
              }
            }
          );
          // 播放错误事件
          currentPlayer?.hooks.onError.tap("play error", (errorData: any) => {
            // eslint-disable-next-line no-console
            const { errorInfo = {}, errorType, subErrorType } = errorData;

            const { code, message: msg, data } = errorInfo;

            console.warn(
              "%c Error:1111----",
              code,
              msg,
              data,
              errorInfo,
              "color: green"
            );
            // 设备断流需要 关闭通道流
            const channelData: any = optionChannelDataRef.current.find(
              (oneData: ChannelInfo) => {
                if (
                  oneData.channelNo == errorInfo.channel &&
                  oneData.authId === errorInfo.devId
                ) {
                  return true;
                }
                return false;
              }
            );
            if (
              channelData &&
              (subErrorType == "H5_SDK_STREAM_ABORT" ||
                subErrorType === "H5_SDK_NETWORK_ERROR")
            ) {
              const dataOrg: any =
                channelInfoMapRef.current[channelData.channel] || {};
              if (!["error", "loading"].includes(dataOrg.status)) {
                // 已经是error、loading状态的，不做处理
                channelData && destroy(channelData, "error", undefined, true);
              }
            }
            // 直接电脑本地断网后，自动重试后均失败后需要手动销毁一次，否则无法再播放
            const reconnectCount =
              config.reconnectCount || defaultReconnectCount;
			  const reconnectAgainCount = reconnect ?
			  config.reconnectAgainCount : 0;
                        const reconnectCountAll = reconnectCount + reconnectAgainCount;
            if (
              channelData &&
              reconnectCountMapRef.current[channelData.channel] ===
                reconnectCountAll
            ) {
              destroy(channelData, "error", undefined);
              reconnectCountMapRef.current[channelData.channel] = 0;
            }
            // 如果当前状态整体状态是 idle 或者 loading状态，则做销毁操作。处理进入时网络错误
            if (
              [PLAYER_STATUS.idle, PLAYER_STATUS.loading].includes(
                playerStatusRef.current
              )
            ) {
              destroy(channelData, "error", undefined);
            }
            // TODO 此处逻辑与回放不一样，回放直接使用了subErrorType，errorType，直通使用了后续是否考虑统一
            /** sdk说二级错误编码按照如下方式取 */
            let subErrorCode = (window as any).playerjs?.errorCode
              ?.sdkErrorCode[errorType];
            // 这两个错误码返回的errorType是NETWORK_ERROR，需要展示定制文案
            if(['NO_GPU_RESOURCE', 'VIDEO_OVER_LOAD'].includes(subErrorType)){
                subErrorCode = subErrorType;
                dispatch({
                  type: 'setGpuError',
                  payload: true,
                });
                dispatch({
                  type: 'setGpuErrorData',
                  payload: {
                    [channelData?.channel]: true,
                  },
                });
            }
            updateChannelInfo(channelData?.channel, {
              status: PLAYER_STATUS.error,
              enlarge: ENLARGE_STATE.close,
              errorMsg: getErrorCode()[subErrorCode],
            });
            /** @description 根据业务要求，只对部分错误进行消息提示 */
            debounceGlobalMessage(subErrorCode, getNeedMessageErrorCode());
            // 如果边看边录的通道中有一个报错，则停止录制
            if (
              recordInfo.current &&
              recordInfo.current.channels.filter((item) => {
                return (
                  item.channel == errorInfo.channel &&
                  item.authId === errorInfo.devId
                );
              }).length
            ) {
              handleEndRecord();
            }

            if (playerStatusRef.current === PLAYER_STATUS.loading) {
              // 如果存在通道的播放状态为playing，则将播放器的状态重置为playing
              const playingChannel = Object.keys(
                channelInfoMapRef.current
              ).find((key) => {
                return channelInfoMapRef.current[key].status === PLAYER_STATUS.playing;
              });
              if (playingChannel) {
                dispatch({
                  type: "setPlayerStatus",
                  payload: PLAYER_STATUS.playing,
                });
              }
            }
          });
          // loading 超时事件
          currentPlayer?.hooks.loadingTimeout.tap(
            "loading Timeout",
            (deviceInfos: any) => {
              console.error("SDK TimeOut Error:", deviceInfos);
              // 确保触发中的hooks是当前播放器，而不是上一次播放器触发的hooks
              if (players.current) {
                deviceInfos.forEach((oneDev: any) => {
                  const channelData: ChannelInfo =
                    optionChannelDataRef.current?.find(
                      (oneData: ChannelInfo) => {
                        if (
                          (oneData.authId === oneDev.realDevId &&
                            oneData.channelNo == oneDev.realChannel) ||
                          (oneData.authId === oneDev.devId &&
                            oneData.channelNo == oneDev.channel)
                        ) {
                          return true;
                        }
                        return false;
                      }
                    );
                  if (channelData) {
                    destroy(channelData, "error", undefined, !oneDev.reconnectExhausted);
                  }
                });
              }
            }
          );
          // 画面不同步最多提示2次
          currentPlayer?.pluginMap.liveDelay.setOptions({
            interval: PLAYER_LIVE_DELAY_INTERVAL, //每30秒报一次直通延迟3s
            delayTime: PLAYER_LIVE_DELAY_DELAY_TIME, // 延迟3s才触发
            onMessage: () => {
              // 只提示1次 2.12.2版本暂不提示
              // if (syncTimesRef.current < 1) {
              //     message.warn({
              //         content: (
              //             <span className="sync-times-wrap">
              //                 <span>
              //                     {i18n.t(
              //                         'message',
              //                         '当前网络较差，可能会出现视频时间不同步的情况',
              //                     )}
              //                 </span>
              //                 <span className="sync-times-wrap-close-icon">
              //                     <IconClose02Line
              //                         onClick={() => {
              //                             syncTimesRef.current = syncTimesRef.current + 1;
              //                             message.destroy('syncTimes');
              //                         }}
              //                     />
              //                 </span>
              //             </span>
              //         ),
              //         key: 'syncTimes',
              //         duration: 0,
              //     });
              // }
            },
          });
          currentPlayer.hooks?.afterDestroy?.tap(
            "afterDestroy",
            (player: any, data: any) => {
              // console.trace(player, data);
            }
          );
        }
      })
      .finally(() => {
        createTaskQueueManger.doneAndExecLatestTask(() => {
          const allStatus = [PLAYER_STATUS.error, PLAYER_STATUS.playing].includes(
            playerStatusRef.current
          );
          const channelStatus = channelInfoList.every((p) => {
            return channelInfoMapRef.current[p.channel] && channelInfoMapRef.current[p.channel].status === PLAYER_STATUS.error;
          });
          return allStatus || channelStatus;
        });
      });
  };

  // 设置音量
  function handleVolume(value: number) {
    // 将选中的通道设置为音量，其他通道置为0
    // for (const key in players.current) {
    // players.current[key].volume = key === activeChannel ? value : 0;
    // const volume = key === activeChannel ? value : 0;
    // const [devId, channel] = key.split("-");
    // players.current[key].volume = [{
    //     volume,
    //     channel,
    //     devId,
    // }];
    // }
    if (players.current) {
      const channelInfo = players?.current?.getRenderInfoList?.() || [];
      channelInfo.forEach((oneInfo: any) => {
        const { devId, channel } = oneInfo;
        const channelItem = optionChannelDataRef.current?.find(
          (p: ChannelInfo) => p.authId === devId && p.channelNo == channel
        );
        if (channelItem) {
          const volumeNow =
            channelItem.channel === activeChannelRef.current ? volumeRef.current : 0;
          players.current.volume = [
            {
              volume: volumeNow,
              channel,
              devId,
            },
          ];
        }
      });
    }
  }

  // 切换清晰度
  function handleChangeClarity(qualityType: StreamType, channel?: string) {
    if (!isNil(channel) && players.current) {
      const [deviceId, channelNo] = channel.split("-");
      const authId = optionChannelDataRef.current.find(
        (p) => p.deviceId === deviceId
      )?.authId;
      players.current.streamType = [
        {
          streamType: qualityType,
          channel: Number(channelNo),
          devId: authId,
        },
      ];
      handleChangeEnlarge(channel, "cancelEnlarge");
      dispatch({
        type: "updateChannelInfo",
        payload: {
          channel,
          info: {
            streamType: qualityType,
            enlarge: ENLARGE_STATE.close,
          },
        },
      });
      const channelStatus = (channelInfoMapRef.current as any)?.[channel]?.status;
      if (channelStatus === PLAYER_STATUS.error) {
          reload(channel, qualityType);
      }
    }
  }
  // 更新边看边录状态
  function updateRecordState(states: RecordStatus) {
    dispatch({
      type: "setRecordStatus",
      payload: states,
    });
  }

  const getSessionData = (channel: number, devId: string) => {
    const sessionList = players?.current?.getSessionList?.() || [];
    const data = sessionList.find((oneSession: any) => {
      if (oneSession.devId === devId && oneSession.channel == channel) {
        return true;
      }
      return false;
    });
    return data || {};
  };
  /**needPicture取值逻辑
   * videoDrawFrame 首帧截图 ? videoDrawFrame为1时进行下一步判断 : 0
   * hasMosaic 是否开启马赛克 ? 开启进行下一步马赛克类型判断 : 1
   * mosaicType 设备马赛克类型 ? 取值3 : 进行下一步图片打码租户参数判断
   * pictureMosaic 租户开启图片打码 ? 开启取值3 : 关闭取值1 
   * ****/ 
  const getNeedPictureValue = ({
      videoDrawFrame,
      hasMosaic,
      mosaicType,
      platformOrMix,
      pictureMosaic,
  }: {
      videoDrawFrame: number;
      hasMosaic: boolean;
      mosaicType: string | undefined;
      platformOrMix: string[];
      pictureMosaic: string | undefined;
  }) => {
      if (videoDrawFrame !== 1) {
          return NEED_IMAGE.NO_IMG;
      }
      if (!hasMosaic) {
          return NEED_IMAGE.ONLY_IMG;
      }
      if (!platformOrMix.includes(mosaicType)) {
          return NEED_IMAGE.IMG_AND_MOSIC;
      }
      return pictureMosaic?.includes('1')
          ? NEED_IMAGE.IMG_AND_MOSIC
          : NEED_IMAGE.ONLY_IMG;
  };
  // 边看边录-开始录制
  async function startRecord() {
    if ([RECODING_STATUS.end, RECODING_STATUS.close].includes(recordStatus)) {
      updateRecordState("startLoading");
      // 1. 获取正在播放的通道
      const playingPlayers: ChannelInfo[] = [];
      channels.forEach((item) => {
        const channel = item.channel;
        if (channelInfoMap[channel]?.status === PLAYER_STATUS.playing) {
          const sessionData = getSessionData(item.channelNo, item.authId);
          playingPlayers.push({
            ...item,
            session: sessionData.session || "",
          });
        }
      });
      // 2. 如果没有正在播放的通道则返回
      if (playingPlayers.length == 0) {
        message.error(i18n.t("message", "请选择设备"));
        updateRecordState(RECODING_STATUS.close);
        return;
      }
      // 3. 逐个调用开始录制接口
      const optId: string = uuid();
      let needPicture = 1;
      const deviceDetail = await getDeviceDetail({
        deviceNo: playingPlayers[0]?.deviceNo,
      });
      // N9M的设备需要H264和首帧图
      const isN9M = deviceDetail.protocolType === PROTOCOL_TYPE.N9M;
      let parameterValue: string | undefined;
      let n9mVideoTransMp4Enable: string | undefined;
      let videoDrawFrame: number | undefined = 1;
      let pictureMosaic: string | undefined;
      let mosaicType: string | undefined;
      if (isN9M) {
          // 是N9M设备，请求租户参数，是否需要MP4转码
          try {
              const { list = [] } = await fetchParameterPostPage(
                  {
                      parameterKeys: [
                          N9M_VIDEO_TRANS_MP4_ENABLE,
                          VIDEO_DRAW_FRAME,
                          EVIDENCE_DOWNLOAD_IMAGE_PLATFORM_MOSAIC_SWITCH,
                          TENANT_MOSAIC_TYPE
                      ],
                  }
              );
              const n9mVideoTransMp4ParamData = list?.find(item => item.parameterKey === N9M_VIDEO_TRANS_MP4_ENABLE);
              const videoDrawFrameParamData = list?.find(item => item.parameterKey === VIDEO_DRAW_FRAME);
              const pictureMosaicParamData = list?.find(item => item.parameterKey === EVIDENCE_DOWNLOAD_IMAGE_PLATFORM_MOSAIC_SWITCH);
              const mosaicTypeParamData = list?.find(item => item.parameterKey === TENANT_MOSAIC_TYPE);
              if (!isNil(n9mVideoTransMp4ParamData?.parameterValue)) {
                  n9mVideoTransMp4Enable = n9mVideoTransMp4ParamData?.parameterValue;
              }
              if (!isNil(videoDrawFrameParamData?.parameterValue)) {
                  videoDrawFrame = Number(videoDrawFrameParamData?.parameterValue);
              }
               if (!isNil(pictureMosaicParamData?.parameterValue)) {
                  pictureMosaic = pictureMosaicParamData?.parameterValue;
              }
              if (!isNil(mosaicTypeParamData?.parameterValue)) {
                  mosaicType = mosaicTypeParamData?.parameterValue?.toString();
              }
          } catch (error) {
              // eslint-disable-next-line no-console
              console.log(`不存在参数${N9M_VIDEO_TRANS_MP4_ENABLE}, ${VIDEO_DRAW_FRAME}, `, error);
          }
          const hasMosaic =  mosaicManager.getTenantMosaicSwitch();
          /**needPicture取值逻辑
           * videoDrawFrame 首帧截图 ? videoDrawFrame为1时进行下一步判断 : 0
           * hasMosaic 是否开启马赛克 ? 开启进行下一步马赛克类型判断 : 1
           * mosaicType 设备马赛克类型 ? 取值3 : 进行下一步图片打码租户参数判断
           * pictureMosaic 租户开启图片打码 ? 开启取值3 : 关闭取值1 
           * ****/ 
          needPicture = getNeedPictureValue({
            videoDrawFrame,
            hasMosaic,
            mosaicType,
            platformOrMix,
            pictureMosaic,
        });
      }
      const openRequests = playingPlayers.map(async (item: any) => {
        let streamType = "MAJOR";
        for (const key in channelInfoMap) {
          if (channelInfoMap[key]?.streamType !== "MAJOR") {
            // 有不是主码流的就统一使用子码流
            streamType = "MINOR";
          }
        }
        const data: any = await recordOpen({
          opSession: item.session,
          optId,
          needH264: "1",
          needMP4: isN9M ? n9mVideoTransMp4Enable || "1" : "1",
          needPicture,
          streamType,
        });
        copySession.current.push(data?.copySession);
      });
      // 4. 录制开始和结束的通道必须保持一致
      await Promise.all(openRequests)
        .then(async () => {
          // 5. 获取开始录制时间
          let startTime;
          startTime = await getTime();
          recordTimeStamp.current = [startTime, ""];
          startTimeRef.current = startTime;
          startTime = utils.formator.zeroTimeStampToFormatTime(
            startTime,
            undefined,
            "YYYY-MM-DD HH:mm:ss"
          );
          recordTime.current = [startTime, ""];

          const {
            vehicleNumber,
            deviceNo,
            deviceAlias,
            authId,
            deviceId,
            vehicleId,
          } = (liveVideoOptions?.channelData?.[0] || {});
          // 保存相关信息-用于结束录制
          recordInfo.current = {
            optId,
            channels: playingPlayers,
            vehicleNumber,
            deviceNo,
            deviceAlias,
            authId,
            deviceId,
            vehicleId,
          };
          message.success(i18n.t("message", "录制开始"));
          updateRecordState(RECODING_STATUS.start);
        })
        .catch((error) => {
          // 任何一个开始录制失败即全部失败,失败后将成功开启录制的全部关闭
          copySession.current.forEach((item: any) => {
            recordEnd(item.copySession);
          });
          updateRecordState(RECODING_STATUS.close);
          message.error(i18n.t("message", "录制失败,请重新操作"));
        });
    }
  }

  // 边看边录 结束录制
  const recordEnd = async (session: string) => {
    const data = await recordClose({
      copySession: session,
    });
    return data;
  };

  /**
   * 边看边录 结束录制
   * 这里需要做防抖，是因为当设备离线时，每个通道都会触发error的hook，会连续调用多次停止边看边录方法
   */
  const handleEndRecord = debounce(async () => {
    try {
      if (!recordInfo.current) return false;
      // 1. 获取当前时间
      let endTime;
      try {
        endTime = await getTime();
      } catch {
        message.error({
          content: i18n.t("message", "结束录制错误,请重试"),
          key: "retryRecordStop",
        });
        return false;
      }
      recordTimeStamp.current[1] = endTime;
      endTime = utils.formator.zeroTimeStampToFormatTime(
        endTime,
        undefined,
        "YYYY-MM-DD HH:mm:ss",
        undefined,
        // @ts-ignore
        startTimeRef.current
      );
      message.success({
        content: i18n.t("message", "录制停止"),
        key: "recordStop",
      });
      updateRecordState("endLoading");
      // 逐个调用结束录制接口
      copySession.current.forEach((item: any) => {
        recordEnd(item);
      });
      recordTime.current[1] = endTime;

      updateRecordState("end");
      const {
        vehicleNumber,
        deviceNo,
        deviceAlias,
        authId,
        deviceId,
        vehicleId,
      } = recordInfo.current;
      dispatch({
        type: "setActionInfo",
        payload: {
          actionName: "recordFinish",
          actionPayload: {
            optId: recordInfo.current.optId,
            recordChannels: recordInfo.current.channels.map((item) => {
              // @ts-ignore
              item.channel = item.channelNo;
              return item;
            }),
            recordTime: recordTime.current,
            recordTimeStamp: recordTimeStamp.current,
            vehicleNumber,
            deviceNo,
            deviceAlias,
            authId,
            deviceId,
            vehicleId,
          },
        },
      });
      recordInfo.current = null;
      copySession.current = [];
      return true;
    } catch (error) {
      return false;
    }
  }, 100);

  // 设置帧率码率分辨率
  function handleFrameRateAndBitRate(value: FMPTYPE[]) {
    for (const key in players.current) {
      players.current[key]?.videoInfoLayer?.setInfoList(value || []);
    }
  }

  // 电子放大
  function handleChangeEnlarge(
    channel: string,
    type: "enlarge" | "cancelEnlarge"
  ) {
    const player = players.current;
    if (!player) return;
    const [deviceId, channelNo] = channel.split("-");
    const channelNum = parseInt(channelNo);
    const authId = optionChannelDataRef.current.find(
      (p) => p.deviceId === deviceId
    )?.authId;
    if (type === "enlarge") {
      player.pluginMap.magnifier.enable(channelNum, authId);
    }
    if (type === "cancelEnlarge") {
      player.pluginMap.magnifier.cancelZoomIn(channelNum, authId);
      player.pluginMap.magnifier.disable(channelNum, authId);
    }
  }

    // 重新加载
    async function reload(channel?: string, qualityType?: StreamType) {
        // 判断当前状态，修改全局配置
        if (playerJSRef.current) {
            playerJSRef.current.config = {
                adas: playMode === PLAY_MODE.algorithm,
                dms: playMode === PLAY_MODE.algorithm,
                bucket: playMode === PLAY_MODE.algorithm,
                keyFrame: playMode === PLAY_MODE.save ? 1 : 0,
            };
            // 根据状态，重置重连数据
            if (reconnect) {
                const {
                    reconnectAgainCount,
                    reconnectAgainTime,
                } = config;
                if (typeof reconnectAgainCount === "number") {
                    playerJSRef.current.config = {
                        reconnectAgainCount: reconnectAgainCount
                    };
                }
                if (typeof reconnectAgainTime === "number") {
                    playerJSRef.current.config = {
                        reconnectAgainTime: reconnectAgainTime + RECONNECT_LOADING_PRE_TIME
                    };
                }
            } else {
                playerJSRef.current.config = {
                    reconnectAgainCount: undefined,
                    reconnectAgainTime: undefined
                };
            }
        }
        // 加载所有播放器前先设置所有通道为加载中状态，然后再判断限流，否则会出现一瞬间的暂停中的画面
        if (isNil(channel)) {
            (getBaseVideos() || []).forEach((item) => {
                if (item && !item.channelInfo?.flowLimit) {
                    const { channelInfo } = item;
                    channelInfoMapRef.current[channelInfo.channel] = {
                        ...channelInfoMapRef.current[channelInfo.channel],
                        status: PLAYER_STATUS.loading,
                    };
                }
            });
            dispatch({
                type: 'setChannelInfoMap',
                payload: channelInfoMapRef.current,
            });
            // 设置播放器loading状态
            dispatch({
                type: 'setPlayerStatus',
                payload: PLAYER_STATUS.loading,
            });
        }
        // 直通数据没有通道时不进行播放，防止切换时未销毁
        const channelData = options.liveVideoOptions?.channelData || [];
        if (channelData.length === 0) return;
        // 流量管控
        const flowLimitChannel = channelData.filter((item) => item.flowLimit);
        // 所有通道都管控
        if (
            flowLimitChannel?.length === channelData.length &&
            channelData.length > 0
        ) {
            destroy(undefined, 'stopUse', UpdatePosterAction.CLEAR);
            dispatch({
                type: 'setPlayerStatus',
                payload: PLAYER_STATUS.stopUse,
            });
            // 删除loading
            getBaseVideos().forEach((item) => {
                if (!item) return;
                const { channelInfo } = item;
                updateChannelInfo(channelInfo.channel, {
                    status: PLAYER_STATUS.idle,
                });
            });
            message.warn(i18n.t('message', '流量使用超额，功能暂停使用'));
            return;
        }
        (getBaseVideos() || []).forEach((item) => {
            const { channelInfo } = item || {};
            if (channelInfo && channelInfo.flowLimit) {
                channelInfoMapRef.current[channelInfo.channel].status =
                    PLAYER_STATUS.stopUse;
            }
        });
        dispatch({
            type: 'setChannelInfoMap',
            payload: channelInfoMapRef.current,
        });

    // 重新播放，获取更新设备使能信息
    if (!getBaseVideos() || !getBaseVideos().length) return;
    // 重新设置播放之前，获取设备信息更新要播放的通道的使能、隐私通道等信息
    const deviceIds = [
      ...new Set(getBaseVideos()?.map((item) => item?.channelInfo?.deviceId)),
    ].join(",");
    const deviceChannelList = await getDevicesChannels(deviceIds);
    // 加载所有播放器
    if (isNil(channel)) {
      dispatch({
        type: "setPlayerStatus",
        payload: PLAYER_STATUS.loading,
      });
      const renderChannelKeys = renderChannel.map((item) => item.channel);
      const domList: HTMLElement[] = [];
      const channelInfoList: ChannelInfo[] = [];
      getBaseVideos().forEach((item) => {
        if (!item || !renderChannelKeys.includes(item.channel)) return;
        const { dom, channelInfo, channel } = item;
        if (channelInfo.flowLimit) return;
        // 整合新获取的通道信息到播放的通道
        const targetChannel = deviceChannelList.find(
          (i: any) => i.channel === channel
        );
        // initPlayer(dom, { ...channelInfo, ...(targetChannel || {}) }, PLAYER_STATUS.loading);
        domList.push(dom);
        channelInfoList.push({
          ...channelInfo,
          ...(targetChannel || {}),
        });
      });
      // 先停止，再重建
      if (players.current) {
        await destroy(undefined, "loading");
      }
      if (domList.length) {
        initPlayer(domList, channelInfoList, PLAYER_STATUS.loading);
      }
      // 加载指定通道的播放器
    } else {
      const result = getBaseVideos().find((item) => item?.channel === channel);
      const channelData = deviceChannelList.find(
        (item) => item?.channel === channel
      );
      if (result && channelData) {
        const { dom, channelInfo } = result;
        if (channelInfo.flowLimit) return;
        if (channelData.onlineState === 0) {
          message.error(i18n.t("message", "设备离线"));
          return;
        }
        // 判断是否数据都是错误状态
        // const allStatus = Object.values(channelInfoMapRef.current) || [];
        // const allError = allStatus.filter((oneStatus: any) => {
        //     return oneStatus.status != PLAYER_STATUS.error;
        // }).length === 0;
        // if (allError) {
        //     players.current = null;
        //     createLivingPlayer([dom], [channelInfo]);
        // }
        // 整合新获取的通道信息到播放的通道
        if (
          (players.current && !qualityType) || 
          (players.current && qualityType && players.current.channelStatus?.length > 0)
        ) {
          // 判断是否非播放状态，否则就给销毁
          const status = players.current.status || [];
          const currentStatus = status.find((oneDev: any) => {
            const { info } = oneDev || {};
            return (
              info.devId == channelInfo.authId &&
              info.channel == channelInfo.channelNo
            );
          });
          if (currentStatus && currentStatus.state != "destroyed") {
            await players.current.destroy([
              {
                channel: channelInfo.channelNo,
                devId: channelInfo.authId,
              },
            ]);
          }
          players.current.addStream([
            {
              dom,
              channel: channelInfo.channelNo,
              devId: channelInfo.authId,
            },
          ]);
          const prevStreamType = channelInfoMapRef.current?.[channel]?.streamType;
          if (prevStreamType) {
            // 记录需要恢复为上次的清晰度，需要视频至少afterReady后才设置有效，故现记录任务信息
            recoverClarityTask.current = { channel, clarity: prevStreamType};
          }
          players.current.pluginMap.aspectRatio.set(playAspectRatioRef.current || "origin", {
            channel: channelInfo.channelNo,
            devId: channelInfo.authId,
          });
        } else {
          if (qualityType) {
            channelInfo.streamType = qualityType;
          }
          createLivingPlayer([dom], [channelInfo]);
        }
        updateChannelInfo(channelInfo.channel, {
          status: PLAYER_STATUS.loading,
        });
      }
    }
  }
  enum UpdatePosterAction {
    UPDATE = 1,
    CLEAR = 0,
    KEEP = 2,
  }
  // 销毁播放器
  /**
   *
   * @param channel 销毁的通道
   * @param state 可控销毁后的状态【stop状态会展示重新播放按钮】
   * @returns
   */
  async function destroy(
    channelInfo?: ChannelInfo | string,
    state?: PlayerStatus,
    updatePosterAction: UpdatePosterAction = UpdatePosterAction.UPDATE,
    onlyShotPoster?: boolean
  ) {
    const _destroySingle = (
      channel: string,
      channelData: ChannelData,
      updatePosterAction: UpdatePosterAction = UpdatePosterAction.UPDATE
    ) => {
      let base64 = "";
      if (updatePosterAction == UpdatePosterAction.UPDATE) {
        base64 = getPosterBase64(channelData, channel);
      } else if (updatePosterAction == UpdatePosterAction.KEEP) {
        base64 = channelInfoMapRef.current[channel]?.poster;
      }
      return {
        channel,
        status: isNil(channelInfo) ? PLAYER_STATUS.idle : state || "idle",
        enlarge: ENLARGE_STATE.close,
        poster: base64,
      };
    };
    // 销毁所有播放器
    if (isNil(channelInfo)) {
      // 如果正在边看边录，需要结束录制
      if (recordStatus === RECODING_STATUS.start) handleEndRecord();
      // 更新整个播放器的状态
      if (players.current) {
        dispatch({
          type: "setPlayerStatus",
          payload: state || "idle",
        });
      }
      const destroyQueue: any[] = [];
      if (players.current) {
        const renderList = players.current?.getRenderInfoList?.() || [];
        if (renderList.length > 0) {
          // 已经渲染的，截图作为海报展示
          renderList.forEach((oneRender: any) => {
            const authId = oneRender.devId;
            const channelItem = optionChannelDataRef.current?.find(
              (p: ChannelInfo) =>
                p.authId === authId && p.channelNo == oneRender.channel
            );
            if (channelItem) {
              const channelInfo = {
                devId: oneRender.devId,
                channel: oneRender.channel,
              };
              destroyQueue.push(
                _destroySingle(
                  channelItem.channel,
                  channelInfo,
                  updatePosterAction
                )
              );
            }
          });
          // 处理其他的未在renderList中的数据
          renderChannel.forEach((oneItem: ChannelInfo) => {
            const findItem = renderList.find((r: any) => {
              return (
                r.realDevId === oneItem.authId &&
                r.realChannel == oneItem.channelNo
              );
            });
            if (!findItem) {
              const data: any = {
                channel: oneItem.channel,
                status: isNil(channelInfo)
                  ? PLAYER_STATUS.idle
                  : state || "idle",
                enlarge: ENLARGE_STATE.close,
              };
              // 车辆监控页面时，全局关闭不要保留数据，在其他页面时，需要保留（undefined情况保留原有截图）
              if (poster && poster.stop === false) {
                data.poster = "";
              }
              destroyQueue.push(data);
            }
          });
        } else {
          // 未渲染情况，无法截图
          renderChannel.forEach((oneItem: ChannelInfo) => {
            destroyQueue.push({
              channel: oneItem.channel,
              status: isNil(channelInfo) ? PLAYER_STATUS.idle : state || "idle",
              enlarge: ENLARGE_STATE.close,
            });
          });
        }
        const temp = players.current;
        if (players.current) {
          // 预先将引用置为null，防止dom切换时出现问题
          players.current = null;
        }
        const newState = checkFlowLimit()
          ? PLAYER_STATUS.stopUse
          : state || PLAYER_STATUS.stop;
        updateChannelsInfo(destroyQueue);
        dispatch({
          type: "setPlayerStatus",
          payload: newState,
        });
        // 同步更改播放器的状态，是SDK的hook回调中能拿到最新的数据
        playerStatusRef.current = newState;
        if (!onlyShotPoster) {
          // 最后执行关闭，防止异步逻辑
          const key = Date.now();
          destroyTaskQueue.push({ key });
          try {
            await temp?.destroy();
            updateChannelsInfo(destroyQueue);
          } catch (error) {
          } finally {
            destroyTaskQueue = destroyTaskQueue.filter((p) => p.key !== key);
            // createTaskQueueManger.doneTask();
          }
        }
      } else {
        // 未创建成功关闭逻辑
        const newState = checkFlowLimit()
          ? PLAYER_STATUS.stopUse
          : state || PLAYER_STATUS.idle;
        dispatch({
          type: "setPlayerStatus",
          payload: newState,
        });
        const destroyQueue = liveVideoOptions?.channelData?.map(
          (oneData: ChannelInfo) => {
            return {
              channel: oneData.channel,
              status: state || "idle",
              enlarge: ENLARGE_STATE.close,
              poster: "",
            };
          }
        );
        updateChannelsInfo(destroyQueue || []);
      }
      createTaskQueueManger?.doneAndExecLatestTask?.(() => true);
    } else if (channelInfo && players.current) {
      // 销毁单个播放器
      let channel = "";
      let channelData: any = {};
      // const channel = `${channelInfo.authId}-${channelInfo.channelNo}`;
      // const channelData = {
      //     devId: channelInfo.authId,
      //     channel: channelInfo.channelNo
      // };
      if (typeof channelInfo === "string") {
        channel = channelInfo;
        const [deviceId, channelNo] = channelInfo.split("-");
        const channelItem = liveVideoOptions?.channelData?.find(
          (p: ChannelInfo) => p.deviceId === deviceId
        );
        channelData = {
          devId: channelItem?.authId,
          channel: Number(channelNo),
        };
      } else {
        channel = channelInfo.channel;
        channelData = {
          authId: channelInfo?.authId,
          devId: channelInfo?.authId,
          channel: Number(channelInfo?.channelNo),
        };
      }
      // 获取蒙层截图
      const channelInfoData = _destroySingle(
        channel,
        channelData,
        updatePosterAction
      );
      // 先更新截图
      updateChannelInfo(channel, channelInfoData);
      if (channelInfoMapRef.current?.[channel]) {
        channelInfoMapRef.current[channel] = {
          ...(channelInfoMapRef.current?.[channel] || {}),
          ...channelInfoData,
        };
      }
      if (!onlyShotPoster) {
        // 再执行销毁
        setTimeout(async () => {
          await players.current?.destroy([channelData]);
        });
      }
    }
  }
  // 更新通道状态信息
  function updateChannelInfo(
    channel: string,
    info: ChannelStateInfo,
    clearPoster?: boolean
  ) {
    const originPoster =
      channelInfoMapRef.current[channel]?.poster ||
      channelInfoMap[channel]?.poster;
    const poster = clearPoster ? "" : info?.poster ?? originPoster;
    // console.trace("updateChannelInfo errorData", channel, info, clearPoster);
    dispatch({
      type: "updateChannelInfo",
      payload: {
        channel,
        info: {
          ...(channelInfoMapRef.current?.[channel] || {}),
          ...info,
          poster,
        },
      },
    });
  }

  // 批量更新通道状态信息
  function updateChannelsInfo(
    infos: ChannelStateInfo[],
    excludePlayerStatus?: PlayerStatus
  ) {
    if (excludePlayerStatus && playerStatusRef.current === excludePlayerStatus)
      return;
    dispatch({
      type: "updateChannelsInfo",
      payload: {
        infos,
      },
    });
    // nextChannelInfo是下方onLoading回调过于频繁添加的优化点，对更新通道信息做了聚合。
    // 当执行到这里时，表示dispatch这批聚合的动作，提交完成后，清空这个聚合队列，即：nextChannelInfo，以便开始新的聚合
    nextChannelInfo.current.length = 0;
  }
  const { run: throttleUpdateChannelInfo } = useThrottleFn(updateChannelInfo, {
    wait: 100,
  });

  const { run: throttleUpdateChannelsInfo } = useThrottleFn(
    updateChannelsInfo,
    {
      wait: 100,
    }
  );

  // 渲染播放
  function renderPlay(channel: number | string) {
    // 只要是报错或者停止状态就展示重新加载按钮
    // PLAYER_STATUS.error 状态不展示暂停状态, 关闭播放器时，每个通道不需要展示播放蒙版
    // console.log("playerStatus:", [PLAYER_STATUS.stop].includes(channelInfoMap[channel]?.status), playerStatus);
    if (
      // @ts-ignore
      [PLAYER_STATUS.stop].includes(channelInfoMap[channel]?.status) &&
      ![PLAYER_STATUS.stop, PLAYER_STATUS.stopUse].includes(playerStatus)
    ) {
      return (
        <span className="reload-icon">
          <Reload
            channel={channel}
            iconText={i18n.t("name", "视频已暂停")}
            icon={<IconPlayHollowFine />}
          />
        </span>
      );
    }
    return null;
  }
  // 渲染重新加载

  function renderReload(channel: number | string) {
    // 只要是报错或者停止状态就展示重新加载按钮
    // PLAYER_STATUS.error 状态不展示暂停状态
    if (
      // @ts-ignore
      [PLAYER_STATUS.error].includes(channelInfoMap[channel]?.status) &&
      ![PLAYER_STATUS.stop, PLAYER_STATUS.stopUse].includes(playerStatus)
    ) {
      return (
        <span className="reload-icon">
          <Reload
            channel={channel}
            iconText={
              channelInfoMap[channel].errorMsg ||
              i18n.t("name", "网络不佳，加载超时")
            }
          />
        </span>
      );
    } else if (
      channelInfoMap[channel]?.status === PLAYER_STATUS.stopUse &&
      ![PLAYER_STATUS.stop, PLAYER_STATUS.stopUse].includes(playerStatus)
    ) {
      return <span className="reload-icon">{i18n.t("name", "暂停使用")}</span>;
    }
    return null;
  }

    // Dom加载完成
    async function handleOnLoaded(
        domList: HTMLElement[],
        channelInfoList: ChannelInfo[],
    ) {
        await checkDestroyTaskQueue();
        if (checkFlowLimit()) {
            dispatch({
                type: 'setPlayerStatus',
                payload: PLAYER_STATUS.stopUse,
            });
            return;
        }
        const notFlowLimitDom: HTMLElement[] = [];
        const notFlowLimitChannelList: ChannelInfo[] = [];
        const updateQueue: any[] = [];
        channelInfoList.forEach((channelInfo, index) => {
            const dom = domList[index];
            const channelStr = channelInfo.channel;
            baseVideos.current[channelStr] = {
                dom,
                channelInfo,
                index,
                channel: channelInfo.channel,
            };
            // 流量管控判断 若播放器都停用则通道停用
            if (playerStatus === PLAYER_STATUS.stopUse) {
                updateQueue.push({
                    channel: channelStr,
                    status: PLAYER_STATUS.stopUse
                });
            } else if (channelInfo.flowLimit) {
                updateQueue.push({
                    channel: channelStr,
                    status: PLAYER_STATUS.stopUse,
                    poster: ''
                });
            } else {
                updateQueue.push({
                    channel: channelStr,
                    status: PLAYER_STATUS.loading
                });
            }
            if (!channelInfo.flowLimit) {
                notFlowLimitDom.push(dom);
                notFlowLimitChannelList.push(channelInfo);
            }
        });
        console.error('create channels info', updateQueue);
        updateChannelsInfo(updateQueue);
        if (playerStatus === PLAYER_STATUS.stopUse) return;
        // 更新状态为loading
        dispatch({
            type: 'setPlayerStatus',
            payload: PLAYER_STATUS.loading,
        });
        if (notFlowLimitDom.length) {
            await initPlayer(
                notFlowLimitDom,
                notFlowLimitChannelList,
                PLAYER_STATUS.loading,
            );
        }
    }
    const domMapJudgeRef = useRef<any[]>([]);
    const handleDomLoaded = useCallback((
        dom: HTMLElement,
        channelInfo: ChannelInfo,
        index: number,
    ) => {
        const flagKey = "data-init-flag";
        if(dom.getAttribute(flagKey)) return;
        dom.setAttribute(flagKey, '1');
        const oneData = {
            channelInfo,
            dom,
            index,
        };
		// bugID=125889，限流不应该参与计算，比如当前9个通道，只有四个通道能播，那么每隔四个就会加载sdk，然后剩一个，导致后续异常
		if (channelInfo.flowLimit) return;
        // 先一个通道打开隐私模式时打开视频，然后关闭隐私模式，再次打开该车辆视频时会导致之前的隐私模式通道被push两次
        if (!newRenderChannelKeysRef.current.length) return;
        const pushIndex = domMapJudgeRef.current.findIndex(p => p.channelInfo?.channel === channelInfo.channel);
        if (pushIndex !== -1) {
            domMapJudgeRef.current[pushIndex] = oneData;
        } else {
            domMapJudgeRef.current.push(oneData);
        }
        // 需要过滤掉隐私模式的通道数据
		const page = currentPage || 1;
        const len = (liveVideoOptions?.channelData || [])
		.slice((page  - 1) * layoutCount, page * layoutCount)
		.filter((channelInfo: ChannelInfo) => {
            return channelInfo.privacyState;
        }).length;
        if (
            domMapJudgeRef.current.length ===
            (newRenderChannelKeysRef.current.length - len)
        ) {
            const domList: HTMLElement[] = [];
            const channelInfoList: ChannelInfo[] = [];
            domMapJudgeRef.current.forEach((oneDomInfo: any) => {
                const { dom, channelInfo, index } = oneDomInfo;
                domList.push(dom);
                channelInfoList.push(channelInfo);
            });
            // 使用完毕，替换成新数据，并且清除数据
            domMapJudgeRef.current = [];
            // if (createTaskQueueManger.queueList.length > 1) {
            //     // 说明已经有等候的创建任务了，没必要继续创建
            //     createTaskQueueManger.doneTask();
            //     createTaskQueueManger.execLatestTask(() => true);
            //     return;
            // }
            handleOnLoaded(domList, channelInfoList);
        }
    }, [liveVideoOptions, activeChannel]);

  const closePreVideos = async (item: string) => {
    if (players.current) {
      const channelItemData = (liveVideoOptions?.channelData || []).find(
        (channelItem: ChannelInfo) => {
          return channelItem.channel === item;
        }
      );
      if (channelItemData) {
        // 判断是否在渲染列表中
        const renderList = players.current?.getRenderInfoList?.();
        const channelItem = (renderList || []).find((channelItem: any) => {
          return (
            channelItem.realChannel == channelItemData.channelNo &&
            channelItem.realDevId == channelItemData.authId
          );
        });
        // 如果在，则关闭
        if (channelItem) {
          const { realChannel, realDevId } = channelItem;
          dispatch({
            type: "updateChannelInfo",
            payload: {
              channel: channelItemData.channel,
              info: {
                enlarge: ENLARGE_STATE.close,
              },
            },
          });
          await players.current.destroy([
            {
              devId: realDevId,
              channel: realChannel,
            },
          ]);
        }
      }
    }
  };

    const allChannelKeys = getAllChannels();
    const renderChannelKeys = useCallback(renderChannel.map((item) => item.channel), [renderChannel]);
    return (
        <React.Fragment>
            {allChannelKeys.map((item: any, index: number) => {
                if (renderChannelKeys.includes(item)) {
                    const channelItem = renderChannel.find(
                        (channelItem: ChannelInfo) => {
                            return channelItem.channel === item;
                        },
                    );
                    const { channel } = channelItem;
                    if (
                        renderPlayerLayout &&
                        typeof renderPlayerLayout === 'function'
                    ) {
                        return renderPlayerLayout(
                            <BaseVideo
                                channel={channel}
                                onLoaded={handleDomLoaded}
                                key={channel}
                                channelItem={channelItem}
                                index={index}
                                coverContent={renderPlay(channel)}
                                errorContent={renderReload(channel)}
                                className={prefix + '-video'}
                            />,
                            {
                                ...(channelItem || {}),
                                channel: channel,
                                status: channelInfoMap[channel]?.status,
                                vehicleNumber: channelItem.vehicleNumber,
                            },
                        );
                    }

          return <BaseVideo channel={channel} key={channel} />;
        } else if (!renderChannel.length && showEmptyPlaceholder) {
          return " ";
        } else {
          closePreVideos(item);
        }
        return null;
      })}
    </React.Fragment>
  );
};

export default LiveVideoGroup;
