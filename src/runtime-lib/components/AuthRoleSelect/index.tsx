import AuthSelect, {
    RefSelectProps,
    SelectProps,
} from '@/components/AuthSelectShow';
import { STATE_ENABLE } from '@/runtime-pages/tenant-center/space-manage';
import { getRoleListByPage, getRoleListMergeByPage, RoleListMergeParams } from '@/service/role';
import { MAX_USER_ROL_SELECT_NUMBER, SELECT_PAGE_SIZE } from '@/utils/constant';
import {
    StarryAbroadFormItem as AFormItem,
    getAppGlobalData,
    i18n,
} from '@base-app/runtime-lib';
import { isNil } from 'lodash';
import React, { useEffect } from 'react';
import { useImperativeHandle, useRef } from 'react';

export enum RoleTypeEnum {
    /**租户管理员**/ 
    ADMIN = 1,
    /**应用管理员***/ 
    APPLICATION = 2,
    /**功能管理员**/ 
    FUN = 3
}

type AuthRoleSelectProps = SelectProps & {
    appId?: number | string; //角色查询List所需appId
    formatRequestParams: (params: RoleListMergeParams) => RoleListMergeParams
    adminInfo: {
        adminVisible?: boolean; //是否展示租户管理员角色回显
        adminIdList?: string[];
    }
};
type RefAuthRoleSelect = {
    getAuth: boolean; //获取单选是否存在无权限的数据
    getModeAuth: boolean; //获取多选是否存在无权限的数据
    // 新增方法
    refresh: () => Promise<any>; //从新加载刷新下拉数据
    search: () => Promise<any>; //搜素下拉数据
} & RefSelectProps;
const AuthRoleSelect = React.forwardRef<RefAuthRoleSelect, AuthRoleSelectProps>(
    (props, ref) => {
        const { appId, formatRequestParams, adminInfo = {}, ...restProps } = props;
        const { adminVisible = true, adminIdList = [] } = adminInfo;
        const adminIdListRef = useRef([]);
        adminIdListRef.current = adminIdList;
        const inSaaS = !getAppGlobalData('APP_ID');
        const authSelectRoleMsgRef = useRef(null);
        const getAppId = () => {
            if (isNil(appId)) {
                return inSaaS ? undefined : getAppGlobalData('APP_ID');
            }
            return appId;
        };
        const getRoleOptions = async (
            userAuthorityNewRoleSearchParams: Record<string, any>,
            roleIdList: string[],
        ) => {
            const { searchValue } = userAuthorityNewRoleSearchParams;
            delete userAuthorityNewRoleSearchParams['searchValue'];
            let params: RoleListMergeParams = {
                userAuthorityNewRole: {
                    page: 1,
                    pageSize: SELECT_PAGE_SIZE,
                    appId: getAppId(),
                    roleType: RoleTypeEnum.FUN,
                    // state: 1,
                    isOwn: false,
                    userId: getAppGlobalData('APP_USER_INFO')?.userId,
                    userSearch: searchValue,
                    searchNameOnly: 1,
                    ...userAuthorityNewRoleSearchParams,
                },
                roleIdList: roleIdList ?? [],
            };
            // 开放参数定制
            if (typeof formatRequestParams === 'function') {
                params = formatRequestParams(params);
            }
            // 当前租户启用的用户
            const resultData = await getRoleListMergeByPage(params);
            const { roleIdInfoList, userAuthorityNewRole } = resultData;
            const userAuthorityNewRoleList = userAuthorityNewRole?.list || [];
            const userAuthorityNewRoleIds = userAuthorityNewRoleList?.map((item) => item.roleId);
            const roleIdArray = roleIdInfoList?.map((item) => item.roleId);
            const adminRoleId = adminIdListRef.current?.[0];
            /****对于没有租户管理员权限的租户需要做如下处理****/
            // if(!adminVisible && adminRoleId && !roleIdArray.includes(adminRoleId)) {
            //     roleIdArray.unshift(adminRoleId);
            //     roleIdInfoList.unshift({
            //         roleId: adminRoleId,
            //         roleType: RoleTypeEnum.ADMIN
            //     });
            // }
            if(!adminVisible && adminRoleId && !userAuthorityNewRoleIds?.includes(adminRoleId)) {
                userAuthorityNewRoleList.unshift({
                    roleId: adminRoleId,
                    roleType: RoleTypeEnum.ADMIN
                });
            }
            /*****end***/
            return {
                baseList: {
                    list: (userAuthorityNewRoleList || [])
                        .map((item: any) => ({
                            label: i18n.t(
                                `@i18n:@role__${item.roleId}`,
                                item.roleName,
                            ),
                            value: item.roleId,
                            show: adminVisible ? true : item.roleType !== RoleTypeEnum.ADMIN
                        })),
                    total: userAuthorityNewRole?.total,
                    hasMore: userAuthorityNewRole.list.length == SELECT_PAGE_SIZE,
                },
                customList: {
                    list:
                        roleIdInfoList.map((item: any) => ({
                            label: i18n.t(
                                `@i18n:@role__${item.roleId}`,
                                item.roleName,
                            ),
                            value: item.roleId,
                            show: adminVisible ? true : item.roleType !== RoleTypeEnum.ADMIN
                        })) || [],
                    noAuthList: (roleIdList || []).filter(
                        (roleId) => !roleIdArray.includes(roleId),
                    ),
                },
            };
        };
        useImperativeHandle(ref, () => {
            return {
                getAuth: authSelectRoleMsgRef.current?.getAuth,
                getModeAuth: authSelectRoleMsgRef.current?.getModeAuth,
                // 新增方法
                refresh: authSelectRoleMsgRef.current?.refresh,
                search: authSelectRoleMsgRef.current?.search,
            };
        });
        return (
            <AuthSelect
                ref={authSelectRoleMsgRef}
                mode="multiple"
                placeholder={i18n.t('message', '请选择生效角色')}
                lazyLoad={true}
                labelInValue
                onLoadMore={getRoleOptions}
                showSearch={true}
                virtual={true}
                // @ts-ignore
                filterOption={(
                    inputValue: string,
                    option: {
                        label: string;
                    },
                ) => {
                    return option?.label
                        ?.toUpperCase()
                        ?.includes?.(inputValue?.toUpperCase());
                }}
                maxCount={MAX_USER_ROL_SELECT_NUMBER}
                {...restProps}
            />
        );
    },
);
export default AuthRoleSelect;
