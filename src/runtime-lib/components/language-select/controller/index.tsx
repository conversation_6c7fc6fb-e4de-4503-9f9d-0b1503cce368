/**
 * Language Select 控制器层
 * 负责UI渲染、用户交互、组件生命周期管理和数据管理
 */
import React, { useEffect, useCallback, useState } from 'react';
import { Dropdown, Menu } from '@streamax/poppy';
import { IconLanguage } from '@streamax/poppy-icons';
import { getAppGlobalData, i18n, CacheType } from '@/runtime-lib';
import type { ILanguageSelectProps, ILanguageOption } from '../interface';
import { LANGUAGE_LABEL_LIST } from '../constant';
import { Loading, Error } from '../components';
import '../style/index.less';
import { DropDownProps } from '@streamax/poppy/lib/dropdown';
import {
    fetchLanguageList,
    LanguageItem,
    LanguageState,
} from '@/service/language';

const CLASS_NAME = 'material-language-select';
/** 默认数据源函数 */
const defaultGetDataSource = async (): Promise<ILanguageOption[]> => {
    try {
        const data = await fetchLanguageList();
        return data
            .filter((item) => item.state === LanguageState.ENABLED)
            .map((item: LanguageItem) => ({
                value: item.language,
                label: LANGUAGE_LABEL_LIST[
                    item.language as keyof typeof LANGUAGE_LABEL_LIST
                ],
            }));
    } catch (error) {
        throw error;
    }
};

const LanguageSelectController: React.FC<
    ILanguageSelectProps & Omit<DropDownProps, 'overlay'>
> = ({
    getDataSource,
    onSelect,
    child,
    placement = 'bottomRight',
    trigger = ['click'],
    ...restProps
}) => {
    // 状态管理
    const [options, setOptions] = useState<ILanguageOption[]>([]);
    const [selectedValue, setSelectedValue] = useState<string[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    /** 获取语言选项列表 */
    const fetchLanguageOptions = useCallback(
        async (customGetDataSource?: () => Promise<ILanguageOption[]>) => {
            try {
                setLoading(true);
                setError(null);

                const dataSource = customGetDataSource || defaultGetDataSource;
                const optionsData = await dataSource();

                setOptions(optionsData);

                // 设置默认选中项
                const currentLang = getAppGlobalData('APP_LANG');
                const currentOption = optionsData.find(
                    (opt) => opt.value === currentLang,
                );

                if (currentOption) {
                    setSelectedValue([currentLang]);
                } else if (optionsData.length > 0) {
                    // 如果当前语言不在列表中，选择第一个
                    setSelectedValue([optionsData[0].value]);
                }

                setLoading(false);
                return optionsData;
            } catch (err) {
                setError(String(err));
                setLoading(false);
            }
        },
        [],
    );

    /** 默认选择处理逻辑 */
    const handleDefaultSelect = useCallback((value: string) => {
        try {
            localStorage.setItem('LANG', value);
            // 将 cacheControl 设置到 URL 的 search 参数上
            const url = new URL(window.location.href);
            url.searchParams.set('cacheControl', CacheType.NetworkFirst);
            window.history.replaceState(null, '', url.toString());
            window.location.reload();
        } catch (error) {
            console.error(error);
        }
    }, []);

    /** 处理语言选择 */
    const handleSelect = useCallback(
        (
            value: string,
            option: ILanguageOption,
            customOnSelect?: (value: string, option: ILanguageOption) => void,
        ) => {
            setSelectedValue([value]);

            if (customOnSelect) {
                customOnSelect(value, option);
            } else {
                handleDefaultSelect(value);
            }
        },
        [handleDefaultSelect],
    );

    // 初始化数据
    useEffect(() => {
        fetchLanguageOptions(getDataSource);
    }, []);

    const reloadData = () => {
        fetchLanguageOptions(getDataSource);
    };
    // 处理菜单选择事件
    const handleMenuSelect = useCallback(
        ({ key }: { key: string }) => {
            const option = options.find((opt) => opt.value === key);
            if (option) {
                handleSelect(key, option, onSelect);
            }
        },
        [options, handleSelect, onSelect],
    );

    // 渲染菜单项
    const renderMenuItems = () => {
        return options.map((option) => (
            <Menu.Item
                className={`${CLASS_NAME}-item`}
                key={option.value}
                onClick={handleMenuSelect}
            >
                {option.label}
            </Menu.Item>
        ));
    };

    // 渲染选中项显示内容
    const renderSelectedContent = () => {
        return (
            <Menu defaultSelectedKeys={selectedValue}>
                {loading && <Loading className={`${CLASS_NAME}-loading`} />}
                {error && (
                    <Error
                        className={`${CLASS_NAME}-error`}
                        onReload={reloadData}
                    />
                )}
                {!loading && !error && renderMenuItems()}
            </Menu>
        );
    };

    return (
        <Dropdown
            overlay={renderSelectedContent()}
            trigger={trigger}
            placement={placement}
            overlayClassName={`${CLASS_NAME}`}
            getPopupContainer={(triggerNode) => triggerNode}
            arrow
            {...restProps}
        >
            <div className={`${CLASS_NAME}-content`}>
                {child || <IconLanguage title={i18n.t('name', '语言')} />}
            </div>
        </Dropdown>
    );
};

export default LanguageSelectController;
