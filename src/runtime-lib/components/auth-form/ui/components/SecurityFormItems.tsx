/*
 * @LastEditTime: 2025-06-24 16:23:10
 */
/*
 * @LastEditTime: 2025-06-11 09:40:02
 */
import { i18n, StarryAbroadFormItem as AFormItem } from '@/runtime-lib';
import { Form, Input, Select } from '@streamax/poppy';
import { RspFormLayout } from '@streamax/responsive-layout';
import { ReactElement, useEffect, useState } from 'react';
import './securityFormItems.less';
import { VerifyTypeEnum } from '../../controller';

const PHONE_AUTH_CODE_LENGTH = 6;
const EMAIL_AUTH_CODE_LENGTH = 8;
interface SecurityFormItemsUIProps {
    validateOptions: any[]; //下拉透传数据
    verifyCodeButton: ReactElement; //获取验证码组件ui
    onVerifyTypeChange?: (verifyType) => void; //验证方式改变回调
    responsive?: boolean; //是否支持自适应
    verifyType: VerifyTypeEnum | undefined;
}
export default (props: SecurityFormItemsUIProps) => {
    const {
        validateOptions = [],
        onVerifyTypeChange,
        verifyCodeButton,
        responsive,
        verifyType,
    } = props;
    // 验证方式
    const validateChooseWay = (_: any, value: string) => {
        if (!value) {
            return Promise.reject(i18n.t('message', '验证方式不能为空'));
        }
        return Promise.resolve();
    };
    // 验证方式
    const validateAuthCode = (_: any, value: string) => {
        // 验证码为空
        if (!value) {
            return Promise.reject(
                i18n.t('message', '验证码不能为空'),
            );
        }
        // 验证码不为空
        const verifyRegExp = verifyType === VerifyTypeEnum.PHONE_NUMBER
            ? /^[0-9]*$/ : /^[0-9a-zA-Z]*$/; // 手机 只能是数字；邮箱 数字、大小写字母
        if (!verifyRegExp.test(value)) {
            return Promise.reject(
                i18n.t('message', '验证码格式错误，请重新输入'),
            );
        }
        return Promise.resolve();
    };
    // 处理验证方式改变
    const handleSelect = (e) => {
        onVerifyTypeChange?.(e);
    };
    const formItem = (
        <>
            <RspFormLayout.SingleRow>
                <AFormItem
                    label={i18n.t('name', '验证方式')}
                    name="verifyType"
                    required
                    wrapperCol={{ span: 24 }}
                    rules={[
                        {
                            validator: validateChooseWay,
                        },
                    ]}
                >
                    <Select
                        dropdownClassName="second-page-select-dropdown"
                        options={validateOptions}
                        placeholder={i18n.t('message', '请选择验证方式')}
                        onSelect={handleSelect}
                    />
                </AFormItem>
            </RspFormLayout.SingleRow>
            <RspFormLayout.SingleRow>
                <Form.Item noStyle>
                    <div className="auth-code-form-item">
                        <AFormItem
                            name="verifyCode"
                            validateFirst
                            wrapperCol={{ span: 24 }}
                            rules={[
                                {
                                    validator: validateAuthCode,
                                },
                            ]}
                            className="auth-code-input"
                        >
                            <Input
                                placeholder={i18n.t('message', '请输入验证码')}
                                maxLength={
                                    verifyType === 'phoneNumber'
                                        ? PHONE_AUTH_CODE_LENGTH
                                        : EMAIL_AUTH_CODE_LENGTH
                                }
                                allowClear
                            />
                        </AFormItem>
                        {verifyCodeButton}
                    </div>
                </Form.Item>
            </RspFormLayout.SingleRow>
        </>
    );
    return (
        <span className="security-form-items-wrap">
            {responsive ? (
                <RspFormLayout layoutType="auto">{formItem}</RspFormLayout>
            ) : (
                formItem
            )}
        </span>
    );
};
