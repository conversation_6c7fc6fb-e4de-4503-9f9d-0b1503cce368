/*
 * @LastEditTime: 2025-06-30 19:43:59
 */
/* eslint-disable indent */
import { Auth, getAppGlobalData, i18n } from '@/runtime-lib';
import { useForm } from '@streamax/poppy/lib/form/Form';
import {
    forwardRef,
    useEffect,
    useRef,
    useImperativeHandle,
    useState,
} from 'react';
import {
    getEmailVerifyCode,
    getPhoneVerifyCode,
    verifyUserCode,
} from '../../../../service/user';
import SecurityFormUI from './../ui';
import SecurityFormItemsUI from './../ui/components/SecurityFormItems';
import VerifyCodeUI, { COUNT_DOWN_TOTAL } from './../ui/components/VerifyCode';
import { checkAuth } from '@/runtime-lib/auth/utils';
import { getAuthUserInfoConfig, checkResourceExist } from '@/service/tenant';
import { message } from '@streamax/poppy';
/**
 * 验证类型
 */
enum BizTypeEnum {
    /**
     * 登录验证 0
     * **/
    LOGIN = 0,
    /**
     * 绑定手机号
     * **/
    BIND_PHONE = 1,
    /**
     * 身份验证
     * */
    AUTH_VERIFY = 2,
    /**
     * 二次验证
     * **/
    SECOND_VERIFY = 3,
    /**
     * 找回密码
     * **/
    FIND_PASSWORD = 4,
}
/**
 * 验证方式
 */
export enum VerifyTypeEnum {
    /**
     * 手机号验证
     * **/
    PHONE_NUMBER = 'phoneNumber',
    /**
     * 邮箱验证
     * **/
    MAIL_NUMBER = 'mailNumber',
}
const verifyTypeNumber = {
    /**
     * 手机号验证
     * **/
    [VerifyTypeEnum.PHONE_NUMBER]: 0,
    /**
     * 邮箱验证
     * **/
    [VerifyTypeEnum.MAIL_NUMBER]: 1,
};

export default forwardRef((props: { responsive?: boolean }, ref) => {
    // const [validateOptions, setValidateOptions] = useState<any>([]);
    const { responsive } = props;
    const [countDown, setCountDown] = useState<number>(COUNT_DOWN_TOTAL);
    const [loading, setLoading] = useState(false);
    const [validateOptions, setValidateOptions] = useState([]);
    const [verifyType, setVerifyType] = useState<VerifyTypeEnum>();
    const [form] = useForm();
    const countDownTimer = useRef();
    const userInfo = getAppGlobalData('APP_USER_INFO');
    const realUserInfo = getAppGlobalData('REAL_USER_INFO');
    const defaultValidateOptions = [
        {
            label: userInfo?.phoneNumber
                ? `+${userInfo?.areaCode} ` + userInfo?.phoneNumber
                : null,
            value: VerifyTypeEnum.PHONE_NUMBER,
        },
        {
            label: userInfo?.email ? userInfo?.email : null,
            value: VerifyTypeEnum.MAIL_NUMBER,
        },
    ].filter((item) => item && item.label);

    useEffect(() => {
        getUserConfig();
    }, []);
    const getUserConfig = async () => {
            const params = {
                tenantId: userInfo.tenantId,
                key: 'tenant.user.info.config',
            };
            
            // 检查资源权限
            const resourceParams = {
                resourceCode: '@base:@page:tenant.detail@action:tab.tenant.config:mobile.security',
                tenantId: userInfo.tenantId,
            };
            
            // 并发请求用户配置和资源权限
            const [userConfigInfo, hasResourcePermission] = await Promise.all([
                getAuthUserInfoConfig(params),
                checkResourceExist(resourceParams)
            ]);
            
            const info = userConfigInfo['tenant.user.info.config']
                ? JSON.parse(userConfigInfo['tenant.user.info.config'])
                : {};
            const list = [];
            if(info.verificationType) {
                for(const key in verifyTypeNumber) {
                    const type = verifyTypeNumber[key];
                    if(info.verificationType.includes(type)) {
                        // 对于手机号验证，需要检查资源权限
                        if(key === VerifyTypeEnum.PHONE_NUMBER && !hasResourcePermission) {
                            continue; // 如果没有权限，跳过添加手机号验证选项
                        }
                        
                        const item = defaultValidateOptions.find(item => item.value === key);
                        if(item) {
                            list.push(item);
                            if(info.defaultType == type) {
                                form.setFieldsValue({
                                    verifyType: item.value
                                });
                                setVerifyType(item.value);
                            }
                        }
                    }
                }
            }
            setValidateOptions(list);
        };
    /**获取验证码**/
    const onVerifyCodeClick = async () => {
        // 清除验证码校验的错误
        form.setFields([
            {
                name: 'verifyCode',
                errors: [],
            },
        ]);
        const { verifyType } = await form.validateFields(['verifyType']);
        const verifyTypeValue =
            verifyType === VerifyTypeEnum.PHONE_NUMBER
                ? realUserInfo.phoneNumber
                : realUserInfo.email;
        const params = {
            bizType: BizTypeEnum.AUTH_VERIFY,
            [verifyType]: verifyTypeValue,
            account: userInfo?.account,
        };
        if (verifyType === VerifyTypeEnum.PHONE_NUMBER) {
            params.areaCode = userInfo.areaCode;
        }
        // 开始倒计时
        setCountDown((s) => s - 1);
        countDownTimer.current = setInterval(() => {
            setCountDown((s) => {
                if (s === 1) {
                    countDownTimer.current &&
                        clearInterval(countDownTimer.current);
                    return COUNT_DOWN_TOTAL;
                } else {
                    return s - 1;
                }
            });
        }, 1000);
        try {
            setLoading(true);
            if (verifyType === 'phoneNumber') {
                await getPhoneVerifyCode(params);
            } else {
                await getEmailVerifyCode(params);
            }
        } catch (error) {
            setCountDown(COUNT_DOWN_TOTAL);
            countDownTimer.current && clearInterval(countDownTimer.current);
        }
        setLoading(false);
    };
    /**提交验证**/
    const securitySubmit = async () => {
        const { verifyType, verifyCode } = await form.validateFields();
        const verifyTypeValue = verifyTypeNumber[verifyType];
        const number =
            verifyType === VerifyTypeEnum.PHONE_NUMBER
                ? realUserInfo.phoneNumber
                : realUserInfo.email;
        const params: any = {
            verifyType: verifyTypeValue, // 验证类型 0手机验证 1邮箱验证
            bizType: BizTypeEnum.AUTH_VERIFY, // 二次验证
            number,
            verifyCode,
            account: userInfo?.account,
        };
        if (verifyType === VerifyTypeEnum.PHONE_NUMBER) {
            params.areaCode = userInfo?.areaCode?.toString();
        }
        const res = await verifyUserCode(params);
        message.success(i18n.t('message', '验证成功'));
        return res;
    };

    /**表单切换验证方式**/
    const onVerifyTypeChange = (e: VerifyTypeEnum) => {
        setVerifyType(e);
        // 清除验证码校验的错误信息
        form.setFields([
            {
                name: 'verifyCode',
                errors: [],
            },
        ]);
    };

    useImperativeHandle(ref, () => ({
        securitySubmit,
        form,
    }));

    return (
        <SecurityFormUI form={form}>
            <>
                <SecurityFormItemsUI
                    validateOptions={validateOptions}
                    responsive={responsive}
                    verifyType={verifyType}
                    onVerifyTypeChange={onVerifyTypeChange}
                    verifyCodeButton={
                        <VerifyCodeUI
                            loading={loading}
                            countdown={countDown}
                            onVerifyCodeClick={onVerifyCodeClick}
                        />
                    }
                />
            </>
        </SecurityFormUI>
    );
});
