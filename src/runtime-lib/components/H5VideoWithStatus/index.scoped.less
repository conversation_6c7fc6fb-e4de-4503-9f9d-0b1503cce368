@import '~@streamax/poppy-themes/starry/index.less';
@import '~@streamax/poppy-themes/starry/abroad.less';

.center-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 90px;
    transform: translate3d(-50%, -50%, 0);

    &-label {
        margin-top: -14px;
        font-size: 14px;
        text-align: center;
    }
}

.data-wrapper {
    //卡片背景颜色深浅模式不变，里面字体颜色固定
    color: rgba(255, 255, 255, 0.45);
    font-size: 36px;
    text-align: center;

    .data-icon {
        font-size: 80px;
        line-height: 80px;
    }
}
.protect-image-wrap {
    .poppy-empty-image::global {
        height: 80px;
        margin-bottom: 16px;
    }
    .poppy-empty-description::global {
        position: relative;
        top: -10px;
    }
}