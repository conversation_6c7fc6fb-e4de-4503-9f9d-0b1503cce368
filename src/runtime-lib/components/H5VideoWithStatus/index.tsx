/*
 * @LastEditTime: 2025-07-04 10:00:07
 */
import { evidenceDataProps, H5Video } from '@/components/HistoryPlayer/Video';
import { IndustryOSD } from '@/types/pageReuse/evidenceDetail';
import {
    forwardRef,
    useEffect,
    useImperativeHandle,
    useRef,
    useState,
} from 'react';
import { Empty } from '@streamax/poppy';
import { ReactComponent as NoPermission } from '@/assets/icons/icon_nopermission_drak.svg';
import { ReactComponent as ProtectImage } from '@/assets/images/icon-status-conceal.svg';
import { i18n } from '@base-app/runtime-lib';
import './index.scoped.less';
import { noChannelAuth } from '@/utils/commonFun';
import { DisplayAuthEnum } from '@/runtime-lib/utils/evidenceFormatData';
import { useAsyncEffect } from 'ahooks';
import { addTextToImage } from '@/runtime-lib/utils/commonFun';
const privateImg = require('@/assets/images/icon-status-conceal.svg');

const H5VideoWithStatus: React.FC<any> = forwardRef(
    (props: evidenceDataProps & IndustryOSD, ref) => {
        //默认展示播放器
        const { evidenceData, ...H5VideoProps } = props;
        const [privateImgPoster, setPrivateImgPoster] = useState('');
        const H5VideoRef = useRef<any>(null);
        useAsyncEffect(async () => {
            const textUrl = await addTextToImage(
                privateImg,
                i18n.t('message', '隐私保护'),
            );
            setPrivateImgPoster(textUrl);
        }, []);
        
        const videoRender = () => {
            const noPermission = noChannelAuth(
                evidenceData.fileChannelAuth,
                false,
            );
            if (noPermission) {
                //没有通道权限
                return (
                    <div className="center-icon data-wrapper">
                        <span className="data-icon">
                            <Empty
                                description={
                                    <span>
                                        {i18n.t('message', '暂无通道权限')}
                                    </span>
                                }
                                image={<NoPermission />}
                            />
                        </span>
                    </div>
                );
            } else if (evidenceData.displayType === DisplayAuthEnum.PRIVATE_PROTECT && (!evidenceData?.fileList || evidenceData?.fileList.length === 0)) {
                // 隐私保护展示判断
                return (
                    <div className="center-icon data-wrapper protect-image-wrap">
                        <span className="data-icon">
                            <Empty
                                description={
                                    <span>
                                        {i18n.t('message', '隐私保护')}
                                    </span>
                                }
                                image={<ProtectImage />}
                            />
                        </span>
                    </div>
                );
            } else {
                return (
                    <H5Video
                        ref={H5VideoRef}
                        evidenceData={evidenceData}
                        {...H5VideoProps}
                        privateImg={privateImgPoster}
                    />
                );
            }
        };

        useImperativeHandle(ref, () => ({
            ...H5VideoRef.current,
        }));

        return <>{videoRender()}</>;
    },
);
export default H5VideoWithStatus;
