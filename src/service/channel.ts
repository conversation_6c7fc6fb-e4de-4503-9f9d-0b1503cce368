import { SwitchStatusEnum } from '@/types';
import { request } from '@base-app/runtime-lib'; // 使用request工具
import { BaseQueryParams } from './types';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;

// 查询通道列表
export const getChannelList = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/channel/setting/query',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 新增通道类型
 * @param params  { channelTypeName: string })
 * @returns  string
 */
export const createChannelType = async (params?: {
    channelTypeName: string;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<string>
    >({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/channel/type/add',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export type ChannelTypeParams = {
    /** 通道类型id */
    id: string;
    /** 通道类型名称，限制50个字符 */
    channelTypeName?: string;
    /** 0：停用 1：启用 */
    state?: SwitchStatusEnum;
};

/**
 * 通道类型编辑
 * @param params  ChannelTypeParams
 * @returns  string
 */
export const updateChannelType = async (params?: ChannelTypeParams) => {
    const { success, code, message, data } = await request<
        Request.Response<boolean>
    >({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/channel/type/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 通道类型删除
 * @param params  { channelTypeName: string })
 * @returns  string
 */
export const deleteChannelType = async (params?: { ids: string }) => {
    const { success, code, message, data } = await request<
        Request.Response<boolean>
    >({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/channel/type/delete',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export enum ChannelTypeEnum {
    /** 0:默认类型 1：自定义类型 */
    default,
    custom,
}

export interface ChannelTypeItem {
    typeId: string;
    /** 通道类型id */
    id: string;
    /** 通道类型名称 */
    channelTypeName: string;
    /** 0:默认类型 1：自定义类型 */
    type: ChannelTypeEnum;
    /** 0：停用 1：启用 */
    state: SwitchStatusEnum;
    /** 绑定策略的数据 */
    bindConfigureNum: number;
    /** 创建用户Id */
    createUser: string;
    /** 创建用户名 */
    createUserName: string;
    /** 创建时间 */
    createTime: number;
    /** 更新用户Id */
    updateUser?: string;
    /** 更新用户名 */
    updateUserName?: string;
    /** 更新时间戳  */
    updateTime?: number;
}

/**
 * 通道类型查询
 * @param params  { channelTypeName: string })
 * @returns  string
 */
export const fetchChannelTypePageList = async (
    params?: {
        channelTypeName?: string;
        type?: ChannelTypeEnum;
    } & BaseQueryParams,
) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<ChannelTypeItem>
    >({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/channel/type/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 通道类型详情查询
 * @param params  { channelTypeName: string })
 * @returns  string
 */
export const fetchChannelTypeDetail = async (params?: { id?: string }) => {
    const { success, code, message, data } = await request<
        Request.Response<ChannelTypeItem>
    >({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/channel/type/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 通道类型是否重复
 * @param params  { channelTypeName: string })
 * @returns  string
 */
export const checkChannelTypeRepeat = async (params?: {
    channelTypeName: string;
    id?: string;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<boolean>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/channel/type/repeat',
        params: {
            ...params,
            channelTypeName: encodeURIComponent(params?.channelTypeName ?? ''),
        },
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export interface ChannelTypeBindConfigure {
    /** 通道类型id */
    configureId: string;
    /** 策略名称 */
    configureName: string;
    /** 应用Id */
    appId: number;
    /** 应用名称 */
    applicationName: string;
    configureType: string;
}

/**
 * 通道类型关联设置分页查询
 * @param params  { id: string })
 * @returns  string
 */
export const fetchChannelTypeBindConfigurePageList = async (
    params?: { id?: string } & BaseQueryParams,
) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<ChannelTypeBindConfigure>
    >({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/channel/type/bind/configure',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export interface ChannelInfo {
    deviceId: string;
    channelInfoList: ChannelInfoList[];
    authId: string;
}

export interface ChannelInfoList {
    /** 通道编号 */
    channelNumber: number;
    /** 通道名称 */
    channelName: string;
    /** 通道类型列表 */
    channelTypeList: ChannelTypeList;
}

export interface ChannelTypeList {
    /** 通道类型Id */
    channelTypeId: string;
    /** 通道类型名称 */
    channelTypeName: string;
}

/**
 * 查询设备生效的通道配置策略
 * @param params  { id: string })
 * @returns  string
 */
export const fetchDeviceConfigureChannelList = async (params?: {
    deviceIds?: string;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<ChannelInfo[]>
    >({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/configure/device/channel/list',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/*************通道类型设置----设备维度***************/
/**
 * 通道类型查询
 * @param params  { channelTypeName: string })
 * @returns  string
 */
export const fetchDeviceChannelTypePageList = async (
    params?: {
        channelTypeName?: string;
        type?: ChannelTypeEnum;
    } & BaseQueryParams,
) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<ChannelTypeItem>
    >({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/channel/type/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
export const getCurrentChannelMode = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-business-service/api/v1/channel/setting/mode/query',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
export const submitUpdatedChannelMode = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-business-service/api/v1/channel/setting/mode/change',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};


