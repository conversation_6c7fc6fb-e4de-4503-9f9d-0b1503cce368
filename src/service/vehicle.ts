//@ts-nocheck
import { request, i18n, getAppGlobalData } from '@base-app/runtime-lib'; // 使用request工具
import { getcarListPOST } from './videowall';
import { DeviceBaseInfo } from './device';
import { message } from '@streamax/poppy';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;
interface DeviceList {
    authId: string;
    deviceId: string;
    deviceNo: string;
    vehicleId: string;
    channelInfo: string;
}
// 分页查询车辆列表
export interface VehiclePageItem {
    vehicleNumber: string;
    vehicleId: string;
    deviceList: DeviceList[];
    fleetList: {
        fleetId: string;
        fleetName: string;
        path: string;
    }[];
    [k: string]: unknown;
}
export interface AddVehicleDeviceItem extends DeviceBaseInfo {
    primaryType: number;
}
export interface AddVehicleParamsV2 {
    vehicleNumber: string;
    vehicleColor: number;
    description?: string;
    fleetIds: string;
    deviceList: AddVehicleDeviceItem[];
}
export interface VehicleBindDeviceParams {
    vehicleId: string;
    deviceAlias: string;
    deviceId?: string;
    primaryDeviceId?: string;
}
export interface EditVehicleBaseParams {
    vehicleId: string;
    vehicleNumber?: string;
    vehicleColor: number;
    description?: string;
    fleetIds: string;
}
export const noStatusQueryParams = {
    // queryDeviceStatus 是否查询设备状态（onlineState,alarmState,networkState字段）标志：0-不查询，1-查询：默认查询
   queryDeviceStatus: 0,
   // queryVehicleStatus 是否查询车辆状态（onlineState,alarmState,networkState字段）标志：0-不查询，1-查询：默认查询
   queryVehicleStatus: 0
};
export const getVehiclerListByPage = async (params?: {
    page?: number;
    pageSize?: number;
    vehicleIds?: string;
    fleetIds?: string;
    [k: string]: any;
}, notQueryVehicleDeviceStatus = false, headers?: Record<string, any>) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<VehiclePageItem>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/vehicle/page',
        params: {
            ...params,
            fleetName: params?.fleetName?.trim() || '',
            ...(notQueryVehicleDeviceStatus ? noStatusQueryParams : {})
        },
        headers,
        timeout: 120000,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export const getVehicleNumberCount = async (params) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<VehiclePageItem>
    >({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/user/authority/vehicle/domain/page',
        data: {
            ...params,
            pageSize: 1,
            page: 1
        },
        timeout: 10000,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data?.total;
};

// 分页查询车辆列表- POST 请求
export const postVehiclerListByPage = getcarListPOST;

export default {
    getPageList: getVehiclerListByPage,
    getList: async (params?: any) => {
        const { success, code, message, data } = await request({
            baseURL,
            method: 'get',
            url: '/base-server-service/api/v1/vehicle/list',
            params: {
                ...params,
                domainType: 2,
            },
        });
        if (!success || code !== successCode) {
            throw message;
        }

        return data;
    },
};

// 查询车辆列表（没有车队权限，有车辆权限）
export const getSpecialVehicle = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/fleet/special/vehicle/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
interface SpecialVehiclePage {
    userId: string;
    page: number;
    pageSize: number;
    deviceNo?: string;
    vehicleNumber?: string;
}
// 查询车辆列表（没有车队权限，有车辆权限） 支持分页 按车组车牌查询
export const getSpecialVehiclePage = async (params?: SpecialVehiclePage) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<VehiclePageItem>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/fleet/special/vehicle/page',
        params,
        // data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 查询车辆详情
export const getVehiclerDetail = async (params?: any, headers?: Record<string, any>) => {
    return getVehicleDetail(params, undefined, headers);
};

// 查询设备信息
export const getDeviceInfo = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/vision/vehicle/device',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 查询车辆状态
export const getVehicleStateList = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/vision/vehicle/stateList',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 查询车辆最新gps
export const getVehicleNewest = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/vision/gps/newest',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 查询车辆最新gps
export const getVehicleCount = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/vision/vehicle/count',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 查询设备轨迹回放月历
export const getDeviceCalendar = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/vision/playback/device/calendar',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 查询设备回放文件
export const getDeviceFile = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/vision/playback/device/file',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 删除车辆
export const deleteVehicle = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/vehicle/delete',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 获取车辆详情
export const getVehicleDetail = async (params?: any, showFailedMessage = true, headers?: Record<string, any>) => {
    if (!params.vehicleId) {
        const info = i18n.t('message', '车辆不存在');
        message.error(info);
        throw info;
    }
    const {
        success,
        code,
        message: resMessage,
        data,
    } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/vehicle/detail',
        params: {
            countFlag: true,
            ...params,
        },
        timeout: 60000,
        showFailedMessage,
        headers
    });
    if (!success || code !== successCode) {
        throw resMessage;
    }
    return data;
};

export enum ChannelEnableState {
    enable = 1,
    unable = 0,
}


// 获取车辆详情（过滤通道使能启用的通道）
export const getVehicleDetailWithEnabledChannels = async (params?: any, showFailedMessage = true, headers?: Record<string, any>) => {
    const data = await getVehicleDetail(params, showFailedMessage, headers);
    
    // 过滤 deviceList 中每个设备的 deviceChannelList，只保留 enable=1 的通道
    if (data && data.deviceList) {
        data.deviceList.forEach(device => {
            if (device.deviceChannelList) {
                device.deviceChannelList = device.deviceChannelList.filter(channel =>channel.enable === ChannelEnableState.enable);
            }
        });
    }
    return data as RealtimeVideoVehicleDetail;
};
type NetworkState = 1 | 2 | 3 | 4 | 5;
export interface RealtimeVideoVehicleDetail {
    vehicleId: string;
    // 车辆ID:
    vehicleNumber: string;
    // 车牌号:
    vehicleColor: number;
    // 车辆状态 0: 未激活， 1: 启用 , 2: 停用
    vehicleState: 0 | 1 | 2;
    // 车辆启用、停用状态（1启用，2停用）:
    onlineState: 0 | 1;
    alarmState: 0 | 1;
    // 报警状态：0-正常，1-报警
    // 车辆状态：0-离线，1-在线:
    networkState: NetworkState;
    // 网络状态类型: 1-wifi,2-2G,3-3G,4-4G,5-5G
    vehicleStateList?: {
        stateId: string;
        // 车辆状态主键id	
        stateCode: string;
        // 车辆状态编码：1001-在线，1002-离线，1003-报警	
        num: string;	
        // 车辆状态排序值	
        stateName: string;	
        // 车辆状态名称	
        stateColor: string;	
        // 车辆状态颜色 RGB值
    }[];
    deviceList: {
        deviceId: string;
        // 设备ID
        deviceNo: string;
        // 设备编号
        deviceAlias: string;
        // 设备别称
        onlineState: 0 | 1 | 2;
        // 车辆状态：0-离线，1-在线，2-休眠
        alarmState: 0 | 1;
        // 报警状态：0-正常，1-报警
        networkState: NetworkState;
        // 网络状态类型，1-wifi,2-2G,3-3G,4-4G,5-5G
        authId: string;
        // 鉴权码
        primaryType: string;
        // 主/辅设备类型：1-主设备，2-辅助设备
        flowLimit: 1 | 0;
        // 流量限制: 1-限制，0-不限制
        deviceChannelList: {
            channelNo: string;
            // 通道编号
            channelAlias: string;
            // 通道别名
            enable: string;
            // 设备启停标识：            0：停用            1：启用
            peripheralType: string;
            // 外设类型,1-摄像头，2-DSM，3-ADAS,4-BSD
            privacyState: string;
            // 隐私模式
            flowLimit: 1 | 0;
            // 流量限制: 1-限制，0-不限制
        }[];
    }[];
    fleetList?: {
        fleetId: string;
        fleetName: string;
    }[]
}
// 批量获取车辆详情,用于实时视频播放
export const getRealtimeVideoVehiclesDetail = async (params: {vehicleIds: string[]; fields?: string}, showFailedMessage = true) => {
    const {
        success,
        code,
        message: resMessage,
        data,
    } = await request<Request.Response<RealtimeVideoVehicleDetail[]>>({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/monitoring/vehicle/list',
        data: {
            vehicleIds: params.vehicleIds,
            fields: params.fields || 'device,channel,state,fleet,driver',
            complexSort: params.vehicleIds.length > 1 ? 'monitorGroup' : null //排序 多车时，需要根据添加到监控组的时间排序
        },
        showFailedMessage,
    });
    if (!success || code !== successCode) {
        throw resMessage;
    }
    data.forEach(item => {
        item.deviceList.forEach(item => {
            item.deviceChannelList?.forEach(channel=>{
                channel.flowLimit = item.flowLimit;
            });
        });
    });    
    return data;
};
// 单个获取车辆详情,用于实时视频播放
export const getRealtimeVideoVehicleDetail = async (params: {vehicleId: string; fields?: string}, showFailedMessage = true) => {
    const data = await getRealtimeVideoVehiclesDetail({
        vehicleIds: [params.vehicleId],
        fields: params.fields || 'device,channel,state',
    }, showFailedMessage);
    return data[0];
};

// 获取车辆详情--实时司机信息
export const getVehicleDetailRealTime = async (params?: any) => {
    if (!params.vehicleId) {
        const info = i18n.t('message', '车辆不存在');
        message.error(info);
        throw info;
    }
    const {
        success,
        code,
        message: resMessage,
        data,
    } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/vehicle/detail',
        params: {
            countFlag: true,
            ...params,
        },
        timeout: 60000,
    });
    if (!success || code !== successCode) {
        throw resMessage;
    }
    return data;
};

// 添加车辆
export const addVehicle = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/vehicle/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 编辑车辆
export const editVehicle = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/vehicle/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 车辆状态统计查询
export const enum VehicleStateMenu {
    // 0 未激活
    NOT_ACTIVATED = 0,
    // 1启用
    ENABLE = 1,
    // 2停用
    DISABLE = 2,
}
// 排序规则：0-车牌号正序（车牌号ascii）  1-车牌号倒序（车牌号ascii） 默认不进行排序
export enum OrderRuleEnum {
    ASC = 0,
    DESC = 1,
}
export const getSimpleVehicleList = async (params?: {
    // 车辆状态 1启用 2停用
    vehicleState?: VehicleStateMenu;
    orderRule?: OrderRuleEnum
}) => {
    const { success, code, message, data } = await request<Request.Response<string[]>>({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/fleet/vehicle/simple/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
export const enum DeviceStateType {
    LIVE = 0,
    PLAYBACK = 1,
}
export interface VehicleOrderState extends BaseDeviceState {
    type: DeviceStateType;
    num: number;
    appIdList?: string[];
}

// 带优先级的车辆状态查询
export const getOrderVehicleStatePage = async (params?: {
    // 应用ID，优先于头信息_appId
    appId?: string;
    // 排序用途 0-直通 1-回放
    type: DeviceStateType;
}) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<VehicleOrderState>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/vehicle/state/order/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// [API]获取用户权限下的在线车辆列表
export const getOnlineVehicleAndGps = async (params: Record<string, any>) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/vehicle/state/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 车辆状态-分页查询
interface BaseDeviceState {
    stateId: string;
    stateCode: string;
    stateName: string;
    stateColor: string;
    enableDel: string;
}
interface VehicleState extends BaseDeviceState {
    description: string;
    appIdList?: string[];
}
export const getVehicleStateConfigList = async (params?: {
    page: number;
    pageSize: number;
    queryField?: string;
    appId?: string;
    queryAppIdFlag: boolean;
}) => {
    const { success, code, message, data } = await request<Request.ResponsePageList<VehicleState>>({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/vehicle/state/page',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 重点监控车辆列表查询
export const fetchVehicleFocusList = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/vehicle/focus/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 车辆重点监控设置
export const settingVehicleFocus = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/vehicle/focus',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 新增车辆 V2
export const addVehicleV2 = async (params?: AddVehicleParamsV2) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v2/vehicle/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 编辑车辆V2
export const editVehicleV2 = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v2/vehicle/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 车辆解绑设备
export const unbindDevice = async (params: { deviceId: string }) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v2/vehicle/device/unbinding',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 设置设备为主设备
export const primaryDevice = async (params: { deviceId: string; vehicleId: string }) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v2/vehicle/device/primary',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 车辆绑定设备
export const bindDevice = async (params: VehicleBindDeviceParams) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v2/vehicle/device/binding',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 车辆换绑主设备
export const changePrimaryDevice = async (params: VehicleBindDeviceParams) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v2/vehicle/primary/device/change',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 编辑车辆基本信息
export const editVehicleBaseInfo = async (params: EditVehicleBaseParams) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v2/vehicle/simple/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 车辆停启用
export const setVehicleState = async (params: { vehicleId: string; vehicleState: number }) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v1/vehicle/vehicle-state/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// FT 接口 133044定制
export const fetchVehicleType = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/ftmanager/vehicle/vehicleType',
        params
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * 枚举字典详情查询参数
 */
export interface EnumDetailParams {
  /** id */
  id: string;
}

/**
 * 枚举字典详情返回数据
 */
export interface EnumDetailItem {
  /** id */
  id: string;
  /** 字典编码 */
  enumCode: string;
  /** 字典名称 */
  enumName: string;
  /** 字典类型 */
  enumType: string;
  /** 文件id */
  fileId: string;
  appId: string;
  /** 业务类型：1基础，2增值，3自定义 */
  businessType: string;
  /** 扩展数据 */
  extendList?: {
    /** 字段名 */
    key: string;
    /** 字段值 */
    value: string;
  }[];
  /** 创建人ID */
  createUser: string;
  /** 创建人名称 */
  createUserName: string;
  /** 创建时间 */
  createTime: number;
  /** 编辑人ID */
  updateUser?: string;
  /** 编辑人名称 */
  updateUserName?: string;
  /** 编辑时间 */
  updateTime?: number;
}

/**
 * 枚举字典新增参数
 */
export interface EnumCreateParams {
  /** 应用Id */
  appId: number;
  /** 字典类型，如车辆类型：vehicleType */
  enumType: string;
  /** 字典编码 */
  enumCode: string;
  /** 字典名称 */
  enumName: string;
  /** 文件id */
  fileId: string;
  /** 扩展数据 */
  extendList?: {
    /** 字段名称 */
    key: string;
    /** 字段值 */
    value: string;
  };
  translationList: any[];
}

/**
 * 枚举字典编辑参数
 */
export interface EnumUpdateParams {
  /** id */
  id: number;
  /** 字典名称 */
  enumName: string;
  /** 图标文件id */
  fileId: string;
  /** 扩展数据集合 */
  extendList?: {
    /** 字段名称 */
    key: string;
    /** 字段值 */
    value: string;
  };
  translationList: any[]
}

/**
 * 枚举字典删除参数
 */
export interface EnumRemoveParams {
  /** id集合，多个以英文逗号隔开 */
  ids: string;
}

/**
 * 枚举字典替换参数
 */
export interface EnumReplaceParams {
  /** 应用Id */
  appId: number;
  /** 字典类型，如车辆类型：vehicleType */
  enumType: string;
  /** 被替换字典编码 */
  enumCode: string;
 toEnumCode: string;   
}
type FetchEnumPageParams = {
    appId: string;
    enumType: string;
    page?: number;
    pageSize?: number;
    nameCode?: string;
    enumCodeList?: string;
    filterSets?: boolean;
    orderBy?: string;
    sort?: string;
};
// 业务类型：1默认，2自定义
export enum VehicleBusinessTypeEnum {
    /**默认类型**/ 
    base = 1,
    /***自定义类型**/ 
    custom = 2
}
export enum DictionaryTypeEnum {
    vehicleType = 'vehicleType'
}
/**
 * 枚举字典列表查询
 * @param params 查询参数
 * @returns 枚举字典详情
 */
export const fetchEnumPage = async (params: FetchEnumPageParams) => {
  const { success, code, message, data } = await request<Request.Response<EnumDetailItem[]>>({
    baseURL,
    method: 'post',
    url: '/base-server-service/api/v1/enum/page',
    data: params,
  });
  if (!success || code !== successCode) {
    throw message;
  }
  return data;
};
/**
 * 枚举字典详情查询
 * @param params 查询参数
 * @returns 枚举字典详情
 */
export const fetchEnumDetail = async (params: EnumDetailParams) => {
  const { success, code, message, data } = await request<Request.Response<EnumDetailItem>>({
    baseURL,
    method: 'get',
    url: '/base-server-service/api/v1/enum/detail',
    params,
  });
  if (!success || code !== successCode) {
    throw message;
  }
  return data;
};

/**
 * 枚举字典新增
 * @param params 新增参数
 * @returns 操作结果
 */
export const addEnum = async (params: EnumCreateParams) => {
  const { success, code, message, data } = await request<Request.Response<boolean>>({
    baseURL,
    method: 'post',
    url: '/base-server-service/api/v1/enum/create',
    data: params,
  });
  if (!success || code !== successCode) {
    throw message;
  }
  return data;
};

/**
 * 枚举字典编辑
 * @param params 编辑参数
 * @returns 操作结果
 */
export const updateEnum = async (params: EnumUpdateParams) => {
  const { success, code, message, data } = await request<Request.Response<boolean>>({
    baseURL,
    method: 'post',
    url: '/base-server-service/api/v1/enum/update',
    data: params,
  });
  if (!success || code !== successCode) {
    throw message;
  }
  return data;
};

/**
 * 枚举字典删除
 * @param params 删除参数
 * @returns 操作结果
 */
export const removeEnum = async (params: EnumRemoveParams) => {
  const { success, code, message, data } = await request<Request.Response<boolean>>({
    baseURL,
    method: 'post',
    url: '/base-server-service/api/v1/enum/remove',
    data: params,
  });
  if (!success || code !== successCode) {
    throw message;
  }
  return data;
};

/**
 * 枚举字典替换
 * @param params 替换参数
 * @returns 操作结果
 */
export const replaceEnum = async (params: EnumReplaceParams) => {
  const { success, code, message, data } = await request<Request.Response<boolean>>({
    baseURL,
    method: 'post',
    url: '/base-server-service/api/v1/enum/replace',
    data: params,
  });
  if (!success || code !== successCode) {
    throw message;
  }
  return data;
};
export interface RepeatValidateEnumRequest {
  /** id，修改场景传入，新增场景不传 */
  id?: string;
  /** 名称 */
  name: string;
  /** 字典类型，如车辆类型：vehicleType */
  enumType: string;
  /** 应用ID */
  appId: string;
}
/**
 * 验重
 * @param params 验重
 * @returns 验重
 */
export const repeatValidateEnum = async (params: RepeatValidateEnumRequest) => {
  const { success, code, message, data } = await request<Request.Response<boolean>>({
    baseURL,
    method: 'get',
    url: '/base-server-service/api/v1/enum/repeat',
    params,
  });
  if (!success || code !== successCode) {
    throw message;
  }
  return data;
};