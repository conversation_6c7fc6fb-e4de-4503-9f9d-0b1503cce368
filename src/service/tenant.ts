//@ts-nocheck
import { request } from '@base-app/runtime-lib'; // 使用request工具
import { fetchProtocolPage } from './protocol';
import type { ExcelHeader } from './types';

const baseURL = (window as any).APP_CONFIG['gateway.public.url'];
const successCode = 200;

// 申请状态变更
export const applyStatusChange = async (params: {
    currentStatus: string;
    targetStatus: string;
    applyId: string;
}) => {
    const { code, message, data, errorVar, langKey } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/tenant/access/apply/status/update',
        data: params,
        showFailedMessage: false,
    });

    // if (!success || code !== successCode) {
    //     throw message;
    // }
    return { data, errorVar, langKey, message, code };
};
// 申请 删除
export const applyDelete = async (params: {
    applyId: string;
    parent: number;
}) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/tenant/access/apply/delete',
        data: params,
    });

    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 创建访问子租户申请
export const createApplyVisit = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/tenant/access/apply',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 申请访问page
export const applyVisitPage = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/access/apply/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export interface SpaceCapacity {
    count: number;
    capacity: string;
    unit: 'GB' | 'TB';
}

export interface Sets {
    setsId: number;
    setsName: string;
    setsStatus: 1 | 0;
    attributionAppId: string;
    attributionAppName: string;
    setsType: 1 | 2;
    createTime: string;
    setsDescription: string;
    extend: string;
    setsTypeName: string;
    createUser: string; // 实际是操作人的useId
}

// 租户订阅套餐page
export const getTenantSetMealPage = async (params?: {
    tenantId: any;
    setsName?: string;
    startTime?: number;
    endTime?: number;
    page?: number;
    pageSize?: number;
    complexSort?: string;
    attributionAppId?: any;
    setsTypes?: string;
    setsStatus?: number;
    distinct?: number;
}) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<Sets>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/sets/business/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export interface MealBasicInfo {
    id: number;
    setsName: string;
    // planStatus: 1 | 2
    attributionAppId: number;
    attributionAppName: string;
    setsType: 1 | 2;
    createTime: number;
    setsDescription: string;
}

// 套餐基本信息
export const getMealInfo = async (params: { setsId: string }) => {
    const { success, code, message, data } = await request<
        Request.Response<MealBasicInfo>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/sets/business/tenant/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export interface MealServerList {
    id: number;
    parentId: number;
    serviceName: string;
    serviceType: 1 | 2;
    serviceDescribe: string;
    checkStatus: 1 | 2; //  TODO 待后端修改为 0 1
}

export interface MealServerData {
    list: MealServerList;
    total: number;
}

// 套餐服务查询
export const getMealServer = async (params: {
    setsId: string;
    resourceType: number;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<string[]>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/sets/business/service/list',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export enum TenantStateEnum {
    unActivate = 0,
    available = 1,
    ban = 2,
    expired = 3,
    activating = 4,
}

export type TenantDetail = {
    tenantId: number;
    tenantName: string;
    secretKey: string;
    description: string;
    parentId: number;
    parentName: string;
    expireTime: number;
    state: TenantStateEnum;
    storageSwitch: string;
    createTime: number;
    updateTime: number;
    createUserName: string;
    updateUserName: string;
    adminList?: object[];
    invitationCode: string;
    tenantCode: string;
    deviceLimitType: 0 | 1; //租户最大设备数类型，0-无限制，1-限制
    deviceLimit: number; //租户最大设备数，当且仅当deviceLimitType=1时有值
    usedDeviceCount: number; //租户已使用的设备数量
    allocateTenantNum: number; //已分配的租户数量
    tenantType: number; //0-预付租户 1-后付租户
    stateChangeTime: string; // 租户最后一次状态改变时间
    stateChangeSecond: string; // 租户最后一次状态改变时间间隔多少秒（以服务器时间为参照）
};
// 租户明细
export const getTenantDetail = async (params?: any) => {
    const { success, code, message, data } = await request<
        Request.Response<TenantDetail>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 租户是否超过最大设备数
export const tenantMaxDevice = async (params?): boolean => {
    const data = await getTenantDetail(params);
    const { deviceLimitType, deviceLimit, usedDeviceCount } = data;
    if (deviceLimitType && usedDeviceCount >= deviceLimit) return true;
    return false;
};

// 获取租户账单信息
export const getTenantBill = async (params: { billPeriod: string }) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/bill/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 获取租户账单信息
export const getTenantBillPage = async (params: { billPeriod: string }) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/bill/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export interface ITenantDetail {
    alarmCode: string;
    alarmType: number;
    alarmTypeId: number;
    appId: number;
    appName: string;
    authId: number;
    source: number;
    state: number;
    tenantCode: string;
    tenantId: number;
    tenantName: string;
    tenantState: number;
    typeName: string;
}

// 获取租户分页数据
export const getTenantPage = async (params?: any) => {
    const { success, code, message, data } = await request<
        Request.ResponsePageList<ITenantDetail>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 创建租户
export const createTenant = async (params?: any, errorCodeMessageList?) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/create`,
        data: params,
        errorCodeMessageList,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 更新租户
export const updateTenant = async (params?: any, errorCodeMessageList?) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/update`,
        data: params,
        errorCodeMessageList,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 删除租户，多个租户id
export const deleteTenant = async (params?: { tenantIds: string }) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/delete`,
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 锁定租户
export const lockTenant = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/lock`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 解锁租户
export const unlockTenant = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/unlock`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 租户授权应用
export const grantapp = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/grantapp`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 租户管理变更
export const tenantManagerChange = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/manager/change`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 租户管理配置
export const getTenantManagerConfig = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: `/base-server-service/api/v1/tenant/config`,
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 租户管理配置
export const postTenantManagerConfig = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/config/update`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 获取租户SSO配置信息
export const tenantSSODetail = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: `/base-server-service/api/v1/tenant/sso/detail`,
        params: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 获取租户SSO配置信息
export const tenantSSOUpdate = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/sso/update`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 租户授权协议
export const tenantAuthorityProtocol = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/tenant/authority/protocol`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 分页获取协议列表
export const getProtocolAuthoritypage = async (params: any) => {
    return fetchProtocolPage(params);
};

// 获取租户登录配置
export const getTenatLoginSetedConfig = async () => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-server-service/api/v1/tenant/login/query',
        method: 'GET',
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data || {};
};

// 设置租户登录配置
export const setTenatLoginConfig = async (values: any) => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-server-service/api/v1/tenant/login/edit',
        method: 'POST',
        data: values,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 获取客户端ip
export const getCustomerIp = async () => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-config-service/api/v1/ip',
        method: 'GET',
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 获取租户水印(开启/关闭)
export const getTenantWatermark = async () => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-business-service/api/v1/tenant/watermark/query',
        method: 'GET',
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 设置租户水印(开启/关闭)
export const setTenatWatermark = async (watermarkFlag: number) => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-server-service/api/v1/tenant/watermark/edit',
        method: 'POST',
        data: { watermarkFlag },
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 退出登录 修改完配置后使用
export const doLogout = async (token: string) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-server-service/api/v1/user/logout',
        params: { token },
        baseURL,
        showFailedMessage: false,
    });

    if (!success || code !== 200) throw message;

    return data;
};

// 获取租户密码设置
export const fetchTenantPwdConfig = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/config/pwd/query',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 修改租户密码设置
export const updateTenantPwdConfig = async (params?: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/tenant/config/pwd/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 刷新企业邀请码
export const doRefreshInvitationCode = async (tenantId: number) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-server-service/api/v1/tenant/invitationcode/refresh',
        params: { tenantId },
        baseURL,
        showFailedMessage: false,
    });

    if (!success || code !== 200) throw message;

    return data;
};

// 获取租户详情Secret
export const getSensitiveSecret = async (tenantId: number) => {
    const { success, data, message, code } = await request({
        method: 'get',
        url: '/base-server-service/api/v1/tenant/secret/query',
        params: { tenantId },
        baseURL,
        showFailedMessage: false,
    });

    if (!success || code !== 200) throw message;

    return data;
};


export type TenantPrivacyConfigData = {
    mosaicSwitch: number; // 隐私设置-视频、图片打码开关，0-关闭，1-开启（必需）
    customSwitch: number; // 隐私设置-允许用户自定义是否打码开关，0-关闭，1-开启（必需）
    specifyRoleSwitch: number; // 隐私设置-指定角色开关，0-关闭，1-开启（必需）
    roles?: string; // 指定角色列表（可选）
    specifyUserSwitch: number; // 隐私设置-指定用户开关，0-关闭，1-开启（必需）
    users?: string; // 指定用户列表（可选）
}
export const fetchTenantPrivacyConfig = async (params?: { needUserCustomMosaic?: 0 | 1 }) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/privacy/query',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data as TenantPrivacyConfigData & {
        tenantHavePrivacy: number; // 是否有隐私设置页面资源，0-无，1-有（必需）
        userHavePrivacy?: number; // 用户是否有隐私设置页面资源，0-无，1-有（可选）
        userCustomMosaic?: number; // 用户是否可以自定义打码，0-不可以，1-可以（可选）
    };
};
export const updateTenantPrivacyConfig = async (params?: TenantPrivacyConfigData) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/tenant/privacy/update',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data as boolean;
};


// 上级租户审核状态枚举
export enum AuditStatusEnum {
    'Pending' = 1, // 待审核
    'Fulfilled', // 可访问
    'Rejected', // 已拒绝
    'Canceled', // 已关闭
}

// 访问子租户申请分页列表 页面类型
export enum PageTypeEnum {
    'Parent' = 1, // 申请列表（父租户页面使用）
    'Child', // 申请访问列表（子租户页面使用）
}

// 访问列表每项类型
export interface VisitRecord {
    applyId: string;
    userId: string;
    tempUserId: string;
    tenantId: number;
    tenantName: string;
    account: string;
    phone: string;
    email: string;
    areaCode: string;
    createTime: number;
    status: AuditStatusEnum;
    memo: string; // 备注
}

// 访问详情
export type VisitDetailBaseInfo = VisitRecord;
type SortQuery = 'orderBy createTime asc' | 'orderBy createTime desc';

// 获取上级租户访问列表
export const getTenantVisitPage = async (params: {
    page: number;
    pageSize: number;
    type: PageTypeEnum;
    complexSort?: SortQuery; // 排序
    tenantId?: number; // ‘上级租户访问’页面不需要
    statusS?: string;
    account?: string; // 用户模糊搜索
    ownedTenantId?: string; // ‘上级租户访问’页面租户过滤字段
}) => {
    const { success, code, message, data } = await request<
        Request.Response<{ total: number; list: VisitRecord[] }>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/access/apply/page',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data || {};
};
// 获取上级租户访问详情
export const getSupTenantVisitDetail = async (params: { applyId: string }) => {
    const { success, code, message, data } = await request<
        Request.Response<VisitDetailBaseInfo>
    >({
        baseURL,
        method: 'get',
        url: '/base-server-service/api/v1/tenant/access/apply/detail',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data || {};
};

// 上级租户申请访问状态更新
export const updateTenantVisitStatus = async (params: {
    applyId: string;
    currentStatus: AuditStatusEnum; // 当前状态
    targetStatus: AuditStatusEnum; // 想更新到的状态
}) => {
    const { success, code, data, errorVar, langKey, message } = await request<
        Request.Response<boolean>
    >({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/tenant/access/apply/status/update',
        showFailedMessage: false,
        data: params,
    });
    if (!success || code !== successCode) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw { code, langKey, errorVar, message };
    }
    return data;
};

// 通知访问申请可访问
export const noticeTenantVisitFulfilled = async (params: {
    applyId: string;
}) => {
    const { success, code, message, data } = await request<
        Request.Response<boolean>
    >({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/tenant/access/authority/message',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 删除申请
export const deleteSupTenantVisit = async (params: {
    applyId: string;
    parent: PageTypeEnum;
}) => {
    const { success, code, langKey, data, errorVar, message } = await request<
        Request.Response<boolean>
    >({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/tenant/access/apply/delete',
        data: params,
        showFailedMessage: false,
    });
    if (!success || code !== successCode) {
        // eslint-disable-next-line @typescript-eslint/no-throw-literal
        throw { code, langKey, errorVar, message };
    }
    return data;
};

// 获取用户配置信息
export const getUserInfoConfig = async (params: any) => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-server-service/api/v1/tenant/config',
        method: 'GET',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 获取带有authRoleInfo的用户配置信息
export const getAuthUserInfoConfig = async (params: any) => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-business-service/api/v1/tenant/config',
        method: 'GET',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 检查租户是否存在资源
export const checkResourceExist = async (params: {
    resourceCode: string;
    tenantId: number;
}) => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-server-service/api/v2/resource/exist/check/tenant',
        method: 'POST',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data; // 返回 true 或 false
};

// 设置用户配置信息
export const setUserInfoConfig = async (params: any) => {
    const { success, message, code, data } = await request({
        baseURL,
        url: '/base-server-service/api/v1/tenant/config/update',
        method: 'POST',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export interface AddTenantParams {
    tenantName: string;
    tenantCode: string;
    description?: string;
    expireTime: number;
    tenantAdmin: {
        account: string;
        name: string;
        password: string;
        email: string;
        areaCode: string;
        phone: string;
    };
    setsId?: string;
    storageSwitch?: string;
    deviceLimitType: number;
    deviceLimit?: number;
    templateId?: string;
}

export interface EditTenantParams {
    tenantId: string;
    tenantName?: string;
    description?: string;
    expireTime?: number;
    deviceLimit?: number;
    setsId?: string;
    storageSwitch?: number;
    templateId: string;
}

// 新增租户v2
export const addTenantV2 = async (params: AddTenantParams) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v2/tenant/create',
        data: params,
    });

    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 删除未激活租户
export const deleteUnActiveTenant = async (params: {
    tenantId: string | number;
}) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v2/tenant/delete',
        data: params,
    });

    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 编辑租户
export const editTenant = async (params: EditTenantParams) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'POST',
        url: '/base-server-service/api/v2/tenant/update',
        data: params,
    });

    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

// 激活租户
export const activeTenant = async (params: any) => {
    const { success, code, message, data } = await request({
        baseURL,
        timeout: 300000,
        method: 'GET',
        url: '/base-server-service/api/v2/tenant/active',
        params,
    });

    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 重置密码
export const resetTenantPassword = async (params: {
    resetUserId: string;
    languageType: string;
    emailAddress: string;
}) => {
    const { success, code, message, data } = await request({
        baseURL,
        method: 'post',
        url: `/base-server-service/api/v1/email/reset/tenant/admin/password`,
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
// 创建租户配置包导入任务
export const createScheduleTenantPackage = async (params?: any) => {
    const { success, data, message, code } = await request({
        baseURL,
        method: 'post',
        url: '/base-config-service/api/v1/task/schedule/tenant-package/create',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export type DeviceBillParams = {
    /** 文件名称 */
    fileName: string;
    /** excel格式，XLSX、XLS */
    excelType: 'XLSX' | 'XLS';
    /** 注释 */
    sheetNames: string[];
    /** 账单ID */
    billId: string;
    /** 注释 */
    excelHeader: ExcelHeader[];
};

/**
 * 账单激活设备excel导出
 * @param params
 * @returns
 */
export const deviceBillExport = async (params?: DeviceBillParams) => {
    const { success, data, message, code } = await request<
        Request.Response<string>
    >({
        baseURL,
        method: 'post',
        url: '/base-server-service/api/v1/bill/device/export',
        data: params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

/**
 * @description: 查询租户单车存储空间容量变更列表
 * @param {object} params
 * @return {*}
 */
export const getTenantSingleStorePage = async (params: {
    tenantId: string;
}) => {
    const { success, message, code, data } = await request<
        Request.ResponsePageList<Sets>
    >({
        baseURL,
        url: '/base-server-service/api/v1/storage/tenant/vehicle/record/page',
        method: 'GET',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};

export type TenantBillParams = {
    /** 设备编号 */
    deviceNo?: string;
    /** 归属车组 */
    fleetName?: string;
    /** 设备SIM卡号
     *  中台后端已经支持，前端开放复用供行业层覆写，需要支持该参数
     * */
    simNo?: string;
    /** 归属租户 */
    tenantCode?: string;
    /** 账单ID */
    billId: string;
    /** 激活开始时间 */
    activatedTimeStart?: number;
    /** 激活结束时间 */
    activatedTimeEnd?: number;
};

export interface BillDevice {
    id: number;
    billId: string; // 账单id
    vehicleId: string; // 车辆id
    vehicleNumber: string; // 车牌号
    deviceNo: string; // 设备编号
    deviceId: string; // 设备id
    activateDateString: number; // 激活日期
}

/**
 * @description: 查询租户账单设备明细列表
 * @param {object} params
 * @return {*}
 */
export const getTenantBillDevicePage = async (params: TenantBillParams) => {
    const { success, message, code, data } = await request<
        Request.ResponsePageList<BillDevice>
    >({
        baseURL,
        url: '/base-server-service/api/v1/bill/device/page',
        method: 'GET',
        params,
    });
    if (!success || code !== successCode) {
        throw message;
    }
    return data;
};
