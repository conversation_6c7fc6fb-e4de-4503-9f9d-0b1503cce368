/*
 * @LastEditTime: 2025-06-30 13:39:59
 */
import type { ChannelSettingItem } from "@/service/application";
import { ChannelTypeEnum } from "@/service/channel";
import { i18n } from "@base-app/runtime-lib";
import { cloneDeep, groupBy, isNil } from "lodash";
import { CONFIGURE_TYPE } from "./constants";
import { CONFIGURE_TYPE_DEVICE } from "@/runtime-pages/strategy-center/channel-setting/constants";

/**
 * 返回通道类型国际化
 */
export const getChannelTypeInternationalName = ({
    typeId,
    type,
    channelTypeName,
}: {
    typeId: string;
    type: ChannelTypeEnum;
    channelTypeName: string;
    }) => {
    const result =
        type == ChannelTypeEnum.default
            ? i18n.t(`@i18n:@channelType__${typeId}`, channelTypeName)
            : channelTypeName;
    return result || '-';
};

/**通道前缀 */
const CHANNEL_PREFIX = 'channel_';

export const formatChannelParams = {
    /**
     * 将 ChannelSettingItem[] 转换为参数对象
     *  ChannelSettingItem[] => {paramName: string, paramValue: string}[]
     * 2. 然后将每个 item 转换为 {paramName: string, paramValue: string} 对象
     *    其中 paramName 是 ${CHANNEL_PREFIX}${channelNumber}, paramValue 是 item 的 JSON 字符串
     * @param {ChannelSettingItem[]} params - 需要转换的 ChannelSettingItem[]
     * @param {{ oldData: ChannelSettingItem[]; isShowChanelTypeColumns: boolean }} options - 选项对象
     * @returns { {paramName: string, paramValue: string}[] } - 转换后的参数对象数组
     */
    toFormatBackParams: (
        params: ChannelSettingItem[],
        options?: { oldData: ChannelSettingItem[]},
    ) => {
        const { oldData } = options || {};
        const result = params || [];
        const oldDataMap = groupBy(oldData, 'channelNumber');
        return (result || []).map((item, index) => {
            const oldItem = oldDataMap[item.channelNumber]?.[0] || {};
            return {
                paramName: `${CHANNEL_PREFIX}${item.channelNumber}`,
                paramValue: JSON.stringify({
                    ...oldItem,
                    ...item,
                }),
            };
        });
    },
    /**
     * 将 {paramName: string, paramValue: string}[] 转换回 ChannelSettingItem[]
     * 1. 将每个 item 的 paramValue json解析回对象
     * 2. 将每个item的paramName去除 ${CHANNEL_PREFIX} 前缀
     * @param {{paramName: string, paramValue: string}[]} params - 需要转换的 {paramName: string, paramValue: string}[]
     * @returns {ChannelSettingItem[]} - 转换后的 ChannelSettingItem[]
     */
    toFormatFrontParams: (params: { paramName: string; paramValue: string }[], configureType: number) => {
        try {
            if(configureType == CONFIGURE_TYPE_DEVICE) {
                let maxData = [];
                let len = 0;
                (params || []).forEach((item, index) => {
                    const data = JSON.parse(item?.paramValue || '[]');
                    if(data?.length > len) {
                        maxData = data;
                        len = data.length;
                    }
                });
                return maxData;
            } else {
                return (params || []).map((item) => {
                    const data = JSON.parse(item.paramValue || '{}');
                    return {
                        ...data,
                    };
                });
            }
            
        } catch (err) {
            console.error('params format error', err);
        }
    },
    /***
     * 设备模式转化时增加默认值
     * **/ 
    toFormatDeviceBackParams: (
        params: ChannelSettingItem[],
    ) => {
        const result = cloneDeep(params) || [];
        (result || []).forEach((item, index) => {
            const paramValue = JSON.parse(item?.paramValue || '{}');
            paramValue.forEach(channelItem => {
                if(isNil(channelItem.enable)) {
                    channelItem.enable = 1;
                }
            });
            item.paramValue = JSON.stringify(paramValue);
        });
        return result;
    }
};

