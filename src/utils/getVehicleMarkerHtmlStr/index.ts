import './index.less';
import { getVehicleIconUrl } from '../getVehicleIcon';
import { escapeHtml } from '../util';

export interface vehicleMarkerProps {
    vehicleInfo: {
        vId?: string;
        vNo?: string;
        angle?: number;
        isSelected?: boolean;
        vType?: string;
        color?: string;
    };
    options: {
        showAngle?: boolean;
        showLicense?: boolean;
    };
}

/**
 * 根据车辆类型返回对应的车辆图标
 */
export default async (props: vehicleMarkerProps = { vehicleInfo: {}, options: {} }) => {
    const {
        vehicleInfo: { vNo = '', angle = 0, isSelected = false } = {
            vNo: '',
            vType: '',
            angle: 0,
            isSelected: false,
            color: '',
        },
        options: { showLicense = false, showAngle = false } = {
            showAngle: false,
            showLicense: false,
        },
    } = props;
    let imgUrl = '';
    let color = props.vehicleInfo?.color;

    let vType = props.vehicleInfo?.vType ? String(props.vehicleInfo?.vType) : '';
    if (!color) {
        color = '#09ab69';
    }        
    // 获取车辆图标URL
    const iconUrl = await getVehicleIconUrl(vType);
    const escapeVNo = escapeHtml(vNo);
    imgUrl = `<img
        class="car ${isSelected && 'selected-car'} ${showAngle ? 'has-angle' : ''}"
        src="${iconUrl}"
    />`;

    return `
            <div class="vehicle-wrapper">
                <div 
                    class="vehicle-icon"
                >
                ${
                    showAngle
                        ? ` <span role="img" class="bg-border ${
                              isSelected && 'selected-bg-border'
                          }" style="transform: ${
                              isSelected ? 'translate(-50%, -50%)' : ''
                          } rotate(${angle + 180}deg)">
                                                <svg width="${
                                                    isSelected ? '36px' : '32px'
                                                }" height="${
                              isSelected ? '90px' : '72px'
                          }" viewBox="0 0 34 58" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                                <g id="监控" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                    <g id="a" transform="translate(-64.000000, -85.000000)">
                                        <g id="icon_bg" transform="translate(64.000000, 85.000000)">
                                            <rect id="矩形备份-5" x="0" y="0" width="34" height="58"></rect>
                                            <path class="${
                                                isSelected ? 'border-path' : 'no-border-path'
                                            }" d="M17,13 C25.836556,13 33,20.163444 33,29 C33,32.3260464 31.9851253,35.4150562 30.2481513,37.9742539 L30.2505971,37.9735015 L17,57 L3.76803896,38.0002611 C2.02202026,35.4367644 1,32.337735 1,29 C1,20.163444 8.163444,13 17,13 Z" id="形状结合备份-8"></path>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                        </span>`
                        : ''
                }
                        ${
                            // 如果不展示方向则只展示一个圆
                            showAngle
                                ? `<span role="img" class="bg ${isSelected && 'selected-bg'} ${
                                      showAngle && 'show'
                                  }
                                    " style="transform: ${
                                        isSelected ? 'translate(-50%, -50%)' : ''
                                    } rotate(${angle + 180}deg)">
                                                        <svg width="${
                                                            isSelected ? '28px' : '28px'
                                                        }" height="${
                                      isSelected ? '76px' : '68px'
                                  }" viewBox="0 0 34 58" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                                    <g id="监控" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <g id="a" transform="translate(-64.000000, -85.000000)">
                                            <g id="icon_bg" transform="translate(64.000000, 85.000000)">
                                                <rect id="矩形备份-5" x="0" y="0" width="34" height="58"></rect>
                                                <path d="M17,13 C25.836556,13 33,20.163444 33,29 C33,32.3260464 31.9851253,35.4150562 30.2481513,37.9742539 L30.2505971,37.9735015 L17,57 L3.76803896,38.0002611 C2.02202026,35.4367644 1,32.337735 1,29 C1,20.163444 8.163444,13 17,13 Z" id="形状结合备份-8" fill="${
                                                    color || '#52C41A'
                                                }"></path>
                                            </g>
                                        </g>
                                    </g>
                                </svg>
                            </span>`
                                : `<span class="bg circle-bg out"></span><span class="bg circle-bg" style="background-color: ${color}"></span>`
                        } 
                        ${imgUrl}
                        ${
                            showAngle
                                ? ` <img 
                                class="angle-border ${isSelected && 'selected-angle-border'}" 
                                src="${require('../../assets/icons/icon_wireframe.svg')}"
                                style="transform: translate(-50%, -50%) rotate(${angle + 180}deg)"
                            />`
                                : ''
                        }
                    
                    </div>
                    ${
                        showLicense
                            ? `<div class="vehicle-name ${
                                  isSelected && 'selected-vehicle-name'
                              }" title="${escapeVNo}">${escapeVNo}</div>`
                            : ''
                    }
        </div>`;
};