/*
 * @LastEditTime: 2025-05-14 10:56:37
 */
// @ts-ignore
import { getAppGlobalData, i18n, utils, evidenceUtils, LocationResolution, mosaicManager, Provider } from '@base-app/runtime-lib';
import { geoPositionAnalysisV2 } from '@/service/geo';
import { createParkImageTask, getTravelImage } from '@/service/track-playback';
import { getEvidenceByAlarm } from '@/service/evidence';
import { evidenceFormatData } from '@/utils/evidenceFormatData';
import { calcDuration, FileChannelAuthEnum, FileType, getAllVideos, getVideosByFileType, noChannelAuth } from '@/utils/commonFun';
import { createVideoDom, destoryVideoDom } from '@/components/HistoryPlayer/commonFun';
import { getAlarmDetail } from '@/service/alarm';
import type { VideoParam, PopupDataParams, MediaSource, RenderMapPanelParams } from './types';
import './index.less';
import { getCustomFun } from '../pageReuse';
import { ProtocolTypesEnum } from '@/types';
import { aspectRatioImage } from '@base-app/runtime-lib';
import { head, stubFalse } from 'lodash';
import { getDriverName } from '@/components/AuthDriverShow'; import ReactDOM from "react-dom"; import {H5Video} from "@/components/HistoryPlayer/Video";
import DisableRightMouseClickImage from '@/components/DisableRightMouseClickImage';
import { escapeHtml } from '../util';
const { zeroTimeStampToFormatTime } = utils.formator;
const ALARM_ICON = require('@/assets/icons/icon_direction.svg');
// const EYES_ICON = require('@/assets/icons/icon_eyes.svg');
const ADDR_ICON = require('@/assets/icons/icon_address.svg');
const LEFT_ICON = require('@/assets/icons/icon_arrow02_left.svg');
const LEFT_ICON_DISABLED = require('@/assets/icons/icon_arrow02_left_disable.svg');
const RIGHT_ICON = require('@/assets/icons/icon_arrow02_right.svg');
const RIGHT_ICON_DISABLED = require('@/assets/icons/icon_arrow02_right_disable.svg');
const { timeFormat } = getAppGlobalData('APP_USER_CONFIG');
const TIME_FORMAT = `${timeFormat?.replace(/-?YYYY-?/, '')} HH:mm`;
const {
    formator: { formatByte },
} = utils;

export const getTime = (time: number, timeFormat: string = TIME_FORMAT): string => {
    return zeroTimeStampToFormatTime(time, undefined, timeFormat);
};

const getAlarmTime = (time: number): string => {
    const format = getAppGlobalData('APP_USER_CONFIG')?.timeFormat
        ? `${getAppGlobalData('APP_USER_CONFIG')?.timeFormat} HH:mm:ss`
        : 'YYYY-MM-DD HH:mm:ss';
    return zeroTimeStampToFormatTime(time, undefined, format);
};

const getTimeData = (time: number) => {
    if (typeof time !== 'number') {
        // eslint-disable-next-line no-param-reassign
        time = 0;
    }
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor((time % 3600) % 60);
    const format = (num: number) => {
        if (num <= 9) {
            return `0${num}`;
        }
        return num;
    };
    return {
        hours: format(hours),
        minutes: format(minutes),
        seconds: format(seconds),
    };
};

export const formateSeconds = (
    time: number,
    type: 'standard' | 'minutes' | 'seconds' = 'standard',
) => {
    const { hours, minutes, seconds } = getTimeData(time);
    const lang: string = getAppGlobalData('APP_LANG');
    const splitChar = /^zh_/.test(lang) ? '' : ' ';
    const getUnit = (zhUnit: string, otherUnit: string) => {
        return /^zh_/.test(lang) ? zhUnit : otherUnit;
    };
    const hourStr = Number(hours)
        ? `${Number(hours)}${getUnit(i18n.t('name', '时'), 'h')}${splitChar}`
        : '';
    const minuteStr =
        Number(hours) || Number(minutes)
            ? `${Number(minutes)}${getUnit(i18n.t('name', '分'), 'm')}${splitChar}`
            : '';
    const secondStr = `${Number(seconds)}${getUnit(i18n.t('name', '秒'), 's')}`;
    let timeStr = '';
    switch (type) {
        case 'standard':
            timeStr = `${hours}:${minutes}:${seconds}`;
            break;
        case 'minutes':
            timeStr = `${hourStr}${minuteStr}`;
            timeStr = timeStr || `<1${getUnit(i18n.t('name', '分'), 'm')}`;
            break;
        case 'seconds':
            timeStr = `${hourStr}${minuteStr}${secondStr}`;
        default:
            break;
    }
    return timeStr;
};
const getMediaSource = (
    file?: MediaSource,
    existH264?: boolean,
    videoParam?: VideoParam,
    onloadedmetadataFun?: string,
    hasWatermark?: boolean,
    fileChannelAuth?: FileChannelAuthEnum
) => {
    // 有权限才进行后续判断
    if (!noChannelAuth(fileChannelAuth, false) && (!file || !Object.keys(file).length || !file.url)) {
        return '';
    }
    const domId = `alarm-video-${file?.alarmId}`;
    // 有权限才进行后续判断
    if (!noChannelAuth(fileChannelAuth, false) && (!file.url || !file.url.length)) {
        return '';
    }
    
    // 通道无权限时渲染播放器，且不展示时长和视频大小
    return `
        <div class="img-box" id="img-box-${domId}">
            ${
                file?.fileSize ||
                existH264 ||
                noChannelAuth(fileChannelAuth, false)
                    ? `
                    ${
                        (file?.fileType === FileType.H264 && existH264) ||
                        noChannelAuth(fileChannelAuth, false)
                            ? `<div style="height: 100%" id="${domId}">
                        ${
                            !noChannelAuth(fileChannelAuth, false)
                                ? `<div class="img-info ${domId}">${formatByte(
                                    file?.fileSize as number,
                                )}
                                        <span style="float: right">
                                            ${file?.duration}
                                        </span>
                                    </div>`
                                : ''
                        }
                            </div>`
                            : ''
                    }
                    ${
                        file?.fileType === FileType.VIDEO &&
                        !noChannelAuth(fileChannelAuth, false)
                            ? `<span class="disable-right-mouse-click">
                                <video src="${
                                    file.url
                                }" controls onloadedmetadata="${onloadedmetadataFun}()" id="${
                                  videoParam?.videoDomId
                              }" ${
                                  hasWatermark
                                      ? 'oncontextmenu="contextImage(event)"'
                                      : null
                              }></video>
                            </span>
                            <div class="img-info">
                                ${formatByte(file.fileSize as number)}
                                <span style="float: right;" id="${
                                    videoParam?.videoLengthDomId
                                }">00:00</span>
                            </div>
                        `
                            : ''
                    }
                `
                    : getImg(file.url, hasWatermark)
            }
        </div>
    `;
};
let photoNum = 1;
const setSwitchState = (type: 'left' | 'right', state: 'not-allowed' | 'pointer') => {
    const switchBtn = document.getElementsByClassName(`switch-${type}`)[0];
    switchBtn.setAttribute('style', `cursor: ${state};`);
    if (state === 'not-allowed') {
        switchBtn?.setAttribute('src', type === 'left' ? LEFT_ICON_DISABLED : RIGHT_ICON_DISABLED);
    } else {
        switchBtn?.setAttribute('src', type === 'left' ? LEFT_ICON : RIGHT_ICON);
    }
};
const setImageAndPhotoNum = (urlArr: string[]) => {
    const img = document.querySelector('.alarm-and-park-point-popup-image');
    img?.setAttribute('src', urlArr[photoNum - 1]);
    const photoNumDom: any = document.getElementById('alarm-and-park-point-popup-photo-num');
    photoNumDom.innerHTML = photoNum;
};
const switchImage = (type: 'left' | 'right', imageData: string) => {
    const urlArr = imageData.split(',') || [];
    const reverseType = type === 'left' ? 'right' : 'left';
    if (type === 'left' ? photoNum !== 1 : photoNum !== urlArr.length) {
        photoNum = type === 'left' ? photoNum - 1 : photoNum + 1;
        if (type === 'left' ? photoNum === 1 : photoNum === urlArr.length) {
            setSwitchState(type, 'not-allowed');
        }
        setSwitchState(reverseType, 'pointer');
        setImageAndPhotoNum(urlArr);
    }
};
const contextImage = (e) => {
    if (
        (e.target.tagName.toLowerCase() === 'img' || e.target.tagName.toLowerCase() === 'video') &&
        e.target?.closest('.disable-right-mouse-click') instanceof HTMLSpanElement
    ) {
        e.preventDefault(); // 阻止默认的右键菜单显示
    }
};
(window as any).contextImage = contextImage;
let firstImageUrl = '';

const getImg = (imageData?: string[] | string | null, hasWatermark?: boolean) => {
    let urlArr: string[] = [];
    firstImageUrl = '';
    let total = 0;
    if (imageData) {
        if (typeof imageData !== 'string') {
            urlArr = imageData;
            firstImageUrl = urlArr?.[0];
            total = urlArr.length;
        } else {
            urlArr.push(imageData);
            firstImageUrl = imageData;
            total = 1;
        }
    }

    if (urlArr && urlArr.length) {
        let imageDataString = '';
        let leftHandle = null;
        let rightHandle = null;
        if (total !== 1) {
            imageDataString = urlArr.join(',');
            leftHandle = () => {
                switchImage('left', imageDataString);
            };
            rightHandle = () => {
                switchImage('right', imageDataString);
            };
            (window as any).leftHandle = leftHandle;
            (window as any).rightHandle = rightHandle;
        }

        return `
            <span class="disable-right-mouse-click">
                <div
                    class="info-image"
                    id="alarm-and-park-point-info-image"
                >
                </div>
            </span>
            ${
                total !== 1
                    ? `
                <div class="image-num">
                    <img
                        src='${LEFT_ICON_DISABLED}'
                        style="cursor: not-allowed" class="switch-left"
                        onclick="leftHandle()"
                    />
                    <span class="num">
                        <span id="alarm-and-park-point-popup-photo-num">${photoNum}</span>/${total}
                    </span>
                    <img
                        src='${RIGHT_ICON}'
                        style="cursor: pointer"
                        class="switch-right switch"
                        onclick="rightHandle()"
                    />
                </div>`
                    : ''
            }

        `;
    }
    return '';
};

const renderPopup = (
    html: string,
    hasWatermark?: boolean,
    hasHeadVideo?: boolean,
    address?: string,
    file?: MediaSource,
    existH264?: boolean,
    fileData?: any,
    videoParam?: VideoParam, // 设置video的id用于触发挂在window下的getVideoDuration
    onloadedmetadataFun?: string, // video的onloadedmetadata方法
    fileChannelAuth?: FileChannelAuthEnum,
) => {
    const { startTime, vehicleInfo, driverInfo } = fileData || {};
    const authDriverName = getDriverName(driverInfo);
    const getFileView = () => {
        if (!hasHeadVideo) {
            return file?.type === 'alarm'
                ? getMediaSource(file, existH264, videoParam, onloadedmetadataFun, hasWatermark, fileChannelAuth)
                : getImg(file?.url, hasWatermark);
        }
        return '';
    };
    const escapeDeviceNo = escapeHtml(vehicleInfo?.deviceNo) || '-';
    const escapeAuthDriverName = escapeHtml(authDriverName);
    const newHtml = `
        <div class="map-info-panel" >
            ${getFileView()}
            <div
                class="alarm-info"
                style="padding: ${
                    !file || !file.url || !file.url.length || hasHeadVideo
                        ? '16px 24px'
                        : '8px 24px 16px 24px'
                }"
            >
                ${html}
                ${
                    file?.type === 'alarm'
                        ? `<div class="info-driver" title=" ${
                              escapeDeviceNo
                          } | ${escapeAuthDriverName}">
                            ${
                                escapeDeviceNo
                            }&nbsp;&nbsp; | &nbsp;&nbsp;${escapeAuthDriverName}
                        </div>`
                        : ''
                }
                <div id="info-address-container"></div>
            </div>
        </div>
    `;
    return newHtml;
};
// 防抖函数
let temFn: string;
let timer: any = null;
const popVisiable = (marker: any, popup: any, fn: 'closePopup' | 'openPopup', map?: any) => {
    temFn = fn;
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
        // 如果已经打开 则不做操作，否会会触发popupopen事件
        if (map && map._popup?.isOpen() && fn == 'openPopup') return;
        marker.bindPopup(popup)[temFn]();
    }, 300);
};
const getPriorityArr = (
    arr: any[],
    allVideoList: any,
    allImgList: any,
    priorityVideoArr: any,
    priorityImgArr: any,
) => {
    arr.forEach((item) => {
        // @ts-ignore
        allVideoList.forEach((v) => {
            if (v.channelNo == item) {
                priorityVideoArr.push(v);
            }
        });
        // @ts-ignore
        allImgList.forEach((i) => {
            if (i.channelNo == item) {
                priorityImgArr.push(i);
            }
        });
    });
};
const getAsyncLocation = (lng: number, lat: number, mediaData: any, alarmDetail: any) => {
    const dom = document.getElementById('info-address-container');
    dom && ReactDOM.render(
        <>
            <div className="info-address">
                <LocationResolution overflowEllipsis={false} showTitle point={{lng: lng*1000000,lat: lat*1000000}} />
            </div>

            { mediaData?.type === 'alarm'
                ? <div className="info-time">
                    {getAlarmTime(alarmDetail.startTime) || '-'}
                </div>
                : ''}
        </> ,
        dom as HTMLElement,
    );
}

const getAsyncImg = () => {
    const imgDom = document.getElementById('alarm-and-park-point-info-image');
    const { AspectRatioImage } = aspectRatioImage.AspectRatioImageV1;
    
    imgDom && ReactDOM.render(
        <Provider>
            <AspectRatioImage 
                className="alarm-and-park-point-popup-image"
                onContextMenu={contextImage}
                src={firstImageUrl}
                preview={false}
            />
        </Provider> ,
        imgDom as HTMLElement,
    );
}
const getPopupData = (params: PopupDataParams) => {
    const {
        lng,
        lat,
        startTime,
        html,
        icon,
        type,
        alarmId,
        onloadedmetadataFun,
        videoParam,
        resourceCode,
        map,
        L,
        vehicleId,
        deviceId,
        hasWatermark,
        hasHeadVideo,
    } = params;
    let showAsyncDomTimer: any;
    // if (!alarmId || !html) return;
    const marker = L?.marker([lat, lng], {
        icon,
        zIndexOffset: type === 'alarm' ? 20 : 10,
    });
    const domId = `alarm-video-${alarmId}`;
    const popup = L?.popup({
        offset: [0, type === 'alarm' ? -8 : 4],
        closeButton: false,
        className: 'alarm-and-park-point-popup-container',
        keepInView: true,
    }).setContent(renderPopup(html, hasWatermark, hasHeadVideo));
    marker.bindPopup(popup);

    marker.on('popupopen', async () => {
        // 获取最新的马赛克状态
        let mosaicFlag = undefined;
        if(resourceCode) {
            mosaicFlag = mosaicManager.checkMosaicEnable(resourceCode) ? 1 : 0;
        }
        Promise.all([
            type === 'alarm'
                ? getEvidenceByAlarm({
                      alarmIds: alarmId,
                      fields: 'vehicle,file_url,driver,videoChannel',
                      mosaicFlag
                  }).catch(() => {})
                : getTravelImage({
                      vehicleId,
                      mosaicFlag,
                      // 为了保证大概率能获取到停车图片，因为如果按照停车时间去查，基本是没有图片的
                      startTime: Number(startTime) - 5,
                      endTime: Number(startTime) + 5,
                  }).catch(() => {}),
            lat && lng
                ? geoPositionAnalysisV2({
                      lat: lat * 10 ** 6,
                      lng: lng * 10 ** 6,
                  }).catch(() => {})
                : '',
            type === 'alarm'
                ? getAlarmDetail({
                      alarmId: alarmId,
                      fields: 'vehicle,driver',
                  })
                : '',
        ])
            .then(([fileData, addrData, alarmDetail]: any) => {
                if (Array.isArray(fileData)) {
                    // eslint-disable-next-line no-param-reassign
                    fileData = fileData[0] || {};
                }
                const {
                    fileList = [],
                    url = '',
                    alarmInfo = {},
                    videoChannelList = [],
                    vehicleInfo,
                    fileChannelAuth
                } = fileData || {};
                const { alarmChannelInfo = '' } = alarmInfo;
                // const { existH264 } = evidenceFormatData(fileData);
                let existH264: boolean | undefined;
                let mediaData: MediaSource = {
                    url,
                    type, // 停车点展示图片getTravelImage
                } as MediaSource;
                if (!url && deviceId) {
                    createParkImageTask({
                        captureTime: Number(startTime) - 5,
                        deviceId: deviceId as unknown as string,
                        vehicleId,
                    }).catch(() => {});
                }
                
                if (type === 'alarm') {
                    if (fileList.length) {
                         const { protocolType } = vehicleInfo || {};
                         const { videoList, videoPriority } = evidenceUtils.getVideoList({
                             fileList: fileList,
                             channelPriority: videoChannelList,
                             isN9M: protocolType === ProtocolTypesEnum.N9M,
                         });
                        const videoData = head(videoList) as any;
                        const imageList = evidenceUtils.getImageList(fileList, videoPriority);
                         const imageData = imageList?.map((i) => i.url);
                        existH264 = videoData?.fileType === FileType.H264;
                        // 计算时长
                        const duration = calcDuration(fileData, existH264, videoData?.channelNo);
                        // const videos = getVideosByFileType(fileList || [], existH264);
                        const videoSize = getAllVideos(fileList).reduce(
                            (total: any, item: any) => total + item.fileSize,
                            0,
                        );
                        // 展示顺序: 接口有返回以返回的顺序为准（视频优先） > 原有逻辑(视频>图片>不展示)
                        mediaData = {
                            url: videoData?.url || imageData || '',
                            fileSize: videoSize || '',
                            type: 'alarm',
                            alarmId,
                            duration,
                            fileType: videoData?.fileType,
                            channelNo: videoData?.channelNo,
                        };
                    } else {
                        mediaData = {
                            url: '',
                            fileSize: null,
                            type: 'alarm',
                            alarmId,
                            duration: '',
                        };
                    }
                }
                popup.on('contentupdate', () => {
                    if (mediaData?.fileType === FileType.H264 && mediaData?.channelNo) {
                        createVideoDom(
                            domId,
                            fileData,
                            resourceCode,
                            mediaData.channelNo as string,
                            mediaData,
                            existH264,
                        );
                    }
                    // 接口返回不存在走原有逻辑
                    if ((!alarmChannelInfo && existH264) || noChannelAuth(fileChannelAuth, false)) {
                        createVideoDom(
                            domId,
                            fileData,
                            resourceCode,
                            mediaData?.channelNo as string,
                            mediaData,
                            existH264,
                            fileChannelAuth
                        );
                    }
                    if (showAsyncDomTimer){
                        clearTimeout(showAsyncDomTimer);
                        showAsyncDomTimer = null;
                    }
                    showAsyncDomTimer = setTimeout(()=>{
                        // 处理地址
                        getAsyncLocation(lng, lat, mediaData, alarmDetail);
                        // 处理图片
                        getAsyncImg();
                    },300);
                });
                popup.setContent(
                    renderPopup(
                        html,
                        hasWatermark,
                        hasHeadVideo,
                        addrData?.address || i18n.t('message', '暂无地址'),
                        mediaData,
                        existH264,
                        alarmDetail,
                        videoParam,
                        onloadedmetadataFun,
                        fileChannelAuth
                    ),
                );
                if (showAsyncDomTimer){
                    clearTimeout(showAsyncDomTimer);
                    showAsyncDomTimer = null;
                }
                showAsyncDomTimer =setTimeout(()=>{
                    // 处理地址
                    getAsyncLocation(lng, lat, mediaData, alarmDetail);
                    // 处理图片
                    getAsyncImg();
                },300);

            })
            .catch((error) => {
                console.error(error);
                popup.setContent(
                    renderPopup(
                        html,
                        hasWatermark,
                        hasHeadVideo,
                        i18n.t('message', '地址解析失败'),
                    ),
                );
                if (showAsyncDomTimer){
                    clearTimeout(showAsyncDomTimer);
                    showAsyncDomTimer = null;
                }
                showAsyncDomTimer =setTimeout(()=>{
                const dom = document.getElementById('info-address-container');
                  dom && ReactDOM.render(
                   <>
                          <div className="info-address">
                             { i18n.t('message', '地址解析失败')}
                          </div>
                    </> ,
                        dom as HTMLElement,
                );
                } ,300);
            });
    });
    marker.on('popupclose', () => {
        popup.off('contentupdate');
        if (showAsyncDomTimer){
            clearTimeout(showAsyncDomTimer);
            showAsyncDomTimer = null;
        }
        destoryVideoDom(domId);
    });
    marker.on('mouseout', () => {
        popup.off('contentupdate');
        map._popup?.getElement().addEventListener('mouseover', () => {
            popVisiable(marker, popup, 'openPopup', map);
        });
        map._popup?.getElement().addEventListener('mouseout', () => {
            popVisiable(marker, popup, 'closePopup');
        });
        popVisiable(marker, popup, 'closePopup');
    });
    marker.on('mouseover', () => {
        photoNum = 1;
        marker.bindPopup(popup).openPopup();
    });
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return marker;
};

/**
 * 渲染地图中报警点/停车点的卡片
 * @param panelData 卡片数据源
 * @param panelType 卡片类型 default='alarm'
 * @param map map实例需要popup实现tooltip效果
 * @param resourceCode createVideoDom需要
 * @param pageOrigin 页面来源 作为后续可能会有一些页面有特殊要求展示的扩展
 * @param videoParam video标签id和加载的videolength标签 元素的ids
 * @param onClickAlarmFun 报警名称点击事件 需要挂在window下执行
 * @param onloadedmetadataFun video标签onloadedmetadata事件 需挂在window下执行
 * @param hasWatermark 是否开启了水印，添加图片禁用右键菜单点击
 * @param hasHeadVideo 是否隐藏视频
 * @returns 返回模板字段串
 */
export const renderMapPanel = (params: RenderMapPanelParams) => {
    const {
        data,
        type,
        map,
        L,
        resourceCode,
        pageOrigin,
        videoParam,
        onClickAlarmFun,
        onloadedmetadataFun,
        hasWatermark, //是否开启了水印
        hasHeadVideo,
        customFormateSeconds,
    } = params;
    if (type === 'park') {
        const { startTime, endTime, isStart, isEnd } = data;
        let html = `
            <h4
                class="info-title"
                title="
                    ${i18n.t('name', '停车时长')}:
                        ${
                            endTime && startTime
                                ? getCustomFun(customFormateSeconds, formateSeconds, {
                                      time: endTime - startTime,
                                      type: 'seconds',
                                  })
                                : '-'
                        }
                "
            >
                ${i18n.t('name', '停车时长')}:
                    ${
                        endTime && startTime
                            ? getCustomFun(customFormateSeconds, formateSeconds, {
                                  time: endTime - startTime,
                                  type: 'seconds',
                              })
                            : '-'
                    }
            </h4>
            <div class="info-time">
                ${startTime ? getTime(startTime) : '-'} ~ ${endTime ? getTime(endTime) : '-'}
            </div>
        `;
        if (isStart || isEnd) {
            html = `
                <div
                    class="info-time"
                    style="margin-top: 0px;"
                >
                    ${startTime ? getTime(startTime) : '-'}
                </div>
            `;
        }
        const marker = getPopupData({
            ...data,
            type,
            html,
            pageOrigin,
            videoParam,
            onloadedmetadataFun,
            hasWatermark,
            hasHeadVideo,
            icon: L?.divIcon({
                className: 'park-point-open-alarm-and-park-point-popup',
                iconSize: [16, 16],
                iconAnchor: [8, 8],
            }),
            map,
            L,
            resourceCode,
        });
        return marker;
    } else {
        const { alarmName, alarmId } = data;
        const escapeAlarmName = escapeHtml(alarmName);
        const html = `
            <h4
                class="info-title alarm-info-title"
                onclick="${onClickAlarmFun}('${alarmId}')"
                title="${escapeAlarmName}"
                style="color: ${!!onClickAlarmFun ? getAppGlobalData('APP_THEME') : '#fffff'}"
            >
                ${escapeAlarmName}
            </h4>
        `;
        const marker = getPopupData({
            ...data,
            type,
            html,
            pageOrigin,
            videoParam,
            hasWatermark,
            hasHeadVideo,
            onloadedmetadataFun,
            icon: L?.icon({
                iconUrl: ALARM_ICON,
                iconSize: [20, 20],
                iconAnchor: [10, 20],
            }),
            map,
            L,
            resourceCode,
        });
        return marker;
    }
};
