import type { STORE_MEAL_TYPE } from '@/types/meal';
import { i18n } from '@base-app/runtime-lib';
import {EXPIRE_TYPE} from "@/runtime-pages/user-manage/components/ActivateUserModal";
export const LINE_ACCURACY = 1000_00; // 后端存储的精度
export const NO_AUTH_TEXT = '******'; //无权限是展示
export const NO_AUTH_SIGN = '-2'; //无权限时id
// 设置类型枚举
export const STRATEGY_TYPE = {
    EVIDENCE: 1, // 证据回传
    ALARM_LINKAGE: 2, // 报警联动
    AUTO_HANDLE: 10, // 自动处理
    MAIL_SEND: 3, // 邮件发送
    PICTURE_CAPTURE: 11, // 图片抓拍
    FACE_CONTRAST: 8, // 人脸对比
    DATA_CLEANING: 7, // 数据清理
    AUTO_UPLOAD: 9, // 自动上传
    USER: 6, // 用户设置
    DEVICE_CHANNEL_SETTING: 20, // 设备机型通道设置
    VEHICLE_CHANNEL_SETTING: 18 // 车辆通道设置
};
export const MAX_USER_ROL_SELECT_NUMBER = 20;
// 新的版本允许最大选择99个
export const MAX_USER_ROL_SELECT_NUMBER_V2 = 100;
export const SELECT_PAGE_SIZE = 50;
// (报警联动设置)消息类型枚举
export const LINKAGE_MESSAGE_TYPE = {
    TEXT: '1', // 文本消息（下发）
    AUDIO: '2', // 音频消息（下发）
};

// 语言类型枚举
export const LANGUAGE_TYPE = {
    SPANISH: 1, // 西班牙语
};

// (报警联动设置)音频下发类型枚举
export const LINKAGE_AUDIO_TYPE = {
    COMMON: 1, // 常用音频
    CUSTOM: 2, // 自定义音频
    TTS: 3     // 文本转语音
};

// (报警联动设置)文本下发类型枚举
export const LINKAGE_TEXT_TYPE = {
    COMMON: 1, // 常用文本
    CUSTOM: 2, // 自定义文本
};

// 设置查询 查看详情权限的限制枚举
export const STRATEGY_QUERY_LIMIT = {
    LIMITED: 1, // 受限 不可查看详情
    UNLIMITED: 0, // 不受限 可见详情，能跳转
    DEFAULT: 2, // 默认设置，前端验证页面权限
};

// 存储套餐
export const SIZE_UNIT = {
    1: 'GB',
    2: 'TB',
};

export enum MEAL {
    severMeal = 1, // 服务套餐
    apiMeal, // API套餐
    storeMeal, // 存储套餐
    singleMeal, // 单车套餐
}
export enum SPACE_STATUS {
    normal = 1, // 空间正常
    insufficient, // 空间不足
    full, // 空间已满
}
export const FUNC_MEAL = `${MEAL.severMeal},${MEAL.apiMeal}`; // 功能套餐 === 服务套餐 + API套餐
export const SAAS_APPID = 0; // bp应用的appid

export const PROTOCOL_TYPE = {
    JT905: 6,
    N9M: 1,
    JT808: 2,
    ST808: 3,
    JT1078: 4,
    LENZ: 11,
    CMS: 12,
    TK: 10,
    WB_XB: 14,
    GENVO: 15,
    LTY: 19,
};

// 设备类型
export const DEVICE_PRIMARY_TYPE = {
    main: 1, // 主设备
    assist: 2, // 辅助设备
};

export const VEHICLE_STATE = {
    online: 1001,
    offline: 1002,
    alarm: 1003,
};
export const ProtocolTypeOptions = [
    { label: 'N9M', value: 1 },
    { label: 'JT808', value: 2 },
    { label: 'ST808', value: 3 },
    { label: 'JT1078', value: 4 },
    { label: 'JT905', value: 6 },
    { label: 'LENZ', value: 11 },
    { label: 'CMS', value: 12 },
    { label: 'TK', value: 10 },
    { label: 'WB_XB', value: 14 },
    { label: 'GENVO', value: 15 },
    { label: 'LTY', value: 19 },
];
export interface OptionItemProps {
    protocolType?: number;
    label: string;
    value: number | string;
}
export enum ProtocolTypes {
    PROTOCOLN9M = 1,
    PROTOCOLJT808 = 2,
    PROTOCOLST808 = 3,
    PROTOCOLJT1078 = 4,
    PROTOCOLJT905 = 6,
    PROTOCOLLENZ = 11,
    PROTOCOLCMS = 12,
    PROTOCOLTK = 10,
    PROTOCOLGB28181 = 7,
    PROTOCOLLTY = 19,
}
export const ProtocolTypesEnum = {
    1: 'N9M',
    2: 'JT808',
    3: 'ST808',
    4: 'JT1078',
    6: 'JT905',
    7: 'GB28181',
    10: 'TK',
    11: 'LENZ',
    12: 'CMS',
    14: 'WB_XB',
    15: 'GENVO',
    19: 'LTY',
};
export enum SCREEN_OPTION {
    TERMINAL = '1',
    HEADSTOCK = '2',
    BODY_OF_CAR = '3',
    TAILSTOCK = '4',
    ADVERTISING = '5',
}
export enum LINE_PATTERN {
    solid = 1, // 实线
    high_dashed, // 高密度虚线
    low_dashed, // 低密度虚线
}
export const DASH_ARRAY = {
    solid: [], // 实线 1
    // low_dashed: [5, 10], // 低密度虚线 3
    // high_dashed: [1, 10], // 高密度虚线 2
    low_dashed: [10, 12], // 低密度虚线 3
    high_dashed: [5, 8], // 高密度虚线 2
};
export const getScreenOptions = () => [
    {
        label: i18n.t('name', '终端屏幕'),
        value: SCREEN_OPTION.TERMINAL,
    },
    {
        label: i18n.t('name', '车头'),
        value: SCREEN_OPTION.HEADSTOCK,
    },
    {
        label: i18n.t('name', '车身'),
        value: SCREEN_OPTION.BODY_OF_CAR,
    },
    {
        label: i18n.t('name', '车尾'),
        value: SCREEN_OPTION.TAILSTOCK,
    },
    {
        label: i18n.t('name', '广告屏'),
        value: SCREEN_OPTION.ADVERTISING,
    },
];
export enum FENCE_STATE {
    notStarted, // 未开始
    effective, // 生效中
    expired, // 已过期
}
export const FENCE_STATE_ARR = () => [
    // 这里需求未定暂时取消围栏状态
    {
        label: i18n.t('state', '未开始'),
        value: FENCE_STATE.notStarted,
        color: '#faad14',
    },
    {
        label: i18n.t('state', '生效中'),
        value: FENCE_STATE.effective,
        color: '#53c21c',
    },
    {
        label: i18n.t('state', '已过期'),
        value: FENCE_STATE.expired,
        color: '#bfbfbf',
    },
];
export type ScopeModuleType = 'fleet' | 'vehicle' | 'device';
export const StorageTypes = {
    3: i18n.t('name', '租户存储套餐'),
    4: i18n.t('name', '单车存储套餐'),
};

export const FUNC_MEAL_TYPE_MAP = () => ({
    1: i18n.t('name', '服务套餐'),
    2: i18n.t('name', 'API套餐'),
});

export const FUNC_MEALS = {
    SERVER_MEAL: 1,
    API_MEAL: 2,
};
export const SERVER_MEAL = 1;
export const API_MEAL = 2;

export enum ROLE_TYPE {
    TENANT = 1,
    APPLICATION,
    FUNCTION,
}
export const STORE_MEAL_TYPE_VALUE: Record<STORE_MEAL_TYPE, number> = {
    tenant: 3,
    vehicle: 4,
};
// 存储套餐类型选项
export const STORE_MEAL_TYPE_OPTIONS = [
    { label: i18n.t('name', '租户存储套餐'), value: 'tenant' },
    { label: i18n.t('name', '单车存储套餐'), value: 'vehicle' },
];
//邮箱密码最大128位
export const MAX_EMAIL_PASSWORD_LENGTH = 128;
// GB最大展示1023
export const MAX_GB = 1023;
export const regExpPasswordDefault = /[^A-Za-z\d()`~!@#$%^&*\-+=_|{}[\]:;'<>,\.?/]/g;

// 重置密码后60秒内不允许再次操作
export const SIXTY_SECOND_EXPIRE_TIME = 60 * 1000;
// 重置密码后60秒内不允许再次操作的过期时间key
export const RESET_PASSWORD_SIXTY_SECOND_EXPIRE_TIME_KEY =
    'RESET_PASSWORD_SIXTY_SECOND_LIMIT_REPEAT_HANDLE_EXPIRE_TIME_KEY';
// 用户启用
export const USER_ENABLE = 1;
// 用户停用
export const USER_UNABLE = 2;
// 用户休眠
export const USER_DORMANCY = 3;
// 用户到期
export const USER_EXPIRE = 4;
// 默认登录天数限制
export const DEFAULT_LOGIN_LIMIT_DAYS = 180;
// 登录限制天数
export const LOGIN_LIMIT_DAYS_OPTIONS = [
    { label: 30, value: 30 },
    { label: 90, value: 90 },
    { label: 180, value: 180 },
    { label: 360, value: 360 },
];
// 默认到期前提醒天数
export const DEFAULT_EXPIRE_NOTICE_DAYS = 30;
// 到期前提醒天数
export const EXPIRE_NOTICE_DAYS_OPTIONS = [
    { label: 90, value: 90 },
    { label: 60, value: 60 },
    { label: 30, value: 30 },
    { label: 15, value: 15 },
    { label: 10, value: 10 },
    { label: 7, value: 7 },
    { label: 3, value: 3 },
];
// 默认配置包上传限制
export const DEFAULT_UPLOAD_SIZE = 200 * 1024; //配置包导入默认200M
export const allLang = {
    ar_EG: 'العربية',
    bg_BG: 'български',
    ca_ES: 'Català',
    cs_CZ: 'Česky',
    de_DE: 'Deutsch',
    el_GR: 'Ελληνικά',
    en_GB: 'English',
    en_US: 'English',
    es_ES: 'Español',
    et_EE: 'Eestlane',
    fa_IR: 'فارسی',
    fi_FI: 'suomalainen',
    fr_BE: 'Français (Belgique)',
    fr_FR: 'Français',
    he_IL: 'עברית',
    hi_IN: 'हिन्दी',
    hr_HR: 'hrvatski',
    hu_HU: 'magyar',
    is_IS: 'Íslensku',
    id_ID: 'Orang indonesia',
    it_IT: 'Italiano',
    ja_JP: '日本語',
    kn_IN: 'ಕನ್ನಡ',
    ko_KR: '한국어',
    nb_NO: 'norsk språk',
    ne_NP: 'नेपाली',
    nl_BE: 'Nederlands (België)',
    nl_NL: 'Nederlands',
    pl_PL: 'Polski',
    pt_BR: 'Português (Brasil)',
    pt_PT: 'Português',
    sk_SK: 'slovenského jazyk',
    sr_RS: 'Српски',
    sl_SI: 'Slovensko',
    sv_SE: 'svenska',
    ta_IN: 'தமிழ் மொழி',
    th_TH: 'ไทย',
    tr_TR: 'Türk dili',
    ro_RO: 'românesc',
    ru_RU: 'русский',
    uk_UA: 'Українська',
    vi_VN: 'Tiếng việt nam',
    zh_CN: '简体中文',
    zh_TW: '繁體中文(台湾)',
    zh_HK: '繁體中文(香港)',
    tg_TJ: 'Тоҷикӣ',
} as Record<string, string>;


// 直通视频断线重连时间间隔，单位秒
export const LIVE_VIDEO_RECONNECT_INTERVAL = 2;
// 禁止重试，防止性能问题
export const LIVE_VIDEO_RECONNECT_COUNT_NO_TRY = 0;
export const LIVE_RECONNECT_COUNT_TIME = [3,3,3,3]; //实时视频默认重连次数和时间

export const PickFrameModelMap = [
    {
        label: i18n.t('name', 'Auto'),
        value: 3
    },
    {
        label: i18n.t('name', '高清'),
        value: 1
    },
    {
        label: i18n.t('name', '标清'),
        value: 2
    }
];
export const expireTypeMap = [
    {
        value: EXPIRE_TYPE.FOREVER,
        label: i18n.t('name', '永久有效')
    },
    {
        value: EXPIRE_TYPE.CUSTOM,
        label: i18n.t('name', '自定义')
    }
];
// 角色类型
export enum RoleType {
    TenantManager = 1, // 租户管理员
    ApplicationManager = 2, // 应用管理员
    FunctionManager = 3 // 功能管理员
}
// 单车空间最大最小限制
export const VEHICLE_SPACE_SIZE = {
    MAX: 9999.5,
    MIN: 0.5
};

// 网络模式
export const checkboxOptions = [
    {
        label: i18n.t('name', '有线网络'),
        value: '0'
    },
    {
        label: i18n.t('name', 'WIFI'),
        value: '1'
    },
        {
        label: i18n.t('name', '3G/4G/5G'),
        value: '2'
    }
];
// 报警类型启用
export const ALARM_TYPE_ENABLE = 1;
// 报警类型停用
export const ALARM_TYPE_DISABLE = 0;
// 是否支持换绑
export const changeBindingType = {
    0: i18n.t('name','不支持'),
    1: i18n.t('name','支持'),
};
export const IN_CAR_CHANNEL_ID = 2; //仓内通道的id
