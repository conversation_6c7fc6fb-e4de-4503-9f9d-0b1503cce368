// 格式化展示存储套餐容量
export const formatStoreSpace = (space?: number) => {
    if (space === undefined) {
        return '';
    } else if (space && space > 1023.99) {
        return `${(space / 1024).toFixed(2)}TB`;
    } else {
        return `${space}GB`;
    }
};
/**
 * @description wma格式的音频不展示播放按钮
 * @description true: 显示  false: 不显示
 */
export const judgeIsDisplay = (fileName: any) => {
    return !fileName?.split('.')?.[1]?.includes('wma');
};

export function freeObject(obj: Record<string, any>, deep: boolean = true) {
    // return;
    if (obj === null || typeof obj !== 'object') {
        // If the input is not an object or is null, do nothing
        return;
    }

    // Iterate over the properties of the object
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            // If the property is an object, recursively clear it
            if (deep && value && typeof value === 'object') {
                freeObject(value);
            }
            // Delete the property
            delete obj[key];
        }
    }
    // If the object is an array, clear its length (optional, but helps with arrays)
    if (Array.isArray(obj)) {
        obj.length = 0;
    }
}

export function sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
}


/**
 * HTML转义函数，防止特殊字符破坏HTML结构
 * @param str 需要转义的字符串
 * @returns 转义后的字符串
 */
export const escapeHtml = (str: string): string => {
    if (!str) return '';
    
    const newStr = typeof str === 'string' ? str : String(str);
    
    const htmlEscapeMap: Record<string, string> = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
    };
    
    return newStr.replace(/[&<>"']/g, (match) => htmlEscapeMap[match]);
};