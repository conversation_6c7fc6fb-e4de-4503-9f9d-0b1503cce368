/* eslint-disable consistent-return */
/* eslint-disable no-param-reassign */
//@ts-nocheck
import { message } from '@streamax/poppy';
import { getAppGlobalData } from '@base-app/runtime-lib';
import { utils, i18n } from '@base-app/runtime-lib';
import moment from 'moment';
import type { Moment } from 'moment';
import timeFun from './time';
import { getDeviceListByPage } from '../service/device';
import { postQueryDriverList } from '../service/driver';
import { fetchFileDownloadUrl } from '../service/gss';
import type { VehicleOrderState } from '../service/vehicle';
import { getVehiclerListByPage } from '../service/vehicle';
import { fetchDeviceConfigureChannelList } from '@/service/channel';
//@ts-ignore
import type { FileItem } from './evidenceFormatData';
import {
    StateActive,
    type DeviceItem,
} from '@/hooks/useRTData/useRTDeviceData';
import { fetchApplicationConfig } from '@/service/application';
import { getTenantParamsSetting } from '@/service/parameter';
import { NO_AUTH_TEXT, IN_CAR_CHANNEL_ID } from '@/utils/constant';
import type { ScopeModuleType } from '@/utils/constant';
import { isNil, isEmpty } from 'lodash';
import { getUserInfoConfig } from '@/service/tenant';

const { timestampToZeroTimeStamp, getSummerTimeOffsetByTime } = utils.formator;
const { TIME_ZONE_UTC_OPTIONS } = utils.constant;
// 是否抽帧视频；0-不抽帧，1-抽帧，只对视频/264有效
enum PickFrameEnum {
    NoPickFrame,
    PickFrame,
}
const TRANS_STORAGE_UNIT = 1024;
const LIMIT = 2147483647;
const MAX_USER_NUMBER = 20;

export const weekList = [
    i18n.t('name', '周一'),
    i18n.t('name', '周二'),
    i18n.t('name', '周三'),
    i18n.t('name', '周四'),
    i18n.t('name', '周五'),
    i18n.t('name', '周六'),
    i18n.t('name', '周日'),
];

export const monthList = (): string[] => {
    const list = [];
    for (let index = 1; index < 32; index++) {
        list.push(index.toString().padStart(2, '0'));
    }
    return list;
};

// 千分位展示数字  并保留两位小数
export const numFormat = (num: number | string) => {
    let value = num;
    try {
        if (typeof value == 'string') value = parseInt(value);
        if (Number.isNaN(value)) return '-';

        return value
            .toFixed(2)
            .split('')
            .reverse()
            .join('')
            .replace(/(\d{3})+?/g, function (s) {
                return s + ',';
            })
            .replace(/,$/, '')
            .split('')
            .reverse()
            .join('');
    } catch (error) {
        return '-';
    }
};

export const languageLabelList = {
    ar_EG: 'العربية',
    bg_BG: 'български',
    ca_ES: 'Català',
    cs_CZ: 'Česky',
    de_DE: 'Deutsch',
    el_GR: 'Ελληνικά',
    en_GB: 'English',
    en_US: 'English',
    es_ES: 'Español',
    et_EE: 'Eestlane',
    fa_IR: 'فارسی',
    fi_FI: 'suomalainen',
    fr_BE: 'Français (Belgique)',
    fr_FR: 'Français',
    he_IL: 'עברית',
    hi_IN: 'हिन्दी',
    hr_HR: 'hrvatski',
    hu_HU: 'magyar',
    is_IS: 'Íslensku',
    id_ID: 'Orang indonesia',
    it_IT: 'Italiano',
    ja_JP: '日本語',
    kn_IN: 'ಕನ್ನಡ',
    ko_KR: '한국어',
    nb_NO: 'norsk språk',
    ne_NP: 'नेपाली',
    nl_BE: 'Nederlands (België)',
    nl_NL: 'Nederlands',
    pl_PL: 'Polski',
    pt_BR: 'Português (Brasil)',
    pt_PT: 'Português',
    sk_SK: 'slovenského jazyk',
    sr_RS: 'Српски',
    sl_SI: 'Slovensko',
    sv_SE: 'svenska',
    ta_IN: 'தமிழ் மொழி',
    th_TH: 'ไทย',
    tr_TR: 'Türk dili',
    ro_RO: 'românesc',
    ru_RU: 'русский',
    uk_UA: 'Українська',
    vi_VN: 'Tiếng việt nam',
    zh_CN: '简体中文',
    zh_TW: '繁體中文(台湾)',
    zh_HK: '繁體中文(香港)',
    tg_TJ: 'Тоҷикӣ',
};

// 格式化方向
export const formatAngle = (direction: number) => {
    const angel = Math.floor(direction / 22.5);
    let strDirection = '';
    switch (angel) {
        case 0:
            strDirection = i18n.t('name', '正北');
            break;
        case 1:
        case 2:
            strDirection = i18n.t('name', '东北');
            break;
        case 3:
        case 4:
            strDirection = i18n.t('name', '正东');
            break;
        case 5:
        case 6:
            strDirection = i18n.t('name', '东南');
            break;
        case 7:
        case 8:
            strDirection = i18n.t('name', '正南');
            break;
        case 9:
        case 10:
            strDirection = i18n.t('name', '西南');
            break;
        case 11:
        case 12:
            strDirection = i18n.t('name', '正西');
            break;
        case 13:
        case 14:
            strDirection = i18n.t('name', '西北');
            break;
        case 15:
            strDirection = i18n.t('name', '正北');
            break;
        default:
            break;
    }
    return strDirection;
};

// 全屏
export const fullScreen = (dom: Element) => {
    const rfs =
        dom.requestFullscreen ||
        //@ts-ignore
        dom.webkitRequestFullScreen ||
        //@ts-ignore
        dom.mozRequestFullScreen ||
        //@ts-ignore
        dom.msRequestFullScreen;
    if (rfs) {
        rfs.call(dom);
    } else if (typeof window.ActiveXObject !== 'undefined') {
        // for IE，这里其实就是模拟了按下键盘的F11，使浏览器全屏
        const wscript = new ActiveXObject('WScript.Shell');
        if (wscript != null) {
            wscript.SendKeys('{F11}');
        }
    }
};

// 退出全屏
export const exitFullScreen = () => {
    const dom = document;
    const cfs =
        dom.exitFullscreen ||
        //@ts-ignore
        dom.mozCancelFullScreen ||
        //@ts-ignore
        dom.webkitCancelFullScreen ||
        //@ts-ignore
        dom.msExitFullscreen;

    if (cfs) {
        cfs.call(dom);
    } else if (typeof window.ActiveXObject !== 'undefined') {
        // for IE，这里和fullScreen相同，模拟按下F11键退出全屏
        const wscript = new ActiveXObject('WScript.Shell');
        if (wscript != null) {
            wscript.SendKeys('{F11}');
        }
    }
};

// 获取数组中某一项的某字段，常用语翻译value为label.为找到返回原value值
type Key = string | number;
type ObjectMap = {
    [key in Key]: any;
};
export const getLabelByValue = (
    arr: ObjectMap[],
    value: string | number,
    valueKey = 'value',
    labelKey = 'label',
) => {
    const target = arr.find((i) => i[valueKey] === value);
    return target ? target[labelKey] : value;
};

// 获取当前应用的速度单位，默认返回label
export const getAppSpeedUnit = (getValue = false) => {
    const value = getAppGlobalData('APP_USER_CONFIG').speedUnit || '1';
    if (getValue) {
        return value;
    }
    return getLabelByValue(utils.constant.SPEED_UNIT_OPTIONS, value);
};

// 获取当前应用的里程单位，默认返回label
export const getAppMileageUnit = (getValue = false) => {
    const value = getAppGlobalData('APP_USER_CONFIG').mileageUnit || '1';
    if (getValue) {
        return value;
    }
    return getLabelByValue(utils.constant.MILEAGE_UNIT_OPTIONS, value);
};

// 判断是否是mac系统
export const isMacOS = () => {
    const agent = navigator.userAgent.toLowerCase();
    if (/macintosh|mac os x/g.test(agent)) {
        return true;
    }
    return false;
};
export const downloadFile = (fileId: string) => {
    if (!fileId) return;
    try {
        fetchFileDownloadUrl(
            { fileIdList: fileId },
            { noDataInterceptor: true },
        ).then((rs: any) => {
            const result = rs.data;
            if (result && result.message === 'success') {
                if (result.data && result.data.length > 0) {
                    const url = result.data[0].fileUrl;
                    window.open(url);
                }
            }
        });
    } catch (error) {
        console.error(error);
    }
};

export const exportFile = (url: string, name: string) => {
    let a: any = document.createElement('a');
    a.setAttribute('src', url);
    a.setAttribute('download', name);
    a.click();
    a = null; // 世界更清净
};

type FetchType = 'driver' | 'vehicle' | 'device';
interface FetchParams {
    type: FetchType;
    value: string;
    needDriverPicture?: boolean;
}
export const fetchBaseData = async (params: FetchParams) => {
    const { type, value } = params;
    let ids = '';
    const pageParam = {
        page: 1,
        pageSize: LIMIT,
    };
    let rs;
    if (type == 'driver') {
        rs = await postQueryDriverList({
            ...pageParam,
            driverName: value,
            needDriverPicture: params?.needDriverPicture ?? false,
        });
        if (rs && rs.list) {
            ids = rs.list.map((item) => item.driverId).join(',');
        }
    } else if (type == 'vehicle') {
        rs = await getVehiclerListByPage(
            {
                ...pageParam,
                vehicleNumber: value ? encodeURIComponent(value) : value,
                decode: value ? 1 : 0,
            },
            true,
        );
        if (rs && rs.list) {
            ids = rs.list.map((item) => item.vehicleId).join(',');
        }
    } else if (type == 'device') {
        rs = await getDeviceListByPage({ ...pageParam, deviceNo: value });
        if (rs && rs.list) {
            ids = rs.list.map((item: any) => item.deviceId as string).join(',');
        }
    }
    return ids;
};

// 菜单、页面、操作模块的编码非法字符
export const validatorResourceCode = (rule: any, value: string) => {
    if (value && /[\\/*?"<>|]/g.test(value)) {
        return Promise.reject(i18n.t('name', '非法字符'));
    }
    return Promise.resolve();
};

// 车组，监控组非法字符
export const groupIllegalCharacter = (rule: any, value: string) => {
    if (value && /[\\/*,？?‘’''“”""<>｜]/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};

// 车组，监控组非法字符
export const isGroupIllegalCharacter = (value: string) => {
    if (value && /[\\/*,？?‘’''“”""<>｜]/g.test(value)) {
        message.error(i18n.t('message', '非法字符'));
        return false;
    }
    return true;
};

// 补零
export const fillZero = (num: number, len: number) =>
    num.toString().padStart(len, '0');

// 转换时长单位毫秒为x时x分x秒
export const transTimePeriod = (period: number, noSeconds = false) => {
    if (period > 0) {
        const hours = Math.floor(
            (period % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
        );
        const minutes = Math.floor((period % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((period % (1000 * 60)) / 1000);
        const timeStr = `${hours}${i18n.t('name', '小时')}${minutes}${i18n.t(
            'name',
            '分钟',
        )}`;
        if (noSeconds) return timeStr;
        return `${timeStr}${seconds}${i18n.t('name', '秒')}`;
    }
    return '';
};

//扁平数据 转化tree
export const toTreeByRecursion = (
    source: any[] = [],
    idField = 'id',
    parentIdField = 'parentId',
    parentIdNoneValue: string | null,
    childrenField = 'children',
    treeOption?: any,
) => {
    const treeOptions = {
        enable: false, // 是否开启转tree数据
        keyField: 'key', // 标识字段名称，默认为key
        valueField: 'value', // 值字段名称，默认为value
        titleField: 'title', // 标题字段名称，默认为title

        keyFieldBind: 'id', // 标识字段绑定字段名称，默认为id
        valueFieldBind: 'id', // 值字段名称绑定字段名称，默认为id
        titleFieldBind: 'name', // 标题字段名称绑定字段名称，默认为name
    };
    // 合并tree树形配置
    if (treeOption) {
        Object.assign(treeOptions, treeOption);
    }

    const cloneData = JSON.parse(JSON.stringify(source));
    const pIds = cloneData.map((i) => i[idField]);
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return cloneData.filter((parent: any) => {
        // 返回每一项的子级数组
        const branchArr = cloneData.filter(
            (child: any) => parent[idField] === child[parentIdField],
        );

        // 绑定tree树形配置
        if (treeOptions.enable) {
            branchArr.map((child: any) => {
                child[treeOptions.keyField] = child[treeOptions.keyFieldBind];
                child[treeOptions.valueField] =
                    child[treeOptions.valueFieldBind];
                child[treeOptions.titleField] =
                    child[treeOptions.titleFieldBind];
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                return child;
            });
        }

        // 如果存在子级，则给父级添加一个children属性，并赋值，否则赋值为空数组
        if (branchArr.length > 0) {
            parent[childrenField] = branchArr;
        } else {
            parent[childrenField] = [];
        }

        // 绑定tree树形配置
        if (treeOptions.enable) {
            parent[treeOptions.keyField] = parent[treeOptions.keyFieldBind];
            parent[treeOptions.valueField] = parent[treeOptions.valueFieldBind];
            parent[treeOptions.titleField] = parent[treeOptions.titleFieldBind];
        }
        if (
            // 有上级服务，只是当前租户无权限访问的服务节点， 需要展示为顶级服务
            !pIds.includes(parent[parentIdField]) &&
            parent[parentIdField] !== parentIdNoneValue
        ) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-return
            return parent;
        } else {
            return parent[parentIdField] === parentIdNoneValue; // 返回第一层
        }
    });
};

// 将json字符串格式化为 JSON显示格式的字符串；需要放到Info.Textarea中才有格式化效果
export const formatJsonString = (value: any) => {
    if (value) {
        try {
            const obj = JSON.parse(value);
            if (typeof obj != 'object') {
                return value;
            }
            return JSON.stringify(obj, undefined, 4);
        } catch (error) {
            return value;
        }
    } else {
        return value;
    }
};

// （证据）文件类型
export enum FileType {
    IMAGE = 9,
    VIDEO = 1,
    H264 = 13,
}

interface Params {
    fileList: any[];
    startTime: number;
    endTime: number;
}

// 获取fileList中所有264或者mp4资源
export const getAllVideos = (fileList: FileItem[]) => {
    const videos = (fileList || []).filter(
        (p: FileItem) =>
            p.fileType == FileType.H264 ||
            (p.fileType == FileType.VIDEO && p.url),
    );
    return videos.sort((a: FileItem, b: FileItem) => a.channelNo - b.channelNo);
};

// 获取fileList中的264或者mp4资源
export const getVideosByFileType = (
    fileList: FileItem[],
    existH264?: boolean,
) => {
    const videosH264 = (fileList || []).filter(
        (p: FileItem) => p.fileType == FileType.H264,
    );
    const videosMP4 = (fileList || []).filter(
        (p: FileItem) => p.fileType == FileType.VIDEO && p.url,
    );
    const videos = videosH264.length && existH264 ? videosH264 : videosMP4;
    return videos.sort((a: FileItem, b: FileItem) => a.channelNo - b.channelNo);
};

// 计算duration规则，报警列表 证据列表 保持一致
export const calcDuration = (
    { fileList = [] }: Params,
    existH264?: boolean,
    playChannel?: string,
) => {
    // 获取有效的视频数据
    let filterFileList = fileList;
    if (playChannel) {
        filterFileList = filterFileList.filter(
            (item) => item.channelNo == playChannel,
        );
    }
    const videos = getVideosByFileType(filterFileList, existH264);
    let duration = 0;
    if (videos.length > 0) {
        // 计算视频时长
        if (videos[0].pickFrame === PickFrameEnum.PickFrame) {
            duration = Math.max(
                ...videos.map(({ videoTime }) => videoTime || 0),
            );
        } else if (videos[0].videoTime) {
            duration = videos[0].videoTime;
        } else {
            const startTime = videos.sort(
                (a: any, b: any) => a.startTime - b.startTime,
            )[0].startTime;
            const endTime = videos.sort(
                (a: any, b: any) => b.endTime - a.endTime,
            )[0].endTime;
            duration = endTime - startTime;
        }
    }
    // 非视频数据显示00:00, 兼容undefined * 1000 = NaN
    return timeFun.formatTime(isNil(duration) ? 0 : duration * 1000);
};

// 国际化和词条编辑名称校验
export const validatorI18n = (rule: any, value: string) => {
    if (value && /[\\*<|]/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};
//放开对|\字符的校验，用于国际化编辑
export const validateCharacters = (rule: any, value: string) => {
    if (value && /[*<]/g.test(value)) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};
// 相同时间校验
export const validatorDiffTime = (rule: any, values: string) => {
    const start =
        values && values.length
            ? timestampToZeroTimeStamp(values[0] as any)
            : '';
    const end =
        values && values.length
            ? timestampToZeroTimeStamp(values[1] as any)
            : '';
    if (values && values.length && start == end) {
        return Promise.reject(i18n.t('message', '开始时间和结束时间相同'));
    }
    return Promise.resolve();
};
// 禁用今天以后的时间
export function disabledAfterDate(current: Moment) {
    return current > moment().endOf('days');
}
// 禁用今天以前的时间
export function disabledBeforeDate(current: Moment) {
    return current < moment().endOf('days');
}

/**
 * 时间范围默认时间
 * @param days 需要的是前x天
 * @returns 返回前x天的日期
 */
export function getInitTimeRange(days: number) {
    return [
        moment()
            .subtract(days - 1, 'days')
            .startOf('day'),
        moment().endOf('day'),
    ];
}
// 获取今天以后的x天时间范围
export function getAfterDateTimeRange(days: number) {
    return [moment().startOf('day'), moment().add(days - 1, 'days')];
}

// 获取时间范围的extractor操作
export function getPickerRanges() {
    return {
        [`${i18n.t('name', '当天')}`]: [
            moment().subtract(0, 'days').startOf('day'),
            moment().endOf('day'),
        ],
        [`${i18n.t('name', '最近7天')}`]: [
            moment().subtract(6, 'days').startOf('day'),
            moment().endOf('day'),
        ],
        [`${i18n.t('name', '最近30天')}`]: [
            moment().subtract(29, 'days').startOf('day'),
            moment().endOf('day'),
        ],
    };
}
// 检查表单字段值是否只有空格
export const checkFieldSpace = (value: any, message: string) => {
    if (!value || (value.length && !value.trim())) {
        return Promise.reject(message);
    }
    return Promise.resolve();
};

// 时区下拉选 25个
export const getGmtOptions = () => {
    const emailTimeZoneOptions: { label: string; value: number | string }[] =
        [];
    for (let i = 0; i < 2; i++) {
        for (let j = 0; j < 12; j++) {
            if (i === 0) {
                emailTimeZoneOptions.push({
                    label: 'GMT-' + (12 - j),
                    value: '-' + (12 - j) * 3600,
                });
            } else {
                emailTimeZoneOptions.push({
                    label: 'GMT+' + (j + 1),
                    value: String((j + 1) * 3600),
                });
            }
            if (i === 0 && j === 11) {
                emailTimeZoneOptions.push({ label: 'GMT+0', value: '+0' });
            }
        }
    }
    return emailTimeZoneOptions;
};
// 租户时区
export const getTenantTimezone = async () => {
    const userTimezone = getAppGlobalData('APP_USER_CONFIG')?.timeZone;
    let timezone: string = userTimezone;
    if (!(window as any)?.['TENANT.TIMEZONE.OFFSET']) {
        const tenantTimezone =
            (await getTenantParamsSetting({
                parameterKey: 'TENANT.TIMEZONE.OFFSET',
            })) ?? userTimezone;
        (window as any)['TENANT.TIMEZONE.OFFSET'] = String(
            Number(tenantTimezone) * 60 * 60,
        );
        timezone = String(Number(tenantTimezone) * 60 * 60);
    } else {
        timezone = (window as any)['TENANT.TIMEZONE.OFFSET'];
    }
    return Number(timezone);
};
/**
 * 根据透明度数值获取对应的16进制的透明度
 * @param opacity 透明度
 */
export const getHexOpacity = (opacity: number) => {
    opacity = Math.max(opacity);
    opacity = Math.min(opacity, 1);
    let num = Math.round(255 * opacity);
    let str = '';
    const arrHex = [
        '0',
        '1',
        '2',
        '3',
        '4',
        '5',
        '6',
        '7',
        '8',
        '9',
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
    ];
    while (num > 0) {
        const mod = num % 16;
        num = (num - mod) / 16;
        str = arrHex[mod] + str;
    }
    if (str.length === 1) {
        str = `0${str}`;
    }
    if (str.length === 0) {
        str = '00';
    }
    return str;
};

/**
 * 将颜色转化成带透明度的颜色
 * @param color
 * @param opacity
 * @returns
 */
export const getHexOpacityColor = (color: string, opacity: number) => {
    if (!color) return '';
    const opacityHex = getHexOpacity(opacity);
    color = color.replace('#', '').toLocaleUpperCase();
    return `#${color}${opacityHex}`;
};

export const getCountryLocation = () => {
    const { areaLocation } = getAppGlobalData('APP_USER_CONFIG');
    // 国家地区设置的第一个经度，第二个纬度
    try {
        if (areaLocation) {
            const center = areaLocation.split(',');
            if (
                center.length !== 2 ||
                Number(center[1]) > 90 ||
                Number(center[1]) < -90 ||
                Number(center[0]) > 180 ||
                Number(center[0]) < -180
            ) {
                console.error('地图中心点经纬度不在符合范围内');
                return;
            }
            return [Number(center[1]), Number(center[0])];
        }
    } catch (error) {
        return null;
    }
    return null;
};
/***************************权限规则展示公共方法，司机，车组，车辆 start***********************/
type FleetItem = {
    fleetId: string;
    fleetName: string;
};
type AuthFleetListProps = {
    fleetCount: number;
    fleetList: FleetItem[];
};
type FleetParse = {
    fleetList: FleetItem[];
    authDisable: boolean;
};
type VehicleItem = {
    vehicleId: string;
    vehicleNumber: string;
};
type AuthVehicleListProps = {
    vehicleCount: number;
    vehicleList: VehicleItem[];
};
type VehicleParse = {
    vehicleList: VehicleItem[];
    authDisable: boolean;
};
type DriverItem = {
    driverId: string;
    driverName: string;
};
export const NO_AUTH_NUMBER = '-2'; //-2表示无权限
export const UNKNOWN_DRIVER = '-3'; // 未知司机
/**
 * @description: 根据车组详情判断是否存在无权限的车组
 * @param {AuthFleetListProps} fleetData 车组信息
 * @return {*}有权限的车组，是否禁用
 */
export const getAuthFleetList = (fleetData: AuthFleetListProps): FleetParse => {
    // 通过fleetCount和fleetList长度判断是否存在无权限的车组
    const { fleetCount, fleetList } = fleetData;
    let authDisable = false;
    try {
        if (fleetCount === 0) {
            return {
                fleetList: [],
                authDisable,
            };
        } else if (fleetCount > fleetList.length) {
            // 有没有权限的车组
            authDisable = true;
            fleetList.unshift({
                fleetId: NO_AUTH_NUMBER,
                fleetName: NO_AUTH_TEXT,
            });
        }
        return { fleetList, authDisable };
    } catch (error) {
        console.error(i18n.t('message', '车组权限规则展示出错'));
        return { fleetList, authDisable };
    }
};

/**
 * @description: 根据车辆详情判断有权限的车辆列表，是否disable；//todo 字段不同，抽离公共方法后，似乎不好用，后面提测后和车组抽取公共方法，
 * @param {AuthVehicleListProps} vehicleData 车辆详情
 * @return {*} 有权限的车辆
 */
export const getAuthVehicleList = (
    vehicleData: AuthVehicleListProps,
): VehicleParse => {
    // 通过vehicleCount和vehicleList长度判断是否存在无权限的车辆
    const { vehicleCount, vehicleList } = vehicleData;
    let authDisable = false;
    try {
        if (vehicleCount === 0) {
            return {
                authDisable,
                vehicleList: [],
            };
        } else if (vehicleCount > vehicleList.length) {
            // 有没有权限的车组
            authDisable = true;
            vehicleList.unshift({
                vehicleId: NO_AUTH_NUMBER,
                vehicleNumber: NO_AUTH_TEXT,
            });
        }
        return { vehicleList, authDisable };
    } catch (error) {
        console.error(i18n.t('message', '车组权限规则展示出错'));
        return { vehicleList, authDisable };
    }
};
// 判断车组否有权限
export const fleetHasAuthDisable = (fleetId, fleetName) => {
    if (fleetId == NO_AUTH_NUMBER && !fleetName) {
        return true;
    }
    return false;
};
/**
 * @description: 根据车组path 获取有权限的车组路径
 * @param {string} fleetPath 车组path
 * @param {any} fleetAuthList 有权限的车组列表
 * @return {*} string Path
 */
export const getAuthFleetPath = (fleetPath: string, fleetAuthList: any[]) => {
    if (fleetPath) {
        let fleetPathName = '';
        if (fleetPath.includes(',')) {
            fleetPath = fleetPath.split(',');
        } else {
            fleetPath = [fleetPath];
        }
        const fleetList = fleetPath.map((item) => {
            return item.split('/');
        });
        for (let i = 0; i < fleetList.length; i++) {
            const fleetItem = fleetList[i];
            if (fleetPathName) {
                fleetPathName += ',';
            }
            let fleetItemName = '';
            for (let j = fleetItem.length - 1; j >= 0; j--) {
                const item = fleetItem[j];
                const hasAuth = fleetAuthList.find(
                    (fleet) => fleet.fleetId === item,
                );
                if (hasAuth) {
                    if (fleetItemName) {
                        fleetItemName = hasAuth.fleetName + '/' + fleetItemName;
                    } else {
                        fleetItemName = hasAuth.fleetName + fleetItemName;
                    }
                } else {
                    break;
                }
            }
            fleetPathName += fleetItemName;
        }

        return fleetPathName || '-';
    }
    return '-';
};

/**
 * @description: 司机权限判断，无权限******
 * @param {DriverItem} driverListSource 司机列表
 * @return {*} 司机字符窜，逗号隔开
 */
export const getAuthDriverString = (
    driverListSource: DriverItem[],
    driverId?: string,
    emptyText?: string,
): string => {
    const isUnknownDriver = (id?: string) => {
        if (!id) return false;
        return id.indexOf('-') === 0 && id != NO_AUTH_NUMBER;
    };
    if (driverId == NO_AUTH_NUMBER) {
        return NO_AUTH_TEXT;
    } else if (isUnknownDriver(driverId)) {
        return !isNil(emptyText) ? emptyText : i18n.t('name', '-');
    }

    // 司机只有id=-2才是无权限，id=0为无司机
    const driverNameHtml: any = [];
    if (Array.isArray(driverListSource)) {
        const driverList = driverListSource.filter((item: any) => {
            const isUnknown = isUnknownDriver(item.driverId);
            return (
                Object.keys(item).length > 0 &&
                item.driverId &&
                item.driverId != '0' &&
                !isUnknown
            );
        });
        if (driverList.length > 0) {
            const authDriverList = driverList.filter(
                (item: any) =>
                    item.driverId &&
                    item.driverName &&
                    item.driverId != NO_AUTH_NUMBER,
            );
            //有权限的司机
            // 只要有有权限的司机就展示有的司机，没有的不管，如果一个司机的权限都没有，就展示“******”
            if (authDriverList.length > 0) {
                if (authDriverList.find((i) => i.driverId === NO_AUTH_NUMBER)) {
                    return NO_AUTH_TEXT;
                }
                authDriverList.forEach((item: any, index: number) => {
                    driverNameHtml.push(item.driverName);
                });
            } else {
                driverNameHtml.push(NO_AUTH_TEXT);
            }
        } else {
            driverNameHtml.push(!isNil(emptyText) ? emptyText : '-');
        }
    } else {
        driverNameHtml.push(!isNil(emptyText) ? emptyText : '-');
    }
    return driverNameHtml.join(',');
};
/***************************权限规则展示，司机，车组，车辆 end***********************/

// 获取是否开启一车多设备
export const getIsOpenMutilDevice = async (appId: string) => {
    const devicenumber = await fetchApplicationConfig({
        appId,
        keys: 'vehicle.device.max',
    });
    if (devicenumber[0]?.value) {
        return devicenumber[0]?.value !== '-1' ? true : false;
    } else {
        return false;
    }
};

export const Month = {
    // 某一月有多少天
    days(year: number, month: number) {
        const days = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
        if (year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0)) {
            days[1] = 29;
        }
        return days[month - 1];
    },
    // 一个月的日期列表 : ['07-01','07-02',...]
    dayList(month: number, days: number) {
        const dateList: string[] = [];
        for (let i = 1; i <= days; i++) {
            dateList.push(
                `${month < 10 ? '0' + month : month}-${i < 10 ? '0' + i : i}`,
            );
        }
        return dateList;
    },
    startAndEndDateStr(year: number, month: number, days?: number) {
        let lastDay = days;
        if (!lastDay) {
            lastDay = this.days(year, month);
        }
        return {
            startTime: `${year}-${month < 10 ? '0' + month : month}-01`,
            endTime: `${year}-${month < 10 ? '0' + month : month}-${lastDay}`,
        };
    },
};
export const countryUtc = (areaTimeZone: number) => {
    return `${
        TIME_ZONE_UTC_OPTIONS?.find(
            (timeZone: any) => timeZone.value == areaTimeZone,
        )?.label
    }`;
    if (devicenumber[0]?.value) {
        return devicenumber[0]?.value !== '-1' ? true : false;
    } else {
        return false;
    }
};
export const int2latlng = (value: number) => value / Math.pow(10, 6);
// 转换经纬度 缩小10e6
export const transformIntList2LatLngList = (list: any[]) =>
    list.map((item) => {
        const { lat, lng } = { lat: item.lat, lng: item.lng };
        return {
            ...item,
            lat: int2latlng(lat),
            lng: int2latlng(lng),
        };
    });
// 获取抓拍设置详情-适用范围的tab数组
export const getCaptureScopeModules = async (appId: string) => {
    const isOpen = await getIsOpenMutilDevice(appId);
    const modules: ScopeModuleType[] = ['fleet', 'vehicle'];
    if (isOpen) {
        modules.push('device');
    }
    return modules;
};
export const getOffsetTimeByTimeStamp = (
    timeStamp: number | Moment,
    unit?: 'minute' | 'second',
) => {
    const { timeZone, dst } = getAppGlobalData('APP_USER_CONFIG');
    let dstTime = getSummerTimeOffsetByTime(timeStamp);
    if (unit === 'minute') {
        dstTime =
            (dst == 1 ? Number(timeZone) + dstTime : Number(timeZone)) / 60;
    } else {
        dstTime = dst == 1 ? Number(timeZone) + dstTime : Number(timeZone);
    }
    console.warn('时区加夏令时偏移：', dstTime);
    return dstTime;
};
/**
 * @description: 根据传入的字符串判断word之间是否有多个空格，并进行格式化，word之间只保留一个空格
 * @param {string} value
 * @return {string} 返回格式化后（word之间只有一个空格）的字符串
 */
export const getOnlyOneSpaceBetweenWords = (value: string): string => {
    if (!Boolean(value.trim())) {
        // 输入都为空格
        return value.trim();
    } else {
        let spaceNum = 0; // 空格数量
        const valueArr = value.split(' ');
        for (let i = 0; i < valueArr.length; i++) {
            if (valueArr[i] === '') {
                spaceNum++;
            }
        }
        if (spaceNum >= 1) {
            if (valueArr[valueArr.length - 1] !== '') {
                // 最后一位不是空格不需要设置空格（复制过来的情况）
                return `${value
                    .split(' ')
                    .filter((i) => i)
                    .join(' ')}`;
            }
            // 输入两个以上空格只保留一个
            return `${value
                .split(' ')
                .filter((i) => i)
                .join(' ')} `;
        } else {
            return value;
        }
    }
};
/**
 * @description: 根据传入的时间判断夏令时是否生效
 * @param {number} datumTime
 * @return {*}
 */
export const IS_DST = (datumTime: number | Moment) => {
    // dst 是否实行夏令时1实行 0关闭  summerTimeList 实行的夏令时范围
    const { dst, summerTimeList } = getAppGlobalData('APP_USER_CONFIG');
    let isDstTime = false;
    if (parseInt(dst, 10) === 1) {
        // 实行夏令时
        if (summerTimeList) {
            // 夏令时范围时间段转为number，方便后面比较
            summerTimeList.forEach((item: SummerTimeItem) => {
                item.startTime = parseInt(item.startTime as string, 10);
                item.endTime = parseInt(item.endTime as string, 10);
            });
            if (datumTime) {
                // datumTime如果是moment对象转化为时间戳
                if (moment.isMoment(datumTime)) {
                    const format = 'YYYY-MM-DD HH:mm:ss';
                    datumTime = moment.utc(datumTime.format(format)).unix();
                }
                // @ts-ignore 基准时间10进制number类型
                datumTime = parseInt(datumTime, 10);
                const datumTimeInSummerTime = summerTimeList.some(
                    (item: SummerTimeItem) =>
                        // @ts-ignore
                        datumTime > item.startTime && datumTime < item.endTime,
                );
                // 在夏令时范围内
                if (datumTimeInSummerTime) {
                    isDstTime = true;
                }
            }
        }
    }
    return isDstTime;
};

export const validatorMaxUser = (_, value) => {
    if (value?.length > MAX_USER_NUMBER) {
        return Promise.reject(
            new Error(
                i18n.t('message', '最多选择{number}个推送用户', {
                    number: MAX_USER_NUMBER,
                }),
            ),
        );
    }
    return Promise.resolve();
};

export const validatorMaxRole = (_, value) => {
    if (value?.length > 20) {
        return Promise.reject(
            new Error(
                i18n.t('message', '最多选择{number}个推送角色', { number: 20 }),
            ),
        );
    }
    return Promise.resolve();
};
// 获取列表配置项
export const getTableStorageKey = (key?: string): string[] | null => {
    if (!key) return null;
    try {
        const settingKeys = JSON.parse(
            localStorage.getItem('STARRY_TABLE_COLUMN_SETTING_KEYS')!,
        );
        if (settingKeys[key]) {
            return settingKeys[key];
        }
        return null;
    } catch (error) {
        console.warn(error);
    }
    return null;
};
// 套餐格式化 GB -> GB/TB
export const gbTransform = (space: number, hasUnit?: boolean = true) => {
    if (space && space > 1024) {
        return `${(space / 1024).toFixed(2)}${hasUnit ? 'TB' : ''}`;
    } else {
        return `${space}${hasUnit ? 'GB' : ''}`;
    }
};

/**
 * @description: 获取设备状态
 * @param {DeviceItem} device
 * @param {VehicleOrderState} stateConfig
 * @return {*}
 */
export const getDeviceStates = (
    device: DeviceItem,
    stateConfig: VehicleOrderState[],
): VehicleOrderState[] => {
    if (!device?.stateList) return [];
    const activeDeviceStateIds = device.stateList
        // 兼容 目前一个定位是string 一个是number
        .filter(
            (item) => Number(item.stateActive) === Number(StateActive.ACTIVE),
        )
        .map((item) => item.stateId);
    return stateConfig.filter((item) =>
        activeDeviceStateIds.includes(item.stateId),
    );
};

/**
 * @description 将设备实时状态拼入原数据
 * @param {any[]} originDeviceList 不携带设备状态的数据
 * @param {any[]} stateDeviceList 携带设备状态的数据
 * @return {*}
 */
export const getCompleteDevice = (
    originDeviceList: any[],
    stateDeviceList: any[],
) => {
    return originDeviceList.map((deviceItem: any) => {
        return {
            ...deviceItem,
            ...stateDeviceList?.find(
                (clickDeviceItem: any) =>
                    clickDeviceItem.deviceId === deviceItem.deviceId,
            ),
        };
    });
};

/**
 * @description: 处理sdk onError hook，可指定某些错误类型进行提示
 * @param {string} subErrorType
 * @param {Record<string, string>} needMessageErrorcode
 * @return {*}
 */
export const globalMessageMention = (
    subErrorType: string,
    needMessageErrorcode: Record<string, string>,
) => {
    if (!subErrorType) return;
    if (Object.keys(needMessageErrorcode).includes(subErrorType)) {
        message.error(needMessageErrorcode[subErrorType]);
    }
};

export const illegalCharacter = (rule: any, value: string) => {
    if (
        value &&
        (/[\\/*,？?‘’''“”()""<>|｜]/g.test(value) || /\[|\]/g.test(value))
    ) {
        return Promise.reject(i18n.t('message', '非法字符'));
    }
    return Promise.resolve();
};

/**
 * @description: 检测code是否有权限
 * @param {string} code
 * @return {*}
 */
export const checkAuth = (code: string): boolean => {
    const { disableAuth } = getAppGlobalData('__RUNTIME_STARRY_CONFIG');
    if (disableAuth) return true;
    const { actions } = getAppGlobalData('APP_RESOURCE');
    return !!actions[code?.trim?.()];
};

/**
 * 根据给定的车队ID过滤车辆列表，并根据需要包含子车队。
 *
 * @param {string} fleetIds - 要过滤车辆列表的车队ID。
 * @param {number} includeSubFleet - 是否包含子车队的标志。
 * @param {any[]} carList - 要过滤的车辆列表。
 * @return {[]} - 过滤后的车辆列表。
 */
export const getVehicleByFleetIds = (
    fleetIds: string,
    includeSubFleet: number,
    carList: any[],
) => {
    return carList.filter((item: any) => {
        let judgeField = 'fleetId';
        if (includeSubFleet) judgeField = 'path';
        return !!item.fleetList.find(
            (fleetItem: any) => !!fleetItem[judgeField].includes(fleetIds),
        );
    }) as [];
};

// 是否过期
export const isExpired = (expireTime: number) => {
    return new Date(expireTime).getTime() < new Date().getTime();
};

// 是否开启过长期不登录休眠
export const getUserStateConfig = async () => {
    const APP_USER_INFO = getAppGlobalData('APP_USER_INFO');
    const params = {
        tenantId: APP_USER_INFO.tenantId,
        keys: 'tenant.user.state.config',
    };
    const result = { isSleep: false, isExpired: false };
    try {
        const userConfigInfo = await getUserInfoConfig(params); // 无值时返回 ：{}
        const info = userConfigInfo['tenant.user.state.config']
            ? JSON.parse(userConfigInfo['tenant.user.state.config'])
            : {};
        if (typeof info?.longTermNoLoginLock === 'number') {
            result.isSleep = true;
        } else {
            result.isSleep = false;
        }
        result.isExpired = Boolean(info?.userAutoExpireSwitch);
        return result;
    } catch (error) {
        return { isSleep: false, isExpired: false };
    }
};

/**
 * @param {string} IP
 * @return {boolean}
 */
export const validIPAddress = (ip: string) => {
    const ipv4 = /^((\d|[1-9]\d|1\d\d|2([0-4]\d|5[0-5]))\.){4}$/;
    const ipv6 =
        /^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)|::([\da−fA−F]1,4:)0,4((25[0−5]|2[0−4]\d|[01]?\d\d?)\.)3(25[0−5]|2[0−4]\d|[01]?\d\d?)|::([\da−fA−F]1,4:)0,4((25[0−5]|2[0−4]\d|[01]?\d\d?)\.)3(25[0−5]|2[0−4]\d|[01]?\d\d?)|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)|([\da−fA−F]1,4:)2:([\da−fA−F]1,4:)0,2((25[0−5]|2[0−4]\d|[01]?\d\d?)\.)3(25[0−5]|2[0−4]\d|[01]?\d\d?)|([\da−fA−F]1,4:)2:([\da−fA−F]1,4:)0,2((25[0−5]|2[0−4]\d|[01]?\d\d?)\.)3(25[0−5]|2[0−4]\d|[01]?\d\d?)|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)|([\da−fA−F]1,4:)4:((25[0−5]|2[0−4]\d|[01]?\d\d?)\.)3(25[0−5]|2[0−4]\d|[01]?\d\d?)|([\da−fA−F]1,4:)4:((25[0−5]|2[0−4]\d|[01]?\d\d?)\.)3(25[0−5]|2[0−4]\d|[01]?\d\d?)|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}|:((:[\da−fA−F]1,4)1,6|:)|:((:[\da−fA−F]1,4)1,6|:)|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)|([\da−fA−F]1,4:)2((:[\da−fA−F]1,4)1,4|:)|([\da−fA−F]1,4:)2((:[\da−fA−F]1,4)1,4|:)|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)|([\da−fA−F]1,4:)4((:[\da−fA−F]1,4)1,2|:)|([\da−fA−F]1,4:)4((:[\da−fA−F]1,4)1,2|:)|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?|([\da−fA−F]1,4:)6:|([\da−fA−F]1,4:)6:/;
    return ipv4.test(ip + '.') || ipv6.test(ip + ':');
};
export const chunkArray = (array: any[], size: number) => {
    if (!Array.isArray(array)) return null;
    const result = [];
    for (let i = 0; i < array.length; i += size) {
        result.push(array.slice(i, i + size));
    }
    return result;
};

export interface OffsetInfoType {
    top: number;
    left: number;
    [params: string]: any;
}

export function getOffsetInfo(element: HTMLElement): OffsetInfoType {
    const ret = { top: 0, left: 0, width: 0, height: 0 };
    if (!element) {
        return ret;
    }

    if (!element.getClientRects().length) {
        return ret;
    }

    const rect = element.getBoundingClientRect();

    if (rect.width || rect.height) {
        const doc = element.ownerDocument;
        const docElem = doc.documentElement;
        return {
            top: Math.round(rect.top - docElem.clientTop),
            left: Math.round(rect.left - docElem.clientLeft),
            width: Math.round(rect.width),
            height: Math.floor(rect.height),
        };
    }

    return {
        top: Math.round(rect.top),
        left: Math.round(rect.left),
        width: 0,
        height: 0,
    };
}
// 单位（MB）换算
export const formatMByte = (size: number) => {
    if (size >= 1024) {
        return size / 1024 + 'M';
    } else {
        return size + 'K';
    }
};
export const getTipMessage = (DEFAULT_UPLOAD_SIZE: number) => ({
    isSizeOk: i18n.t('message', '仅支持最大上传{size}', {
        size: formatMByte(DEFAULT_UPLOAD_SIZE),
    }),
});
// 获取第一个tab的key，并支持设置第一个key
export const setDefaultTabKey = (
    tabList: (React.ReactElement | boolean)[],
    setFun?: (key: string) => void,
) => {
    if (Array.isArray(tabList)) {
        const firstTab = tabList.find((item) => item && item.key);
        firstTab && firstTab.key && setFun?.(firstTab.key);
        return firstTab;
    }
    return null;
};

// 增加最少6个字符的校验
export const validateMinSixLen = (rule: any, value: string) => {
    if (value.trim().length < 6) {
        return Promise.reject(i18n.t('message', '至少填写6个字符'));
    }
    return Promise.resolve();
};
/**
 * @description: 传入一个数据，返回该数字最接近0.5的倍数最近的值
 * @param {*} number
 * @return {*}
 */
export const formatToNearestHalf = (number: number) => {
    if (!number) {
        return number;
    }
    // 确定最近的0.5的倍数
    const lowerMultiple = Math.floor(number * 2) / 2; // 下一个0.5的倍数
    const upperMultiple = (Math.floor(number * 2) + 1) / 2; // 上一个0.5的倍数
    // 比较并选择
    if (number - lowerMultiple < upperMultiple - number) {
        return lowerMultiple;
    } else {
        return upperMultiple;
    }
};
export const transBtoGB = (storeNumber: number) => {
    if (storeNumber && Number.isFinite(Number(storeNumber))) {
        const trans =
            TRANS_STORAGE_UNIT * TRANS_STORAGE_UNIT * TRANS_STORAGE_UNIT;
        return storeNumber / trans;
    }
    return storeNumber;
};

// 根据value查找label
export const findLabelPath = (tree, targetValue, path = []) => {
    for (const node of tree) {
        // 如果当前节点的 value 与目标 value 匹配
        if (node.value === targetValue) {
            return path.concat(node.label).join('/');
        }
        // 如果当前节点有子节点，则递归遍历子节点
        if (node.children && node.children.length > 0) {
            const result = findLabelPath(
                node.children,
                targetValue,
                path.concat(node.label),
            );
            if (result) return result;
        }
    }
    return null; // 如果未找到目标 value
};
export const parseToSecond = (time?: number) => {
    // 大于1秒就向下取整，小于1秒向上取整
    if (time) {
        const second = time / 1000;
        if (second > 1) {
            return Math.floor(second);
        } else {
            return Math.ceil(second);
        }
    }
    return 0;
};
export const executeMethod = async (
    methods: (((data: any) => void) | undefined)[],
    requestData,
): any => {
    const validMethod = methods.find((method) => typeof method === 'function');
    if (validMethod) {
        const result = await validMethod(requestData);
        return result;
    }
    return {};
};
export const sortObjectKeys = (obj) => {
    return Object.keys(obj)
        .sort()
        .reduce((acc, key) => {
            acc[key] = obj[key];
            return acc;
        }, {});
};

export const isValid = (value) => {
    return (
        !isNil(value) &&
        value !== '' &&
        value !== 'null' &&
        value !== 'undefined'
    );
};
export const disabledFutureMonths = (current) => {
    // 禁用当前日期之后的月份
    return current && current > moment().endOf('month');
};
export enum FileChannelAuthEnum {
    ALL_AUTH = 1, //为空或者1：证据视频、图片全部通道均有权限；
    PART_AUTH = 2, // 2：证据视频、图片部分通道有权限； 
    NO_AUTH = 3, //3：证据视频、图片全部通道均无权限
}
/**
 * @description: 判断是否存在通道权限
 * @param {null} fileChannelAuth //通道权限字段
 * @param {null} messagePrompt //是否要提示语
 * @return {boolean}
 */
export const noChannelAuth = (fileChannelAuth: FileChannelAuthEnum | undefined | null, messagePrompt = true): boolean => {
    if(fileChannelAuth === FileChannelAuthEnum.NO_AUTH) {
        messagePrompt && message.warn(i18n.t('message', '暂无通道权限，不支持下载/分享/导出操作'));
        return true;
    }
    return false;
};
/**
 * @description: 复制传入的内容
 * @param {*} text 要复制的内容
 * @return {*}
 * @deprecated 已废弃，不建议使用（原因：runtime-lib循环引用），改为使用runtime-lib中的copyToClipboard
 */
export const copyToClipboard = async (text) => {
    if (text) {
        let textarea = null;
        try {
            // 现代浏览器，但不支持http
            if (navigator.clipboard?.writeText) {
                await navigator.clipboard.writeText(text);
                message.success(i18n.t('message', '复制成功'));
                return;
            }
            // 兼容http
            textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            const success = document.execCommand('copy');
            if (success) {
                message.success(i18n.t('message', '复制成功'));
            } else {
                message.warn(i18n.t('message', '复制失败'));
            }
        } catch (error) {
            message.warn(i18n.t('message', '复制失败'));
        }
        textarea && document.body.removeChild(textarea);
    }
};
export const getImgUrls = async (fileIds: string | number, headers?: Record<string, any>) => {
    const dataList = await fetchFileDownloadUrl(
        {
            fileIdList: fileIds.toString(),
            validTime: 365,
        },
        { ...headers },
    );
    return dataList;
};
export type TCabinDeviceAndChannelInfo = {
    deviceId: string;
    authId: string;
    cabinChannels: number[]
}
// 根据设备查询通道类型
export const fetchChannelType = async (deviceIds: string) => {
    const filterInCarChannels: TCabinDeviceAndChannelInfo[] = [];
    if(!deviceIds) return [];
    const deviceItemChannel = await fetchDeviceConfigureChannelList({
        deviceIds: deviceIds
    });
    deviceItemChannel.forEach(deviceItem => {
        const deviceChannelInfo = {
            deviceId: deviceItem.deviceId,
            cabinChannels:  [],
            authId: deviceItem.authId
        };
        deviceItem.channelInfoList.forEach(channelItem => {
            if(channelItem?.algorithmModel === IN_CAR_CHANNEL_ID) {
                deviceChannelInfo.cabinChannels.push(channelItem.channelNumber);
            }
        });
        filterInCarChannels.push(deviceChannelInfo);
    });
    return filterInCarChannels;
};
