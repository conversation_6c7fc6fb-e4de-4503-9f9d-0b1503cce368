import qs from 'querystring';
import { BaseSuccessCode, CacheType } from '../../runtime-lib/request';
import { getAppGlobalData, setAppGlobalData } from '../../runtime-lib/global-data';
import createResourceData from '../resource';

import initGoogleAnalytics from '../google-analytics';
import {checkThirdAccountBindResult} from "@/utils/checkThirdAccountBindResult";

import afterAllDataSettings from './utils/afterAllDataSettingsInMainPage';
import handleError from './utils/handleError';
import setRuntimePages from './utils/setRuntimePages';
import setupLoggingAndAuth from './utils/setupLoggingAndAuth';
import handleCustomAuth from './utils/handleCustomAuth';
import handleTenantExchange from './utils/handleTenantExchange';
import fetchSystemInitData from './utils/fetchSystemInitData';
import removeQueryParam from './utils/removeQueryParam';
import processSystemInitData from './utils/processSystemInitData';
import handleAppEntrys from './utils/handleAppEntrys';
import handleAppUserConfig from './utils/handleAppUserConfig';
import initializeInternationalization from './utils/initializeInternationalization';
import setThemeConfig from './utils/setThemeConfig';
import { i18n } from '@/runtime-lib';
import mainPageHandleIframeMessage from './utils/mainPageHandleIframeMessage';
import { setLang, getLang } from './utils/lang';
import handleTokenAndVoucher from './utils/handleTokenAndVoucher';
import { tenantParams } from "@/runtime-lib/utils";
import { initAlarm } from '../../runtime-lib/modules';
import '@/logger';
import { getURLSearch } from './utils/getURLSearch';


type AppId = string | number;

/**
 * 收集运行时配置中各个子应用的生命周期配置
 * @param runtimeConfig - 运行时配置对象
 * @returns {Object} 按appId分组的生命周期配置对象
 *
 * @example
 * const config = {
 *   entries: {
 *     "@streamax/app1": {
 *       appIds: [appId1,appId2],
 *       someConfig: "value"
 *     },
 *  "@streamax/app2": {
 *       appIds: [appId1,appId3],
 *       someConfig: "value"
 *     }
 *   }
 * }
 * collectLifecycleConfigs(config)
 * 返回:
 * {
 *    appId1: { someConfig: "value" },
 *    appId2: { someConfig: "value" }
 * }
 */
export const collectAppConfig = (runtimeConfig: any) => {
    const lifecycleConfigs = Object.entries(runtimeConfig?.entries || {}).reduce((acc: Record<AppId, Record<string, any>>, [key, entry]: [string, any]) => {
        const appIds: AppId[] = entry.appIds || [];
        appIds.forEach(appId => {
            if (!acc[appId]) {
                acc[appId] = {};
            }
            Object.entries(entry).forEach(([configKey, configValue]) => {
                if (configKey !== 'appIds') {
                    acc[appId][configKey] = configValue;
                }
            });
        });
        return acc;
    }, {});
    return lifecycleConfigs;
};
export default async (originAuthAppId: AppId, runtimeConfig: any) => {
    let appId = originAuthAppId;
    try {
        const lifecycleConfig = collectAppConfig(runtimeConfig);
        console.log('runtimeConfig:', runtimeConfig, lifecycleConfig, appId);
        // 设置运行时参数 __RUNTIME_STARRY_PAGES
        setRuntimePages();

        setAppGlobalData('__RUNTIME_STARRY_CONFIG', runtimeConfig || {});

        // 获取url search
        const search = getURLSearch();

        // 执行初始化行业层自定义生命周期
        // 是否已经执行过定制生命周期函数
        let hasCustomLifecycle = false;
        if (appId && !runtimeConfig?.onlySwitchApp) {
            // 如果从url上拿到appid，则执行行业定制的生命周期代码
            await lifecycleConfig[appId]?.customLifecycle?.();
            hasCustomLifecycle = true;
            // customAuth = lifecycleConfig[appId].customAuth || customAuth;
        }

        // 处理语言lang，
        const { lang } = await getLang();
        // 处理token和voucher信令
        // 清除url中的auth_voucher，设置 localStorage 的token
        const { voucher } = await handleTokenAndVoucher(runtimeConfig);

        // 处理主子租户切换
        // 从window.name 获取存储数据并返回
        const tenantExchangeUserInfo = await handleTenantExchange();

        // 调用init接口，获取系统初始化数据
        const cacheControl = search?.cacheControl || runtimeConfig.cacheControl || CacheType.CacheFirst;
        const { data, res, entryPath } = await fetchSystemInitData({
            defaultEntryPath: runtimeConfig.entryPath,
            voucher,
            tenantExchangeUserInfo,
            lang,
            cacheControl
        });

        const { data: systemInitData, code, message, langKey } = data;
        console.warn("================[[颜豪，mainPageDataSetting]]============",lang, systemInitData?.langType)

        setLang(lang, systemInitData?.langType);

        // 错误直接走异常处理
        if (code !== BaseSuccessCode) {
            throw {
                code,
                message,
                langKey,
            };
        }

        // 删除url上的cacheControl
        search?.cacheControl && removeQueryParam('cacheControl');

        // 设置token
        const { headers } = res;
        // [117297] init接口存在返回值中res为undefined的错误
        if (!res?.headers) {
            return;
        }

        // 用户是否自定义过登录
        const customAuth = !runtimeConfig.toLogin;
        // 接口返回数据失败，也会返回 headers 中的 token
        if (!customAuth && headers._token) {
            window.localStorage.setItem('AUTH_TOKEN', headers._token as string);
        }

        // 设置服务入口数据， 设置 globalData APP_ID、APP_ENTRY
        const appEntrys = systemInitData.entrys.mappers;
        appId = systemInitData.appId;
        let appEntry = entryPath;
        try {
            const entryResult = await handleAppEntrys({
                currentServiceEntrance: systemInitData.currentServiceEntrance,
                appEntrys,
                appId,
                entryPath
            });
            if (entryResult) {
                appEntry = entryResult;
            }
        } catch (error) {
            console.log('======handleAppEntrys error', error);
            return appId;
        }

        if(!hasCustomLifecycle && !runtimeConfig?.onlySwitchApp) {
            await lifecycleConfig[appId]?.customLifecycle?.();
        }


        /**
         * 处理初始化数据
         * 设置 globalData
         * STREAMAP_USER_KEY、
         * PLATFORM_TIME_ZONE、
         * REAL_USER_INFO、
         * APP_USER_INFO、
         * APP_ENTRYS
         */
        const {
            entrys,
            userConfigureEfficient,
            zoneDetail,
            needFilterAuthority,
            languageFrontListNew,
            themeStyle,
            themeColor,
            resource,
            activatedAppId,
            languageAllByType
        } = await processSystemInitData(systemInitData);


        // 处理用户配置数据 设置 globalData APP_USER_CONFIG
        handleAppUserConfig({
            userConfigureEfficient,
            zoneDetail,
            needFilterAuthority
        });
        // 处理国际化 初始化i8n，设置moment的语言
        await initializeInternationalization({
            languageFrontListNew,
            languageAllByType,
            lang
        });

        // 处理主题色
        setThemeConfig(
            runtimeConfig.loadTheme !== false,
            appId,
            themeStyle,
            themeColor?.themeColorValue
        );

        // 资源
        setAppGlobalData('APP_RESOURCE', await createResourceData(resource));

        // 等待所有执行中的Promise执行完成
        setAppGlobalData('CACHE_ENTRY_PAGES', {
            ...getAppGlobalData('CACHE_ENTRY_PAGES'),
            [appEntry]: getAppGlobalData('APP_RESOURCE')['pages'],
        });

        // 处理初始化获取租户参数，在init接口初始化之后再执行，租户参数获取需要token和用户信息
        await tenantParams.initTenantParams();

        initGoogleAnalytics();
        // 检查是否从第三方绑定页面跳回
        checkThirdAccountBindResult();
    } catch (e: any) {
        await handleError(e, runtimeConfig);
        return appId;
    }
    // 只切换应用时不再进行后续初始化
    if (runtimeConfig?.onlySwitchApp) {
        // 【127036】合并2.16.5版本修复bug
        // initAlarm();
        return appId;
    }
    await afterAllDataSettings(appId);
    mainPageHandleIframeMessage();
    return appId;
};
