import { CacheType, getAppGlobalData } from "@/runtime-lib";
import fetchSystemInitData from "./fetchSystemInitData";
import handleTenantExchange from "./handleTenantExchange";

/**
 * 临时方案：刷新初始化接口缓存，包含资源，词条，服务入口及服务入口排序等
 */
export const refreshCache = async () => {
    const _entryPath = window.location.pathname.replace(
        /^(\/[^\\/]+)\/?.*/,
        '$1',
    );
    const tenantExchangeUserInfo = await handleTenantExchange();
    fetchSystemInitData({
        defaultEntryPath: _entryPath,
        tenantExchangeUserInfo,
        voucher: '',
        lang: getAppGlobalData('APP_LANG'),
        cacheControl: CacheType.CacheFirst
    });
};